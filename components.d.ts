/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    PrimeVueAccordion: typeof import('primevue/accordion')['default']
    PrimeVueAccordionContent: typeof import('primevue/accordioncontent')['default']
    PrimeVueAccordionHeader: typeof import('primevue/accordionheader')['default']
    PrimeVueAccordionPanel: typeof import('primevue/accordionpanel')['default']
    PrimeVueBreadcrumb: typeof import('primevue/breadcrumb')['default']
    PrimeVuebutton: typeof import('primevue/button')['default']
    PrimeVueButton: typeof import('primevue/button')['default']
    PrimeVueCard: typeof import('primevue/card')['default']
    PrimeVueCheckbox: typeof import('primevue/checkbox')['default']
    PrimeVueChip: typeof import('primevue/chip')['default']
    PrimeVueColumn: typeof import('primevue/column')['default']
    PrimeVueDataTable: typeof import('primevue/datatable')['default']
    PrimeVueDatePicker: typeof import('primevue/datepicker')['default']
    PrimeVueDialog: typeof import('primevue/dialog')['default']
    PrimeVueDivider: typeof import('primevue/divider')['default']
    PrimeVueDrawer: typeof import('primevue/drawer')['default']
    PrimeVueIconField: typeof import('primevue/iconfield')['default']
    PrimeVueInputGroup: typeof import('primevue/inputgroup')['default']
    PrimeVueInputGroupAddon: typeof import('primevue/inputgroupaddon')['default']
    PrimeVueInputIcon: typeof import('primevue/inputicon')['default']
    PrimeVueInputNumber: typeof import('primevue/inputnumber')['default']
    PrimeVueInputText: typeof import('primevue/inputtext')['default']
    PrimeVueListbox: typeof import('primevue/listbox')['default']
    PrimeVueListBox: typeof import('primevue/listbox')['default']
    PrimeVueMessage: typeof import('primevue/message')['default']
    PrimeVuePopover: typeof import('primevue/popover')['default']
    PrimeVueProgressBar: typeof import('primevue/progressbar')['default']
    PrimeVueProgressSpinner: typeof import('primevue/progressspinner')['default']
    PrimeVueRadioButton: typeof import('primevue/radiobutton')['default']
    PrimeVueScrollPanel: typeof import('primevue/scrollpanel')['default']
    PrimeVueSelect: typeof import('primevue/select')['default']
    PrimeVueSkeleton: typeof import('primevue/skeleton')['default']
    PrimeVueStep: typeof import('primevue/step')['default']
    PrimeVueStepList: typeof import('primevue/steplist')['default']
    PrimeVueStepPanel: typeof import('primevue/steppanel')['default']
    PrimeVueStepPanels: typeof import('primevue/steppanels')['default']
    PrimeVueStepper: typeof import('primevue/stepper')['default']
    PrimeVueTab: typeof import('primevue/tab')['default']
    PrimeVueTabList: typeof import('primevue/tablist')['default']
    PrimeVueTabPanel: typeof import('primevue/tabpanel')['default']
    PrimeVueTabPanels: typeof import('primevue/tabpanels')['default']
    PrimeVueTabs: typeof import('primevue/tabs')['default']
    PrimeVueTextarea: typeof import('primevue/textarea')['default']
    PrimeVueToast: typeof import('primevue/toast')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
