// Interfaces
import type { RouterConfig } from '@nuxt/schema';

/**
 * @description List of routes
 * @note We will try to import automatically all routes from the modules later. Because, for right now nitro is not support to use import.meta.glob. Please check this out: https://github.com/nitrojs/nitro/issues/1671
 */

import authenticationRoutes from './src/modules/authentication/routes/authentication.route';
import homepageRoutes from './src/modules/homepage/routes/homepage.route';
import billRoutes from './src/modules/invoice/routes/invoice.route';
import orderRoutes from './src/modules/order/routes/order.route';
import cartRoutes from './src/modules/cart/routes/cart.route';
import checkoutRoutes from './src/modules/checkout/routes/checkout.route';
import profileRoutes from './src/modules/profile/routes/profile.route';
import productRoutes from './src/modules/product/routes/product.route';
import dashboardRoutes from './src/modules/dashboard/routes/dashboard.route';
import taxInvoice from './src/modules/tax-invoice/routes/tax-invoice.route'
import notificationRoutes from './src/modules/notification/routes/notification.route';
// Vue Router
// import { createMemoryHistory } from 'vue-router';

export default {
  // https://router.vuejs.org/api/interfaces/routeroptions.html#routes
  routes: async _routes => [
    ...authenticationRoutes,
    ...homepageRoutes,
    ...billRoutes,
    ...orderRoutes,
    ...cartRoutes,
    ...checkoutRoutes,
    ...profileRoutes,
    ...productRoutes,
    ...dashboardRoutes,
    ...taxInvoice,
    ...notificationRoutes,
  ] /* default */,
} satisfies RouterConfig;
