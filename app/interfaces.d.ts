// Interfaces
import type { BaseValidation } from '@vuelidate/core';
import type { Nullable } from 'vitest';
import type { ICartDetailDataLimit } from './src/modules/cart/interfaces/cart.interface';

export {};

/**
 * @description Here's a way to extend the global interfaces.
 */
declare global {
  /**
   * @description Here's we can define the global interfaces.
   */

  interface IBaseApiResponse<T> {
    error: boolean;
    status: string;
    message: string;
    data: T;
  }

  interface IBank {
    bank_account_name: string;
    bank_account_no: string;
    bank_name: string;
    company_id: string;
    created_by: string;
    created_date: string;
    id: string;
    is_active: boolean;
    modified_by: string;
    modified_date: string;
  }

  interface IBindStateForm {
    solo: boolean;
    flat: boolean;
    placeholder: string;
    class: string;
    hideDetails: string;
    autocomplete: string;
  }

  interface IComponentComposableOptions {
    clearBeforeUnmount?: boolean;
  }

  interface ICurrencyOptions {
    minimumFractionDigits: number;
    style: string;
    currency: string;
  }

  interface INavbarProvided {
    navbar_isOpenDialogConfirmationLogout: Ref<boolean>;
    navbar_isOpenDrawer: Ref<boolean>;
    navbar_onApplySearch: () => Promise<void>;
    navbar_onClearSearch: () => void;
    navbar_onCloseBottomSheetConfirmationLogout: () => void;
    navbar_onCloseDialogConfirmationLogout: () => void;
    navbar_onLogout: () => void;
    navbar_logoutIsLoading: Ref<boolean>;
    navbar_onOpenDialogConfirmationLogout: () => void;
    navbar_hasToken: () => boolean;
    navbar_width: Ref<number>;
    navbar_onHandleResize: () => void;
    navbar_searchQuery: Ref<string>;
  }

  export interface IFormInquiryPayload {
    type: string;
    full_name: string;
    no_telp: string;
    instituion_name: string;
    email: string;
    npwp_address: string;
    topic: string;
    question: string;
    terms: boolean;
  }

  interface ICustomerSupportProvided {
    customerSupport_openWhatsApp: () => void;
    customerSupport_formInquiry: IFormInquiryPayload;
    customerSupport_formValidations: globalThis.Ref<Validation>;
  }

  interface IPaginationResponse {
    total_data: number;
    size: number;
    active_page: number;
    total_page: number;
  }

  interface IStore {
    address: string;
    customer_shipment_id: string;
    name: string;
  }

  interface IOutletStock {
    total_sum: number;
    store_list: IStore[];
  }

  interface IProductCart {
    count: number;
  }

  interface IProductColor {
    key: string;
    value: string;
  }

  interface IProductMedia {
    article_no: string | null;
    is_main_image: boolean;
    sequence_no: number;
    url: string;
    variant: string;
  }

  interface IProductPrice {
    id?: string;
    amount: string;
    created_by: string;
    created_date: string;
    currency?: string;
    modified_by: string;
    modified_date: string;
    sku_code_c: string;
    valid_from: string;
    valid_to: string;
  }

  interface IProductVariantMedia extends IProductMedia {
    color: string;
    variant_no: string | null;
  }

  interface IProductDetailVariant {
    article_id: string;
    article_description: string;
    product_size_c: string;
    product_name: string;
    product_images: unknown[];
    article_category: string | null;
    weight: string;
    dimension: string;
    stock: number;
    stock_toko: IOutletStock;
    price: IProductPrice;
    created_date: string;
    created_by: string;
    modified_date: string;
    modified_by: string;
    min_qty: number;
    cart: IProductCart;

    // additional
    color: string;
  }

  interface IProductSpecification {
    activity: string;
    dimension: string;
    is_custom_logo: false;
    is_custom_size: boolean;
    material: string;
    uomweight: string;
    weight: string;
  }
  interface IProductVariant {
    cross_site: string;
    product_variant_c: string;
  }

  interface IProductCustomVariantPlacement {
    id: 'c27f926b-208f-11ee-830f-0628bea0944a';
    product_group: 'BACKPACK';
    product_type: null;
    image_sequence: '0';
    coordinate: [
      {
        coords: [
          {
            top_left: [360, 290];
            top_right: [640, 290];
            bottom_left: [360, 780];
            bottom_right: [640, 780];
          },
        ];
        tooltip: 'Logo Depan';
      },
    ];
    position_side: 'FRONT';
  }

  interface IProduct {
    article: string;
    article_description: string;
    sku_code_c: string;
    product_name_c: string;
    product_variant_c: string;
    product_size_c: string;
    product_description: string;
    product_style: string;
    product_feature: string;
    product_tag: string[];
    product_type: string;
    product_gender: string;
    product_material: string;
    product_status: null;
    product_category: string;
    wholesales_published_date: string;
    b2b_published_date: string;
    main_image: string;
    specification: IProductSpecification;
    media: IProductMedia[];
    variantmedia: IProductVariantMedia[];
    product_price: IProductPrice;
    variant: IProductVariant[];
    variantcustomer: unknown[];
    custom_placement: IProductCustomVariantPlacement[];
  }

  interface IProductList {
    id: string;
    image: string;
    image_variant: string;
    name: string;
    sku: string | null;
    total_variant: number;
    price: string | null;
    stock: number;
    flag: string[];
    cart: {
      count: number;
    };
  }

  interface IProductHomepage {
    product_name_c: string;
    product_description: string;
    sku_code_c: string;
    amount: string;
    currency: string;
    flag: string[];
    main_image: string;
    stock: number;
  }

  interface IProductSearchRecommendationItem {
    sku_code_c: string;
    product_name_c: string;
  }

  interface IProductListWithPagination {
    data: IProductList[];
    lastPage: boolean;
    showPage: number;
    total: number;
  }

  interface IPropsFormGroup {
    isNameAsLabel: boolean;
    isNameAsPlaceholder: boolean;
    isNotHaveSpacing: boolean;
    labelFor?: string;
    name: string;
    spacingBottom: string;
    validators: BaseValidation;
  }

  interface IPropsBaseAlert {
    durationOfAutoClose: number;
    isHaveIconClose: boolean;
    isOpen: boolean;
    text: string;
    type: EAlertType;
  }

  interface IPropsBaseProductCart {
    image: string;
    name: string;
    price: string;
    sku: string;
  }

  interface IQueryParamsOfProducts {
    activity: string | null;
    color: string | null;
    limit: number;
    maxPrice: number | Nullable;
    minPrice: number | Nullable;
    order_by: string | null;
    page: number;
    reff: string;
    search: string | null;
    size: string | null;
    subcategory: string | null;
  }

  interface IQueryParamsOfProductColorsAndSizes {
    category?: string;
    subcategory?: string;
  }

  interface IResponseListenerForm {
    input: () => void;
    blur: () => void;
  }

  interface IBusEvent {
    [key: string]: unknown;

    [key: symbol]: unknown;
  }

  interface ILimitCredit {
    credit_limit: string;
    credit_limit_used: string;
    credit_limit_used_percentage: string;
    credit_limit_remaining: string;
    currency: number;
    avg_transaction: string | null;
    total_transaction: number;
    last_transaction: string;
    last_update: string;
  }

  interface IDashboardSummaryTotalProduct {
    qty_berjalan: number;
    qty_sebelumnya: number;
    qty_compare: number;
    is_increase: boolean;
    qty_percentage: number;
    error: string;
    status: number;
  }

  interface IDashboardSummaryTotalTransaction {
    sub_total_berjalan: number;
    sub_total_sebelumnya: number;
    sub_total_compare: number;
    is_increase: boolean;
    sub_total_percentage: number;
    error: string;
    status: number;
  }

  interface IDashboardSummaryTransaction {
    error: boolean;
    is_increase: boolean;
    status: number;
    sub_total_berjalan: number;
    sub_total_compare: number;
    sub_total_percentage: number;
    sub_total_sebelumnya: number;
  }

  interface IDashboardOngoingOrder {
    created_date: string;
    order_no: string;
    total: number;
    order_status: string;
    payment_status: string;
    invoice_no: string;
  }

  interface IBreadcrumb {
    label: string;
    path?: string | null;
  }

  interface ICartVariantItem {
    cart_detail_id: string;
    article: string;
    varian: string;
    size: string;
    stock: number;
    stock_toko: {
      total_sum: number;
      store_list: unknown[];
    };
    qty: number;
    moq: number;
    custom_price: number;
    selected: boolean;
    preupdate: boolean;
    image_variant: string;
  }

  interface ICartAttachment {
    id: string;
    attachments_group_id: string;
    file_path: string | null;
    text: string | null;
    color: string;
    estimate_price: number;
  }

  interface ICartItem {
    tag: string;
    serie: string;
    category: string;
    flag: string[];
    product_name: string;
    sku: string;
    image_url: string;
    price: string;
    total_price: number;
    is_available: boolean;
    selected: boolean;
    cart_id: string;
    customs: unknown[];
    is_custom: string; // "1" | "0"
    items: ICartVariantItem[];
    attachments_group_id: string;
    remark: string | null;
    cart_attachments: ICartAttachment[];
  }

  interface ICart {
    cart_id: string;
    discount_percent: number;
    data_limit: ICartDetailDataLimit[];
    data_list_cart: {
      total_data: number;
      size: number;
      active_page: number;
      total_page: number;
      cart_detail: ICartItem[];
      isAbleCheckout: boolean;
    };
  }

  interface IHeaderMenu {
    menu: string;
    submenu: {
      name: string;
      menu_item: string[];
    }[];
  }

  interface INavbarProvided {
    navbar_isOpenDialogConfirmationLogout: Ref<boolean>;
    navbar_isOpenBottomSheetConfirmationLogout: Ref<boolean>;
    navbar_isOpenDrawer: Ref<boolean>;
    navbar_onApplySearch: () => Promise<void>;
    navbar_onClearSearch: () => void;
    navbar_onCloseDialogConfirmationLogout: () => void;
    navbar_onCloseBottomSheetConfirmationLogout: () => void;
    navbar_onLogout: () => void;
    navbar_onOpenDialogConfirmationLogout: () => void;
    navbar_searchQuery: Ref<string>;
  }

  interface IAppProvided {
    app_headerMenu: Ref<IHeaderMenu[]>;
    app_headerMenuIsLoading: Ref<boolean>;
    app_fetchHeaderMenu: () => Promise<void>;
    app_isLoading: Ref<boolean>;
  }

  interface IAppStoreState {
    app_isLoading: boolean;
    app_searchQueryOfProduct: string;
    app_headerMenu: IHeaderMenu[];
    app_headerMenuIsLoading: boolean;
  }

  interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    path: string;
    per_page: string;
    to: number;
    total: number;
  }
}
