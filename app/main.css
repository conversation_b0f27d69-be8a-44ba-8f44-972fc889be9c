@import url('./druk-font.css');
@import url('https://fonts.googleapis.com/css2?family=Albert+Sans:ital,wght@0,100..900;1,100..900&display=swap');
@import 'tailwindcss';

@theme {
  /**
    * Customizing Colors
    * @see https://tailwindcss.com/docs/colors#customizing-your-colors
    */
  --color-hero-black: #15130f;
  --color-black: #18191a;
  --color-hero-light-black: #243545;
  --color-hero-gray: #394049;
  --color-hero-red: #e63b14;
  --color-hero-link: #1e88e5;
  --color-hero-light-gray: #cdd3da;
  --color-hero-dark-light-gray: #b9bec4;
  --color-hero-broken-white: #e3e0e3;
  --color-hero-transparent-light-gray: rgba(205, 211, 218, 0.25);
  --color-gray: #f9fafb;
  --color-light-gray: #f9fafb;
  --color-dark-gray: #acb1b4;
  --color-status-green: #43a047;
  --color-status-red: #d10000;
  --color-status-orange: #fb8c00;
  --color-header-orange: #ff5a00;
  --color-muted: #686f72;
  --color-muted-secondary: #535b66;
  --color-success: #05964c;
  --color-light-success: #ecfdf2;
  --color-danger: #e9151d;
  --color-input-gray: #e5e6e8;
  --color-black-copyright: #242424;
  --color-gray-copyright: #9ba0a6;
  --color-muted-footer: #ced1d3;
  --color-muted-tabs: #f5f6f6;

  --breakpoint-xs: 500px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /** 
    * Customizing Fonts
    * @see https://tailwindcss.com/docs/font-family#customizing-your-theme
    */
  /* --font-bebas: "Druk Text", "cursive", */
  --font-druk: 'Druk Text', 'sans-serif';
  --font-roboto: 'Roboto', 'sans-serif';
  --font-sans: 'Albert Sans', 'sans-serif';
}

body {
  font-family: 'Albert Sans', sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-hero-black);
}

#home-image-auto-slide {
  height: 650px;
  margin-left: auto;
}

@media (min-width: 0px) and (max-width: 400px) {
  #home-image-auto-slide {
    height: 460px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 400px) and (max-width: 500px) {
  #home-image-auto-slide {
    height: 480px;
    margin: 0 auto;
  }
}

@media (min-width: 500px) and (max-width: 640px) {
  #home-image-auto-slide {
    height: 540px;
    margin: 0 auto !important;
  }
}

.hero-slider {
  width: 300px;
  height: 650px;
  overflow: hidden;
  mask-image: linear-gradient(to top, transparent, #000 10% 90%, transparent);
}

@media (min-width: 0px) and (max-width: 400px) {
  /* @media (max-width: 400px) { */
  .hero-slider {
    width: 140px !important;
    height: 420px !important;
  }
}

@media (min-width: 400px) and (max-width: 500px) {
  .hero-slider {
    width: 168px;
    height: 500px;
  }
}

@media (min-width: 500px) and (max-width: 640px) {
  .hero-slider {
    width: 220px;
    height: 540px;
  }
}

.hero-slider .list {
  min-height: calc(280px * var(--quantity));
  position: relative;
}

@media (min-width: 0px) and (max-width: 400px) {
  .hero-slider .list {
    min-height: calc(110px * var(--quantity)) !important;
  }
}

@media (min-width: 400px) and (max-width: 500px) {
  .hero-slider .list {
    min-height: calc(140px * var(--quantity));
  }
}

@media (min-width: 500px) and (max-width: 640px) {
  .hero-slider .list {
    min-height: calc(200px * var(--quantity));
  }
}

.hero-slider .list .item {
  width: 300px;
  height: 300px;
  position: absolute;
  border-radius: 12px;
  overflow: hidden;
  bottom: 100%;
  animation: autoRun 20s linear infinite;
  transition: filter 0.5s;
  animation-delay: calc((20s / var(--quantity)) * (var(--position) - 1) - 20s) !important;
}

@media (min-width: 0px) and (max-width: 400px) {
  .hero-slider .list .item {
    width: 140px !important;
    height: 140px !important;
  }
}

@media (min-width: 400px) and (max-width: 500px) {
  .hero-slider .list .item {
    width: 164px;
    height: 164px;
  }
}

@media (min-width: 500px) and (max-width: 640px) {
  .hero-slider .list .item {
    width: 220px;
    height: 220px;
  }
}

.hero-slider .list .item img {
  width: 100%;
  height: 300px;
}

@media (min-width: 0px) and (max-width: 400px) {
  .hero-slider .list .item img {
    width: 100%;
    height: 140px !important;
  }
}

@media (min-width: 400px) and (max-width: 500px) {
  .hero-slider .list .item img {
    width: 100%;
    height: 172px;
  }
}

@media (min-width: 500px) and (max-width: 640px) {
  .hero-slider .list .item img {
    width: 100%;
    height: 220px;
  }
}

@keyframes autoRun {
  from {
    bottom: 100%;
  }
  to {
    bottom: calc(280px * -1);
  }
}

.hero-slider[reverse='true'] .item {
  animation: reversePlay 20s linear infinite;
}

@keyframes reversePlay {
  from {
    bottom: calc(280px * -1);
  }
  to {
    bottom: 100%;
  }
}

/** button */
.p-button-outlined:not(:disabled):active {
  background-color: transparent !important ;
}

/* popover */
.p-popover::before,
.p-popover::after {
  display: none !important;
}

#popover-mega-menu {
  left: 0 !important;
  top: 100px !important;
  width: 100% !important;
  margin-top: unset;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
}

#popover-mega-menu .p-popover-content {
  border-radius: 0 !important;
  padding: 0 !important;
}

/* input cart qty */
.input-cart-qty {
  border-radius: 8px;
  height: 28px !important;
  -webkit-box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.12);
}

.input-cart-qty button {
  background-color: transparent !important;
  width: 28px !important;
}

.input-cart-qty button.p-disabled {
  background-color: #e6e6e6 !important;
}

/* Custom shadows */
.custom-shadow-xs {
  -webkit-box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.12);
}

.custom-shadow-sm {
  -webkit-box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.12);
}

.custom-shadow-md {
  -webkit-box-shadow: 0px 3px 11px 0px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 3px 11px 0px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 3px 11px 0px rgba(0, 0, 0, 0.12);
}

.custom-shadow-lg {
  -webkit-box-shadow: 0px 4px 20px 4px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 4px 20px 4px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 4px 20px 4px rgba(0, 0, 0, 0.12);
}

/* Product custom drawer toolbar  */
.product-custom-toolbar-drawer .p-drawer-close-button {
  display: none;
}

/**
 Swiper
 */
.swiper-button-next:after {
  content: url(./src/core/assets/icons/next.svg) !important;
}
.swiper-button-prev:after {
  content: url(./src/core/assets/icons/prev.svg) !important;
}

.swiper-button-disabled {
  display: none !important;
}
