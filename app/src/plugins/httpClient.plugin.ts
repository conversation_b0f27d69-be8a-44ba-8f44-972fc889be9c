import { useAuthenticationStore } from "../modules/authentication/stores/authentication.store";

export default defineNuxtPlugin(nuxtApp => {
  const config = useRuntimeConfig();
  const authentication = localStorage.getItem('authentication')!;
  const { authentication_accessToken } = JSON.parse(authentication) ?? '';

  const httpClient = $fetch.create({
    baseURL: config.public.apiBase,
    onRequest({ options }) {
      if (authentication_accessToken) {
        // note that this relies on ofetch >= 1.4.0 - you may need to refresh your lockfile
        options.headers.set('Authorization', `Bearer ${authentication_accessToken}`);
      }
    },
    async onResponseError({ response }) {
      if (response.status === 401) {
        const authStore = useAuthenticationStore();
        authStore.$patch(state => {
          state.authentication_accessToken = '';
          state.authentication_isLoading = false;
          state.authentication_userData = null;
          state.authentication_profileData = null;
          state.authentication_emailRegister = '';
          state.authentication_passwordRegister = '';
        });

        // Redirect to login page
      setTimeout(async () => {
        await nuxtApp.runWithContext(() => navigateTo('/authentication/login'));
      }, 1000);
      }
    },
  });

  return {
    provide: {
      httpClient,
    },
  };
});
