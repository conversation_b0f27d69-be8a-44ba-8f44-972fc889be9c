import Pusher from 'pusher-js';
import Echo from 'laravel-echo';
import { defineNuxtPlugin, useRuntimeConfig } from 'nuxt/app';

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig();
  const pusher = (): Echo<'pusher'> =>
    new Echo({
      client: new Pusher('app-key', {
        wsHost: config.public.wsHost as string,
        forceTLS: true,
        disableStats: true,
        enabledTransports: ['ws', 'wss'],
        cluster: 'ap',
      }),
      broadcaster: 'pusher',
      key: 'app-key',
      wsHost: config.public.wsHost as string,
      forceTLS: true,
      enabledTransports: ['ws', 'wss'],
    });

  return {
    provide: {
      websocket: pusher(),
    },
  };
});
