<script setup lang="ts">
// Constants
import { PROFILE_STATUSES } from '../constants/profile.constant';

interface Props {
  remarks: string;
  status: string;
}

const props = defineProps<Props>();
const router = useRouter();
const route = useRoute();

const showAlert = computed<boolean>(() => {
  return (
    props.status === PROFILE_STATUSES.NEW ||
    props.status === PROFILE_STATUSES.REVISION ||
    props.status === PROFILE_STATUSES.REJECTED
  );
});

const alertSeverity = computed<string>(() => {
  switch (props.status) {
    case PROFILE_STATUSES.NEW:
      return 'secondary';
    case PROFILE_STATUSES.REVISION:
    case PROFILE_STATUSES.REJECTED:
      return 'error';
    // case PROFILE_STATUSES.REJECTED:
    //   return 'error';
    default:
      return '';
  }
});

const alertIcon = computed<string>(() => {
  switch (props.status) {
    case PROFILE_STATUSES.NEW:
      return '/icons/warning-black.svg';
    // case PROFILE_STATUSES.REVISION:
    //   return '/icons/custom-info-blue-icon.svg';
    case PROFILE_STATUSES.REJECTED:
    case PROFILE_STATUSES.REVISION:
      return '/icons/info-red.svg';
    default:
      return '';
  }
});

const onClickUpdate = () => {
  router.push({
    name: 'profile',
    query: {
      isEdit: 1,
    },
  });
};

const isProfilePage = computed(() => route.name === 'profile');
</script>

<template>
  <PrimeVueMessage
    v-if="!status || showAlert"
    :severity="alertSeverity"
    class="!px-0 !py-0 !border-none !outline-none !rounded-[16px]"
    :pt="{
      text: '!w-full',
      content: '!w-full !py-3 !px-3',
    }"
  >
    <div class="w-full !flex items-center gap-3">
      <div class="!flex items-center gap-3 mr-auto">
        <NuxtImg :src="alertIcon" alt="warning-icon" class="w-[20px] h-[20px]" />
        <p class="text-[14px] font-medium">{{ remarks }}</p>
      </div>
      <PrimeVueButton
        v-if="!isProfilePage && status === PROFILE_STATUSES.REVISION"
        class="!border-[#E9151D] !bg-[#E9151D] !text-white !min-w-[100px] ml-auto !rounded-[8px] !p-[4px]"
        size="small"
        @click="onClickUpdate"
      >
        Ubah Data
      </PrimeVueButton>
    </div>
  </PrimeVueMessage>
</template>
