<script setup lang="ts">
import { AUTHENTICATION_PASSWORD_VALIDATIONS } from '../constants/profile.constant';
// Interfaces
import type { IProfileProvided } from '../interfaces/profile.interface';

/**
 * @description Injecting dependencies
 */
const {
  profile_formDataOfChangePassword,
  profile_formValidationsOfChangePassword,
  profile_onSubmitChangePassword,
  profile_checkCurrentPassword,
  profile_isMatchOldPassword,
  profile_updatePasswordSuccess,
  profileCreatePassword_isCustomValidationsValid,
  profileCreatePassword_setDynamicClassLabelValidation,
  profileCreatePassword_setDynamicPathIcon,
} = inject<IProfileProvided>('profile')!;

const onChangeCurrentPassword = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length >= 8) {
    profile_checkCurrentPassword(value);
  } else {
    profile_isMatchOldPassword.value = false;
  }
}, 500);

const getCurrentPasswordMessage = computed(() => {
  if (profile_isMatchOldPassword.value === false) {
    return '<PERSON><PERSON><PERSON> anda sa<PERSON>';
  } else {
    return '';
  }
});

const onDialogVisibleChange = (val: boolean) => {
  if (!val) {
    window.location.reload();
  }
};
</script>

<template>
  <section id="profile-change-password" class="flex flex-col relative inset-0 z-0 gap-4">
    <BaseFormGroup
      v-slot="{ classes }"
      class-label="font-medium text-sm text-black block mb-2"
      is-name-as-label
      label-for="current_password"
      name="Kata Sandi Lama"
      spacing-bottom="mb-0"
      :validators="profile_formValidationsOfChangePassword.current_password"
      :is-success-message="profile_isMatchOldPassword ? 'Password telah sesuai' : ''"
      :is-error-message="getCurrentPasswordMessage"
    >
      <BaseInputPassword
        id="current_password"
        v-model="profile_formDataOfChangePassword.current_password"
        :class="{ ...classes }"
        type="password"
        placeholder="Masukkan Kata Sandi Lama Anda"
        v-on="useListenerForm(profile_formValidationsOfChangePassword, 'current_password')"
        @update:model-value="onChangeCurrentPassword"
      />
    </BaseFormGroup>

    <BaseFormGroup
      v-slot="{ classes }"
      class-label="font-medium text-sm text-black block mb-2"
      is-name-as-label
      label-for="new_password"
      name="Kata Sandi Baru"
      spacing-bottom="mb-0"
      :validators="profile_formValidationsOfChangePassword.new_password"
    >
      <BaseInputPassword
        id="new_password"
        v-model="profile_formDataOfChangePassword.new_password"
        :class="{ ...classes }"
        type="password"
        placeholder="Masukkan Kata Sandi Baru Anda"
        v-on="useListenerForm(profile_formValidationsOfChangePassword, 'new_password')"
      />
    </BaseFormGroup>
    <section
      v-if="!profileCreatePassword_isCustomValidationsValid"
      id="password-validation"
      class="flex flex-col gap-2 py-2 px-4 rounded-lg"
      :class="[profileCreatePassword_isCustomValidationsValid ? 'bg-light-success' : 'bg-light-gray']"
    >
      <h6
        class="text-sm"
        :class="[profileCreatePassword_isCustomValidationsValid ? 'text-success' : 'text-muted-secondary']"
      >
        Password harus memiliki setidaknya:
      </h6>

      <template
        v-for="(validation, validationIndex) in AUTHENTICATION_PASSWORD_VALIDATIONS"
        :key="`password-validation-${validationIndex}`"
      >
        <section id="validation" class="flex items-center gap-2">
          <template v-if="profile_formDataOfChangePassword.new_password">
            <NuxtImg
              :src="profileCreatePassword_setDynamicPathIcon(validation.key)"
              alt="icon-password-validation"
              class="w-4 h-4"
            />
          </template>

          <template v-else>
            <NuxtImg src="/icons/min-circle-muted.svg" alt="icon-min-circle-muted" class="w-4 h-4" />
          </template>

          <span class="text-sm" :class="[profileCreatePassword_setDynamicClassLabelValidation(validation.key)]">
            {{ validation.label }}
          </span>
        </section>
      </template>
    </section>

    <BaseFormGroup
      v-slot="{ classes }"
      class-label="font-medium text-sm text-black block mb-2"
      is-name-as-label
      label-for="new_password_confirmation"
      name="Konfirmasi Kata Sandi Baru"
      spacing-bottom="mb-0"
      :validators="profile_formValidationsOfChangePassword.new_password_confirmation"
    >
      <BaseInputPassword
        id="new_password_confirmation"
        v-model="profile_formDataOfChangePassword.new_password_confirmation"
        :class="{ ...classes }"
        type="password"
        placeholder="Ulangi Kata Sandi Baru Anda"
        v-on="useListenerForm(profile_formValidationsOfChangePassword, 'new_password_confirmation')"
      />
    </BaseFormGroup>

    <PrimeVueButton
      type="button"
      size="large"
      :disabled="profile_formValidationsOfChangePassword.$invalid"
      class="!bg-black !border-none !rounded-lg !px-7"
      @click="profile_onSubmitChangePassword()"
    >
      <template #default>
        <section id="content" class="flex items-center gap-1">
          <span class="text-white text-sm font-medium"> Ubah Password </span>
        </section>
      </template>
    </PrimeVueButton>

    <PrimeVueDialog
      v-model:visible="profile_updatePasswordSuccess"
      modal
      class="h-fit w-[90%] sm:w-[330px]"
      :draggable="false"
      :pt="{
        header: '!hidden',
        root: '!p-0',
        content: '!p-0',
      }"
      :dismissable-mask="true"
      @update:visible="onDialogVisibleChange"
    >
      <template #default>
        <section id="content" class="flex flex-col gap-4 px-2 py-3 mb-2">
          <NuxtImg src="/images/modal-success.svg" />
          <div class="text-center mt-2">
            <p class="font-semibold text-[18px] mb-2">Kata Sandi berhasil diperbarui</p>
            <p class="text-[#686F72] text-[14px]">
              Kata sandi Anda berhasil diperbarui. Silakan gunakan kata sandi baru Anda untuk login ke akun.
            </p>
          </div>
        </section>
      </template>
    </PrimeVueDialog>
  </section>
</template>
