<script setup lang="ts">
// Constants
import type { BaseValidation, ErrorObject } from '@vuelidate/core';
import { PROFILE_PERSONAL_DATA_INFORMATIONS, PROFILE_NPWP_DATA_INFORMATIONS } from '../constants/profile.constant';
// Interfaces
import type { IProfileData, IProfileProvided, IProfileUpdateProvided } from '../interfaces/profile.interface';
import ProfileAccountStatusAlert from './ProfileAccountStatusAlert.vue';
import BaseSelectInput from '~/app/src/core/components/base/BaseSelectInput.vue';
import { formatSizeUnits } from '~/app/src/core/helpers/text.helper';
import { getFilenameFromURL } from '~/app/src/core/helpers/regex.helper';
/**
 * @description Injecting dependencies
 */
const { profile_profileData } = inject<IProfileProvided>('profile')!;
const {
  profileUpdate_formData,
  profileUpdate_formValidations,
  profileUpdate_isLoading,
  profileUpdate_onSubmit,
  profileUpdate_isEditProfile,
  profileUpdate_toggleIsEditProfile,
  profileUpdate_listProvince,
  profileUpdate_listCity,
  profileUpdate_listDistrict,
  profileUpdate_listNpwpProvince,
  profileUpdate_listNpwpCity,
  profileUpdate_listNpwpDistrict,
  profile_isLoadingUploadNpwp,
  profile_isUploadNpwpError,
  profile_listOfTaxType,
  profile_uploadFile,
  profileUpdate_selectedFile,
  profile_toggleIsNeedInvoiceBill,
  profile_isNeedInvoiceBill,
  profile_checkNPWPisLoading,
  profile_isExistNPWP,
  profile_checkKTPIsLoading,
  profile_isExistKTP,
  profile_validateNPWP,
  profile_validateKtp,
} = inject<IProfileUpdateProvided>('profileUpdate')!;

const taxInvoices = ref(['02', '03', '04', '07']);

const dragging = ref<boolean>(false);

// function to handle select file
// return: none
const handleFileSelected = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    profileUpdate_selectedFile.value = file;
    profileUpdate_formData.npwp_file = file;
    // profile_uploadFile(file);
  }
};

// function to handle drop file
// return: none
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragging.value = false;
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0];
    console.log(file);
    profileUpdate_selectedFile.value = file;
    profileUpdate_formData.npwp_file = file;
    profile_uploadFile(file);
  }
};

// function to open file picker
// return: none
const openFilePicker = (field: 'file_identity' | 'file_npwp') => {
  const fileInput = document.getElementById(`fileInput-${field}`) as HTMLInputElement;
  fileInput.click();
};

// function to get error
// return: error value
const getFileError = (fieldName: 'file_identity' | 'file_npwp'): ComputedRef<ErrorObject | null> =>
  computed(() => {
    const fieldValidation = profileUpdate_formValidations.value[fieldName] as BaseValidation;
    if (!fieldValidation || fieldValidation.$errors.length === 0) return null;
    console.log(fieldValidation.$errors[0]);
    return fieldValidation.$errors[0];
  });

const errorNPWP = getFileError('file_npwp');

const onClickViewNpwpImage = (npwp: string | File) => {
  const url = typeof npwp === 'string' ? npwp : URL.createObjectURL(npwp);
  window.open(url, '_blank');
  if (typeof npwp !== 'string') setTimeout(() => URL.revokeObjectURL(url), 1000);
};

watch(profileUpdate_formData.shipment_province, (newValue, oldValue) => {
  console.log('Old value:', oldValue);
  console.log('New value:', newValue);
});

const onChangeShipmentProvince = () => {
  profileUpdate_formData.shipment_city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  profileUpdate_formData.shipment_district = {
    name: '',
    code: '',
  };
};

const onChangeShipmentCity = () => {
  profileUpdate_formData.shipment_district = {
    name: '',
    code: '',
  };
};

const onChangeNpwpProvince = () => {
  profileUpdate_formData.npwp_city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  profileUpdate_formData.npwp_district = {
    name: '',
    code: '',
  };
};

const onChangeNpwpCity = () => {
  profileUpdate_formData.npwp_district = {
    name: '',
    code: '',
  };
};

const onChangeValidateNPWP = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length === 16) {
    profile_validateNPWP(value);
  }
}, 500);

const onChangeValidateKTP = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length === 16) {
    profile_validateKtp(value);
  }
}, 500);

const getIsValidNPWPMessage = computed(() => {
  if (profileUpdate_formData?.npwp?.length >= 15 && profile_isExistNPWP.value == true) {
    return 'Nomor NPWP Sudah terapakai';
  } else {
    return '';
  }
});

const getIsValidKTPMessage = computed(() => {
  if (profileUpdate_formData?.national_id?.length >= 16 && profile_isExistKTP.value == true) {
    return 'Nomor KTP Sudah terpakai';
  } else {
    return '';
  }
});
</script>

<template>
  <section id="profile-account-information" class="flex flex-col relative inset-0 z-0 gap-6">
    <ProfileAccountStatusAlert
      v-if="profile_profileData && profile_profileData?.status"
      :status="profile_profileData?.status"
      :remarks="profile_profileData?.remarks"
    />
    <section id="personal-information" class="grid grid-cols-1 sm:gap-y-0 gap-y-5">
      <section id="personal-data" class="col-span-full md:col-span-6 flex flex-col gap-4 sm:p-0 p-3 rounded-lg">
        <section id="heading" class="pb-2 flex justify-between">
          <h5 class="font-bold text-[18px] text-black">Data Akun</h5>
          <PrimeVueButton
            v-if="!profileUpdate_isEditProfile"
            class="!bg-black !text-white !text-[14px] !border-none"
            @click="profileUpdate_toggleIsEditProfile"
          >
            Ubah Profile
          </PrimeVueButton>
        </section>

        <div v-if="!profileUpdate_isEditProfile">
          <!-- PROFILE -->
          <div class="grid grid-cols-1 sm:grid-cols-2">
            <div
              v-for="(personalData, personalDataIndex) in PROFILE_PERSONAL_DATA_INFORMATIONS"
              :key="`personal-data-${personalDataIndex}`"
              class="flex flex-col my-2"
            >
              <h6 class="font-medium text-[14px] text-muted">
                {{ personalData.label }}
              </h6>

              <p class="font-medium text-[16px] text-black">
                {{
                  ['province', 'city', 'district', 'zip_code'].includes(personalData.key)
                    ? profile_profileData.store_list?.[0]?.[personalData.key] ?? '-'
                    : profile_profileData[personalData.key as keyof IProfileData] ?? '-'
                }}
              </p>
            </div>
          </div>
          <section id="npwp-data" class="col-span-full md:col-span-6 flex flex-col gap-2 rounded-lg mt-4">
            <section id="heading" class="pb-2">
              <h5 class="font-bold text-lg text-black">Data NPWP</h5>
            </section>

            <div class="grid grid-cols-1 sm:grid-cols-2">
              <div
                v-for="(npwpData, npwpDataIndex) in PROFILE_NPWP_DATA_INFORMATIONS"
                id="information"
                :key="`personal-data-${npwpDataIndex}`"
                class="flex flex-col my-2"
              >
                <h6 class="font-medium text-sm text-muted">
                  {{ npwpData.label }}
                </h6>
                <div
                  v-if="npwpData?.key === 'npwp_file' && profile_profileData?.npwp_file"
                  class="flex items-center gap-1"
                >
                  <div class="rounded h-[36px] w-[36px] overflow-hidden flex items-center justify-center">
                    <NuxtImg
                      v-if="profile_profileData?.npwp_file?.includes('.pdf')"
                      src="/icons/pdf.svg"
                      alt="npwp file"
                      class="!h-[30px] !w-[30px]"
                    />
                    <NuxtImg v-else :src="profile_profileData?.npwp_file" alt="npwp file" />
                  </div>
                  <span>{{ getFilenameFromURL(profile_profileData?.npwp_file) }}</span>
                  <PrimeVueButton
                    variant="text"
                    class="!underline !px-1 !py-1 !text-[#147FFF]"
                    @click="() => onClickViewNpwpImage(profile_profileData?.npwp_file)"
                    >View</PrimeVueButton
                  >
                </div>
                <p v-else-if="npwpData?.key === 'tax_type'" class="font-medium text-base text-black">
                  {{ profile_profileData?.tax_invoice }}
                </p>
                <p v-else class="font-medium text-base text-black">
                  {{ profile_profileData[npwpData.key as keyof IProfileData] ?? '-' }}
                </p>
              </div>
            </div>
          </section>
        </div>

        <div v-else>
          <form @submit.prevent="profileUpdate_onSubmit">
            <div class="grid sm:grid-cols-2 grid-cols-1 sm:gap-4 gap-2">
              <!-- Nama Lengkap -->
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="owner_name"
                name="Nama Lengkap"
                :is-mandatory="true"
                :validators="profileUpdate_formValidations.owner_name"
              >
                <input
                  id="owner_name"
                  v-model="profileUpdate_formData.owner_name"
                  v-bind="{ ...useBindStateForm('Masukkan Nama Lengkap') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="profileUpdate_isLoading"
                  v-on="useListenerForm(profileUpdate_formValidations, 'owner_name')"
                />
              </BaseFormGroup>
              <!-- Nomor Telp -->
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="no_telp"
                name="Nomor HP"
                :is-mandatory="true"
                :validators="profileUpdate_formValidations.phone_number"
              >
                <PrimeVueInputGroup class="h-[3.1em]">
                  <PrimeVueInputGroupAddon class="!bg-[#F9FAFB]">
                    <p class="text-[#18191A] text-base font-normal">+62</p>
                  </PrimeVueInputGroupAddon>
                  <PrimeVueInputText
                    id="no_telp"
                    v-model="profileUpdate_formData.phone_number"
                    v-bind="{ ...useBindStateForm('Masukan nomor handphone') }"
                    class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    inputmode="numeric"
                    v-on="useListenerForm(profileUpdate_formValidations, 'phone_number')"
                  />
                </PrimeVueInputGroup>
              </BaseFormGroup>
              <!-- Email -->
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="email"
                name="Email"
                :is-mandatory="true"
                :validators="profileUpdate_formValidations.email"
              >
                <input
                  id="email"
                  v-model="profileUpdate_formData.email"
                  v-bind="{ ...useBindStateForm('<EMAIL>') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="profileUpdate_isLoading"
                  v-on="useListenerForm(profileUpdate_formValidations, 'email')"
                />
              </BaseFormGroup>
              <!-- No KTP -->
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="identity_number"
                name="Nomor KTP"
                :is-error-message="profile_checkKTPIsLoading ? '' : getIsValidKTPMessage"
                :validators="profileUpdate_formValidations.national_id"
                :is-mandatory="true"
              >
                <input
                  id="identity_number"
                  v-model="profileUpdate_formData.national_id"
                  v-bind="{ ...useBindStateForm('Masukkan Nomor KTP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  :maxlength="16"
                  :readonly="profileUpdate_isLoading"
                  v-on="useListenerForm(profileUpdate_formValidations, 'national_id')"
                  @update:model-value="(value : string) => onChangeValidateKTP(value)"
                />
              </BaseFormGroup>
            </div>
            <section class="grid sm:grid-cols-2 grid-cols-1 sm:gap-5 gap-0 sm:gap-y-4 gap-y-4">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="province"
                name="Provinsi"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="profileUpdate_formValidations.shipment_province"
              >
                <BaseSelectInput
                  v-model:selected="profileUpdate_formData.shipment_province"
                  :class="{ ...classes }"
                  class="rounded-lg"
                  placeholder="Pilih Provinsi"
                  :disable="false"
                  :options="profileUpdate_listProvince"
                  @update:selected="onChangeShipmentProvince"
                />
              </BaseFormGroup>
              <BaseFormGroup
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="City"
                name="Kota"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="profileUpdate_formValidations.shipment_city"
              >
                <BaseSelectInput
                  v-model:selected="profileUpdate_formData.shipment_city"
                  placeholder="Pilih Kota"
                  class="rounded-lg"
                  :disable="false"
                  :options="profileUpdate_listCity"
                  @update:selected="onChangeShipmentCity"
                />
              </BaseFormGroup>

              <BaseFormGroup
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="Kecamatan"
                name="Kecamatan"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="profileUpdate_formValidations.shipment_district"
              >
                <BaseSelectInput
                  v-model:selected="profileUpdate_formData.shipment_district"
                  placeholder="Pilih Kecamatan"
                  class="rounded-lg"
                  :disable="false"
                  :options="profileUpdate_listDistrict"
                />
              </BaseFormGroup>

              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="zip_code"
                name="Kode Pos"
                :validators="profileUpdate_formValidations.shipment_zip_code"
                :is-mandatory="true"
              >
                <input
                  id="zip_code"
                  v-model="profileUpdate_formData.shipment_zip_code"
                  v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
                  class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(profileUpdate_formValidations, 'shipment_zip_code')"
                />
              </BaseFormGroup>
            </section>
            <div class="grid grid-cols-1">
              <!-- Alamat -->
              <BaseFormGroup
                v-slot="{ classes }"
                class="col-span-2"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="shipment_address"
                name="Alamat Pengiriman"
                :validators="profileUpdate_formValidations.shipment_address"
                :is-mandatory="true"
              >
                <PrimeVueTextarea
                  id="shipment_address"
                  v-model="profileUpdate_formData.shipment_address"
                  v-bind="{ ...useBindStateForm('masukan alamat perusahaan sesuai NPWP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  rows="3"
                  v-on="useListenerForm(profileUpdate_formValidations, 'shipment_address')"
                />
              </BaseFormGroup>
            </div>
            <section id="heading" class="pb-2 flex justify-between">
              <h5 class="font-bold text-[18px] text-black">Data NPWP</h5>
            </section>
            <div class="w-full gap-3">
              <!-- NPWP Number -->
              <div class="w-full">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class=""
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="npwp_number"
                  name="Nomor NPWP"
                  :is-error-message="profile_checkNPWPisLoading ? '' : getIsValidNPWPMessage"
                  :validators="profileUpdate_formValidations.npwp"
                  :is-mandatory="true"
                >
                  <input
                    id="npwp_number"
                    v-model="profileUpdate_formData.npwp"
                    v-bind="{ ...useBindStateForm('Masukkan Nomor pada NPWP Anda') }"
                    class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted cl"
                    :class="{ ...classes }"
                    type="text"
                    :maxlength="16"
                    :minlength="15"
                    :readonly="profileUpdate_isLoading"
                    v-on="useListenerForm(profileUpdate_formValidations, 'npwp')"
                    @update:model-value="(value : string) => onChangeValidateNPWP(value)"
                  />
                </BaseFormGroup>
              </div>

              <!-- Upload NPWP -->
              <div class="w-full mb-3">
                <p class="text-[14px] mb-2 font-medium">Upload NPWP</p>
                <div class="relative mb-2">
                  <div
                    v-if="profile_isLoadingUploadNpwp"
                    class="w-full h-full absolute top-0 left-0 bg-red rounded-xl"
                  >
                    <BaseLoaderBoxed :height="200" />
                  </div>
                  <div
                    v-if="profileUpdate_formData?.npwp_file_path"
                    class="border border-dashed border-[#05964C] bg-[#EEFFF5] p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-xl"
                  >
                    <div class="w-full justify-items-center my-5">
                      <NuxtImg
                        src="/icons/checklist-rounded-green.svg"
                        alt="upload-icon"
                        class="w-[26px] h-[26px] mx-auto mb-3"
                      />
                      <div
                        v-if="profileUpdate_selectedFile?.name || profileUpdate_formData?.npwp_file_path"
                        class="mt-1 text-[14px] font-semibold text-[#18191A] mb-3"
                      >
                        {{
                          profileUpdate_selectedFile?.name ??
                          profileUpdate_formData?.npwp_file_path?.split('/').pop()
                        }}
                      </div>
                      <div class="flex items-center justify-center gap-4">
                        <p v-if="profileUpdate_selectedFile?.size" class="text-[#686F72]">
                          {{ formatSizeUnits(profileUpdate_selectedFile?.size) }}
                        </p>
                        <div v-if="profileUpdate_selectedFile?.size" class="w-[3px] h-[3px] bg-[#ACB1B4]" />
                        <div
                          class="flex items-center gap-2 cursor-pointer"
                          @click="
                            () =>
                              onClickViewNpwpImage(
                                profileUpdate_selectedFile ?? profileUpdate_formData?.npwp_file_path,
                              )
                          "
                        >
                          <NuxtImg src="/icons/eye.svg" alt="warning-icon" class="w-[20px] h-[20px]" />
                          <p class="text-[#18191A] font-semibold underline">Lihat Aset</p>
                        </div>
                        <div class="w-[3px] h-[3px] bg-[#ACB1B4]" />
                        <div
                          class="flex items-center gap-2 cursor-pointer"
                          @click="() => openFilePicker('file_npwp')"
                        >
                          <NuxtImg src="/icons/reload-icon.svg" alt="warning-icon" class="w-[20px] h-[20px]" />
                          <p class="text-[#18191A] font-semibold underline">Ganti Asset</p>
                        </div>
                      </div>
                    </div>
                    <input
                      id="fileInput-file_npwp"
                      type="file"
                      data-testid="bulk-file-input"
                      accept=".pdf, .jpg, .jpeg, .png"
                      class="hidden"
                      @change="e => handleFileSelected(e)"
                    />
                  </div>
                  <div
                    v-else
                    class="border-2 border-dashed p-6 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 rounded-xl"
                    :class="dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-gray-400'"
                    data-testid="bulk-file-input-draggable"
                    @dragover.prevent="dragging = true"
                    @dragleave="dragging = false"
                    @drop="e => handleDrop(e)"
                    @click="() => openFilePicker('file_npwp')"
                  >
                    <div class="w-full justify-items-center my-5">
                      <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10 mx-auto" />
                    </div>
                    <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
                    <div class="mt-6 text-sm text-gray-500 mx-auto text-center">
                      Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
                    </div>
                    <input
                      id="fileInput-file_npwp"
                      accept=".pdf, .jpg, .jpeg, .png"
                      type="file"
                      class="hidden"
                      @change="e => handleFileSelected(e)"
                    />
                  </div>
                </div>
                <div
                  v-if="profileUpdate_formValidations.npwp_file_path.$errors[0]"
                  class="bg-red-50 rounded-md p-1 px-3"
                >
                  <PrimeVueMessage severity="error" variant="simple" size="small">
                    {{ errorNPWP?.$message }}
                  </PrimeVueMessage>
                </div>
                <PrimeVueMessage v-if="profile_isUploadNpwpError" severity="error" variant="simple" size="small">
                  Gagal Upload Data
                </PrimeVueMessage>
              </div>
              <div class="flex flex-col mb-5">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="jenis_pajak"
                  name="Jenis Pajak"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="profileUpdate_formValidations.tax_type"
                >
                  <BaseSelectInput
                    v-model:selected="profileUpdate_formData.tax_type"
                    :class="{ ...classes }"
                    class="rounded-lg"
                    placeholder="Pilih Jenis Pajak"
                    :disable="false"
                    :options="profile_listOfTaxType"
                  />
                </BaseFormGroup>
              </div>
              <div class="grid sm:grid-cols-2 grid-cols-1">
                <div class="flex items-center gap-1">
                  <PrimeVueCheckbox
                    v-model="profile_isNeedInvoiceBill"
                    input-id="term_tax"
                    name="cart-check-all"
                    class="mr-2"
                    binary
                    @update:model-value="profile_toggleIsNeedInvoiceBill"
                  />
                  <label for="term_tax" class="font-medium">Saya akan membutuhkan faktur pajak</label>
                </div>
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  :is-name-as-label="false"
                  label-for="faktur_pajak"
                  name="Invoice"
                  spacing-bottom="mb-0"
                  :validators="profileUpdate_formValidations.tax_invoice"
                >
                  <BaseSelectInput
                    v-model:selected="profileUpdate_formData.tax_invoice"
                    :class="{ ...classes }"
                    class="rounded-lg"
                    placeholder="Pilih Tipe Faktur Pajak"
                    :disable="!profile_isNeedInvoiceBill"
                    :options="taxInvoices"
                  />
                </BaseFormGroup>
              </div>
              <hr class="h-px my-8 bg-gray-200 border-0" />

              <!-- NPWP Name -->
              <BaseFormGroup
                v-slot="{ classes }"
                class=""
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="npwp_name"
                name="Nama sesuai NPWP"
                :validators="profileUpdate_formValidations.npwp_name"
                :is-mandatory="true"
              >
                <input
                  id="npwp_name"
                  v-model="profileUpdate_formData.npwp_name"
                  v-bind="{ ...useBindStateForm('Masukkan Nama NPWP Anda') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted cl"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="profileUpdate_isLoading"
                  v-on="useListenerForm(profileUpdate_formValidations, 'npwp_name')"
                />
              </BaseFormGroup>
              <BaseFormGroup
                v-slot="{ classes }"
                class="col-span-2"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="npwp_address"
                name="Alamat Perusahaan"
                :validators="profileUpdate_formValidations.npwp_address"
                :is-mandatory="true"
              >
                <PrimeVueTextarea
                  id="npwp_address"
                  v-model="profileUpdate_formData.npwp_address"
                  v-bind="{ ...useBindStateForm('masukan alamat perusahaan sesuai NPWP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  rows="3"
                  v-on="useListenerForm(profileUpdate_formValidations, 'npwp_address')"
                />
              </BaseFormGroup>
              <section class="col-span-2 grid sm:grid-cols-2 grid-cols-1 sm:gap-5 gap-0 sm:gap-y-4 gap-y-4">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="province"
                  name="Provinsi"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="profileUpdate_formValidations.npwp_province"
                >
                  <BaseSelectInput
                    v-model:selected="profileUpdate_formData.npwp_province"
                    :class="{ ...classes }"
                    class="rounded-lg"
                    placeholder="Pilih Provinsi"
                    :disable="false"
                    :options="profileUpdate_listNpwpProvince"
                    @update:selected="onChangeNpwpProvince"
                  />
                </BaseFormGroup>
                <BaseFormGroup
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="City"
                  name="Kota"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="profileUpdate_formValidations.npwp_city"
                >
                  <BaseSelectInput
                    v-model:selected="profileUpdate_formData.npwp_city"
                    placeholder="Pilih Kota"
                    class="rounded-lg"
                    :disable="false"
                    :options="profileUpdate_listNpwpCity"
                    @update:selected="onChangeNpwpCity"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="Kecamatan"
                  name="Kecamatan"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="profileUpdate_formValidations.npwp_district"
                >
                  <BaseSelectInput
                    v-model:selected="profileUpdate_formData.npwp_district"
                    placeholder="Pilih Kecamatan"
                    class="rounded-lg"
                    :disable="false"
                    :options="profileUpdate_listNpwpDistrict"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="zip_code"
                  name="Kode Pos"
                  :validators="profileUpdate_formValidations.npwp_zip_code"
                  :is-mandatory="true"
                >
                  <input
                    id="zip_code"
                    v-model="profileUpdate_formData.npwp_zip_code"
                    v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
                    class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    v-on="useListenerForm(profileUpdate_formValidations, 'npwp_zip_code')"
                  />
                </BaseFormGroup>
              </section>
            </div>
            <!-- :disabled="profileUpdate_formValidations.$invalid" -->
            <PrimeVueButton
              :disabled="
                profileUpdate_isLoading ||
                profileUpdate_formValidations.$invalid ||
                profile_isExistKTP ||
                profile_isExistNPWP
              "
              class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted cursor-pointer"
              :class="{
                'opacity-50':
                  profileUpdate_isLoading ||
                  profileUpdate_formValidations.$invalid ||
                  profile_isExistKTP ||
                  profile_isExistNPWP,
              }"
              type="submit"
            >
              Simpan profil
            </PrimeVueButton>
          </form>
        </div>
      </section>
    </section>
  </section>
</template>
