// Components
import ProfileAccountInformation from '../components/ProfileAccountInformation.vue';
import ProfileChangePassword from '../components/ProfileChangePassword.vue';

// Interfaces
import type { IProfileData } from '../interfaces/profile.interface';

export const PROFILE_LIST_TABS = [
  {
    component: ProfileAccountInformation,
    label: 'Informasi Akun',
    value: 'account',
  },
  {
    component: ProfileChangePassword,
    label: 'Ubah Kata Sandi',
    value: 'password',
  },
];

export const PROFILE_PERSONAL_DATA_INFORMATIONS = [
  {
    key: 'owner_name',
    label: '<PERSON><PERSON>',
    value: 'Di<PERSON>',
  },
  {
    key: 'phone_number',
    label: 'Nomor Telepon',
    value: '***********',
  },
  {
    key: 'email',
    label: 'E-mail',
    value: '<EMAIL>',
  },
  {
    key: 'national_id',
    label: 'KTP',
    value: '**********',
  },
  {
    key: 'province',
    label: '<PERSON><PERSON>si',
    value: 'Jawa Barat',
  },
  {
    key: 'city',
    label: 'Kota/Kabupaten',
    value: 'Bandung Barat',
  },
  {
    key: 'district',
    label: 'Kecamatan',
    value: 'Padalarang',
  },
  {
    key: 'zip_code',
    label: 'Kode Pos',
    value: '40553',
  },
  {
    key: 'address',
    label: 'Alamat Pengiriman',
    value: '-',
  },
];

export const PROFILE_NPWP_DATA_INFORMATIONS = [
  {
    key: 'npwp_name',
    label: 'Nama NPWP',
    value: '',
  },
  {
    key: 'npwp',
    label: 'No. NPWP',
    value: '',
  },
  {
    key: 'npwp_file',
    label: 'Upload NPWP',
    value: '',
  },
  {
    key: 'tax_type',
    label: 'Tipe Faktur Pajak',
    value: '',
  },
  {
    key: 'npwp_province',
    label: 'Provinsi',
  },
  {
    key: 'npwp_city',
    label: 'Kota/Kabupaten',
  },
  {
    key: 'npwp_district',
    label: 'Kecamatan',
  },
  {
    key: 'npwp_zip_code',
    label: 'Kode Pos',
  },
  {
    key: 'npwp_address',
    label: 'Alamat',
    value: 'Jl. Kebon Jeruk No. 1',
  },
];

export const PROFILE_ADDRESS_DATA_INFORMATIONS = [
  {
    key: 'address',
    label: 'Alamat',
    value: 'Jl. Kebon Jeruk No. 1',
  },
];

export const PROFILE_OUTLET_DATA_INFORMATIONS = [
  {
    key: 'outletName',
    label: 'Nama Outlet',
    value:
      'Jl Lorem Ipsum is simply dummy text Rt33/12, Kelurahan, Kecamatan, Kabupaten, Kota, Provinsi, Kode POS',
  },
];

export const PROFILE_STATE_PROFILE_DATA: IProfileData = {
  address: '',
  credit_limit: '',
  credit_limit_currency: 0,
  credit_limit_remaining: '',
  credit_limit_used: '',
  credit_limit_used_percentage: '',
  customer_id: '',
  customer_type: '',
  distribution_channel: '',
  email: '',
  is_active: false,
  is_change_password: false,
  is_pending_payment: false,
  is_verified: false,
  national_id: '',
  npwp: '',
  shipment_province: '',
  shipment_province_code: '',
  shipment_city: '',
  shipment_city_code: '',
  shipment_district: '',
  shipment_district_code: '',
  shipment_zip_code: '',
  npwp_address: '',
  npwp_province: '',
  npwp_province_code: '',
  npwp_city: '',
  npwp_city_code: '',
  npwp_district: '',
  npwp_district_code: '',
  npwp_zip_code: '',
  npwp_name: '',
  owner_name: '',
  phone_number: '',
  rejection_reason: '',
  store_list: [],
  bank_list: [],
  top: '',
  top_days: '',
  va_list: [],
  status: '',
  remarks: '',
  npwp_file: '',
  tax_type: '',
  tax_invoice: '',
};

export const AUTHENTICATION_PASSWORD_VALIDATIONS = [
  {
    key: 'isContainLowerCase',
    label: 'Huruf kecil [a-z]',
  },
  {
    key: 'isContainNumber',
    label: 'Angka [0-9]',
  },
  {
    key: 'isContainSpecialCharacter',
    label: 'Simbol [!@#$^*]',
  },
  {
    key: 'isContainUpperCase',
    label: 'Huruf kapital [A-Z]',
  },
  {
    key: 'isLengthValid',
    label: 'Minimal 8 karakter',
  },
] as const;

export const PROFILE_STATUSES = {
  NEW: 'Baru',
  REVISION: 'Perlu Revisi',
  REJECTED: 'Ditolak',
  APPROVED: 'Disetujui',
};
