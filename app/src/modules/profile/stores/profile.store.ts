// Constants
import {
  AUTHENTICATION_ENDPOINT_UPLOAD_FILE,
  AUTHENTICATION_ENDPOINT_VALIDATE_NPWPKTP,
  AUTHENTICATION_GET_TAX_TYPE,
} from '../../authentication/constants/authentication.api.constant';
import type { IProfile } from '../../authentication/interfaces/authentication-login.interface';
import type { IAuthentucationRegisterDetailPostFileAws } from '../../authentication/interfaces/authentication-register-detail.interface';
import type { IAuthenticationResponseUploadFile } from '../../authentication/interfaces/authentication.interface';
import {
  PROFILE_ENDPOINT_CHANGE_PASSWORD,
  PROFILE_ENDPOINT_GET_PROFILE,
  PROFILE_ENDPOINT_CHECK_PASSWORD,
  PROFILE_ENDPOINT_UPDATE_PROFILE,
  PROFILE_ENDPOINT_GET_PRONIVCE,
  PROFILE_ENDPOINT_GET_CITY,
  PROFILE_ENDPOINT_GET_SUBDISTRICT,
} from '../constants/profile.api.constant';
import { PROFILE_STATE_PROFILE_DATA } from '../constants/profile.constant';

// Interfaces
import type {
  IProfileChangePasswordPayload,
  IProfileStoreStates,
  IProfileUpdatePayload,
  IProfileUpdateCitiesPayload,
  IProfileUpdateDistrictPayload,
} from '../interfaces/profile.interface';

// Pinia
import { defineStore } from 'pinia';

export const useProfileStore = defineStore('profile', {
  state: (): IProfileStoreStates => ({
    profile_isLoading: false,
    profile_profileData: { ...PROFILE_STATE_PROFILE_DATA },
    profile_isMatchOldPassword: null,
    profile_checkPasswordIsLoading: false,
    profile_updatePasswordSuccess: false,
    profileUpdate_listProvince: [],
    profileUpdate_listCity: [],
    profileUpdate_listDistrict: [],
    profileUpdate_listNpwpProvince: [],
    profileUpdate_listNpwpCity: [],
    profileUpdate_listNpwpDistrict: [],
    profile_isLoadingUploadNpwp: false,
    profile_isUploadNpwpError: false,
    profile_uploadNpwpProgress: 0,
    profile_uploadedNpwpSize: '',
    profile_totalNpwpSize: '',
    profile_uploadNpwpFileName: null,
    profile_listOfTaxType: [],

    profile_checkNPWPisLoading: false,
    profile_isExistNPWP: false,
    profile_checkKTPIsLoading: false,
    profile_isExistKTP: false,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    // function to fetch change password
    // return: response fetch change password
    async fetchProfile_changePassword(payload: IProfileChangePasswordPayload): Promise<unknown> {
      try {
        this.profile_isLoading = true;

        const { data, error } = await useApiFetch(PROFILE_ENDPOINT_CHANGE_PASSWORD, {
          method: 'POST',
          body: payload,
          onResponse: async ({ response }) => {
            if (response?._data) {
              this.profile_updatePasswordSuccess = true;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        this.profile_updatePasswordSuccess = false;
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_isLoading = false;
      }
    },
    // function to hit check curretnt password
    // return: none
    async fetchProfile_checkCurrentPassword(payload: string): Promise<unknown> {
      try {
        this.profile_checkPasswordIsLoading = true;
        this.profile_isMatchOldPassword = null;

        const { data, error } = await useApiFetch(PROFILE_ENDPOINT_CHECK_PASSWORD, {
          method: 'POST',
          body: {
            password: payload,
          },
          onResponse: async ({ response }) => {
            if (response?._data?.data?.isMatch) {
              this.profile_isMatchOldPassword = true;
            } else {
              this.profile_isMatchOldPassword = false;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Promise.resolve(data.value?.data?.isMatch);

        // return Promise.resolve(data);
      } catch (error) {
        this.profile_isMatchOldPassword = false;
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_checkPasswordIsLoading = false;
      }
    },

    // function to fetch url profile
    // return: response profile
    async fetchProfile_getProfile(): Promise<IProfile> {
      try {
        this.profile_isLoading = true;

        const { data, error } = await useApiFetch<IBaseApiResponse<IProfile>>(PROFILE_ENDPOINT_GET_PROFILE, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.profile_profileData = response._data.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data.value?.data as unknown as IProfile);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_isLoading = false;
      }
    },

    // function to fetch update profile
    // return: response update profile
    async updateProfile_patchProfile(payload: IProfileUpdatePayload): Promise<unknown> {
      try {
        this.profile_isLoading = true;
        const body = {
          owner_name: payload.owner_name,
          email: payload.email,
          phone_number: payload.phone_number,
          national_id: payload.national_id,
          npwp: payload.npwp,
          npwp_name: payload.npwp_name,
          npwp_file: payload.npwp_file_path,
          npwp_address: payload.npwp_address,
          shipment_address: payload.shipment_address,
          npwp_province: payload.npwp_province.name,
          npwp_province_code: payload.npwp_province.code,
          npwp_city: payload.npwp_city.city_name,
          npwp_city_code: payload.npwp_city.code,
          npwp_district: payload.npwp_district.name,
          npwp_district_code: payload.npwp_district.code,
          npwp_zip_code: payload.npwp_zip_code,
          shipment_province: payload.shipment_province.name,
          shipment_province_code: payload.shipment_province.code,
          shipment_city: payload.shipment_city.city_name,
          shipment_city_code: payload.shipment_city.code,
          shipment_district: payload.shipment_district.name,
          shipment_district_code: payload.shipment_district.code,
          shipment_zip_code: payload.shipment_zip_code,
          tax_type: payload.tax_type,
          tax_invoice: payload.tax_invoice,
        };
        const { data, error } = await useApiFetch(PROFILE_ENDPOINT_UPDATE_PROFILE, {
          method: 'POST',
          body: body,
          onResponse: async ({ response }) => {
            console.log(response);
            this.fetchProfile_getProfile();
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_isLoading = false;
      }
    },

    // function to fetch province
    // return: list province
    async updateProfile_getProvinces(): Promise<unknown> {
      try {
        this.profile_isLoading = true;

        const { data, error } = await useApiFetch(PROFILE_ENDPOINT_GET_PRONIVCE, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.profileUpdate_listProvince = response._data.data.original.data;
            this.profileUpdate_listNpwpProvince = response._data.data.original.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_isLoading = false;
      }
    },
    // function to fetch city
    // return: list city
    async updateProfile_getCities(payload: IProfileUpdateCitiesPayload, type: string): Promise<unknown> {
      if (type == 'npwp') {
        try {
          this.profile_isLoading = true;
          const { data, error } = await useApiFetch(`${PROFILE_ENDPOINT_GET_CITY}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.profileUpdate_listNpwpCity = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }

          return Promise.resolve(data);
        } catch (error) {
          return Promise.reject(new Error(error as string));
        } finally {
          this.profile_isLoading = false;
        }
      } else {
        try {
          this.profile_isLoading = true;
          const { data, error } = await useApiFetch(`${PROFILE_ENDPOINT_GET_CITY}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.profileUpdate_listCity = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }

          return Promise.resolve(data);
        } catch (error) {
          return Promise.reject(new Error(error as string));
        } finally {
          this.profile_isLoading = false;
        }
      }
    },
    // function to fetch district
    // return: list district
    async updateProfile_getDistrict(payload: IProfileUpdateDistrictPayload, type: string): Promise<unknown> {
      if (type == 'npwp') {
        try {
          this.profile_isLoading = true;
          const { data, error } = await useApiFetch(`${PROFILE_ENDPOINT_GET_SUBDISTRICT}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.profileUpdate_listNpwpDistrict = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }
          return Promise.resolve(data);
        } catch (error) {
          return Promise.reject(new Error(error as string));
        } finally {
          this.profile_isLoading = false;
        }
      } else {
        try {
          this.profile_isLoading = true;
          const { data, error } = await useApiFetch(`${PROFILE_ENDPOINT_GET_SUBDISTRICT}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.profileUpdate_listDistrict = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }
          return Promise.resolve(data);
        } catch (error) {
          return Promise.reject(new Error(error as string));
        } finally {
          this.profile_isLoading = false;
        }
      }
    },

    /**
     * Validate npwp
     * @param payload string
     * @returns Promise<unknown>
     */
    async profile_validateNPWP(payload: string): Promise<unknown> {
      try {
        this.profile_checkNPWPisLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_VALIDATE_NPWPKTP, {
          method: 'POST',
          body: {
            type: 'npwp',
            number: payload,
          },
          onResponse: async ({ response }) => {
            console.log(response._data.error == false, 'check is exist');
            if (response._data.error == false) {
              this.profile_isExistNPWP = false;
            } else {
              this.profile_isExistNPWP = true;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Promise.resolve(data.value?.data?.isMatch);

        // return Promise.resolve(data);
      } catch (error) {
        // this.profile_isMatchOldPassword = false;
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_checkNPWPisLoading = false;
      }
    },

    /**
     * Validate ktp
     * @param payload string
     * @returns Promise<unknown>
     */
    async profile_validateKtp(payload: string): Promise<unknown> {
      try {
        this.profile_checkKTPIsLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_VALIDATE_NPWPKTP, {
          method: 'POST',
          body: {
            type: 'ktp',
            number: payload,
          },
          onResponse: async ({ response }) => {
            console.log(response._data.error == false, 'check is exist');
            if (response._data.error == false) {
              this.profile_isExistKTP = false;
            } else {
              this.profile_isExistKTP = true;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Promise.resolve(data.value?.data?.isMatch);

        // return Promise.resolve(data);
      } catch (error) {
        // this.profile_isMatchOldPassword = false;
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_checkKTPIsLoading = false;
      }
    },

    /**
     * Upload file
     */
    async profile_uploadFile(file: File): Promise<string> {
      try {
        this.profile_isLoadingUploadNpwp = true;
        let _filePath = null;
        const { error } = await useApiFetch<IBaseApiResponse<IAuthenticationResponseUploadFile>>(
          AUTHENTICATION_ENDPOINT_UPLOAD_FILE,
          {
            method: 'POST',
            body: { name: file.name },
            headers: {},
            onResponse: async ({ response }) => {
              const filePath = response?._data.data?.filepath;
              const s3Url = response?._data.data?.s3_url;
              _filePath = filePath;
              if (filePath && s3Url) {
                return this.profile_postAws({
                  file: file,
                  file_type: file.type,
                  url: s3Url,
                });
              } else {
                return Promise.reject(null);
              }
            },
          },
        );
        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(_filePath as unknown as string);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.profile_isLoadingUploadNpwp = false;
      }
    },

    /**
     * Post aws
     */
    async profile_postAws(payload: IAuthentucationRegisterDetailPostFileAws) {
      const { file, file_type, url } = payload;
      if (!file) {
        throw new Error(`${file_type} is null`);
      }
      try {
        const res = await fetch(url, {
          method: 'PUT',
          headers: {
            'Content-Type': file.type,
          },
          body: file,
        });

        if (!res.ok) {
          throw new Error(`Failed to upload ${file_type}`);
        }

        return Promise.resolve(true);
      } catch {
        return Promise.reject(new Error(`Failed to upload ${file_type}`));
      }
    },

    async profile_fetchListOfTaxType(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(AUTHENTICATION_GET_TAX_TYPE, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.profile_listOfTaxType = response._data.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      }
    },
  },
});
