import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useProfileStore } from '../profile.store';
import { PROFILE_STATE_PROFILE_DATA } from '../../constants/profile.constant';

describe('Profile store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useProfileStore();
      expect(store.profile_isLoading).toBe(false);
      expect(store.profile_profileData).toMatchObject(PROFILE_STATE_PROFILE_DATA);
      expect(store.profile_isMatchOldPassword).toBeNull();
      expect(store.profile_checkPasswordIsLoading).toBe(false);
      expect(store.profile_updatePasswordSuccess).toBe(false);
      expect(store.profileUpdate_listProvince).toMatchObject([]);
      expect(store.profileUpdate_listCity).toMatchObject([]);
      expect(store.profileUpdate_listDistrict).toMatchObject([]);
      expect(store.profileUpdate_listNpwpProvince).toMatchObject([]);
      expect(store.profileUpdate_listNpwpCity).toMatchObject([]);
      expect(store.profileUpdate_listNpwpDistrict).toMatchObject([]);
    });

    it('has correct initial computed state', () => {
      const store = useProfileStore();
      expect(store.profile_isLoading).toBe(false);
      expect(store.profile_checkPasswordIsLoading).toBe(false);
    });
  });

  it('Is correct default profile state', () => {
    const store = useProfileStore();
    expect(store.profile_profileData).toMatchObject(PROFILE_STATE_PROFILE_DATA);
  });
});
