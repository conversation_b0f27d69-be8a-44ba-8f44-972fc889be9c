// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Interfaces
import type {
  IProfileUpdatePayload,
  IProfileUpdateCitiesPayload,
  IProfileUpdateDistrictPayload,
} from '../interfaces/profile.interface';
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
// import { useProfileStore } from '../stores/profile.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { required, minLength, maxLength, helpers, decimal } from '@vuelidate/validators';
import { useProfileStore } from '../stores/profile.store';
// import { getFilenameFromURL } from '~/app/src/core/helpers/regex.helper';
/**
 * @description Closure function that returns everything what we need into an object
 */
export const useProfileUpdateService = () => {
  /**
   * @description Injected variables
   */
  // Please put your injected variables here
  const store = useProfileStore();
  const toast = useToast(); // Instance of the toast
  const { $bus } = useNuxtApp();
  const {
    profile_profileData,
    profileUpdate_listProvince,
    profileUpdate_listCity,
    profileUpdate_listDistrict,
    profileUpdate_listNpwpProvince,
    profileUpdate_listNpwpCity,
    profileUpdate_listNpwpDistrict,
    profile_isLoadingUploadNpwp,
    profile_isUploadNpwpError,
    profile_uploadNpwpProgress,
    profile_uploadedNpwpSize,
    profile_totalNpwpSize,
    profile_uploadNpwpFileName,
    profile_listOfTaxType,
    profile_checkNPWPisLoading,
    profile_isExistNPWP,
    profile_checkKTPIsLoading,
    profile_isExistKTP,
  } = storeToRefs(store);
  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const profileUpdate_formData = reactive<IProfileUpdatePayload>({
    owner_name: '',
    email: '',
    phone_number: '',
    national_id: '',
    npwp: '',
    npwp_raw: '',
    npwp_name: '',
    npwp_address: '',
    shipment_address: '',
    shipment_province: {
      code: '',
      name: '',
      island: '',
    },
    shipment_city: {
      id: '',
      name: '',
      code: '',
      city_name: '',
    },
    shipment_district: {
      name: '',
      code: '',
    },
    shipment_zip_code: '',
    npwp_province: {
      code: '',
      name: '',
      island: '',
    },
    npwp_city: {
      id: '',
      name: '',
      code: '',
      city_name: '',
    },
    npwp_district: {
      name: '',
      code: '',
    },
    npwp_zip_code: '',
    npwp_file: null,
    npwp_file_path: null,
    tax_type: '',
    tax_invoice: '',
  });

  const profileUpdate_isLoading: Ref<boolean> = ref<boolean>(false);
  const profileUpdate_isEditProfile = ref<boolean>(false);
  const profileUpdate_selectedFile = ref<File | null>(null);
  const profile_isNeedInvoiceBill = ref(true);
  /**
   * @description Form validations
   */
  const profileUpdate_formRules: ComputedRef = computed(() => ({
    owner_name: {
      required,
      maxLength: maxLength(255),
      invalid_full_name: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[a-zA-Z\s]+$/.test(value);
      }),
    },
    phone_number: {
      required,
      maxLength: maxLength(16),
      invalid_phone_number: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[1-9][0-9]*$/.test(value);
      }),
    },
    email: {
      required,
      maxLength: maxLength(255),
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
    },
    national_id: {
      required,
      maxLength: maxLength(16),
      minLength: minLength(16),
      invalid_ktp_digits: helpers.withParams(
        { customMessage: 'ID should contain only digits' },
        function (value: string) {
          return /^\d+$/.test(value);
        },
      ),
    },
    npwp: {
      required,
      invalid_npwp_digits: helpers.withParams(
        { customMessage: 'NPWP harus terdiri dari 15 atau 16 digit' },
        (value: string) => {
          const onlyDigits = value?.replace(/\D/g, '') || '';
          return onlyDigits.length >= 15 && onlyDigits.length <= 16;
        },
      ),
      // valid_format: helpers.withParams(
      //   { customMessage: 'Format NPWP tidak sesuai (contoh: 12.345.678.9-012.345)' },
      //   (value: string) => {
      //     if (!value) return true
      //     return /^\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3}$/.test(value)
      //   }
      // )
    },
    npwp_name: {
      required,
      maxLength: maxLength(255),
      invalid_full_name: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[a-zA-Z\s]+$/.test(value);
      }),
    },
    npwp_address: { required, maxLength: maxLength(1000) },
    shipment_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    shipment_city: {
      id: { required },
      code: { required },
      name: { required },
    },
    shipment_district: {
      code: { required },
      name: { required },
    },
    shipment_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams(
        { customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return /^[1-9][0-9]*$/.test(value);
        },
      ),
    },
    npwp_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    npwp_city: {
      id: { required },
      code: { required },
      name: { required },
    },
    npwp_district: {
      code: { required },
      name: { required },
    },
    npwp_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams(
        { customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return /^[1-9][0-9]*$/.test(value);
        },
      ),
    },
    shipment_address: { required, maxLength: maxLength(1000) },
    // file_npwp: {
    //   required: helpers.withMessage('File is required', required), // File is required
    //   formatXLX: helpers.withMessage(
    //     'Format File tidak valid. Hanya file .pdf or .png .jpg .jpeg yang diperbolehkan',
    //     (value: File | null) => {
    //       if (!value) return true;
    //       const allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
    //       const fileExtension = value.name.split('.').pop()?.toLowerCase();
    //       return allowedExtensions.includes(fileExtension || '');
    //     },
    //   ),
    //   fileSizeMax: helpers.withMessage(
    //     'Ukuran file tidak boleh lebih atau kurang dari 2MB',
    //     (value: File | null) => {
    //       if (!value) return true;
    //       const size = value.size;
    //       const minSizeInBytes = 1 * 1024; // 1KB
    //       const maxSizeInBytes = 2 * 1024 * 1024; // 2MB
    //       return size >= minSizeInBytes && size <= maxSizeInBytes;
    //     },
    //   ),
    // },
    npwp_file_path: { required },
    tax_type: { required },
    tax_invoice: {},
  }));

  const profileUpdate_formValidations = useVuelidate(profileUpdate_formRules, profileUpdate_formData, {
    $autoDirty: true,
    $scope: false,
  });

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  // Please put your side effect here

  /**
   * @description auto add 62 in number phone
   */
  const toIdInternational = (phone: string): string => {
    const digits = phone.replace(/\D/g, '');

    if (digits.startsWith('62')) return digits;
    if (digits.startsWith('0')) return '62' + digits.slice(1);
    return '0' + digits;
  };

  /**
   * @description Handle fetch api authentication login. We call the fetchAuthenticationLogin function from the store to handle the request.
   */
  const profileUpdate_fetchUpdate = async (): Promise<void> => {
    try {
      const payload = {
        ...profileUpdate_formData,
        phone_number: toIdInternational(profileUpdate_formData.phone_number),
      };
      await store.updateProfile_patchProfile(payload).then(() => {
        profileUpdate_selectedFile.value = null;
      });
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Update Profile Success',
        life: 3000,
      });

      profileUpdate_isEditProfile.value = !profileUpdate_isEditProfile.value;
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  // function to fetch province
  // return: none
  const profileUpdate_fetchProvinces = async (): Promise<void> => {
    try {
      await store.updateProfile_getProvinces();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  /**
   * Validate npwp
   */
  const profile_validateNPWP = async (payload: string): Promise<void> => {
    await store.profile_validateNPWP(payload);
  };

  /**
   * Validate ktp
   */
  const profile_validateKtp = async (payload: string): Promise<void> => {
    await store.profile_validateKtp(payload);
  };

  /**
   * fetch list of tax ttype
   */
  const profile_fetchListOfTaxType = async (): Promise<void> => {
    await store.profile_fetchListOfTaxType();
  };

  /**
   * @description Handle upload file
   */
  const profile_uploadFile = async (file: File): Promise<void> => {
    try {
      const result = await store.profile_uploadFile(file);
      if (result) {
        profileUpdate_formData.npwp_file_path = result;
      }
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };
  /**
   * @description Handle toggle edit profile
   */
  const profileUpdate_toggleIsEditProfile = () => {
    if (!profileUpdate_isEditProfile.value) {
      if (profile_profileData.value) {
        profileUpdate_formData.owner_name = profile_profileData.value.npwp_name;
        profileUpdate_formData.email = profile_profileData.value.email;
        profileUpdate_formData.phone_number = profile_profileData.value.phone_number.replace(/^(?:\+?62|0)/, '');
        profileUpdate_formData.national_id = profile_profileData.value.national_id;
        profileUpdate_formData.npwp = profile_profileData.value.npwp;
        profileUpdate_formData.npwp_name = profile_profileData.value.npwp_name;
        profileUpdate_formData.npwp_address = profile_profileData.value.npwp_address;
        profileUpdate_formData.npwp_zip_code = profile_profileData.value.npwp_zip_code;
        profileUpdate_formData.npwp_province = {
          code: profile_profileData.value.npwp_province_code,
          island: profile_profileData.value.npwp_province,
          name: profile_profileData.value.npwp_province,
        };

        profileUpdate_formData.npwp_city = {
          id: profile_profileData.value.npwp_city_code,
          code: profile_profileData.value.npwp_city_code,
          city_name: profile_profileData.value.npwp_city,
          name: profile_profileData.value.npwp_city,
        };

        profileUpdate_formData.npwp_district = {
          code: profile_profileData.value.npwp_district_code,
          name: profile_profileData.value.npwp_district,
        };

        profileUpdate_formData.tax_type = profile_profileData.value.tax_type;
        profileUpdate_formData.npwp_file_path = profile_profileData.value.npwp_file;
        // profileUpdate_formData.npwp_file_path = getFilenameFromURL(profile_profileData.value.npwp_file);

        if (profile_profileData.value?.tax_invoice?.includes('Ya')) {
          profile_isNeedInvoiceBill.value = true;

          const _value = profile_profileData.value?.tax_invoice.match(/\d{2}$/);
          if (_value?.[0]) {
            profileUpdate_formData.tax_invoice = _value?.[0];
          }
        } else {
          profile_isNeedInvoiceBill.value = false;
        }

        if (profile_profileData.value.store_list && profile_profileData.value.store_list.length > 0) {
          const storeData = profile_profileData.value.store_list[0];
          profileUpdate_formData.shipment_address = storeData.address || profile_profileData.value.address;
          profileUpdate_formData.shipment_zip_code =
            storeData.zip_code || profile_profileData.value.shipment_zip_code;

          profileUpdate_formData.shipment_province = {
            code: storeData.province_code || '',
            island: storeData.province || '',
            name: storeData.province || '',
          };

          profileUpdate_formData.shipment_city = {
            id: storeData.city_code || '',
            city_name: storeData.city || '',
            code: storeData.city_code || '',
            name: storeData.city || '',
          };

          profileUpdate_formData.shipment_district = {
            code: storeData.district_code || '',
            name: storeData.district || '',
          };

          profileUpdate_formData.shipment_zip_code = storeData.zip_code;
        } else {
          profileUpdate_formData.shipment_address = profile_profileData.value.address;
          profileUpdate_formData.shipment_zip_code = profile_profileData.value.shipment_zip_code;
          profileUpdate_formData.shipment_province = {
            code: profile_profileData.value.shipment_province_code || '',
            island: profile_profileData.value.shipment_province || '',
            name: profile_profileData.value.shipment_province || '',
          };
          profileUpdate_formData.shipment_city = {
            id: profile_profileData.value.shipment_city_code,
            name: profile_profileData.value.shipment_city,
            code: profile_profileData.value.shipment_city_code,
            city_name: profile_profileData.value.shipment_city,
          };
        }

        if (!profileUpdate_listProvince.value?.length) {
          profileUpdate_fetchProvinces();
        }
      }
    }

    profileUpdate_isEditProfile.value = !profileUpdate_isEditProfile.value;
  };

  // function to toggle checkbox
  const profile_toggleIsNeedInvoiceBill = (value: boolean): void => {
    if (!value) {
      profileUpdate_formData.tax_invoice = '';
    }
  };

  // watch(
  //   () => profileUpdate_formData.npwp,
  //   val => {
  //     const raw = val?.replace(/\D/g, '').slice(0, 15) || '';
  //     const formatted = raw
  //       .replace(/^(\d{2})(\d)/, '$1.$2')
  //       .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
  //       .replace(/^(\d{2})\.(\d{3})\.(\d{3})(\d)/, '$1.$2.$3.$4')
  //       .replace(/^(\d{2})\.(\d{3})\.(\d{3})\.(\d)(\d{3})(\d{1,3})/, '$1.$2.$3.$4-$5.$6');

  //     profileUpdate_formData.npwp_raw = raw;

  //     if (val !== formatted) {
  //       profileUpdate_formData.npwp = formatted;
  //     }
  //   },
  // );

  watch(
    () => profileUpdate_formData.npwp_province,
    async newVal => {
      await store.updateProfile_getCities(newVal as IProfileUpdateCitiesPayload, 'npwp');
    },
  );

  watch(
    () => profileUpdate_formData.npwp_city,
    async newVal => {
      await store.updateProfile_getDistrict(newVal as IProfileUpdateDistrictPayload, 'npwp');
    },
  );
  watch(
    () => profileUpdate_formData.shipment_province,
    async newVal => {
      await store.updateProfile_getCities(newVal as IProfileUpdateCitiesPayload, 'shipment');
    },
  );

  watch(
    () => profileUpdate_formData.shipment_city,
    async newVal => {
      await store.updateProfile_getDistrict(newVal as IProfileUpdateDistrictPayload, 'shipment');
    },
  );

  /**
   * @description Handle action on submit form.
   */
  const profileUpdate_onSubmit = async (): Promise<void> => {
    profileUpdate_formValidations.value.$touch();
    if (profileUpdate_formValidations.value.$invalid) return;

    if (profileUpdate_formData?.npwp_file) {
      await profile_uploadFile(profileUpdate_formData?.npwp_file as File);
    }
    await profileUpdate_fetchUpdate();
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    profileUpdate_formData,
    profileUpdate_formValidations,
    profileUpdate_isLoading,
    profileUpdate_onSubmit,
    profileUpdate_isEditProfile,
    profileUpdate_selectedFile,
    profileUpdate_toggleIsEditProfile,
    profileUpdate_fetchProvinces,
    profile_validateNPWP,
    profile_validateKtp,
    profile_uploadFile,
    profileUpdate_listProvince,
    profileUpdate_listCity,
    profileUpdate_listDistrict,
    profileUpdate_listNpwpProvince,
    profileUpdate_listNpwpCity,
    profileUpdate_listNpwpDistrict,
    profile_isLoadingUploadNpwp,
    profile_isUploadNpwpError,
    profile_uploadNpwpProgress,
    profile_uploadedNpwpSize,
    profile_totalNpwpSize,
    profile_uploadNpwpFileName,
    profile_listOfTaxType,
    profile_fetchListOfTaxType,
    profile_toggleIsNeedInvoiceBill,
    profile_isNeedInvoiceBill,
    profile_checkNPWPisLoading,
    profile_isExistNPWP,
    profile_checkKTPIsLoading,
    profile_isExistKTP,
  };
};
