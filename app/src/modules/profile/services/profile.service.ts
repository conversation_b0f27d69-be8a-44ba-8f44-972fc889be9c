// Helpers
import {
  is<PERSON><PERSON>ain<PERSON><PERSON>er<PERSON><PERSON>,
  isContainNumber,
  isContainSpecialCharacter,
  isContainUpperCase,
} from '~/app/src/core/helpers/regex.helper';

// Interfaces
import type {
  IProfileChangePasswordPayload,
  IProfileProvided,
  IProfileChangePasswordValidations,
} from '../interfaces/profile.interface';
import { PROFILE_LIST_TABS } from '../constants/profile.constant';

// Primevue
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
import { useProfileStore } from '../stores/profile.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { minLength, required, sameAs } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useProfileService = (): IProfileProvided => {
  /**
   * @description Injected variables
   */
  const store = useProfileStore(); // Instance of the store
  const toast = useToast(); // Instance of the toast
  const {
    profile_isLoading,
    profile_profileData,
    profile_isMatchOldPassword,
    profile_checkPasswordIsLoading,
    profile_updatePasswordSuccess,
  } = storeToRefs(store);

  /**
   * @description Reactive data binding
   */
  const profile_formDataOfChangePassword = reactive<IProfileChangePasswordPayload>({
    current_password: '',
    new_password: '',
    new_password_confirmation: '',
  });

  const profileCreatePassword_customValidations = reactive<IProfileChangePasswordValidations>({
    isContainLowerCase: false,
    isContainNumber: false,
    isContainSpecialCharacter: false,
    isContainUpperCase: false,
    isLengthValid: false,
  });

  const profile_selectedTabs = ref(PROFILE_LIST_TABS[0].value);

  /**
   * @description Form validations
   */
  const profile_formRulesOfChangePassword: ComputedRef = computed(() => ({
    current_password: {
      required,
      minLength: minLength(8),
    },
    new_password: {
      required,
      minLength: minLength(8),
      isContainLowerCase,
      isContainUpperCase,
      isContainNumber,
      isContainSpecialCharacter,
    },
    new_password_confirmation: {
      required,
      sameAs: sameAs(profile_formDataOfChangePassword.new_password),
    },
  }));
  const profile_formValidationsOfChangePassword = useVuelidate(
    profile_formRulesOfChangePassword,
    profile_formDataOfChangePassword,
    {
      $autoDirty: true,
      $scope: false,
    },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    () => profile_formDataOfChangePassword.new_password,
    value => {
      if (value) {
        profileCreatePassword_customValidations.isContainLowerCase = isContainLowerCase(value);
        profileCreatePassword_customValidations.isContainNumber = isContainNumber(value);
        profileCreatePassword_customValidations.isContainSpecialCharacter = isContainSpecialCharacter(value);
        profileCreatePassword_customValidations.isContainUpperCase = isContainUpperCase(value);
        profileCreatePassword_customValidations.isLengthValid = value.length >= 8;
      }
    },
  );

  /**
   * @description Handle business logic to check if all custom validations are valid
   */
  const profileCreatePassword_isCustomValidationsValid: ComputedRef<boolean> = computed(() => {
    return Object.values(profileCreatePassword_customValidations).every(value => value);
  });

  /**
   * @description Handle fetch api change password. We call the fetchProfile_changePassword function from the store to handle the request.
   */
  const profile_fetchChangePassword = async (): Promise<void> => {
    await store.fetchProfile_changePassword(profile_formDataOfChangePassword);
  };
  /**
   * @description Handle fetch api check current password. We call the fetchProfile_checkCurrentPassword function from the store to handle the request.
   */
  const profile_checkCurrentPassword = async (val: string): Promise<void> => {
    await store.fetchProfile_checkCurrentPassword(val);
  };

  /**
   * @description Handle fetch api get profile. We call the fetchProfile_getProfile function from the store to handle the request.
   */
  const profile_fetchGetProfile = async (): Promise<void> => {
    await store.fetchProfile_getProfile();
  };

  /**
   * @description Handle business logic to set dynamic class label validation
   */
  const profileCreatePassword_setDynamicClassLabelValidation = (
    key: keyof typeof profileCreatePassword_customValidations,
  ): string => {
    if (profile_formDataOfChangePassword.new_password) {
      return profileCreatePassword_customValidations[key] ? 'text-success' : 'text-danger';
    }

    return 'text-black';
  };

  /**
   * @description Handle business logic to set dynamic path icon
   */
  const profileCreatePassword_setDynamicPathIcon = (
    key: keyof typeof profileCreatePassword_customValidations,
  ): string => {
    if (profileCreatePassword_customValidations[key]) {
      return '/icons/check-circle.svg';
    } else {
      return '/icons/x-circle-red.svg';
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const profile_onSubmitChangePassword = async (): Promise<void> => {
    profile_formValidationsOfChangePassword.value.$touch();

    if (profile_formValidationsOfChangePassword.value.$invalid) {
      return;
    }

    await profile_fetchChangePassword();
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    profileCreatePassword_customValidations,
    profileCreatePassword_isCustomValidationsValid,
    profileCreatePassword_setDynamicClassLabelValidation,
    profileCreatePassword_setDynamicPathIcon,
    profile_fetchGetProfile,
    profile_checkCurrentPassword,
    profile_formDataOfChangePassword,
    profile_formValidationsOfChangePassword,
    profile_isLoading,
    profile_onSubmitChangePassword,
    profile_profileData,
    profile_isMatchOldPassword,
    profile_checkPasswordIsLoading,
    profile_updatePasswordSuccess,
    profile_selectedTabs,
  };
};
