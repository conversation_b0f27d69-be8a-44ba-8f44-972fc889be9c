<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import ProfileTabs from '../components/ProfileTabs.vue';

// Services
import { useProfileService } from '../services/profile.service';
import { useProfileUpdateService } from '../services/profile-update.service';
/**
 * @description Destructure all the data and methods what we need
 */
const {
  profile_fetchGetProfile,
  profile_formDataOfChangePassword,
  profile_formValidationsOfChangePassword,
  profile_isLoading,
  profile_onSubmitChangePassword,
  profile_profileData,
  profile_checkCurrentPassword,
  profile_isMatchOldPassword,
  profile_updatePasswordSuccess,
  profileCreatePassword_customValidations,
  profileCreatePassword_isCustomValidationsValid,
  profileCreatePassword_setDynamicClassLabelValidation,
  profileCreatePassword_setDynamicPathIcon,
  profile_selectedTabs,
} = useProfileService();

const {
  profileUpdate_formData,
  profileUpdate_formValidations,
  profileUpdate_isLoading,
  profileUpdate_onSubmit,
  profileUpdate_isEditProfile,
  profileUpdate_toggleIsEditProfile,
  profileUpdate_listProvince,
  profileUpdate_listCity,
  profileUpdate_listDistrict,
  profileUpdate_listNpwpProvince,
  profileUpdate_listNpwpCity,
  profileUpdate_listNpwpDistrict,
  profile_isLoadingUploadNpwp,
  profile_isUploadNpwpError,
  profile_uploadNpwpProgress,
  profile_uploadedNpwpSize,
  profile_totalNpwpSize,
  profile_uploadNpwpFileName,
  profile_validateNPWP,
  profile_validateKtp,
  profile_uploadFile,
  profile_listOfTaxType,
  profile_fetchListOfTaxType,
  profileUpdate_selectedFile,
  profile_toggleIsNeedInvoiceBill,
  profile_isNeedInvoiceBill,
  profile_checkNPWPisLoading,
  profile_isExistNPWP,
  profile_checkKTPIsLoading,
  profile_isExistKTP,
} = useProfileUpdateService();

/**
 * @description Provide all the data and methods what we need
 */
provide('profile', {
  profile_formDataOfChangePassword,
  profile_formValidationsOfChangePassword,
  profile_onSubmitChangePassword,
  profile_profileData,
  profile_checkCurrentPassword,
  profile_isMatchOldPassword,
  profile_updatePasswordSuccess,
  profileCreatePassword_customValidations,
  profileCreatePassword_isCustomValidationsValid,
  profileCreatePassword_setDynamicClassLabelValidation,
  profileCreatePassword_setDynamicPathIcon,
  profile_selectedTabs,
});

provide('profileUpdate', {
  profileUpdate_formData,
  profileUpdate_formValidations,
  profileUpdate_isLoading,
  profileUpdate_onSubmit,
  profileUpdate_isEditProfile,
  profileUpdate_toggleIsEditProfile,
  profileUpdate_listProvince,
  profileUpdate_listCity,
  profileUpdate_listDistrict,
  profileUpdate_listNpwpProvince,
  profileUpdate_listNpwpCity,
  profileUpdate_listNpwpDistrict,
  profile_isLoadingUploadNpwp,
  profile_isUploadNpwpError,
  profile_uploadNpwpProgress,
  profile_uploadedNpwpSize,
  profile_totalNpwpSize,
  profile_uploadNpwpFileName,
  profile_validateNPWP,
  profile_validateKtp,
  profile_uploadFile,
  profile_listOfTaxType,
  profileUpdate_selectedFile,
  profile_toggleIsNeedInvoiceBill,
  profile_isNeedInvoiceBill,
  profile_checkNPWPisLoading,
  profile_isExistNPWP,
  profile_checkKTPIsLoading,
  profile_isExistKTP,
});

/**
 * @description Define page title state so it can be accessed in main layout component
 */
useState('pageTitle', () => 'Profil');

const route = useRoute();

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  await profile_fetchGetProfile();
  await profile_fetchListOfTaxType();
  const hasEditParams = route.query?.isEdit === '1';
  if (hasEditParams) {
    profileUpdate_toggleIsEditProfile();
  }
});
</script>

<template>
  <MainLayout page-title="Profile">
    <section id="profile" class="flex flex-col gap-6 w-full h-full relative inset-0 z-0">
      <div class="w-full mt-4">
        <BaseLoader v-if="profile_isLoading" />
        <ProfileTabs />
      </div>
    </section>
  </MainLayout>
</template>
