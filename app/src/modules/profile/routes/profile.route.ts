// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/profile',
    component: AppBaseWrapper,
    children: [
      {
        path: '',
        name: 'profile',
        component: () => import('../views/ProfileUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            {
              label: 'Beranda',
              path: '/dashboard',
            },
            {
              label: 'Profile',
              path: '/profile',
            },
          ],
        },
      },
    ],
  },
];

export default routes;
