// Interfaces
import type { Validation } from '@vuelidate/core';
import type { IProfile } from '../../authentication/interfaces/authentication-login.interface';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface IProfileData extends IProfile {}

export interface IProfileChangePasswordPayload {
  current_password: string;
  new_password: string;
  new_password_confirmation: string;
}

export interface IProfileChangePasswordValidations {
  isLengthValid: boolean;
  isContainLowerCase: boolean;
  isContainNumber: boolean;
  isContainSpecialCharacter: boolean;
  isContainUpperCase: boolean;
}

export interface IProfileUpdateCitiesPayload {
  code: string;
  name: string;
  island: string;
}

export interface IProfileUpdateDistrictPayload {
  name: string;
  code: string;
  id: string;
}

export interface IProfileUpdatePayload {
  owner_name: string;
  email: string;
  phone_number: string;
  national_id: string;
  npwp: string;
  npwp_raw: string;
  npwp_name: string;
  npwp_address: string;
  shipment_address: string;
  shipment_province: {
    code: string;
    name: string;
    island: string;
  };
  shipment_city: {
    name: string;
    id: string;
    code: string;
    city_name: string;
  };
  shipment_district: {
    code: string;
    name: string;
  };
  shipment_zip_code: unknown;
  npwp_province: {
    code: string;
    name: string;
    island: string;
  };
  npwp_city: {
    name: string;
    id: string;
    code: string;
    city_name: string;
  };
  npwp_district: {
    code: string;
    name: string;
  };
  npwp_zip_code: unknown;
  npwp_file: File | null;
  npwp_file_path: string | null;
  tax_type: string;
  tax_invoice: string;
}

export interface IProfileProvided {
  profile_fetchGetProfile: () => Promise<void>;
  profile_formDataOfChangePassword: IProfileChangePasswordPayload;
  profile_formValidationsOfChangePassword: globalThis.Ref<Validation>;
  profile_isLoading: Ref<boolean>;
  profile_onSubmitChangePassword: () => Promise<void>;
  profile_checkCurrentPassword: (val: string) => Promise<void>;
  profile_profileData: Ref<IProfileData>;
  profile_isMatchOldPassword: Ref<boolean | null>;
  profile_checkPasswordIsLoading: Ref<boolean>;
  profile_updatePasswordSuccess: Ref<boolean>;
  profileCreatePassword_customValidations: IProfileChangePasswordValidations;
  profileCreatePassword_isCustomValidationsValid: Ref<boolean>;
  profileCreatePassword_setDynamicClassLabelValidation: (key: keyof IProfileChangePasswordValidations) => string;
  profileCreatePassword_setDynamicPathIcon: (key: keyof IProfileChangePasswordValidations) => string;
  profile_selectedTabs: Ref<string> | string;
  profile_fetchListOfTaxType: () => Promise<void>;
}

export interface IProfileUpdateProvided {
  profileUpdate_formData: IProfileUpdatePayload;
  profileUpdate_formValidations: globalThis.Ref<Validation>;
  profileUpdate_isLoading: Ref<boolean>;
  profileUpdate_listProvince: Ref<[]>;
  profileUpdate_listCity: Ref<[]>;
  profileUpdate_listDistrict: Ref<[]>;
  profileUpdate_listNpwpProvince: Ref<[]>;
  profileUpdate_listNpwpCity: Ref<[]>;
  profileUpdate_listNpwpDistrict: Ref<[]>;
  profileUpdate_onSubmit: () => void;
  profileUpdate_isEditProfile: boolean;
  profileUpdate_toggleIsEditProfile: () => void;
  profileUpdate_fetchProvinces: () => Promise<void>;
  profileUpdate_fetchCities: () => Promise<void>;
  profileUpdate_fetchDistrict: () => Promise<void>;

  profile_isLoadingUploadNpwp: Ref<boolean>;
  profile_isUploadNpwpError: Ref<boolean>;
  profile_uploadNpwpProgress: Ref<number>;
  profile_uploadedNpwpSize: Ref<string>;
  profile_totalNpwpSize: Ref<string>;
  profile_uploadNpwpFileName: Ref<string | null>;
  profile_checkNPWPisLoading: Ref<boolean>;
  profile_isExistNPWP: Ref<boolean>;
  profile_checkKTPIsLoading: Ref<boolean>;
  profile_isExistKTP: Ref<boolean>;
  profile_uploadFile: (file: File) => Promise<{ filePath: string }>;
  profile_listOfTaxType: Ref<string[]>;
  profileUpdate_selectedFile: Ref<File | null>;
  profile_toggleIsNeedInvoiceBill: (value: boolean) => void;
  profile_isNeedInvoiceBill: Ref<boolean>;

  profile_validateNPWP: (val: string) => Promise<void>;
  profile_validateKtp: (val: string) => Promise<void>;
}

export interface IProfileStoreStates {
  profile_isLoading: boolean;
  profile_profileData: IProfileData;
  profile_isMatchOldPassword: boolean | null;
  profile_checkPasswordIsLoading: boolean;
  profile_updatePasswordSuccess: boolean;
  profileUpdate_listProvince: [];
  profileUpdate_listCity: [];
  profileUpdate_listDistrict: [];
  profileUpdate_listNpwpProvince: [];
  profileUpdate_listNpwpCity: [];
  profileUpdate_listNpwpDistrict: [];

  // upload NPWP
  profile_isLoadingUploadNpwp: boolean;
  profile_isUploadNpwpError: boolean;
  profile_uploadNpwpProgress: number;
  profile_uploadedNpwpSize: string;
  profile_totalNpwpSize: string;
  profile_uploadNpwpFileName: string | null;
  profile_listOfTaxType: string[];

  profile_checkNPWPisLoading: boolean;
  profile_checkKTPIsLoading: boolean;
  profile_isExistNPWP: boolean;
  profile_isExistKTP: boolean;
}
