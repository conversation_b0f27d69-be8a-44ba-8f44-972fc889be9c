import type { Reactive } from "vue";

export interface INotification {
  id: string;
  date: string;
  title: string;
  message: string;
  url: string;
  category: string;
  level: string;
  is_read: number;
}

export interface INotificationCount {
  Info: number;
  Semua: number;
  Transaksi: number;
}

export interface INotificationStore {
  notification_list: INotification[];
  notification_pagination: IPaginationResponse;
  notification_detailIsLoading: boolean;
  notification_detailData: INotification | null;
  notification_count: INotificationCount | null;
}

export interface IQueryParamsGetListNotification {
  category: 'semua' | 'Info' | 'Transaksi' ;
  page: number
}

export interface INotificationProvided {
  notification_list: Ref<INotification[]>;
  notification_count: Ref<INotificationCount | null>;
  notification_pagination: Ref<IPaginationResponse>;
  notification_detailIsLoading: Ref<boolean>;
  notification_detailData: Ref<INotification | null>;
  notification_queryParamsGetListNotification: Reactive<IQueryParamsGetListNotification>
  notification_getListNotifications: () => Promise<void>;
  notification_getNotificationsCount: () => Promise<void>;
  notification_readAll: () => Promise<void>;
  notification_getNotificationDetail: (orderNumber: string) => Promise<void>;
  notification_onDeleteNotification: (id: string) => Promise<void>;
}
