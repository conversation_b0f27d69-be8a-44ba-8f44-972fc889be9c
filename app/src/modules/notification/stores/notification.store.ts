// Constants
import { NOTIFICATION_ENDPOINT, NOTIFICATION_GET_COUT_ENDPOINT, NOTIFICATION_READ_ALL_ENDPOINT, NOTIFICATION_READ_ENDPOINT } from '../constants/notification.api.constant';

// Interfaces
import type { INotification, INotificationStore, IQueryParamsGetListNotification } from '../interfaces/notification.interface';

// Pinia
import { defineStore } from 'pinia';

export const useNotificationStore = defineStore('notification', {
  state: (): INotificationStore => ({
    notification_list: [],
    notification_pagination: {
      total_data: 0,
      size: 0,
      active_page: 1,
      total_page: 0,
    },
    notification_detailIsLoading: false,
    notification_detailData: null,
    notification_count: null,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get notifications.
     * @url /notification
     * @method GET
     * @access private
     */
    async notification_getListNotifications(params: IQueryParamsGetListNotification): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(NOTIFICATION_ENDPOINT, {
          method: 'GET',
          params,

          onResponse: async ({ response }) => {
            this.notification_list = response._data.data?.data;
            if (response?._data?.data?.total_data) {
              this.notification_pagination.active_page = response?._data?.data?.active_page;
              this.notification_pagination.size = response?._data?.data?.size;
              this.notification_pagination.total_data = response?._data?.data?.total_data;
              this.notification_pagination.total_page = response?._data?.data?.total_page;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get notifications more.
     * @url /notification
     * @method GET
     * @access private
     */
    async notification_getListNotificationsLoad(params: IQueryParamsGetListNotification): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(NOTIFICATION_ENDPOINT, {
          method: 'GET',
          params,

          onResponse: async ({ response }) => {
            this.notification_list = [
              ...this.notification_list,
              ...(response._data.data?.data || [])
            ];
            if (response?._data?.data?.total_data) {
              this.notification_pagination.active_page = response?._data?.data?.active_page;
              this.notification_pagination.size = response?._data?.data?.size;
              this.notification_pagination.total_data = response?._data?.data?.total_data;
              this.notification_pagination.total_page = response?._data?.data?.total_page;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get notifications counter unread.
     * @url /notification/count
     * @method GET
     * @access private
     */
    async notification_getNotificationsCount(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(NOTIFICATION_GET_COUT_ENDPOINT, {
          method: 'GET',

          onResponse: async ({ response }) => {
            this.notification_count = response._data.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api read all notification.
     * @url /notification
     * @method POST
     * @access private
     */
    async notification_readAll(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(NOTIFICATION_READ_ALL_ENDPOINT, {
          method: 'POST',

          onResponse: async ({ response }) => {
            console.log('notification_readAll;', response._data.data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get order list.
     * @url /notification/:id
     * @method GET
     * @access private
     */
    async fetchNotification_getNotificationDetail(
      notificationId: string,
    ): Promise<unknown> {
      try {
        this.notification_detailIsLoading = true;
        const { data, error } = await useApiFetch(`${NOTIFICATION_ENDPOINT}/${notificationId}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const result: INotification = response._data.data;
            console.log('RESULT of fetchOrder_getOrderDetail->', result);
            this.notification_detailData = result;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.notification_detailIsLoading = false;
      }
    },
  
    /**
     * @description Handle fetch api get order list.
     * @url /read/:id
     * @method POST
     * @access private
     */
    async notification_read(
      notificationId: string,
    ): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${NOTIFICATION_READ_ENDPOINT}/${notificationId}`, {
          method: 'POST',
          onResponse: async ({ response }) => {
            console.log('notification_read;', response._data.data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get order list.
     * @url /notification/:id
     * @method DELETE
     * @access private
     */
    async notification_delete(
      notificationId: string,
    ): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${NOTIFICATION_ENDPOINT}/${notificationId}`, {
          method: 'DELETE',
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },
  },
});
