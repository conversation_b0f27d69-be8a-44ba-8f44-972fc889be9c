import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useNotificationStore } from '../notification.store';

describe('Notification store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useNotificationStore();
      expect(store.notification_detailIsLoading).toBe(false);
      expect(store.notification_list).toMatchObject([]);
      expect(store.notification_pagination).toMatchObject({
        total_data: 0,
        size: 0,
        active_page: 1,
        total_page: 0,
      });
      expect(store.notification_detailData).toBeNull();
      expect(store.notification_count).toBeNull();
    });

    it('has correct initial computed state', () => {
      const store = useNotificationStore();
      expect(store.notification_detailIsLoading).toBe(false);
    });
  });

  it('Notification list data should be empty', () => {
    const store = useNotificationStore();
    expect(store.notification_list).toMatchObject([]);
  });

  it('Notification detail data should be null', () => {
    const store = useNotificationStore();
    expect(store.notification_detailData).toBeNull();
  });
});
