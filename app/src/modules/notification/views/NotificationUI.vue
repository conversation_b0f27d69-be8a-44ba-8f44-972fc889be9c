<script setup lang="ts">
// Components
import NotificationContent from '../components/NotificationContent.vue'
import NotificationDetail from '../components/NotificationDetail.vue'

// Services
import { useNotificationService } from '../services/notification.service';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  notification_getNotificationDetail,
} = useNotificationService();

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  if (route?.params?.notificationId) {
    await Promise.allSettled([notification_getNotificationDetail(String(route.params?.notificationId))]);
  }
});

/**
 * @description Handle side effect when reactive data binding is changed
 */
watch(
  [() => route.query?.notificationId],
  async () => {
    await Promise.allSettled([
      notification_getNotificationDetail(String(route.params?.notificationId)),
    ]);
  },
  { deep: true },
);
</script>

<template>
  <MainLayout>
    <section id="dashboard" class="flex flex-row gap-6 w-full h-full relative inset-0 z-0 my-7">
      <section id="notification-list" class="ring-1 ring-[#E5E6E8] rounded-lg max-w-[400px] hidden sm:block">
        <NotificationContent :closeable="false"/>
      </section>
      <section id="notification-detail" class="ring-1 ring-[#E5E6E8] rounded-lg w-full min-h-[70vh] sm:min-h-0">
        <NotificationDetail v-if="route.params?.notificationId" />
      </section>
    </section>
  </MainLayout>
</template>
