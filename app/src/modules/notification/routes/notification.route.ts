// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/notification',
    component: AppBaseWrapper,
    children: [
      {
        path: '',
        name: 'notification',
        component: () => import('../views/NotificationUI.vue'),
        meta: {
          requireAuth: true,
        },
      },
      {
        path: ':notificationId',
        name: 'notification.detail',
        component: () => import('../views/NotificationUI.vue'),
        meta: {
          requireAuth: true,
        },
      },
    ],
  },
];

export default routes;
