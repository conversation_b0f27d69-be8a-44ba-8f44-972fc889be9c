<script setup lang="ts">
import type { INotification } from '../interfaces/notification.interface';

interface NotificationItemProps {
  data: INotification;
}

defineProps<NotificationItemProps>();
const router = useRouter();

const onClickDetailNotification = (id: string) => {
  router.push({
    name: 'notification.detail',
    params: {
      notificationId: id,
    },
  });
};

const getIcon = (level: string) => {
  const baseIconPath = '/icons/notifications';
  if (level == 'success') {
    return `${baseIconPath}/notification-alert-sucess.svg`;
  } else if (level == 'error') {
    return `${baseIconPath}/notification-alert-error.svg`;
  } else if (level == 'reminder') {
    return `${baseIconPath}/notification-alert-reminder.svg`;
  } else if (level == 'overdue') {
    return `${baseIconPath}/notification-alert-overdue.svg`;
  } else {
    return `${baseIconPath}/notification-alert-icon-2.svg`;
  }
};
</script>

<template>
  <PrimeVueButton
    class="!grid !grid-cols-[32px_1fr] !gap-2 !px-6 !py-3 !border-transparent !text-start !items-start !w-full"
    :class="!data.is_read ? '!bg-[#FFF7EC]' : '!bg-white'"
    @click="onClickDetailNotification(data.id);"
  >
    <div class="mt-1">
      <NuxtImg :src="getIcon(data.level ?? 'Info')" alt="close" class="w-[24px] h-[24px]" />
    </div>
    <div>
      <h4 class="text-sm font-semibold mb-2 text-black">{{ data.title }}</h4>
      <p class="text-sm text-muted mb-2 line-clamp-4">
        {{ data.message }}
      </p>
      <div class="flex items-center gap-2">
        <p class="text-[12px] text-muted">{{ data.category ?? 'Info' }}</p>
        <p class="text-[14px] text-muted">·</p>
        <p class="text-[12px] text-muted">
          {{ useDayJsFormat(data.date, 'DD MMMM YYYY') }}
        </p>
        <p class="text-[14px] text-muted">·</p>
        <p class="text-[12px] text-muted"> {{ useDayJsFormat(data.date, 'HH:MM A') }}</p>
      </div>
    </div>
  </PrimeVueButton>
</template>
