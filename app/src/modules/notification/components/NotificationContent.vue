<script setup lang="ts">
import NotificationItem from './NotificationItem.vue';
import type { INotificationProvided } from '../interfaces/notification.interface';

interface Props {
  closeable: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  closeable: true,
});

/**
 * @description Define event emitter
 */
const emits = defineEmits(['onClose']);

const {
  notification_list,
  notification_count,
  notification_pagination,
  notification_queryParamsGetListNotification,
  notification_readAll,
} = inject<INotificationProvided>('notification')!;


const scrollContainer = ref<HTMLElement | null>(null);

const handleScroll = () => {
  const el = scrollContainer.value;
  if (!el) return;

  const reachedBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 1;

  if (reachedBottom && notification_queryParamsGetListNotification.page  < notification_pagination.value.total_page) {
    notification_queryParamsGetListNotification.page = notification_queryParamsGetListNotification.page + 1;
  }
};

const handleClose = () => {
  emits('onClose');
};


onMounted(() => {
  scrollContainer.value?.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  scrollContainer.value?.removeEventListener('scroll', handleScroll);
});
</script>

<template>
  <section id="notification-content" class="relative inset-0 z-0 sm:max-h-[80vh] max-h-[100vh%] overflow-hidden min-w-full sm:min-w-[400px]">
    <div class="flex items-center justify-between px-6 py-3 border-b border-[#E9EBED]">
      <p class="text-md md:text-[20px] font-semibold">Notifikasi</p>
      <PrimeVueButton v-if="props.closeable" severity="danger" variant="text" rounded aria-label="Cancel" @click="handleClose">
        <template #icon>
          <NuxtImg src="/icons/close.svg" alt="close" class="w-6 h-6 cursor-pointer" />
        </template>
      </PrimeVueButton>
    </div>
    <!-- notification filter -->
    <div class="flex items-center gap-4 border-y border-y-[#E9EBED] py-3 px-6">
      <PrimeVueButton
        variant="outlined"
        rounded
        :class="notification_queryParamsGetListNotification.category === 'semua'
          ? '!border-[#FF5A00] !bg-[#FFF7EC] !text-[#FF5A00] !text-[14px]'
          : '!border-[#ACB1B4] !bg-white !text-black !text-[14px]'"
        @click="notification_queryParamsGetListNotification.category = 'semua'"
      >
        Semua 
        <span 
          v-if="notification_count?.Semua"
          class="text-[11px] font-normal mb-2"
        >
          {{ (notification_count?.Semua ?? 0) <= 99 ? (notification_count?.Semua ?? 0) : '99+' }}
        </span>
      </PrimeVueButton>
      <PrimeVueButton
        variant="outlined"
        rounded
        :class="notification_queryParamsGetListNotification.category === 'Info'
          ? '!border-[#FF5A00] !bg-[#FFF7EC] !text-[#FF5A00] !text-[14px]'
          : '!border-[#ACB1B4] !bg-white !text-black !text-[14px]'"
        @click="notification_queryParamsGetListNotification.category = 'Info'"
      >
        Info
        <span
          v-if="notification_count?.Info"
          class="text-[11px] font-normal mb-2"
        >
          {{ (notification_count?.Info ?? 0) <= 99 ? (notification_count?.Info ?? 0) : '99+' }}
        </span>
      </PrimeVueButton>
      <PrimeVueButton
        variant="outlined"
        rounded
        :class="notification_queryParamsGetListNotification.category === 'Transaksi'
          ? '!border-[#FF5A00] !bg-[#FFF7EC] !text-[#FF5A00] !text-[14px]'
          : '!border-[#ACB1B4] !bg-white !text-black !text-[14px]'"
        @click="notification_queryParamsGetListNotification.category = 'Transaksi'"
      >
        Transaksi
        <span 
          v-if="notification_count?.Transaksi"
          class="text-[11px] font-normal mb-2"
        >
          {{ (notification_count?.Transaksi ?? 0) <= 99 ? (notification_count?.Transaksi ?? 0) : '99+' }}
        </span>
      </PrimeVueButton>
    </div>

    <div class="flex items-center justify-between px-6 py-2">
      <p class="font-semibold text-[14px]">Terbaru</p>
      <PrimeVueButton
        label="Tandai sudah dibaca"
        severity="danger"
        variant="text"
        aria-label="Cancel"
        size="small"
        class="!text-[#FF5A00] !text-[12px]"
        @click="notification_readAll"
      />
    </div>

    <div ref="scrollContainer" class="h-[79vh] sm:h-[60vh] overflow-scroll">
      <template v-if="notification_list.length">
        <NotificationItem v-for="(item, index) in notification_list" :key="String(index)" :data="item" />
      </template>
      <BaseEmptyState
        v-else
        title="Tidak ada notifikasi"
        subtitle=""
      />
    </div>
  </section>
</template>
