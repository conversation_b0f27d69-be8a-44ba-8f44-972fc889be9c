<script setup lang="ts">
import type { INotificationProvided } from '../interfaces/notification.interface';

const {
  notification_detailIsLoading: loading,
  notification_detailData,
  notification_onDeleteNotification,
} = inject<INotificationProvided>('notification')!;

const onClickDelete = (id: string) => {
  notification_onDeleteNotification(id);
};
</script>

<template>
  <section id="notification-content" class="relative inset-0 z-0 max-h-[80vh] overflow-hidden">
    <div class="flex items-center gap-4 border-b border-b-[#E9EBED] pb-3 px-6 justify-between">
      <section id="notification-detail-header">
        <div class="flex items-center justify-between mt-3">
          <PrimeVueSkeleton v-if="loading" class="!h-[42px] !w-[200px] mb-3"></PrimeVueSkeleton>
          <p v-else class="text-[28px] font-bold font-druk">{{ notification_detailData?.title }}</p>
        </div>
        <PrimeVueSkeleton v-if="loading" class="!h-[21px] !w-[210px]"></PrimeVueSkeleton>
        <div v-else-if="notification_detailData" class="flex gap-4 items-center text-[14px] text-[#535B66] font-normal">
          <p>{{ notification_detailData?.category ?? 'Info' }}</p>
          <div class="w-1 h-1 rounded-full bg-[#ACB1B4]" ></div> 
          <p>{{ notification_detailData ? useFormatDateTime(notification_detailData?.date) : '-' }}</p>
        </div>
      </section>
      <section id="notification-delete">
        <PrimeVueButton
          variant="outlined"
          size="small"
          class="!text-[#E9151D] !border-[#ACB1B4] !border-none"
          :disabled="loading"
          @click="notification_detailData && onClickDelete(notification_detailData.id)"
        >
          <NuxtImg src="/icons/trash-black.svg" alt="delete-icon" class="w-[24px] h-[24px]" />
        </PrimeVueButton>
      </section>
    </div>

    <!-- notification items -->
    <div class="w-full p-6">
      <div class="h-[65vh] overflow-scroll">
        <div v-if="loading" >
          <PrimeVueSkeleton v-for="i in 3" :key="i" class="!h-[24px] !w-[100%] !mb-3" />
        </div>
        <p v-else>
          {{ notification_detailData?.message }}
        </p>
      </div>
    </div>
  </section>
</template>
