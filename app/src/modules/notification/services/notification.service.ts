// Interfaces
import type { INotificationProvided, IQueryParamsGetListNotification } from '../interfaces/notification.interface';

// Store
import { storeToRefs } from 'pinia';
import { useNotificationStore } from '../stores/notification.store';

// Primevue
import { useToast } from 'primevue/usetoast';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useNotificationService = (): INotificationProvided => {
  const router = useRouter();
  const toast = useToast(); // Instance of the toast
  /**
   * @description Injected variables
   */
  const store = useNotificationStore(); // Instance of the store
  const {
    notification_list,
    notification_pagination,
    notification_detailIsLoading,
    notification_detailData,
    notification_count,
  } = storeToRefs(store);

  /**
   * @description Reactive data binding
   */
  const notification_queryParamsGetListNotification = reactive<IQueryParamsGetListNotification>({
    category: 'semua',
    page: 1,
  });

  /**
   * @description Handle fetch api get activities of product. We call the fetchDashboard_getProductActivities function from the store to handle the request.
   */
  const notification_getListNotifications = async (): Promise<void> => {
    store.notification_getListNotifications(notification_queryParamsGetListNotification);
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchDashboard_getProductActivities function from the store to handle the request.
   */
  const notification_getListNotificationsLoad = async (): Promise<void> => {
    store.notification_getListNotificationsLoad(notification_queryParamsGetListNotification);
  };

  const notification_getNotificationsCount = async (): Promise<void> => {
    store.notification_getNotificationsCount();
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchDashboard_getProductActivities function from the store to handle the request.
   */
  const notification_readAll = async (): Promise<void> => {
    store.notification_readAll().finally(() => {
      notification_getListNotifications();
      notification_getNotificationsCount();
    });
  };

  /**
   * @description Handle fetch api get notification detail. We call the fetchOrder_getNotificationDetail function from the store to handle the request.
   */
  const notification_getNotificationDetail = async (id: string) => {
    await store.fetchNotification_getNotificationDetail(id);
    await store.notification_read(id).finally(() => {
      const updatedList = notification_list.value.map(item => {
        if (item.id === id) {
          return { ...item, is_read: 1 };
        }
        return item;
      });
      store.$patch(state => {
        state.notification_list = updatedList;
      });
      notification_getNotificationsCount();
    });
  };

  /**
   * @description Handle fetch api get notification detail. We call the fetchOrder_getNotificationDetail function from the store to handle the request.
   */
  const notification_onDeleteNotification = async (id: string) => {
    const response = await store.notification_delete(id) as { error: boolean };
    if (response.error) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Gagal Hapus Notifikasi',
        life: 3000,
      });
    } else {
      router.push('/notification');
    }
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(() => notification_queryParamsGetListNotification.category, async () => {
    notification_queryParamsGetListNotification.page = 1;
    await notification_getListNotifications();
    await notification_getNotificationsCount();
  });

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(() => notification_queryParamsGetListNotification.page, async () => {
    if (notification_queryParamsGetListNotification.page !== 1){
      await notification_getListNotificationsLoad();
    }
  });

  return {
    notification_list,
    notification_count,
    notification_pagination,
    notification_detailIsLoading,
    notification_detailData,
    notification_queryParamsGetListNotification,
    notification_getListNotifications,
    notification_getNotificationsCount,
    notification_readAll,
    notification_getNotificationDetail,
    notification_onDeleteNotification,
  };
};
