export interface IDashboardQueryParamsNewProducts {
  // type: string;
  misc: string;
  order_by: string;
  limit: number;
}

export interface IDashboardProvided {
  dashboard_queryParamsNewProducts: IDashboardQueryParamsNewProducts;
  dashboard_listOfNewProducts: Ref<IProductListWithPagination>;
  dashboard_listOfNewProductsLoading: Ref<boolean>;
  dashboard_limitCreditData: Ref<ILimitCredit | null>;
  dashboard_limitCreditIsLoading: Ref<boolean>;
  dashboard_summaryTotalProductData: Ref<IDashboardSummaryTotalProduct | null>;
  dashboard_summaryTotalProductIsLoading: Ref<boolean>;
  dashboard_summaryTransactionData: Ref<IDashboardSummaryTransaction | null>;
  dashboard_summaryTotalTransactionData: Ref<IDashboardSummaryTotalTransaction | null>;
  dashboard_summaryTotalTransactionIsLoading: Ref<boolean>;
  dashboard_ongoingOrdersData: Ref<IDashboardOngoingOrder[]>;
  dashboard_ongoingOrdersIsLoading: Ref<boolean>;
  dashboard_fetchProducts: () => Promise<void>;
  dashboard_getLimitCredit: () => Promise<void>;
  dashboard_getSummaryTotalProduct: () => Promise<void>;
  dashboard_getSummaryTransaction: () => Promise<void>;
  dashboard_getSummaryTotalTransaction: () => Promise<void>;
  dashboard_fetchOngoingOrders: () => Promise<void>;
}

export interface IDashboardCardItem {
  label: string;
  value: string;
  unitItem: string;
  increaseCount: number;
  period: string;
  increasePercentage?: number;
}

export interface IDashboardStoreStates {
  dashboard_isLoading: boolean;
  dashboard_listOfProducts: IProductListWithPagination;
  dashboard_listOfProductIsLoading: boolean;
  dashboard_listOfProductActivities: string[];
  dashboard_listOfProductCategories: string[];
  dashboard_listOfProductColors: IProductColor[];
  dashboard_listOfProductSizes: string[];
  dashboard_productDetail: IProduct;
  dashboard_detailIsLoading: boolean;
  dashboard_productDetailVariants: { [key: string]: IProductDetailVariant[] };
  dashboard_listOfNewProductsLoading: boolean;
  dashboard_listOfNewProducts: IProductListWithPagination;
  dashboard_limitCreditData: ILimitCredit | null;
  dashboard_limitCreditIsLoading: boolean;
  dashboard_summaryTotalProductData: IDashboardSummaryTotalProduct | null;
  dashboard_summaryTotalProductIsLoading: boolean;
  dashboard_summaryTransactionData: IDashboardSummaryTransaction | null;
  dashboard_summaryTotalTransactionData: IDashboardSummaryTotalTransaction | null;
  dashboard_summaryTotalTransactionIsLoading: boolean;
  dashboard_ongoingOrdersData: IDashboardOngoingOrder[];
  dashboard_ongoingOrdersIsLoading: boolean;
  dashboard_searchQueryOfProduct: string;
}
