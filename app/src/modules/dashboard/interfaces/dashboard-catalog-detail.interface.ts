export interface IDashboardCatalogDetailProvided {
  dashboardCatalogDetail_activeMainImageUrl: Ref<string>;
  dashboardCatalogDetail_fetchProductDetail: () => Promise<void>;
  dashboardCatalogDetail_isLoading: Ref<boolean>;
  dashboardCatalogDetail_isOpenDialogGuideSizeProduct: Ref<boolean>;
  dashboardCatalogDetail_onChangeMainImage: (url: string) => void;
  dashboardCatalogDetail_onOpenDialogGuideSizeProduct: () => void;
  dashboardCatalogDetail_productDetail: Ref<IProduct>;
  dashboardCatalogDetail_productDetailVariants: Ref<IProductDetailVariant[]>;
  dashboardCatalogDetail_selectedTabGuideSizeProduct: Ref<string>;
  dashboardCatalogDetail_selectedTabProductVariant: Ref<string>;
  dashboardCatalogDetail_selectedTabProductInformation: Ref<string>;
}
