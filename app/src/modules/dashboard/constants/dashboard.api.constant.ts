import { PRODUCT_ENDPOINT_PRODUCTS } from "../../product/constants/product.api.constant";

export const DASHBOARD_ENDPOINT_PRODUCTS = PRODUCT_ENDPOINT_PRODUCTS
export const DASHBOARD_ENDPOINT_GET_PRODUCT_ACTIVITIES = '/product-filter-list/activity';
export const DASHBOARD_ENDPOINT_GET_PRODUCT_CATEGORIES = '/product-filter-list/sub-category';
export const DASHBOARD_ENDPOINT_GET_PRODUCT_COLORS = '/product-filter-list/color';
export const DASHBOARD_ENDPOINT_GET_PRODUCT_DETAIL = '/products/detail';
export const DASHBOARD_ENDPOINT_GET_PRODUCT_SIZES = '/product-filter-list/size';
export const DASHBOARD_ENDPOINT_GET_CREDIT_LIMIT = '/credit-limit';
export const DASHBOARD_ENDPOINT_GET_SUMMARY_PRODUCT = '/homepage/jumlah_product';
export const DASHBOARD_ENDPOINT_GET_SUMMARY_TRANSACTION = '/homepage/jumlah_transaksi';
export const DASHBOARD_ENDPOINT_GET_ONGOING_ORDERS = '/homepage/pesanan_berjalan';
export const DASHBOARD_ENDPOINT_GET_SUMMARY_TOTAL_TRANSACTION = '/homepage/total_transaksi';