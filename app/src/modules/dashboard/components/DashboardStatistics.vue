<!-- eslint-disable @typescript-eslint/ban-ts-comment -->
<script setup lang="ts">
// Interfaces
import type { IAuthenticationProfileProvided } from '../../authentication/interfaces/authentication-profile.interface';
import type { IDashboardProvided } from '../interfaces/dashboard.interface';

// Components
import DashboardStatisticCardSkeleton from './DashboardStatisticCardSkeleton.vue';

/**
 * @description Injecting dependencies
 */
const {
  dashboard_summaryTransactionData,
  dashboard_summaryTotalProductData,
  dashboard_summaryTotalTransactionData,
  dashboard_summaryTotalTransactionIsLoading,
} = inject<IDashboardProvided>('dashboard')!;
const {
  authProfile_profileData,
  authProfile_profileIsLoading
} = inject<IAuthenticationProfileProvided>('profile')!;

const onIncrease = (val: boolean): string => (val ? '+' : '-');
</script>

<template>
  <section id="dashboard-statistics" class="flex flex-col items-center justify-center pb-2">
    <div class="mb-5 w-full">
      <div class="mb- w-full mb-4">
        <PrimeVueSkeleton v-if="authProfile_profileIsLoading" class="!w-[200px] !h-[42px] mb-2" />
        <h1 v-else class="text-xl md:text-[36px] text-[#18191A] font-druk">
          Halo, {{ authProfile_profileData?.owner_name }}!
        </h1>
        <div class="border-b border-[#E5E6E8] py-1 w-full mx-auto"></div>
      </div>
      <template v-if="dashboard_summaryTotalTransactionIsLoading">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <DashboardStatisticCardSkeleton v-for="i in 3" :key="i"/>
        </div>
      </template>
      <template v-else>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- summary total product -->
          <div class="flex w-full items-center justify-between border rounded-lg border-[#ACB1B4] px-6 py-4">
            <div>
              <p class="text-muted mb-4 uppercase text-[12px] font-medium">Pesanan bulan ini</p>
              <div class="flex items-center gap-2 mb-2">
                <p class="text-black text-[28px] mr-2 font-druk">
                  {{
                    dashboard_summaryTotalProductData?.qty_berjalan
                      ? useCurrencyFormat(dashboard_summaryTotalProductData?.qty_berjalan, true)
                      : '0'
                  }}
                </p>
                <div>
                  <p
                    class="text-[14px]"
                    :class="!dashboard_summaryTotalProductData?.is_increase ? 'text-[#E9151D]' : 'text-[#05964C]'"
                  >
                    ({{ onIncrease(dashboard_summaryTotalProductData?.is_increase ?? false) }}
                      {{ dashboard_summaryTotalProductData?.qty_compare ?? 0 }})
                  </p>
                  <p class="text-muted text-[14px]">(30 hari)</p>
                </div>
              </div>
              <p class="text-muted text-[16px]">Barang</p>
            </div>
            <div
              class="rounded-full px-4 py-1"
              :class="!dashboard_summaryTotalProductData?.is_increase ? 'bg-[#fedfd8]' : 'bg-[#EEFFF5]'"
            >
              <div class="flex items-center justify-center gap-2">
                <NuxtImg
                  v-if="dashboard_summaryTotalProductData?.is_increase"
                  src="/icons/arrow-up-green.svg"
                  alt="arrow-up"
                  class="w-[14px] h-[14px]"
                />
                <NuxtImg v-else src="/icons/arrow-down-red.svg" alt="arrow-up" class="w-[14px] h-[14px]" />
                <p
                  class="font-bold text-[14px]"
                  :class="!dashboard_summaryTotalProductData?.is_increase ? 'text-[#E9151D]' : 'text-[#05964C]'"
                >
                  {{ dashboard_summaryTotalProductData?.qty_percentage ?? 0 }}%
                </p>
              </div>
              <p></p>
            </div>
          </div>

          <!-- summary total transaction -->
          <div class="flex w-full items-center justify-between border rounded-lg border-[#ACB1B4] px-6 py-4">
            <div>
              <p class="text-muted mb-4 uppercase text-[12px] font-medium">Total transaksi bulan ini</p>
              <div class="flex items-center gap-2 mb-2">
                <p class="text-black text-[28px] mr-2 font-druk">
                  {{
                    dashboard_summaryTotalTransactionData?.sub_total_berjalan
                      ? useCurrencyFormat(dashboard_summaryTotalTransactionData?.sub_total_berjalan, true)
                      : '0'
                  }}
                </p>
                <div>
                  <p
                    class="text-[#05964C] text-[14px]"
                    :class="
                      !dashboard_summaryTotalTransactionData?.is_increase ? 'text-[#E9151D]' : 'text-[#05964C]'
                    "
                  >
                    ({{ onIncrease(dashboard_summaryTotalTransactionData?.is_increase ?? false) }}
                      {{ dashboard_summaryTotalTransactionData?.sub_total_sebelumnya ?? 0 }})
                  </p>
                  <p class="text-muted text-[14px]">(30 hari)</p>
                </div>
              </div>
              <p class="text-muted text-[16px]">Transaksi</p>
            </div>
            <div
              class="rounded-full px-4 py-1"
              :class="!dashboard_summaryTotalTransactionData?.is_increase ? 'bg-[#fedfd8]' : 'bg-[#EEFFF5]'"
            >
              <div class="flex items-center justify-center gap-2">
                <NuxtImg
                  v-if="dashboard_summaryTotalTransactionData?.is_increase"
                  src="/icons/arrow-up-green.svg"
                  alt="arrow-up"
                  class="w-[14px] h-[14px]"
                />
                <NuxtImg v-else src="/icons/arrow-down-red.svg" alt="arrow-up" class="w-[14px] h-[14px]" />
                <p
                  class="font-bold text-[14px]"
                  :class="
                    !dashboard_summaryTotalTransactionData?.is_increase ? 'text-[#E9151D]' : 'text-[#05964C]'
                  "
                >
                  {{ dashboard_summaryTotalTransactionData?.sub_total_percentage ?? 0 }} %
                </p>
              </div>
              <p></p>
            </div>
          </div>

          <!-- summary limit credit -->
          <div class="flex w-full items-center justify-between border rounded-lg border-[#ACB1B4] px-6 py-4">
            <div class="flex flex-col">
              <p class="text-[#686F72] mb-4 uppercase text-[12px] font-medium">Total Transaksi </p>
              <p class="text-black text-[28px] text-start font-druk mx-0">
                {{
                  dashboard_summaryTransactionData?.sub_total_berjalan ? useCurrencyFormat(dashboard_summaryTransactionData?.sub_total_berjalan, true) : '0'
                }}
              </p>
              <p class="mb-1 text-[14px] text-[#686F72] font-medium">
                dari
                {{
                  dashboard_summaryTransactionData?.sub_total_compare
                    ? useCurrencyFormat(dashboard_summaryTransactionData?.sub_total_compare, true)
                    : '0'
                }}
              </p>
              <p class="text-[#686F72] text-[16px]">Rupiah </p>
            </div>
            <div
              class="rounded-full px-4 py-1"
              :class="!dashboard_summaryTransactionData?.is_increase ? 'bg-[#fedfd8]' : 'bg-[#EEFFF5]'"
            >
              <div class="flex items-center justify-center gap-2">
                <NuxtImg
                  v-if="dashboard_summaryTransactionData?.is_increase"
                  src="/icons/arrow-up-green.svg"
                  alt="arrow-up"
                  class="w-[14px] h-[14px]"
                />
                <NuxtImg v-else src="/icons/arrow-down-red.svg" alt="arrow-up" class="w-[14px] h-[14px]" />
                <p
                  class="font-bold text-[14px]"
                  :class="
                    !dashboard_summaryTransactionData?.is_increase ? 'text-[#E9151D]' : 'text-[#05964C]'
                  "
                >
                  {{ dashboard_summaryTransactionData?.sub_total_percentage ?? 0 }} %
                </p>
              </div>
              <p></p>
            </div>
          </div>
        </div>
      </template>
    </div>
  </section>
</template>

<style lang="css" scoped>
#credit-limit-circle {
  width: 62px !important;
  height: 62px !important;
}
</style>
