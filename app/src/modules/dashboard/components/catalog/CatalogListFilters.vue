<script setup lang="ts">
// Constants
// import { DASHBOARD_CATALOG_LIST_OF_FILTERS } from '../../constants/dashboard.constant';

// Interfaces
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';
import CatalogFilterCategories from './CatalogFilterCategories.vue';
import CatalogFilterPrice from './CatalogFilterPrice.vue';
import CatalogFilterSizes from './CatalogFilterSizes.vue';

/**
 * @description Injecting dependencies
 */
const {
  catalog_selectedFilter,
  catalog_onClickApplyFilters,
  catalog_onClickResetFilters,
  catalog_tempQueryParamsOfProducts,
  catalog_searchCategory,
} = inject<IDashboardCatalogProvided>('dashboardCatalog')!;
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

const isOpenModalInquiryForm = ref(false);

const onSubmitInquiryForm = () => {
  isOpenModalInquiryForm.value = false;
};

const onCustomProduct = () => {
  isOpenModalInquiryForm.value = true;
};

const onClickResetFilters = () => {
  catalog_searchCategory.value = '';
  catalog_onClickResetFilters();
};
</script>

<template>
  <section
    id="catalog-filters"
    class="w-full md:flex hidden overflow-hidden rounded-xl border border-gray-200 flex-col"
  >
    <div
      v-if="!catalog_tempQueryParamsOfProducts.subcategory"
      class="bg-[#147FFF] rounded-t-xl py-2 px-4 text-white text-[14px] flex items-center gap-2"
    >
      <img src="/icons/info-white.svg" alt="" />
      Pilih Kategori terlebih dahulu
    </div>
    <PrimeVueAccordion v-model:value="catalog_selectedFilter">
      <PrimeVueAccordionPanel value="category">
        <PrimeVueAccordionHeader class="!rounded-md">
          <span class="font-medium text-muted text-sm">Kategori</span>
        </PrimeVueAccordionHeader>
        <PrimeVueAccordionContent>
          <CatalogFilterCategories />
        </PrimeVueAccordionContent>
      </PrimeVueAccordionPanel>
      <PrimeVueAccordionPanel value="price">
        <PrimeVueAccordionHeader class="!rounded-md">
          <span class="font-medium text-muted text-sm">Harga</span>
        </PrimeVueAccordionHeader>
        <PrimeVueAccordionContent>
          <CatalogFilterPrice />
        </PrimeVueAccordionContent>
      </PrimeVueAccordionPanel>
      <PrimeVueAccordionPanel value="sizes">
        <PrimeVueAccordionHeader class="!rounded-md">
          <span class="font-medium text-muted text-sm">Ukuran</span>
        </PrimeVueAccordionHeader>
        <PrimeVueAccordionContent>
          <CatalogFilterSizes />
        </PrimeVueAccordionContent>
      </PrimeVueAccordionPanel>
    </PrimeVueAccordion>

    <div class="flex items-center justify-between gap-4 mt-6 p-4">
      <PrimeVueButton
        variant="outlined"
        class="w-full !border-black !text-black !text-sm !font-medium"
        @click="onClickResetFilters"
      >
        Reset
      </PrimeVueButton>
      <PrimeVueButton
        class="w-full !border-black !bg-black !text-sm !font-medium"
        @click="catalog_onClickApplyFilters"
      >
        Terapkan</PrimeVueButton
      >
    </div>
    <div class="mt-auto p-1">
      <div class="bg-zinc-100 rounded-md flex flex-col items-center">
        <img src="/images/custom-product.png" />
        <div class="p-5">
          <h1 class="font-bold">Personalisasi Produkmu dengan Kustomisasi Eksklusif!</h1>
          <div class="text-sm text-muted my-4">
            Pilih gambar atau teks untuk menciptakan perlengkapan unik. Mulai kreasikan sekarang!
          </div>
        </div>
        <PrimeVueButton
          v-if="!config_hideInquiryForm"
          class="!border-black !bg-white !text-black !text-sm !font-medium !w-fit !my-5"
          @click="onCustomProduct"
        >
          Kustom Produk Sekarang
        </PrimeVueButton>
      </div>
    </div>

    <!-- Modal inquiry form -->
    <PrimeVueDialog
      v-if="!config_hideInquiryForm"
      v-model:visible="isOpenModalInquiryForm"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :pt="{
        header: '!px-5 sm:!px-10',
        content: '!px-5 sm:!px-10',
      }"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-druk text-[28px] md:text-[42px] text-black font-bold">B2B Inquiry Form</h4>
        </header>
      </template>
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDialog>
  </section>
</template>
