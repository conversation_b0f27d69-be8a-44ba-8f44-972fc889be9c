<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';
import CatalogFilterCategories from './CatalogFilterCategories.vue';
import CatalogFilterPrice from './CatalogFilterPrice.vue';
import CatalogFilterSizes from './CatalogFilterSizes.vue';

/**
 * @description Injecting dependencies
 */
const {
  catalog_listOfProducts,
  dashboard_listOfProductIsLoading: loading,
  catalog_onLoadMoreProducts,
  catalog_queryParamsOfProducts: params,
  catalog_selectedFilter,
  catalog_onClickApplyFilters,
  catalog_onClickResetFilters,
  catalog_listOfSorting,
  catalog_tempQueryParamsOfProducts,
} = inject<IDashboardCatalogProvided>('dashboardCatalog')!;

const isShowDrawerSorting = ref(false);
const isShowDrawerFilter = ref(false);

// fucntion to get selected sorting
// return : selected sorting
const selectedSorting = catalog_listOfSorting.value.find(item => item.code == 'newest');

// function to toggle drawer bottom
// return : none
const toggleDrawerSorting = () => (isShowDrawerSorting.value = !isShowDrawerSorting.value);

// function to toggle drawer filter
// return : none
const toggleDrawerFilter = () => (isShowDrawerFilter.value = !isShowDrawerFilter.value);

const arraySkeleton = ref(new Array(9));
const arraySkeletonLoadMore = ref(new Array(3));

const onUpdateSortby = (val: string) => {
  catalog_tempQueryParamsOfProducts.page = 1;
  catalog_tempQueryParamsOfProducts.order_by = val;
  catalog_onClickApplyFilters();
};

const onClickApplyFilterMobile = () => {
  toggleDrawerFilter();
  catalog_onClickApplyFilters();
};

const onClickResetFilterMobile = () => {
  toggleDrawerFilter();
  catalog_onClickResetFilters();
};

const hasFiltered = computed(() => {
  return params.subcategory || params.size;
});
</script>

<template>
  <section id="catalog-products" class="w-full flex flex-col gap-6">
    <div class="sm:flex flex-row justify-end hidden">
      <div class="flex items-center gap-2">
        <label id="sortBy" for="sortBy" class="!font-[14px]"> Berdasarkan : </label>
        <PrimeVueSelect
          id="sortBy"
          v-model="params.order_by"
          :options="catalog_listOfSorting"
          placeholder="Urutkan"
          option-value="code"
          option-label="name"
          class="!min-w-[180px] !h-[37px] !border-[#ACB1B4] !rounded-[8px]"
          :pt="{ label: 'px-2 !py-[6px]' }"
          @update:model-value="onUpdateSortby"
        />
      </div>
    </div>
    <div class="sm:hidden flex items-center justify-between">
      <!-- Filter -->
      <PrimeVueButton
        class="!bg-white !border !border-[#ACB1B4] !text-black !text-[14px] !rounded-[8px] !w-[96px]"
        @click="toggleDrawerFilter"
      >
        <div class="relative">
          <img src="/icons/filter-black.svg" alt="" />
          <div
            v-if="hasFiltered"
            class="bg-[#FF5A00] w-[7px] h-[7px] rounded-full absolute top-[-4px] right-[-2px]"
          />
        </div>
        Filter
      </PrimeVueButton>
      <PrimeVueDrawer
        v-model:visible="isShowDrawerFilter"
        position="bottom"
        :show-close-icon="false"
        class="!h-[640px] !p-0 !rounded-t-lg"
        :pt="{
          header: 'border-b-2 border-b-[#E5E6E8] !py-5',
          content: '!py-4 !px-0',
          root: 'rounded-t-xl',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between w-full">
            <div class="">
              <h1 class="!font-druk !text-[22px] text-black">Filter</h1>
            </div>
            <div class="border-black border-2 rounded-full cursor-pointer" @click="toggleDrawerFilter">
              <img src="/icons/close.svg" alt="" class="w-6 h-6" />
            </div>
          </div>
        </template>
        <div
          v-if="!catalog_tempQueryParamsOfProducts.subcategory"
          class="bg-[#147FFF] rounded-t-xl py-2 px-4 text-white text-[14px] flex items-center gap-2"
        >
          <img src="/icons/info-white.svg" alt="" />
          Pilih Kategori terlebih dahulu
        </div>
        <PrimeVueAccordion v-model:value="catalog_selectedFilter" class="">
          <PrimeVueAccordionPanel value="category">
            <PrimeVueAccordionHeader class="!rounded-md">
              <span class="font-medium text-muted text-sm">Kategori</span>
            </PrimeVueAccordionHeader>
            <PrimeVueAccordionContent>
              <CatalogFilterCategories />
            </PrimeVueAccordionContent>
          </PrimeVueAccordionPanel>
          <PrimeVueAccordionPanel value="price">
            <PrimeVueAccordionHeader class="!rounded-md">
              <span class="font-medium text-muted text-sm">Harga</span>
            </PrimeVueAccordionHeader>
            <PrimeVueAccordionContent>
              <CatalogFilterPrice />
            </PrimeVueAccordionContent>
          </PrimeVueAccordionPanel>
          <PrimeVueAccordionPanel value="sizes">
            <PrimeVueAccordionHeader class="!rounded-md">
              <span class="font-medium text-muted text-sm">Ukuran</span>
            </PrimeVueAccordionHeader>
            <PrimeVueAccordionContent>
              <CatalogFilterSizes />
            </PrimeVueAccordionContent>
          </PrimeVueAccordionPanel>
        </PrimeVueAccordion>
        <div class="flex items-center justify-between gap-4 mt-6 px-5">
          <PrimeVueButton
            variant="outlined"
            class="w-full !border-black !text-black !text-sm !font-medium"
            @click="onClickResetFilterMobile"
          >
            Reset
          </PrimeVueButton>
          <PrimeVueButton
            class="w-full !border-black !bg-black !text-sm !font-medium"
            @click="onClickApplyFilterMobile"
          >
            Terapkan</PrimeVueButton
          >
        </div>
      </PrimeVueDrawer>
      <div class="flex items-center gap-2 text-[14px]">
        Berdasarkan :
        <div class="flex items-center gap-2 font-medium cursor-pointer" @click="toggleDrawerSorting">
          {{ selectedSorting?.name }}
          <img src="/icons/chevron-bottom.svg" alt="" />
        </div>
        <PrimeVueDrawer
          v-model:visible="isShowDrawerSorting"
          position="bottom"
          :show-close-icon="false"
          class="!h-[20em] !p-0"
          :pt="{
            header: 'border-b-2 border-b-[#E5E6E8] !py-5',
            content: '!py-4 !px-0',
            root: 'rounded-t-xl',
          }"
        >
          <template #header>
            <div class="flex items-center justify-between w-full">
              <div class="px-5">
                <h1 class="!font-druk !text-[22px] text-black">Sort</h1>
              </div>
              <div class="border-black border-2 rounded-full cursor-pointer" @click="toggleDrawerSorting">
                <img src="/icons/close.svg" alt="" class="w-6 h-6" />
              </div>
            </div>
          </template>
          <template #default>
            <div class="">
              <PrimeVueScrollPanel class="w-full h-full">
                <BaseListBox
                  v-model:selected="params.order_by"
                  :options="catalog_listOfSorting"
                  :selected-value="params.order_by"
                  :disable="false"
                  @update:selected="onUpdateSortby"
                />
              </PrimeVueScrollPanel>
            </div>
          </template>
        </PrimeVueDrawer>
      </div>
    </div>

    <section class="grid grid-rows-1 grid-cols-12 gap-6">
      <template v-if="loading && params.page === 1">
        <section v-for="(_, index) in arraySkeleton" :key="index" class="col-span-6 md:col-span-4">
          <BaseProductCardSkeleton />
        </section>
      </template>
      <section
        v-for="(product, productIndex) in catalog_listOfProducts.data"
        v-else
        :key="`product-${productIndex}`"
        class="col-span-6 md:col-span-4"
      >
        <BaseProductCard :product="product" />
      </section>
    </section>

    <template v-if="loading && params.page > 1">
      <section class="grid grid-rows-1 grid-cols-12 gap-6">
        <section
          v-for="(_, productIndex) in arraySkeletonLoadMore"
          :key="productIndex"
          class="col-span-6 md:col-span-4"
        >
          <BaseProductCardSkeleton />
        </section>
      </section>
      <section class="flex flex-col w-full items-center justify-center">
        <PrimeVueSkeleton class="!w-[140px] !h-[20px] !mb-1" />
        <PrimeVueSkeleton class="!w-[16px] !h-[16px]" />
      </section>
    </template>

    <template v-else>
      <section
        v-if="catalog_listOfProducts.data.length < catalog_listOfProducts.total"
        id="load-more"
        class="flex justify-center w-full cursor-pointer"
        @click="catalog_onLoadMoreProducts"
      >
        <section id="content" class="flex flex-col items-center gap-3">
          <span class="font-medium text-base text-black"> Muat Lebih Banyak (12) </span>
          <NuxtImg src="/icons/chevron-bottom.svg" alt="chevron-bottom" class="w-6 h-6" />
        </section>
      </section>
    </template>

    <div v-if="!loading && catalog_listOfProducts?.data?.length === 0" class="py-8">
      <BaseEmptyState title="Produk tidak ditemukan" subtitle="" />
    </div>
  </section>
</template>
