<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';

/**
 * @description Injecting dependencies
 */
const { catalog_searchQueryOfProduct, catalog_listOfProducts } =
  inject<IDashboardCatalogProvided>('dashboardCatalog')!;
</script>

<template>
  <header class="flex flex-col pt-4 container mx-auto">
    <h1 class="font-bold font-druk text-4xl leading-12 mt-3">
      <PERSON><PERSON> “{{ catalog_searchQueryOfProduct }}”
    </h1>

    <p class="text-base text-muted">({{ catalog_listOfProducts.total }} items)</p>

    <PrimeVueDivider class="border-input-gray" />
  </header>
</template>
