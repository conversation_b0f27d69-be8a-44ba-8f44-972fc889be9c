<script setup lang="ts">
// Constants
import { PRODUCT_CATALOG_LIST_OF_GENDERS } from '../../../product/constants/product.constant';

const selectedFilter = ref<string | null>(null);
</script>

<template>
  <section id="filter-gender" class="flex flex-col">
    <section
      v-for="(gender, genderIndex) in PRODUCT_CATALOG_LIST_OF_GENDERS"
      :id="`gender-${genderIndex}`"
      :key="`gender-${genderIndex}`"
      class="flex items-center mb-4 gap-2"
    >
      <PrimeVueCheckbox
        v-model="selectedFilter"
        :input-id="`gender-${genderIndex}`"
        name="gender"
        :value="gender.value"
      />
      <label :for="`gender-${genderIndex}`">{{ gender.label }}</label>
    </section>
  </section>
</template>
