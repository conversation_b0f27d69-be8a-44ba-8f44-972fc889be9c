<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';

/**
 * @description Injecting dependencies
 */
const { catalog_listOfProductSizes, catalog_tempQueryParamsOfProducts } =
  inject<IDashboardCatalogProvided>('dashboardCatalog')!;
</script>

<template>
  <section id="filter-sizes" class="flex flex-col">
    <template v-if="catalog_tempQueryParamsOfProducts.subcategory">
      <div class="max-h-80 w-full overflow-scroll">
        <section
          v-for="(size, sizeIndex) in catalog_listOfProductSizes"
          :id="`size-${sizeIndex}`"
          :key="`size-${sizeIndex}`"
          class="flex items-center mb-4 gap-2"
        >
          <PrimeVueCheckbox
            :id="`size-${sizeIndex}`"
            v-model="catalog_tempQueryParamsOfProducts.size"
            :input-id="`size-${sizeIndex}`"
            name="size"
            :value="size"
          />
          <label :for="`size-${sizeIndex}`" class="text-sm text-muted">{{ useCapitalize(size) }}</label>
        </section>
      </div>
    </template>

    <template v-else>
      <section class="flex items-center justify-center">
        <span class="text-muted text-sm">Silahkan pilih category terlebih dahulu</span>
      </section>
    </template>
  </section>
</template>
