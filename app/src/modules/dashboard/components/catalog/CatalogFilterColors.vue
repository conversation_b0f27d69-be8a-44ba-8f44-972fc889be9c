<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';

/**
 * @description Injecting dependencies
 */
const { catalog_listOfProductColors, catalog_queryParamsOfProducts } =
  inject<IDashboardCatalogProvided>('dashboardCatalog')!;
</script>

<template>
  <section id="filter-colors" class="flex flex-col">
    <template v-if="catalog_queryParamsOfProducts.subcategory">
      <section
        v-for="(color, colorIndex) in catalog_listOfProductColors"
        :id="`color-${colorIndex}`"
        :key="`color-${colorIndex}`"
        class="flex items-center mb-4 gap-2"
      >
        <PrimeVueCheckbox
          v-model="catalog_queryParamsOfProducts.color"
          :input-id="`color-${colorIndex}`"
          name="color"
          :value="color.value"
        />
        <label :for="`color-${colorIndex}`" class="text-sm text-muted">{{ useCapitalize(color.value) }}</label>
      </section>
    </template>

    <template v-else>
      <section class="flex items-center justify-center">
        <span class="text-muted text-sm">Please select a category first</span>
      </section>
    </template>
  </section>
</template>
