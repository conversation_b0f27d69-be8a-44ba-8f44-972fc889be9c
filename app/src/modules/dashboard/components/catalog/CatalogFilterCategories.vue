<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';

/**
 * @description Injecting dependencies
 */
const { catalog_listOfProductCategories, catalog_tempQueryParamsOfProducts, catalog_searchCategory } =
  inject<IDashboardCatalogProvided>('dashboardCatalog')!;

const filteredCategories = computed(() => {
  const filtered = Array.from(catalog_listOfProductCategories.value).filter(category => {
    return category.toLowerCase().includes(catalog_searchCategory.value.toLowerCase());
  });
  return filtered;
});

const onChangeSearch = (value: string | undefined) => {
  if (value) {
    catalog_searchCategory.value = value;
  } else {
    catalog_searchCategory.value = '';
  }
};

const onChangeSubCategory = () => {
  catalog_tempQueryParamsOfProducts.color = '';
  catalog_tempQueryParamsOfProducts.size = '';
};
</script>

<template>
  <section id="catalog-filter-categories" class="flex flex-col gap-3 w-full">
    <PrimeVueIconField class="w-full">
      <PrimeVueInputIcon class="pl-1">
        <NuxtImg src="/icons/search.svg" alt="search" class="w-5 h-5" />
      </PrimeVueInputIcon>
      <PrimeVueInputText
        v-model="catalog_searchCategory"
        type="text"
        placeholder="Search"
        class="!text-[14px] !text-black w-full !py-3 !border-input-gray !pl-12 !rounded-lg"
        @update:model-value="onChangeSearch"
      />
    </PrimeVueIconField>

    <PrimeVueScrollPanel class="w-full h-full max-h-80">
      <PrimeVueListbox
        v-model="catalog_tempQueryParamsOfProducts.subcategory"
        :options="filteredCategories"
        class="w-full !p"
        :pt="{
          option: '!border-b !border-solid !border-input-gray',
          root: '!border-none !rounded-none !p-0',
          optionGroup: '!p-0',
        }"
        @change="onChangeSubCategory"
      >
        <template #option="{ option }">
          <section class="flex items-center">
            <div
              class="border-2 rounded h-[24px] w-[24px] mr-3 flex items-center justify-center"
              :class="
                catalog_tempQueryParamsOfProducts.subcategory === option
                  ? '!bg-[#FF5A00] !border-[#FF5A00]'
                  : '!bg-white border-[#ACB1B4] '
              "
            >
              <NuxtImg src="/icons/check-white-icon.svg" alt="check" class="!h-[20] !w-[20px]" />
            </div>
            <span class="text-[14px] text-[#18191A]">
              {{ useCapitalize(option) }}
            </span>
          </section>
        </template>
      </PrimeVueListbox>
    </PrimeVueScrollPanel>
  </section>
</template>

<style lang="css" scoped>
#catalog-filter-categories ul.p-listbox-list {
  background: transparent !important;
  display: none;
}
#catalog-filter-categories .p-listbox-option {
  background-color: transparent;
  padding: 0 !important;
}
</style>
