<script setup lang="ts">
// Constants
import {
  PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION,
  PRODUCT_LIST_OF_SPECIFICATIONS,
} from '../../../product/constants/product.constant';

// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';
import type { ICartProvided } from '../../../cart/interfaces/cart.interface';

// interfaces
interface IAddToCartValue {
  article: string;
  qty: number;
}

/**
 * @description Injecting dependencies
 */
const {
  catalog_isOpenDialogAddToCart,
  catalog_productDetail,
  catalog_detailIsLoading,
  catalog_selectedTabProductInformation,
  catalog_selectedTabProductVariant,
  catalog_productDetailVariants,
} = inject<IDashboardCatalogProvided>('dashboardCatalog')!;
const { cart_getList, cart_addToCartBulk, cart_showToast, cart_toastData, cart_addToCartLoading } =
  inject<ICartProvided>('cart')!;

const filteredUniqueVariant = computed(() => {
  return catalog_productDetail.value?.variant.filter(
    (item, index, self) => index === self.findIndex(t => t.product_variant_c === item.product_variant_c),
  );
});

const addToCartValues = ref<IAddToCartValue[]>([]);

const disabledAddToCart = computed(() => {
  return addToCartValues.value.filter(i => i.qty > 0)?.length === 0;
});

const onClickAddToCart = async () => {
  const result = await cart_addToCartBulk({
    sku_code_c: catalog_productDetail.value?.sku_code_c as string,
    items: addToCartValues.value.filter(i => i.qty > 0),
  });
  if (result) {
    cart_toastData.value.count = addToCartValues.value.filter(i => i.qty > 0).length;
    cart_toastData.value.variants = [catalog_selectedTabProductVariant.value];
    cart_showToast.value = true;
    setTimeout(() => {
      cart_showToast.value = false;
      cart_toastData.value.count = 0;
      cart_toastData.value.variants = [];
    }, 3000);
    cart_getList();

    catalog_isOpenDialogAddToCart.value = false;
    catalog_productDetailVariants.value = {};
    catalog_productDetail.value = null;
  }
};

watch(
  catalog_productDetailVariants.value,
  value => {
    if (value?.[catalog_selectedTabProductVariant?.value]) {
      addToCartValues.value = catalog_productDetailVariants.value?.[catalog_selectedTabProductVariant.value]?.map(
        item => {
          return {
            article: item.article_id,
            qty: item.cart.count,
          };
        },
      );
    }
  },
  { deep: true, immediate: true },
);
</script>

<template>
  <section v-if="catalog_productDetail" id="dialog-add-to-cart" class="relative inset-0 z-0">
    <PrimeVueDialog
      v-model:visible="catalog_isOpenDialogAddToCart"
      modal
      class="h-fit w-[991px]"
      :draggable="false"
    >
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-bold font-druk text-3xl text-black">Langsung Ke Keranjang</h4>
        </header>
      </template>

      <template #default>
        <BaseLoaderBoxed v-if="catalog_detailIsLoading" :height="260" />
        <section v-else id="content" class="flex flex-col gap-3">
          <section id="product-information" class="flex gap-4">
            <NuxtImg
              :src="catalog_productDetail?.main_image ?? '-'"
              alt="product"
              class="w-full h-full max-h-32 max-w-32 rounded-sm"
            />

            <section id="detail-information" class="flex flex-col gap-4 w-full">
              <section id="product" class="flex items-center justify-between w-full">
                <section id="product-sku" class="flex flex-col gap-1">
                  <h6 class="font-medium text-base text-black">
                    {{ catalog_productDetail?.product_name_c ?? '' }}
                  </h6>
                  <p class="text-sm text-muted">SKU {{ catalog_productDetail?.sku_code_c ?? '-' }}</p>
                </section>
                <p id="price" class="font-bold text-xl text-header-orange">
                  {{ useCurrencyOfIDR(+parseInt(catalog_productDetail?.product_price?.amount)) }}
                </p>
              </section>

              <PrimeVueTabs v-model:value="catalog_selectedTabProductInformation">
                <PrimeVueTabList
                  class="w-fit"
                  :pt="{
                    activeBar: '!bg-header-orange',
                  }"
                >
                  <PrimeVueTab
                    v-for="(tab, tabIndex) in PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION"
                    :key="`tab-${tabIndex}`"
                    :value="tab"
                    :pt="{
                      root: `text-sm ${useActiveTab(catalog_selectedTabProductInformation, tab)}`,
                    }"
                  >
                    {{ tab }}
                  </PrimeVueTab>
                </PrimeVueTabList>
                <PrimeVueTabPanels class="!px-0 !pb-0 !pt-6">
                  <PrimeVueTabPanel value="Deskripsi">
                    <div
                      class="text-base text-black overflow-hidden text-ellipsis"
                      v-html="catalog_productDetail?.article_description"
                    ></div>
                  </PrimeVueTabPanel>
                  <PrimeVueTabPanel value="Spesifikasi">
                    <section id="specification-informations" class="grid grid-rows-1 grid-cols-12 gap-4">
                      <section
                        v-for="(specification, specificationIndex) in PRODUCT_LIST_OF_SPECIFICATIONS"
                        id="specification"
                        :key="`specification-${specificationIndex}`"
                        class="col-span-6 flex items-center gap-3 w-full"
                      >
                        <span class="text-base text-black w-36"> {{ specification.label }}: </span>

                        <template v-if="specification.label === 'SKU'">
                          <span class="border-b border-solid border-input-gray text-base text-black pb-1 w-full">
                            {{ catalog_productDetail?.[specification.key as keyof IProduct] ?? '-' }}
                          </span>
                        </template>

                        <template v-else-if="specification.label === 'Berat Satuan'">
                          <span class="border-b border-solid border-input-gray text-base text-black pb-1 w-full">
                            {{
                              catalog_productDetail?.specification?.[
                                specification.key as keyof IProductSpecification
                              ] ?? '-'
                            }}
                            {{ catalog_productDetail?.specification.uomweight ?? '-' }}
                          </span>
                        </template>

                        <template v-else>
                          <span class="border-b border-solid border-input-gray text-base text-black pb-1 w-full">
                            {{
                              catalog_productDetail.specification?.[
                                specification.key as keyof IProductSpecification
                              ] ?? '-'
                            }}
                          </span>
                        </template>
                      </section>
                    </section>
                  </PrimeVueTabPanel>
                </PrimeVueTabPanels>
              </PrimeVueTabs>
            </section>
          </section>

          <section
            id="product-colors-variants"
            class="border border-solid border-input-gray flex flex-col gap-4 rounded-lg p-4"
          >
            <PrimeVueTabs v-model:value="catalog_selectedTabProductVariant">
              <PrimeVueTabList
                class="w-fit"
                :pt="{
                  activeBar: '!bg-header-orange',
                }"
              >
                <PrimeVueTab
                  v-for="(variant, variantIndex) in filteredUniqueVariant"
                  :key="`variant-${variantIndex}`"
                  :value="variant.product_variant_c"
                  :pt="{
                    root: `text-sm ${useActiveTab(catalog_selectedTabProductVariant, variant.product_variant_c)}`,
                  }"
                >
                  {{ useCapitalize(variant.product_variant_c) }}
                </PrimeVueTab>
              </PrimeVueTabList>
              <BaseLoaderBoxed
                v-if="
                  !catalog_productDetailVariants?.[catalog_selectedTabProductVariant] ||
                  catalog_productDetailVariants?.[catalog_selectedTabProductVariant]?.length === 0
                "
                :height="130"
              />
              <PrimeVueTabPanels v-else class="!px-0 !pb-0 !pt-3">
                <div class="w-full">
                  <div class="mt-1">
                    <table class="min-w-full table-auto border-[#ACB1B4]">
                      <thead>
                        <tr class="border-t-1 border-b-1 border-[#ACB1B4]">
                          <th class="py-2 px-4 text-left text-sm w-[100px]">Size</th>
                          <th class="py-2 px-4 text-left text-sm w-[100px]">Stock</th>
                          <th class="py-2 px-4 text-left text-sm w-[100px]">Jumlah Order</th>
                          <th class="py-2 px-4 text-left text-sm w-[100px]">Harga</th>
                        </tr>
                      </thead>
                      <tbody class="border-b border-gray-300">
                        <tr
                          v-for="(item, index) in catalog_productDetailVariants?.[
                            catalog_selectedTabProductVariant
                          ]"
                          :key="String(index)"
                        >
                          <td class="py-2 px-4 text-left text-sm">{{ item.product_size_c }}</td>
                          <td class="py-2 px-4 text-left text-sm">{{ item.stock }}</td>
                          <td class="py-2 px-4 text-left text-sm">
                            <PrimeVueInputNumber
                              v-model="item.cart.count"
                              input-id="horizontal-buttons"
                              show-buttons
                              button-layout="horizontal"
                              :step="1"
                              :min="0"
                              :max="item.stock"
                              fluid
                              class="!w-fit !px-2"
                              :pt="{
                                pcInputText: {
                                  root: '!w-16 !text-center !px-2 !text-sm',
                                },
                              }"
                            >
                              <template #decrementicon>
                                <NuxtImg src="/icons/minus-flat-black.svg" alt="plus" class="w-4 h-4" />
                              </template>
                              <template #incrementicon>
                                <NuxtImg src="/icons/plus-flat-black.svg" alt="plus" class="w-4 h-4" />
                              </template>
                            </PrimeVueInputNumber>
                          </td>
                          <td class="py-2 px-4 text-left text-sm">
                            {{ item?.price?.amount ? useCurrencyFormat(parseInt(item.price.amount), true) : '-' }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </PrimeVueTabPanels>
            </PrimeVueTabs>
          </section>

          <section id="btn-actions" class="w-full">
            <BaseLoaderBoxed v-if="cart_addToCartLoading" size="sm" />
            <PrimeVueButton
              v-else
              type="button"
              size="large"
              class="!border-none !rounded-lg !w-full"
              :disabled="disabledAddToCart"
              :class="{
                '!bg-gray-400 !text-white': disabledAddToCart,
                '!bg-black': !disabledAddToCart,
              }"
              @click="onClickAddToCart"
            >
              <template #default>
                <section id="content" class="flex items-center justify-center gap-2">
                  <NuxtImg src="/icons/plus-flat-white.svg" alt="plus" class="w-5 h-5" />
                  <span class="font-medium text-base text-center text-white w-full"> Produk </span>
                </section>
              </template>
            </PrimeVueButton>
          </section>
        </section>
      </template>
    </PrimeVueDialog>
  </section>
</template>
