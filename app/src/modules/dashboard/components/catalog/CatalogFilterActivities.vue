<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';

/**
 * @description Injecting dependencies
 */
const { catalog_listOfProductActivities, catalog_queryParamsOfProducts } =
  inject<IDashboardCatalogProvided>('dashboardCatalog')!;
</script>

<template>
  <section id="filter-activities" class="flex flex-col">
    <section
      v-for="(activity, activityIndex) in catalog_listOfProductActivities"
      :id="`activity-${activityIndex}`"
      :key="`activity-${activityIndex}`"
      class="flex items-center mb-4 gap-2"
    >
      <PrimeVueCheckbox
        v-model="catalog_queryParamsOfProducts.activity"
        :input-id="`activity-${activityIndex}`"
        name="activity"
        :value="activity"
      />
      <label :for="`activity-${activityIndex}`" class="text-sm text-muted">{{ useCapitalize(activity) }}</label>
    </section>
  </section>
</template>
