<script setup lang="ts">
// Interfaces
import type { IDashboardCatalogProvided } from '../../interfaces/dashboard-catalog.interface';

/**
 * @description Injecting dependencies
 */
const { catalog_tempQueryParamsOfProducts } = inject<IDashboardCatalogProvided>('dashboardCatalog')!;

const onChangeMinValue = (value: number) => {
  if (!catalog_tempQueryParamsOfProducts.maxPrice || catalog_tempQueryParamsOfProducts.maxPrice < value) {
    catalog_tempQueryParamsOfProducts.maxPrice = 99999999;
  }
};
const onChangeMaxPrice = (value: number) => {
  if (!catalog_tempQueryParamsOfProducts.minPrice || catalog_tempQueryParamsOfProducts.minPrice > value) {
    catalog_tempQueryParamsOfProducts.minPrice = 0;
  }
};
</script>

<template>
  <section id="filter-price" class="flex items-center gap-2 w-full">
    <section id="price-minimum" class="flex flex-col gap-2">
      <label for="minimum" class="font-medium text-sm text-black">Minimum</label>
      <div class="w-full md:w-[145px]">
        <PrimeVueInputNumber
          id="minimum"
          v-model="catalog_tempQueryParamsOfProducts.minPrice"
          currency="IDR"
          locale="id-ID"
          placeholder="Rp. 1.000.000"
          class="placeholder:!text-sm w-full"
          @update:model-value="onChangeMinValue"
        />
      </div>
    </section>

    <span class="font-bold text-xs uppercase pt-7">S/D</span>

    <div class="w-full md:w-[148px]">
      <section id="price-minimum" class="flex flex-col gap-2">
        <label for="maksimum" class="font-medium text-sm text-black">Maksimum</label>
        <PrimeVueInputNumber
          id="maksimum"
          v-model="catalog_tempQueryParamsOfProducts.maxPrice"
          currency="IDR"
          locale="id-ID"
          placeholder="Rp. 1.000.000"
          class="placeholder:!text-sm w-full"
          @update:model-value="onChangeMaxPrice"
        />
      </section>
    </div>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-inputnumber-input) {
  width: 145px;
}
</style>
