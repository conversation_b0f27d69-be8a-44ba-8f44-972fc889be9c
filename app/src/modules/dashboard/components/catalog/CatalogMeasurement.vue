<script setup lang="ts">
// Constants
import {
  PRODUCT_LIST_OF_COLUMNS_MEASUREMENT_BOTTOM,
  PRODUCT_LIST_OF_COLUMNS_MEASUREMENT_TOP,
  PRODUCT_LIST_OF_DUMMY_MEASUREMENT_BOTTOM,
  PRODUCT_LIST_OF_DUMMY_MEASUREMENT_TOP,
} from '../../../product/constants/product.constant';
</script>

<template class="">
  <section id="measurement" class="flex flex-col relative inset-0 z-0 gap-8 pb-4">
    <section id="measurement-top" class="flex flex-col gap-3">
      <header class="flex items-center justify-between">
        <PrimeVuebutton class="font-medium text-base text-black">Men Top</PrimeVuebutton>
        <span class="font-bold text-xs text-header-orange uppercase"> Ukuran dihitung dalam (cm) </span>
      </header>

      <PrimeVueDataTable :value="PRODUCT_LIST_OF_DUMMY_MEASUREMENT_BOTTOM" table-style="min-width: 50rem; overflow-x:hidden;">
        <PrimeVueColumn
          v-for="(column, columnIndex) in PRODUCT_LIST_OF_COLUMNS_MEASUREMENT_BOTTOM"
          :key="`column-${columnIndex}`"
          :field="column.key"
          :header="column.label"
          class="overflow-hidden"
        ></PrimeVueColumn>
      </PrimeVueDataTable>
    </section>

    <section id="measurement-top" class="flex flex-col gap-3">
      <header class="flex items-center justify-between">
        <h6 class="font-medium text-base text-black">Bottom</h6>

        <span class="font-bold text-xs text-header-orange uppercase"> Ukuran dihitung dalam (cm) </span>
      </header>

      <PrimeVueDataTable :value="PRODUCT_LIST_OF_DUMMY_MEASUREMENT_TOP" table-style="min-width: 50rem">
        <PrimeVueColumn
          v-for="(column, columnIndex) in PRODUCT_LIST_OF_COLUMNS_MEASUREMENT_TOP"
          :key="`column-${columnIndex}`"
          :field="column.key"
          :header="column.label"
        ></PrimeVueColumn>
      </PrimeVueDataTable>
    </section>
  </section>
</template>
<style scoped>
:deep(.p-datatable-table-container) {
  scrollbar-width: thin;
  scrollbar-color: #ff9800 #f0f0f0; 
}

:deep(.p-datatable-table-container::-webkit-scrollbar) {
  width: 8px;
  height: 6px;
}

:deep(.p-datatable-table-container::-webkit-scrollbar-track) {
  background: red;
  border-radius: 10px;
}

:deep(.p-datatable-table-container::-webkit-scrollbar-thumb) {
  background: #ff9800;
  border-radius: 10px;
}

:deep(.p-datatable-table-container::-webkit-scrollbar-thumb:hover) {
  background: #e68900;
}
</style>
