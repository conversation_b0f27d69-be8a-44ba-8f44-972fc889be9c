<script setup lang="ts">
// components
import Button from 'primevue/button';
import BaseLabel from '~/app/src/core/components/base/BaseLabel.vue';
import type { IDashboardProvided } from '../interfaces/dashboard.interface';

const router = useRouter();

const getSeverity = (status: string) => {
  switch (status.trim().toLocaleLowerCase()) {
    case 'baru':
      return 'warning';
    case 'pembayaran':
    case 'lunas':
      return 'success';
    case 'belum dibayar':
      return 'danger';
    default:
      return 'primary';
  }
};

/**
 * @description Injecting dependencies
 */
const { dashboard_ongoingOrdersIsLoading: loading, dashboard_ongoingOrdersData } =
  inject<IDashboardProvided>('dashboard')!;

const onClickUploadOrder = () => {
  router.push({
    name: 'order',
  });
};

const onClickDetailOrder = (orderGroupId: string, orderId: string, orderStatus: string) => {
  if (orderGroupId && orderStatus?.trim()?.toLowerCase() === 'menunggu konfirmasi') {
    router.push({
      name: 'checkout',
      params: {
        orderNumber: orderGroupId,
      },
    });
  } else {
    router.push({
      name: 'order.detail',
      params: {
        orderNumber: orderId,
      },
    });
  }
};
</script>

<template>
  <section id="dashboard-ongoing-orders" class="flex flex-col items-center justify-center h-full">
    <div class="mb-5 w-full">
      <div class="mb- w-full mb-6">
        <div class="flex items-center justify-between w-full">
          <PrimeVueSkeleton v-if="loading" class="!w-[200px] !h-[40px] mb-2" />
          <h1
            v-else
            class="text-lg md:text-[28px] text-[#18191A]"
            style="font-family: 'Druk Text', sans-serif"
          >
            Pesanan Berjalan
          </h1>
          <PrimeVueSkeleton v-if="loading" class="!w-[100px] !h-[40px] mb-2" />
          <Button v-else class="!bg-white !ring-0 !border !rounded-[8px] !text-black !border-black font-medium" @click="onClickUploadOrder">
            Lihat Semua Pesanan
            <img src="/icons/chevron-right.svg"/>
          </Button>
        </div>
        <div class="border-b border-[#E5E6E8] py-1 w-full mx-auto"></div>
        <div class="border-b-none py-1 w-full mx-auto">
          <template v-if="loading">
            <PrimeVueDataTable
              :value="5"
              :pt="{
              root: {
                class: 'text-[14px]' // Tailwind atau custom class
              },
              headerCell: {
                class: 'text-[14px] font-medium'
              },
              bodyCell: {
                class: 'text-[14px]'
              }
            }"
            >
              <PrimeVueColumn field="code" header="No" class="">
                <template #body>
                  <PrimeVueSkeleton width="2rem" class="!h-[30px]"></PrimeVueSkeleton>
                </template>
              </PrimeVueColumn>
              <PrimeVueColumn field="created_date" header="Tanggal Pesanan">
                <template #body>
                  <PrimeVueSkeleton class="!h-[30px]"></PrimeVueSkeleton>
                </template>
              </PrimeVueColumn>
              <PrimeVueColumn field="order_no" header="Order">
                <template #body>
                  <PrimeVueSkeleton class="!h-[30px]"></PrimeVueSkeleton>
                </template>
              </PrimeVueColumn>
              <PrimeVueColumn field="total" header="Nilai Transaksi">
                <template #body>
                  <PrimeVueSkeleton class="!h-[30px]"></PrimeVueSkeleton>
                </template>
              </PrimeVueColumn>
              <PrimeVueColumn field="order_status" header="Status Pesanan">
                <template #body>
                  <PrimeVueSkeleton class="!h-[30px]"></PrimeVueSkeleton>
                </template>
              </PrimeVueColumn>
              <PrimeVueColumn field="payment_status" header="Status Pembayaran">
                <template #body>
                  <PrimeVueSkeleton class="!h-[30px]"></PrimeVueSkeleton>
                </template>
              </PrimeVueColumn>
            </PrimeVueDataTable>
          </template>
          <PrimeVueDataTable
            v-else
            :value="dashboard_ongoingOrdersData" table-style="min-width: 50rem" class="text-14px"
            :pt="{
              root: {
                class: 'text-[14px]'
              },
              headerCell: {
                class: 'text-[14px] font-medium'
              },
              bodyCell: {
                class: 'text-[14px]'
              }
            }"
          >
            <template #empty>
              <div class="flex items-center justify-center py-8">
                <BaseEmptyState
                  v-if="!loading && dashboard_ongoingOrdersData.length === 0"
                  title="Tidak ada pesanan berjalan"
                  subtitle=""
                />
              </div>
              
            </template>
            <PrimeVueColumn field="code" header="No">
              <template #body="{ data, index }">
                <div
                  class="w-full cursor-pointer"
                  @click="onClickDetailOrder(data.order_group_id, data.order_no, data.order_status)"
                >
                  {{ index + 1 }}
                </div>
              </template>
            </PrimeVueColumn>
            <PrimeVueColumn sortable field="created_date" header="Tanggal Pesanan" >
              <template #sorticon="{ sortOrder }">
                <NuxtImg
                  v-if="sortOrder === 0"
                  src="/icons/sorted-icon.svg"
                  alt="info-icon"
                  class="w-[16px] h-[16px]"
                />
                <NuxtImg
                  v-if="sortOrder === 1"
                  src="/icons/sorted-icon.svg"
                  alt="info-icon"
                  class="w-[16px] h-[16px] rotate-180"
                />
                <NuxtImg
                  v-if="sortOrder === -1"
                  src="/icons/sorted-icon.svg"
                  alt="info-icon"
                  class="w-[16px] h-[16px]"
                />
              </template>
              <template #body="{ data }">
                <div
                  class="w-full cursor-pointer"
                  @click="onClickDetailOrder(data.order_group_id, data.order_no, data.order_status)"
                >
                  {{ useFormatDateTime(data.created_date ?? '') }}
                </div>
              </template>
            </PrimeVueColumn>
            <PrimeVueColumn field="order_no" header="Order">
              <template #body="{ data }">
                <div
                  class="w-full cursor-pointer"
                  @click="onClickDetailOrder(data.order_group_id, data.order_no, data.order_status)"
                >
                  {{ data?.order_no }}
                </div>
              </template>
            </PrimeVueColumn>
            <PrimeVueColumn sortable field="total" header="Nilai Transaksi">
              <template #sorticon="{ sortOrder }">
                <NuxtImg
                  v-if="sortOrder === 0"
                  src="/icons/sorted-icon.svg"
                  alt="info-icon"
                  class="w-[16px] h-[16px]"
                />
                <NuxtImg
                  v-if="sortOrder === 1"
                  src="/icons/sorted-icon.svg"
                  alt="info-icon"
                  class="w-[16px] h-[16px] rotate-180"
                />
                <NuxtImg
                  v-if="sortOrder === -1"
                  src="/icons/sorted-icon.svg"
                  alt="info-icon"
                  class="w-[16px] h-[16px]"
                />
              </template>
              <template #body="{ data }">
                <div
                  class="w-full cursor-pointer"
                  @click="onClickDetailOrder(data.order_group_id, data.order_no, data.order_status)"
                >
                  {{ useCurrencyFormat(parseInt(data.total)) }}
                </div>
              </template>
            </PrimeVueColumn>
            <PrimeVueColumn field="order_status" header="Status Pesanan">
              <template #body="{ data }">
                <div
                  class="w-full cursor-pointer"
                  @click="onClickDetailOrder(data.order_group_id, data.order_no, data.order_status)"
                >
                  <BaseLabel :color="getSeverity(data.order_status)" :text="data.order_status" />
                </div>
              </template>
            </PrimeVueColumn>
            <PrimeVueColumn field="payment_status" header="Status Pembayaran">
              <template #body="{ data }">
                <div
                  class="w-full cursor-pointer"
                  @click="onClickDetailOrder(data.order_group_id, data.order_no, data.order_status)"
                >
                  <BaseLabel
                    :color="
                      data.payment_status === '-' ? getSeverity('Belum dibayar') : getSeverity(data.payment_status)
                    "
                    :text="data.payment_status === '-' ? 'Belum Dibayar' : data.payment_status"
                  />
                </div>
              </template>
            </PrimeVueColumn>
          </PrimeVueDataTable>
        </div>
      </div>
    </div>
  </section>
</template>