<script setup lang="ts">
interface CardItem {
  label: string;
  value: string;
  unitItem: string;
  increaseCount: number;
  period: string;
  increasePercentage?: number;
}

const cardItems = ref<CardItem[]>([
  {
    label: 'Pesanan hari ini',
    value: '1.234',
    unitItem: 'Barang',
    increaseCount: 431,
    period: '30 Hari',
    increasePercentage: 10,
  },
  {
    label: 'total transaksi hari ini',
    value: '1.234',
    unitItem: 'Transaksi',
    increaseCount: 431,
    period: '30 Hari',
    increasePercentage: 10,
  },
]);
</script>

<template>
  <section id="dashboard-statistics" class="flex flex-col items-center justify-center h-full mb-[160px] py-16">
    <div class="mb-5 w-full">
      <div class="mb- w-full mb-6">
        <h1 class="text-xl md:text-[36px] text-[#18191A]" style="font-family: 'Druk Text', sans-serif">
          <PERSON><PERSON>, <PERSON><PERSON><PERSON>!
        </h1>
        <div class="border-b border-[#E5E6E8] py-3 w-full mx-auto"></div>
      </div>

      <div class="grid grid-cols-3 gap-6">
        <div
          v-for="(item, index) in cardItems"
          :key="String(index)"
          class="flex w-full items-center justify-between border rounded-lg border-[#ACB1B4] px-6 py-4"
        >
          <div>
            <p class="text-muted mb-4 uppercase text-sm font-medium">{{ item.label }}</p>
            <div class="flex items-center gap-2 mb-2">
              <p class="text-black text-[28px]" style="font-family: 'Druk Text', sans-serif">
                {{ item.value }}
              </p>
              <div>
                <p class="text-[#05964C] text-xs">(+{{ item.increaseCount }})</p>
                <p class="text-muted text-xs">({{ item.period }})</p>
              </div>
            </div>
            <p class="text-lg text-muted">{{ item.unitItem }}</p>
          </div>
          <div class="bg-[#EEFFF5] rounded-full px-4 py-1">
            <div class="flex items-center justify-center">
              <NuxtImg src="/icons/green-arrow-up.svg" alt="arrow-up" class="w-6 h-6" />
              <p class="text-[#05964C] font-bold">{{ item.increasePercentage }}%</p>
            </div>
            <p></p>
          </div>
        </div>
        <div class="flex w-full items-center justify-between border rounded-lg border-[#ACB1B4] px-6 py-4">
          <div>
            <p class="text-muted mb-4 uppercase text-sm font-medium">Pemakaian limit kredit</p>
            <p class="text-black text-[28px] leading-none" style="font-family: 'Druk Text', sans-serif">
              80.000.000
            </p>
            <p class="mb-1">dari 100.000.000</p>
            <p class="text-lg text-muted">Rupiah</p>
          </div>
          <div class="w-[62px] h-[62px]">
            <NuxtImg src="/icons/credit-limit.svg" alt="arrow-up" class="w-full h-full" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped></style>
