<script setup lang="ts">
// Interfaces
import type { IDashboardProvided } from '../interfaces/dashboard.interface';

// swiper
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay, Navigation } from 'swiper/modules';


const modules = ref([Autoplay, Navigation]);

const router = useRouter();

/**
 * @description Injecting dependencies
 */
const { dashboard_listOfNewProducts, dashboard_listOfNewProductsLoading: loading } =
  inject<IDashboardProvided>('dashboard')!;

const onClickSeeAllProduct = () => {
  router.push({ name: 'catalog' });
};
</script>

<template>
  <section id="dashboard-new-products" class="flex flex-col items-center justify-center h-full">
    <div class="mb-5 w-full">
      <div class="mb- w-full mb-2">
        <div class="flex items-center justify-between w-full mb-4">
          <PrimeVueSkeleton v-if="loading" class="!w-[200px] !h-[40px] mb-2" />
          <h1 v-else class="text-lg md:text-[28px] text-[#18191A]" style="font-family: 'Druk Text', sans-serif">
            Produk Baru
          </h1>
          <PrimeVueSkeleton v-if="loading" class="!w-[100px] !h-[40px] mb-2" />
          <div v-else class="flex border border-black rounded-md gap-2">
            <PrimeVueButton
              class="!text-black !bg-white !border-white !font-medium"
              label="Lihat semua produk"
              @click="onClickSeeAllProduct"
            />
            <img src="/icons/chevron-right.svg" class="mr-2"/>
          </div>
        </div>
        <section id="dashboard-product-content" class="w-full">
          <swiper
            :slides-per-view="2"
            :space-between="24"
            :modules="modules"
            class="mySwiper w-full"
            :navigation="true"
            :breakpoints="{
              '640': { slidesPerView: 2, spaceBetween: 20 },
              '768': { slidesPerView: 3, spaceBetween: 32 },
              '1024': { slidesPerView: 4, spaceBetween: 32 },
            }"
          >
            <template v-if="loading">
              <swiper-slide v-for="(_, index) in 4" :key="String(index)">
                <BaseProductCardSkeleton />
              </swiper-slide>
            </template>
            <template v-else>
              <swiper-slide v-for="(product, index) in dashboard_listOfNewProducts.data" :key="String(index)">
                <BaseProductCard :product="product" />
              </swiper-slide>
            </template>
          </swiper>
        </section>
      </div>
    </div>
    <BaseEmptyState
      v-if="!loading && dashboard_listOfNewProducts?.data?.length === 0"
      title="Tidak ada produk terbaru"
      subtitle=""
    />
  </section>
</template>

<style lang="css" scoped></style>
