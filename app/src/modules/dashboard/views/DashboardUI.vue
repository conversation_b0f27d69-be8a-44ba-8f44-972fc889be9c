<script setup lang="ts">
// Components
import DashboardStatistics from '../components/DashboardStatistics.vue';
import DashboardOngoingOrders from '../components/DashboardOngoingOrders.vue';
import DashboardNewProducts from '../components/DashboardNewProducts.vue';

// Services
import { useDashboardService } from '../services/dashboard.service';
import { useCatalog } from '../services/catalog.service';
import { useAuthenticationProfileService } from '../../authentication/services/authentication-profile.service';
import CatalogDialogAddToCart from '../components/catalog/CatalogDialogAddToCart.vue';
import { useCartService } from '../../cart/services/cart.service';
import CartToastAddToCart from '../../cart/components/CartToastAddToCart.vue';
import { useProductService } from '../../product/services/product.service';
import ProfileAccountStatusAlert from '../../profile/components/ProfileAccountStatusAlert.vue';

/**
 * @description Destructure all the data and methods what we need
 */
const { product_selectedTabProductVariant, product_fetchProductDetail } = useProductService();

const {
  dashboard_queryParamsNewProducts,
  dashboard_listOfNewProducts,
  dashboard_listOfNewProductsLoading,
  dashboard_limitCreditData,
  dashboard_limitCreditIsLoading,
  dashboard_fetchProducts,
  dashboard_getLimitCredit,
  dashboard_getSummaryTransaction,
  dashboard_getSummaryTotalProduct,
  dashboard_getSummaryTotalTransaction,
  dashboard_fetchOngoingOrders,
  dashboard_ongoingOrdersIsLoading,
  dashboard_ongoingOrdersData,
  dashboard_summaryTotalProductData,
  dashboard_summaryTransactionData,
  dashboard_summaryTotalTransactionData,
  dashboard_summaryTotalTransactionIsLoading,
} = useDashboardService();
const {
  catalog_fetchProductDetail,
  catalog_productDetail,
  catalog_detailIsLoading,
  catalog_productDetailVariants,
  catalog_selectedTabProductVariant,
} = useCatalog();
const { authProfile_profileData, authProfile_fetchProfile, authProfile_profileIsLoading } =
  useAuthenticationProfileService();
const { cart_addToCart, cart_getList, cart_addToCartBulk, cart_addToCartLoading, cart_showToast, cart_toastData } =
  useCartService();

/**
 * @description Provide all the data and methods what we need
 */
provide('product', {
  product_selectedTabProductVariant,
  product_fetchProductDetail,
});

provide('dashboard', {
  dashboard_queryParamsNewProducts,
  dashboard_listOfNewProducts,
  dashboard_listOfNewProductsLoading,
  dashboard_limitCreditData,
  dashboard_limitCreditIsLoading,
  dashboard_fetchProducts,
  dashboard_getLimitCredit,
  dashboard_getSummaryTransaction,
  dashboard_getSummaryTotalProduct,
  dashboard_getSummaryTotalTransaction,
  dashboard_fetchOngoingOrders,
  dashboard_ongoingOrdersIsLoading,
  dashboard_ongoingOrdersData,
  dashboard_summaryTransactionData,
  dashboard_summaryTotalProductData,
  dashboard_summaryTotalTransactionData,
  dashboard_summaryTotalTransactionIsLoading,
});
provide('dashboardCatalog', {
  catalog_fetchProductDetail,
  catalog_productDetail,
  catalog_detailIsLoading,
  catalog_productDetailVariants,
  catalog_selectedTabProductVariant,
});

provide('profile', {
  authProfile_profileData,
  authProfile_profileIsLoading,
});
provide('cart', {
  cart_addToCart,
  cart_getList,
  cart_addToCartBulk,
  cart_addToCartLoading,
  cart_showToast,
  cart_toastData,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  await Promise.allSettled([
    authProfile_fetchProfile(),
    dashboard_fetchProducts(),
    // dashboard_getLimitCredit(),
    dashboard_getSummaryTotalProduct(),
    dashboard_getSummaryTotalTransaction(),
    dashboard_fetchOngoingOrders(),
    dashboard_getSummaryTransaction(),
  ]);
});
</script>

<template>
  <MainLayout>
    <section id="dashboard" class="flex flex-col gap-6 w-full h-full relative inset-0 z-0 mt-7">
      <ProfileAccountStatusAlert
        v-if="authProfile_profileData?.status"
        :status="authProfile_profileData?.status"
        :remarks="authProfile_profileData?.remarks"
      />
      <DashboardStatistics />
      <DashboardOngoingOrders />
      <DashboardNewProducts />
      <CatalogDialogAddToCart />
    </section>
    <CartToastAddToCart />
  </MainLayout>
</template>
