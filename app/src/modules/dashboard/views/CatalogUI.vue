<script setup lang="ts">
// Components
import CartToastAddToCart from '../../cart/components/CartToastAddToCart.vue';

import CatalogDialogAddToCart from '../components/catalog/CatalogDialogAddToCart.vue';
import CatalogHeaderSearchProduct from '../components/catalog/CatalogHeaderSearchProduct.vue';
import CatalogListFilters from '../components/catalog/CatalogListFilters.vue';
import CatalogListProducts from '../components/catalog/CatalogListProducts.vue';

// Services
import { useCatalog } from '../services/catalog.service';
import { useAppService } from '~/app/src/core/services/app.service';
import { useCartService } from '../../cart/services/cart.service';
import { useOrderService } from '../../order/services/order.service';
import CatalogFiltersListSkeleton from '../components/catalog/CatalogFiltersListSkeleton.vue';

/**
 * @description Destructure all the data and methods what we need
 */

const { order_formInquiry, order_formValidations } = useOrderService();
const { app_fetchHeaderMenu, app_headerMenu } = useAppService();
const route = useRoute();

const {
  catalog_fetchProductActivities,
  catalog_fetchProductCategories,
  catalog_fetchProductColors,
  catalog_fetchProductSizes,
  catalog_isOpenDialogAddToCart,
  catalog_listOfProductActivities,
  catalog_listOfProductCategories,
  catalog_listOfProductColors,
  catalog_listOfProductSizes,
  catalog_listOfProducts,
  dashboard_listOfProductIsLoading,
  catalog_listOfSorting,
  catalog_fetchProducts,
  catalog_isLoading,
  catalog_onLoadMoreProducts,
  catalog_searchQueryOfProduct,
  catalog_selectedColorOnAddToCart,
  catalog_selectedProduct,
  catalog_selectedTabAddToCart,
  catalog_queryParamsOfProductColorsAndSizes,
  catalog_tempQueryParamsOfProducts,
  catalog_queryParamsOfProducts,
  catalog_fetchProductDetail,
  catalog_productDetail,
  catalog_selectedTabProductInformation,
  catalog_detailIsLoading,
  catalog_productDetailVariants,
  catalog_selectedTabProductVariant,
  catalog_onClickApplyFilters,
  catalog_onClickResetFilters,
  catalog_searchCategory,
} = useCatalog();

const { cart_addToCart, cart_getList, cart_addToCartBulk, cart_addToCartLoading, cart_showToast, cart_toastData } =
  useCartService();

/**
 * @description Provide all the data and methods what we need
 */
provide('app', {
  app_fetchHeaderMenu,
  app_headerMenu,
});
provide('dashboardCatalog', {
  catalog_fetchProductColors,
  catalog_fetchProductSizes,
  catalog_isLoading,
  catalog_isOpenDialogAddToCart,
  catalog_listOfProductActivities,
  catalog_listOfProductCategories,
  catalog_listOfProductColors,
  catalog_listOfProductSizes,
  catalog_listOfProducts,
  dashboard_listOfProductIsLoading,
  catalog_listOfSorting,
  catalog_onLoadMoreProducts,
  catalog_searchQueryOfProduct,
  catalog_selectedColorOnAddToCart,
  catalog_selectedProduct,
  catalog_selectedTabAddToCart,
  catalog_queryParamsOfProductColorsAndSizes,
  catalog_tempQueryParamsOfProducts,
  catalog_queryParamsOfProducts,
  catalog_fetchProductDetail,
  catalog_productDetail,
  catalog_selectedTabProductInformation,
  catalog_detailIsLoading,
  catalog_productDetailVariants,
  catalog_selectedTabProductVariant,
  catalog_onClickApplyFilters,
  catalog_onClickResetFilters,
  catalog_searchCategory,
});
provide('cart', {
  cart_addToCart,
  cart_getList,
  cart_addToCartBulk,
  cart_addToCartLoading,
  cart_showToast,
  cart_toastData,
});

/**
 * @description Provide all the data and methods what we need
 */
provide('order', {
  order_formInquiry,
  order_formValidations,
});

const { app_headerMenuIsLoading } = useAppService();
const headerMenuIsLoading = computed(() => app_headerMenuIsLoading.value);

const subCategoryParam = computed<string | null>(() => {
  return (route.query.subcategory as string) ?? null;
});

/**
 * @description Define page title state so it can be accessed in main layout component
 */
if (catalog_searchQueryOfProduct.value) {
  useState('isUsingHeaderTitle', () => false);
} else {
  useState('pageTitle', () => 'Katalog');
}

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  catalog_searchCategory.value = '';
  // if (catalog_searchQueryOfProduct) {
  //   catalog_tempQueryParamsOfProducts.search = catalog_searchQueryOfProduct.value;
  //   catalog_queryParamsOfProducts.search = catalog_searchQueryOfProduct.value;
  // }

  if (subCategoryParam.value) {
    catalog_tempQueryParamsOfProducts.subcategory = subCategoryParam.value;
    catalog_queryParamsOfProducts.subcategory = subCategoryParam.value;
  } else {
    catalog_tempQueryParamsOfProducts.subcategory = null;
    catalog_queryParamsOfProducts.subcategory = null;
  }

  await Promise.allSettled([
    app_fetchHeaderMenu(),
    catalog_fetchProductActivities(),
    catalog_fetchProductCategories(),
    catalog_fetchProducts({ subcategory: subCategoryParam.value }),
  ]);
});

watch(subCategoryParam, () => {
  if (subCategoryParam.value) {
    catalog_tempQueryParamsOfProducts.subcategory = subCategoryParam.value;
    catalog_queryParamsOfProducts.subcategory = subCategoryParam.value;
    catalog_fetchProducts({ subcategory: subCategoryParam.value });
  } else {
    catalog_tempQueryParamsOfProducts.subcategory = null;
    catalog_queryParamsOfProducts.subcategory = null;
    catalog_fetchProducts({ subcategory: null });
  }
});
</script>

<template>
  <MainLayout :page-title="!catalog_searchQueryOfProduct ? 'Katalog' : ''" :required-auth="false">
    <section id="catalog" class="flex flex-col gap-6 w-full h-full relative inset-0 z-0">
      <CatalogHeaderSearchProduct v-if="catalog_searchQueryOfProduct" />
      <section id="content" class="w-full relative inset-0 z-0 gap-6">
        <div class="grid grid-cols-1 lg:grid-cols-[376px_1fr] gap-6 mb-8">
          <CatalogFiltersListSkeleton v-if="headerMenuIsLoading" />
          <CatalogListFilters v-else />
          <CatalogListProducts />
        </div>
        <CatalogDialogAddToCart />
      </section>
    </section>
    <CartToastAddToCart />
  </MainLayout>
</template>
