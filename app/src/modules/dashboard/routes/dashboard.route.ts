// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/dashboard',
    component: AppBaseWrapper,
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('../views/DashboardUI.vue'),
        meta: {
          requireAuth: true,
        },
      },
    ],
  },
  {
    path: '/catalog',
    component: AppBaseWrapper,
    children: [
      {
        path: '',
        name: 'catalog',
        component: () => import('../views/CatalogUI.vue'),
        meta: {
          requireAuth: true,
        },
      },
    ],
  },
];

export default routes;
