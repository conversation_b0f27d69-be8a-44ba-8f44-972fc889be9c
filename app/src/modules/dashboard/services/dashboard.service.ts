import { useAuthenticationStore } from '../../authentication/stores/authentication.store';
import type { IDashboardProvided, IDashboardQueryParamsNewProducts } from '../interfaces/dashboard.interface';

// stores
import { useDashboardStore } from '../stores/dashboard.store';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useDashboardService = (): IDashboardProvided => {
  /**
   * @description Injected variables
   */
  const store = useDashboardStore(); // Instance of the store
  const authStore = useAuthenticationStore();

  const {
    dashboard_listOfNewProducts,
    dashboard_listOfNewProductsLoading,
    dashboard_limitCreditData,
    dashboard_limitCreditIsLoading,
    dashboard_summaryTotalProductData,
    dashboard_summaryTotalProductIsLoading,
    dashboard_summaryTotalTransactionData,
    dashboard_summaryTransactionData,
    dashboard_summaryTotalTransactionIsLoading,
    dashboard_ongoingOrdersData,
    dashboard_ongoingOrdersIsLoading,
  } = storeToRefs(store);

  const { authentication_userData: user } = storeToRefs(authStore);

  const dashboard_queryParamsNewProducts = reactive<IDashboardQueryParamsNewProducts>({
    misc: 'newarrival',
    order_by: 'newest',
    limit: 8,
  });

  /**
   * @description Handle fetch api get products. We call the fetchDashboard_getNewProducts function from the store to handle the request.
   */
  const dashboard_fetchProducts = async (): Promise<void> => {
    await store.fetchDashboard_getNewProducts(dashboard_queryParamsNewProducts);
  };

  /**
   * @description Handle fetch api get limit credit. We call the fetchDashboard_getLimitCredit function from the store to handle the request.
   */
  const dashboard_getLimitCredit = async (): Promise<void> => {
    await store.fetchDashboard_getLimitCredit();
  };

  /**
   * @description Handle fetch api get summary total product. We call the fetchDashboard_getSummaryTotalProduct function from the store to handle the request.
   */
  const dashboard_getSummaryTotalProduct = async (): Promise<void> => {
    await store.fetchDashboard_getSummaryTotalProduct(String(user.value?.customer_id));
  };

  /**
   * @description Handle fetch api get summary total product. We call the fetchDashboard_getSummaryTotalTransaction function from the store to handle the request.
   */
  const dashboard_getSummaryTotalTransaction = async (): Promise<void> => {
    await store.fetchDashboard_getSummaryTotalTransaction(String(user.value?.customer_id));
  };

  /**
   * @description Handle fetch api get summary total product not this day. We call the fetchDashboard_getSummaryTotalTransaction function from the store to handle the request.
   */
  const dashboard_getSummaryTransaction = async (): Promise<void> => {
    await store.fetchDashboard_getSummaryTransaction(String(user.value?.customer_id));
  };

  /**
   * @description Handle fetch api get ongoing orders. We call the fetchDashboard_getOngoingOrders function from the store to handle the request.
   */
  const dashboard_fetchOngoingOrders = async (): Promise<void> => {
    await store.fetchDashboard_getOngoingOrders(String(user.value?.customer_id));
  };



  /**
   * @description Return everything what we need into an object
   */
  return {
    dashboard_queryParamsNewProducts,
    dashboard_listOfNewProductsLoading,
    dashboard_listOfNewProducts,
    dashboard_limitCreditData,
    dashboard_limitCreditIsLoading,
    dashboard_summaryTotalProductData,
    dashboard_summaryTotalProductIsLoading,
    dashboard_summaryTransactionData,
    dashboard_summaryTotalTransactionData,
    dashboard_summaryTotalTransactionIsLoading,
    dashboard_ongoingOrdersData,
    dashboard_ongoingOrdersIsLoading,
    dashboard_fetchProducts,
    dashboard_getLimitCredit,
    dashboard_getSummaryTotalProduct,
    dashboard_getSummaryTotalTransaction,
    dashboard_getSummaryTransaction,
    dashboard_fetchOngoingOrders,
  };
};
