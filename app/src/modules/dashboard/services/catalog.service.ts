// Constants
import {
  DASHBOARD_CATALOG_LIST_OF_COLORS,
  DASHBOARD_CATALOG_LIST_OF_FILTERS,
} from '../constants/dashboard.constant';

// Interfaces
import type { IDashboardCatalogProvided } from '../interfaces/dashboard-catalog.interface';

// Store
import { storeToRefs } from 'pinia';
import { useAppStore } from '~/app/src/core/stores/app.store';
import { useDashboardStore } from '../stores/dashboard.store';
import { PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION } from '../../product/constants/product.constant';
import { useProductStore } from '../../product/stores/product.store';
// import { useAuthenticationStore } from '../../authentication/stores/authentication.store';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useCatalog = (): IDashboardCatalogProvided => {
  /**
   * @description Injected variables
   */
  const appStore = useAppStore(); // Instance of the store
  const store = useDashboardStore(); // Instance of the store
  const productStore = useProductStore();
  // const authStore = useAuthenticationStore(); // Instance of the store
  const { app_searchQueryOfProduct, app_headerMenu } = storeToRefs(appStore);

  const router = useRouter();

  const {
    dashboard_isLoading,
    dashboard_listOfProductActivities,
    dashboard_listOfProductCategories,
    dashboard_listOfProductColors,
    dashboard_listOfProductSizes,
    dashboard_listOfProducts,
    dashboard_listOfProductIsLoading,
    dashboard_productDetail,
    dashboard_detailIsLoading,
    dashboard_productDetailVariants,
    dashboard_searchQueryOfProduct,
  } = storeToRefs(store);

  /**
   * @description Reactive data binding
   */
  const catalog_isLoadMore = ref<boolean>(false);
  const catalog_isOpenDialogAddToCart = ref<boolean>(false);
  const catalog_tempQueryParamsOfProducts = reactive<IQueryParamsOfProducts>({
    activity: null,
    color: null,
    maxPrice: null,
    minPrice: null,
    limit: 9,
    order_by: 'newest',
    page: 1,
    reff: '',
    search: null,
    size: null,
    subcategory: null,
  });
  const catalog_queryParamsOfProducts = reactive<IQueryParamsOfProducts>({
    activity: null,
    color: null,
    maxPrice: null,
    minPrice: null,
    limit: 9,
    order_by: 'newest',
    page: 1,
    reff: '',
    search: null,
    size: null,
    subcategory: null,
  });
  const catalog_queryParamsOfProductColorsAndSizes = reactive<IQueryParamsOfProductColorsAndSizes>({
    category: '',
    subcategory: '',
  });
  interface SortingList {
    name: string;
    code: string;
  }
  const catalog_listOfSorting = ref<SortingList[]>([
    {
      name: 'Terbaru',
      code: 'newest',
    },
    {
      name: 'Terpopuler',
      code: 'popular',
    },
    {
      name: 'Harga Tertinggi',
      code: 'highest',
    },
    {
      name: 'Harga Terendah',
      code: 'lowest',
    },
  ]);
  const catalog_selectedTabProductInformation = ref<string>(PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION[0]);
  const catalog_selectedColorOnAddToCart = ref<string>(DASHBOARD_CATALOG_LIST_OF_COLORS[0].value);
  const catalog_selectedFilter = ref<string>(DASHBOARD_CATALOG_LIST_OF_FILTERS[0].value);
  const catalog_selectedProduct = ref<IProductList>({} as IProductList);
  const catalog_selectedTabAddToCart = ref<string>(PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION[0]);
  const catalog_selectedTabProductVariant = ref<string>('');
  const catalog_searchCategory = ref<string>('');

  /**
   * @description Handle fetch api get activities of product. We call the fetchDashboard_getProductActivities function from the store to handle the request.
   */
  const catalog_fetchProductActivities = async (): Promise<void> => {
    await store.fetchDashboard_getProductActivities();
  };

  /**
   * @description Handle fetch api get categories of product. We call the fetchDashboard_getProductCategories function from the store to handle the request.
   */
  const catalog_fetchProductCategories = async (): Promise<void> => {
    await store.fetchDashboard_getProductCategories();
  };

  /**
   * @description Handle fetch api get colors of product. We call the fetchDashboard_getProductColors function from the store to handle the request.
   */
  const catalog_fetchProductColors = async (): Promise<void> => {
    console.log('catalog_queryParamsOfProductColorsAndSizes', catalog_queryParamsOfProductColorsAndSizes);
    await store.fetchDashboard_getProductColors(catalog_queryParamsOfProductColorsAndSizes);
  };

  /**
   * @description Handle fetch api get sizes of product. We call the fetchDashboard_getProductSizes function from the store to handle the request.
   */
  const catalog_fetchProductSizes = async (): Promise<void> => {
    await store.fetchDashboard_getProductSizes(catalog_queryParamsOfProductColorsAndSizes);
  };

  /**
   * @description Handle fetch api get products. We call the fetchDashboard_getProducts function from the store to handle the request.
   */
  const catalog_fetchProducts = async (
    params?: Partial<IQueryParamsOfProducts>,
    shouldLoading?: boolean,
  ): Promise<void> => {
    const requestParams = {
      ...catalog_queryParamsOfProducts,
      ...params,
    };

    productStore.product_saveSearchKeyword(requestParams.search ?? '');

    await store.fetchDashboard_getProducts(
      requestParams,
      shouldLoading ? shouldLoading : catalog_isLoadMore.value,
    );
    catalog_isLoadMore.value = false;
  };

  /**
   * @description Handle fetch api get product detail. We call the fetchDashboard_getProductDetail function from the store to handle the request.
   */
  const catalog_fetchProductDetail = async (sku: string): Promise<void> => {
    await store.fetchDashboard_getProductDetail(sku);

    // ? After we've got the product detail, we can set the selected tab of product variant and the main image
    catalog_selectedTabProductVariant.value = dashboard_productDetail.value?.variant?.[0]
      ?.product_variant_c as string;
  };

  /**
   * @description Handle fetch api get detail product variant. We call the fetchDashboard_getProductDetailVariant function from the store to handle the request.
   */
  const catalog_fetchProductDetailVariant = async (sku: string) => {
    await store.fetchDashboard_getProductDetailVariant(sku, catalog_selectedTabProductVariant.value);
  };

  const catalog_onClickApplyFilters = () => {
    catalog_queryParamsOfProducts.activity = catalog_tempQueryParamsOfProducts.activity;
    catalog_queryParamsOfProducts.color = catalog_tempQueryParamsOfProducts.color;
    catalog_queryParamsOfProducts.maxPrice = catalog_tempQueryParamsOfProducts.maxPrice;
    catalog_queryParamsOfProducts.minPrice = catalog_tempQueryParamsOfProducts.minPrice;
    catalog_queryParamsOfProducts.limit = catalog_tempQueryParamsOfProducts.limit;
    catalog_queryParamsOfProducts.order_by = catalog_tempQueryParamsOfProducts.order_by;
    catalog_queryParamsOfProducts.page = catalog_tempQueryParamsOfProducts.page;
    catalog_queryParamsOfProducts.reff = catalog_tempQueryParamsOfProducts.reff;
    catalog_queryParamsOfProducts.search = catalog_tempQueryParamsOfProducts.search;
    catalog_queryParamsOfProducts.size = catalog_tempQueryParamsOfProducts.size;
    catalog_queryParamsOfProducts.subcategory = catalog_tempQueryParamsOfProducts.subcategory;
    // catalog_queryParamsOfProducts.type = catalog_tempQueryParamsOfProducts.type

    setTimeout(() => {
      catalog_fetchProducts();
    }, 200);

    if (catalog_tempQueryParamsOfProducts.subcategory) {
      router.push({
        name: 'catalog',
        query: {
          subcategory: catalog_tempQueryParamsOfProducts.subcategory,
        },
      });
    }
  };

  const catalog_onClickResetFilters = () => {
    // reset temp query
    catalog_tempQueryParamsOfProducts.activity = null;
    catalog_tempQueryParamsOfProducts.color = null;
    catalog_tempQueryParamsOfProducts.maxPrice = null;
    catalog_tempQueryParamsOfProducts.minPrice = null;
    catalog_tempQueryParamsOfProducts.limit = 9;
    catalog_tempQueryParamsOfProducts.order_by = 'newest';
    catalog_tempQueryParamsOfProducts.page = 1;
    catalog_tempQueryParamsOfProducts.reff = '';
    catalog_tempQueryParamsOfProducts.search = null;
    catalog_tempQueryParamsOfProducts.size = null;
    catalog_tempQueryParamsOfProducts.subcategory = null;
    // catalog_tempQueryParamsOfProducts.type = 'wholesales';

    // reset query
    catalog_queryParamsOfProducts.activity = null;
    catalog_queryParamsOfProducts.color = null;
    catalog_queryParamsOfProducts.maxPrice = null;
    catalog_queryParamsOfProducts.minPrice = null;
    catalog_queryParamsOfProducts.limit = 9;
    catalog_queryParamsOfProducts.order_by = 'newest';
    catalog_queryParamsOfProducts.page = 1;
    catalog_queryParamsOfProducts.reff = '';
    catalog_queryParamsOfProducts.search = null;
    catalog_queryParamsOfProducts.size = null;
    catalog_queryParamsOfProducts.subcategory = null;
    // catalog_queryParamsOfProducts.type = 'wholesales';

    // also reset search query at header menu
    app_searchQueryOfProduct.value = '';

    router.push({
      name: 'catalog',
    });

    setTimeout(() => {
      catalog_fetchProducts();
    }, 200);
  };

  /**
   * @description Handle business logic for load more products
   */
  const catalog_onLoadMoreProducts = async (): Promise<void> => {
    catalog_isLoadMore.value = true;
    catalog_queryParamsOfProducts.page += 1;
    catalog_queryParamsOfProducts.limit = 12;

    setTimeout(() => {
      catalog_fetchProducts();
    }, 200);
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    catalog_tempQueryParamsOfProducts,
    useDebounce(async (newValue: IQueryParamsOfProducts) => {
      if (newValue.subcategory !== catalog_queryParamsOfProductColorsAndSizes.subcategory) {
        app_headerMenu.value.forEach((dtcategory: IHeaderMenu) => {
          dtcategory?.submenu.forEach((dtsubcategory: { name: string; menu_item: string[] }) => {
            if (dtsubcategory.menu_item.includes(newValue.subcategory as string)) {
              catalog_queryParamsOfProductColorsAndSizes.category = dtsubcategory.name;
              catalog_queryParamsOfProductColorsAndSizes.subcategory = newValue.subcategory as string;
            }
          });
        });

        await Promise.allSettled([catalog_fetchProductColors(), catalog_fetchProductSizes()]);
      }
    }, 250),
    {
      deep: true,
    },
  );

  // watch(
  //   app_searchQueryOfProduct,
  //   useDebounce(value => {
  //     if (value) {
  //       catalog_queryParamsOfProducts.search = value as string;
  //       catalog_fetchProducts();
  //     }
  //   }, 500),
  // );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    catalog_selectedTabProductVariant,
    async value => {
      if (value && dashboard_productDetail?.value?.sku_code_c) {
        await catalog_fetchProductDetailVariant(dashboard_productDetail?.value?.sku_code_c);
      }
    },
    {
      immediate: true,
    },
  );

  /**
   * @description Return everything what we need into an object
   */
  return {
    catalog_fetchProductActivities,
    catalog_fetchProductCategories,
    catalog_fetchProductColors,
    catalog_fetchProductSizes,
    catalog_fetchProducts,
    catalog_fetchProductDetail,
    catalog_fetchProductDetailVariant,
    catalog_onClickApplyFilters,
    catalog_onClickResetFilters,
    catalog_isLoading: dashboard_isLoading,
    catalog_isOpenDialogAddToCart,
    catalog_listOfProductActivities: dashboard_listOfProductActivities,
    catalog_listOfProductCategories: dashboard_listOfProductCategories,
    catalog_listOfProductColors: dashboard_listOfProductColors,
    catalog_listOfProductSizes: dashboard_listOfProductSizes,
    catalog_listOfProducts: dashboard_listOfProducts,
    dashboard_listOfProductIsLoading,
    catalog_listOfSorting,
    catalog_productDetail: dashboard_productDetail,
    catalog_detailIsLoading: dashboard_detailIsLoading,
    catalog_productDetailVariants: dashboard_productDetailVariants,
    catalog_selectedTabProductVariant,
    catalog_onLoadMoreProducts,
    catalog_tempQueryParamsOfProducts,
    catalog_queryParamsOfProducts,
    catalog_queryParamsOfProductColorsAndSizes,
    catalog_searchQueryOfProduct: dashboard_searchQueryOfProduct,
    catalog_selectedTabProductInformation,
    catalog_selectedColorOnAddToCart,
    catalog_selectedFilter,
    catalog_selectedProduct,
    catalog_selectedTabAddToCart,
    catalog_searchCategory,
  };
};
