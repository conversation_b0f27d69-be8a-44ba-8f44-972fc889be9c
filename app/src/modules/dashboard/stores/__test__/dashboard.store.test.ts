import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActivePinia, createP<PERSON> } from 'pinia';
import { useDashboardStore } from '../dashboard.store';

describe('Dashboard store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useDashboardStore();

      expect(store.dashboard_isLoading).toBe(false);
      expect(store.dashboard_listOfProductActivities).toMatchObject([]);
      expect(store.dashboard_listOfProductCategories).toMatchObject([]);
      expect(store.dashboard_listOfProductColors).toMatchObject([]);
      expect(store.dashboard_listOfProductSizes).toMatchObject([]);
    });

    it('has correct initial computed state', () => {
      const store = useDashboardStore();
      expect(store.dashboard_isLoading).toBe(false);
    });
  });

  it('Default limit credit should be null', () => {
    const store = useDashboardStore();
    expect(store.dashboard_limitCreditData).toBeNull();
  });
});
