// Constants
import {
  DAS<PERSON><PERSON>ARD_ENDPOINT_GET_CREDIT_LIMIT,
  DASH<PERSON>ARD_ENDPOINT_GET_ONGOING_ORDERS,
  DASHBOARD_ENDPOINT_GET_PRODUCT_ACTIVITIES,
  DASHBOARD_ENDPOINT_GET_PRODUCT_CATEGORIES,
  DASH<PERSON>ARD_ENDPOINT_GET_PRODUCT_COLORS,
  DASHBOARD_ENDPOINT_GET_PRODUCT_DETAIL,
  DASHBOARD_ENDPOINT_GET_PRODUCT_SIZES,
  DASHBOARD_ENDPOINT_GET_SUMMARY_PRODUCT,
  DASHBOARD_ENDPOINT_GET_SUMMARY_TRANSACTION,
  DASHBOARD_ENDPOINT_GET_SUMMARY_TOTAL_TRANSACTION,
  DASHBOARD_ENDPOINT_PRODUCTS,
} from '../constants/dashboard.api.constant';

// Helpers
import { setTimestampOfFetchAPI } from '~/app/src/core/helpers/set-timestamp-fetch.helper';

// Interfaces
import type { IDashboardQueryParamsNewProducts, IDashboardStoreStates } from '../interfaces/dashboard.interface';

// Pinia
import { defineStore } from 'pinia';
import { useCachedData } from '~/app/src/core/composables/useCachedData';
import { DASHBOARD_STATE_LIST_OF_PRODUCTS, DASHBOARD_STATE_PRODUCT_DETAIL } from '../constants/dashboard.constant';

export const useDashboardStore = defineStore('dashboard', {
  state: (): IDashboardStoreStates => ({
    dashboard_isLoading: false,
    dashboard_listOfProductActivities: [],
    dashboard_listOfProductCategories: [],
    dashboard_listOfProductColors: [],
    dashboard_listOfProductSizes: [],
    dashboard_listOfProducts: DASHBOARD_STATE_LIST_OF_PRODUCTS,
    dashboard_listOfProductIsLoading: false,
    dashboard_productDetail: DASHBOARD_STATE_PRODUCT_DETAIL,
    dashboard_detailIsLoading: false,
    dashboard_productDetailVariants: {},
    dashboard_listOfNewProductsLoading: false,
    dashboard_listOfNewProducts: DASHBOARD_STATE_LIST_OF_PRODUCTS,
    dashboard_limitCreditData: null,
    dashboard_limitCreditIsLoading: false,
    dashboard_summaryTotalProductData: null,
    dashboard_summaryTotalProductIsLoading: false,
    dashboard_summaryTransactionData: null,
    dashboard_summaryTotalTransactionData: null,
    dashboard_summaryTotalTransactionIsLoading: false,
    dashboard_ongoingOrdersData: [],
    dashboard_ongoingOrdersIsLoading: false,
    dashboard_searchQueryOfProduct: '',
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get activities of product.
     * @url /product-filter-list/activity
     * @method GET
     * @access private
     */
    async fetchDashboard_getProductActivities(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_GET_PRODUCT_ACTIVITIES, {
          method: 'GET',

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.dashboard_listOfProductActivities = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get categories of product.
     * @url /product-filter-list/sub-category
     * @method GET
     * @access private
     */
    async fetchDashboard_getProductCategories(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_GET_PRODUCT_CATEGORIES, {
          method: 'GET',

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.dashboard_listOfProductCategories = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get colors of product.
     * @url /product-filter-list/color
     * @method GET
     * @access private
     */
    async fetchDashboard_getProductColors(params: IQueryParamsOfProductColorsAndSizes): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_GET_PRODUCT_COLORS, {
          method: 'GET',
          params,

          onResponse: async ({ response }) => {
            this.dashboard_listOfProductColors = response._data.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get product detail.
     * @url /products/detail/:sku
     * @method GET
     * @access private
     */
    async fetchDashboard_getProductDetail(sku: string): Promise<unknown> {
      try {
        this.dashboard_detailIsLoading = true;
        const { data, error } = await useApiFetch(`${DASHBOARD_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}`, {
          method: 'GET',

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.dashboard_productDetail = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_detailIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get product detail variant.
     * @url /products/detail/:sku/:variant
     * @method GET
     * @access private
     */
    async fetchDashboard_getProductDetailVariant(sku: string, variant: string): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${DASHBOARD_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}/${variant}`, {
          method: 'GET',

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.dashboard_productDetailVariants[variant] = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        this.dashboard_productDetailVariants[variant] = [];
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get sizes of product.
     * @url /product-filter-list/size
     * @method GET
     * @access private
     */
    async fetchDashboard_getProductSizes(params: IQueryParamsOfProductColorsAndSizes): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_GET_PRODUCT_SIZES, {
          method: 'GET',
          params,

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.dashboard_listOfProductSizes = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get products.
     * @url /products
     * @method GET
     * @access private
     */
    async fetchDashboard_getProducts(
      params: IQueryParamsOfProducts,
      isLoadMore: boolean = false,
    ): Promise<unknown> {
      try {
        this.dashboard_listOfProductIsLoading = true;
        this.dashboard_searchQueryOfProduct = params.search ?? '';
        const formattedParams = {
          ...params,
          size: Array.isArray(params.size) ? params.size.join(',') : params.size,
          color: Array.isArray(params.color) ? params.color.join(',') : params.color,
        };

        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_PRODUCTS, {
          method: 'GET',
          params: formattedParams,

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            const result: IProductListWithPagination = response._data.data;

            if (isLoadMore) {
              this.dashboard_listOfProducts.data = [...this.dashboard_listOfProducts.data, ...result.data];
            } else {
              this.dashboard_listOfProducts = result;
            }
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data);
      } catch (error) {
        this.dashboard_listOfProducts = {
          data: [],
          lastPage: false,
          showPage: 0,
          total: 0,
        };
        this.dashboard_listOfProductIsLoading = false;
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_listOfProductIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get dashboard new products.
     * @url /products
     * @method GET
     * @access private
     */
    async fetchDashboard_getNewProducts(params: IDashboardQueryParamsNewProducts): Promise<unknown> {
      try {
        this.dashboard_listOfNewProductsLoading = true;

        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_PRODUCTS, {
          method: 'GET',
          params,

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            const result: IProductListWithPagination = response._data.data;
            this.dashboard_listOfNewProducts = result;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_listOfNewProductsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get dashboard limit credit.
     * @url /limit-credit
     * @method GET
     * @access private
     */
    async fetchDashboard_getLimitCredit(): Promise<unknown> {
      try {
        this.dashboard_limitCreditIsLoading = true;

        const { data, error } = await useApiFetch(DASHBOARD_ENDPOINT_GET_CREDIT_LIMIT, {
          method: 'GET',

          onResponse: async ({ response }) => {
            const result: ILimitCredit = response._data.data;
            this.dashboard_limitCreditData = result;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_limitCreditIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get dashboard summary total product.
     * @url /homepage/jumlah_product/:customerId
     * @method GET
     * @access private
     */
    async fetchDashboard_getSummaryTotalProduct(customerId: string): Promise<unknown> {
      try {
        this.dashboard_summaryTotalProductIsLoading = true;
        const { data, error } = await useApiFetch(`${DASHBOARD_ENDPOINT_GET_SUMMARY_PRODUCT}/${customerId}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const result: IDashboardSummaryTotalProduct = response._data;
            this.dashboard_summaryTotalProductData = result;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_summaryTotalProductIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get dashboard summary total transaction.
     * @url /homepage/jumlah_transaksi/:customerId
     * @method GET
     * @access private
     */
    async fetchDashboard_getSummaryTotalTransaction(customerId: string): Promise<unknown> {
      try {
        this.dashboard_summaryTotalTransactionIsLoading = true;
        const { data, error } = await useApiFetch(`${DASHBOARD_ENDPOINT_GET_SUMMARY_TRANSACTION}/${customerId}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const result: IDashboardSummaryTotalTransaction = response._data;
            this.dashboard_summaryTotalTransactionData = result;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_summaryTotalTransactionIsLoading = false;
      }
    },
    /**
     * @description Handle fetch api get dashboard summary total transaction.
     * @url /homepage/jumlah_transaksi/:customerId
     * @method GET
     * @access private
     */
    async fetchDashboard_getSummaryTransaction(customerId: string): Promise<unknown> {
      try {
        this.dashboard_summaryTotalTransactionIsLoading = true;
        const { data, error } = await useApiFetch(
          `${DASHBOARD_ENDPOINT_GET_SUMMARY_TOTAL_TRANSACTION}/${customerId}`,
          {
            method: 'GET',
            onResponse: async ({ response }) => {
              const result: IDashboardSummaryTransaction = response._data;
              this.dashboard_summaryTransactionData = result;
            },

            /**
             * @description We also can transform the data before we return it.
             */
            transform: function (input) {
              return setTimestampOfFetchAPI(input);
            },
          },
        );

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_summaryTotalTransactionIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get dashboard ongoing orders.
     * @url /homepage/pesanan_berjalan/:customerId
     * @method GET
     * @access private
     */
    async fetchDashboard_getOngoingOrders(customerId: string): Promise<unknown> {
      try {
        this.dashboard_ongoingOrdersIsLoading = true;
        const { data, error } = await useApiFetch(`${DASHBOARD_ENDPOINT_GET_ONGOING_ORDERS}/${customerId}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const result: IDashboardOngoingOrder[] = response._data?.pesanan_berjalan ?? [];
            this.dashboard_ongoingOrdersData = result;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.dashboard_ongoingOrdersIsLoading = false;
      }
    },
  },
});
