import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useProductStore } from '../product.store';

describe('Product store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useProductStore();

      expect(store.product_isLoading).toBe(false);
      expect(store.product_listOfProductActivities).toMatchObject([]);
      expect(store.product_listOfProductCategories).toMatchObject([]);
      expect(store.product_listOfProductColors).toMatchObject([]);
      expect(store.product_listOfProductSizes).toMatchObject([]);
      expect(store.product_productDetailIsLoading).toBe(false);
      expect(store.product_productDetail).toBeNull();
    });

    it('has correct initial computed state', () => {
      const store = useProductStore();
      expect(store.product_isLoading).toBe(false);
    });
  });

  it('product detail should be null', () => {
    const store = useProductStore();
    expect(store.product_productDetail).toBeNull();
  });

  // describe('Fetch product categories', () => {
  //   beforeEach(() => {
  //     vi.useFakeTimers();
  //   });

  //   it('successfully fetch product categories', async () => {
  //     const store = useProductStore();
  //     await store.fetchProduct_getProductCategories();

  //     // Fast-forward time by 2000ms
  //     vi.advanceTimersByTime(2000);
  //     // Wait for any promises to resolve
  //     await vi.runAllTimersAsync();
  //     expect(store.product_listOfProductCategories?.length).greaterThan(0);
  //   });
  // });
});
