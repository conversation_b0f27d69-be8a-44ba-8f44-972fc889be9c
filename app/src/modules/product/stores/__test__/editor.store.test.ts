import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useEditorStore } from '../editor.store';
import { PRODUCT_CUSTOM_EDIT_MODE } from '../../constants/product.constant';

describe('Editor product custom store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useEditorStore();

      expect(store.editor_mode).toBe(PRODUCT_CUSTOM_EDIT_MODE.EDIT);
      expect(store.editor_backgroundLayers).toMatchObject([]);
      expect(store.layers).toMatchObject([]);
      expect(store.mode).toBe('default');
    });

    it('has correct initial computed state', () => {
      const store = useEditorStore();
      expect(store.editor_isLoading).toBe(false);
    });
  });

  it('Editor layers should be empty', () => {
    const store = useEditorStore();
    expect(store.layers).toMatchObject([]);
  });
});
