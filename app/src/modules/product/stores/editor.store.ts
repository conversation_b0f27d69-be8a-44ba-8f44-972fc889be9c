// Constants

// Helpers

// Interfaces

// Pinia
import { defineStore } from 'pinia';
import type { IEditorStoreState } from '../interfaces/editor.interface';
import { PRODUCT_CUSTOM_EDIT_MODE } from '../constants/product.constant';

export const useEditorStore = defineStore('editor', {
  state: (): IEditorStoreState => ({
    editor_mode: PRODUCT_CUSTOM_EDIT_MODE.EDIT,
    editor_backgroundLayers: [],
    layers: [],
    mode: 'default',
    editor_selectedLayer: {
      backgroundId: '',
      layerId: '',
      textLabel: null,
      layerType: '',
      text: null,
      fill: null,
      fontSize: null,
      fontWeight: 'normal',
      fontStyle: 'normal',
      underline: false,
      fontFamily: null,
      angle: 0,
      flipX: false,
      flipY: false,
      scaleAndZoom: 0,
    },
    editor_currentBackgroundImageId: '',
    selectedModel: null,
    controlTab: '',
    editor_isLoading: false,
    editor_width: 0,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {},
  persist: {
    storage: localStorage,
    pick: ['editor'],
  },
});
