// Constants
import {
  PRODUCT_ENDPOINT_GET_PRODUCT_CATEGORIES,
  PRODUCT_ENDPOINT_GET_PRODUCT_COLORS,
  PRODUCT_ENDPOINT_GET_PRODUCT_DETAIL,
  PRODUCT_ENDPOINT_GET_PRODUCT_SIZES,
  PRODUCT_ENDPOINT_GET_SEARCH_RECOMMENDATION,
  PRODUCT_ENDPOINT_LIST_PRODUCT_HOMEPAGE,
  PRODUCT_ENDPOINT_PRODUCTS,
  PRODUCT_ENDPOINT_UPLOAD_CUSTOM_ATTACHMENT,
} from '../constants/product.api.constant';

// axios
import axios from 'axios';

// Helpers
import { setTimestampOfFetchAPI } from '~/app/src/core/helpers/set-timestamp-fetch.helper';

// Interfaces
import type {
  IProductStoreState,
  IResponseUploadCustomAttachment,
  IReturnTypeUploadCustomAttachments,
} from '../interfaces/product.interface';

// Pinia
import { defineStore } from 'pinia';
import { useCachedData } from '~/app/src/core/composables/useCachedData';
import { PRODUCT_STATE_LIST_OF_PRODUCTS_RECOMMENDATION } from '../constants/product.constant';

export const useProductStore = defineStore('product', {
  state: (): IProductStoreState => ({
    product_isLoading: false,
    product_listOfProductActivities: [],
    product_listOfProductCategories: [],
    product_listOfProductColors: [],
    product_listOfProductSizes: [],
    product_productDetailIsLoading: false,
    product_productDetail: null,
    product_variants: [],
    product_cartValues: [],
    product_variantsIsLoading: false,
    product_listOfProductsRecommendationLoading: false,
    product_listOfProductsRecommendation: PRODUCT_STATE_LIST_OF_PRODUCTS_RECOMMENDATION,
    product_searchData: [],
    product_searchRecommendationData: [],
    product_searchRecommendationIsLoading: false,
    product_searchHistory: [],
    product_searchIsLoading: false,
    product_homepageCustomData: [],
    product_homepageCustomIsLoading: false,
    product_homepagePopularData: [],
    product_homepagePopularIsLoading: false,
    product_homepageData: [],
    product_homepageIsLoading: false,
    product_detailCustomData: null,
    product_detailCustomIsLoading: false,
    product_customVariants: [],
    product_customCartValues: [],
    product_customVariantsIsLoading: false,
    product_checkedRequestOther: false,
    product_isLoadingProductStock: false,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get categories of product.
     * @url /product-filter-list/sub-category
     * @method GET
     * @access private
     */
    async fetchProduct_getProductCategories(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_GET_PRODUCT_CATEGORIES, {
          method: 'GET',

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.product_listOfProductCategories = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get colors of product.
     * @url /product-filter-list/color
     * @method GET
     * @access private
     */
    async fetchProduct_getProductColors(params: IQueryParamsOfProductColorsAndSizes): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_GET_PRODUCT_COLORS, {
          method: 'GET',
          params,

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.product_listOfProductColors = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get product detail.
     * @url /products/detail/:sku
     * @method GET
     * @access private
     */
    async fetchProduct_getProductDetail(sku: string): Promise<unknown> {
      try {
        this.product_productDetailIsLoading = true;
        this.product_isLoadingProductStock = true;
        this.product_productDetail = null;
        const { data, error } = await useApiFetch(`${PRODUCT_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const result: IProduct = response._data.data;
            this.product_productDetail = result;
            if (result?.variant?.length > 0) {
              const uniqueVariants = result?.variant?.filter(
                (item, index, self) =>
                  index === self.findIndex(t => t.product_variant_c === item.product_variant_c),
              );
              // console.log('uniqueVariants', uniqueVariants);
              if (uniqueVariants?.length > 0) {
                // Using Promise.all to wait for all requests to complete
                return await Promise.all(
                  uniqueVariants.map(i => {
                    this.fetchProduct_getVariants(result.sku_code_c, i.product_variant_c);
                  }),
                );
              }
            }
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        this.product_productDetail = null;
        return Promise.reject(new Error(error as string));
      } finally {
        this.product_productDetailIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get product detail variant.
     * @url /products/detail/:sku/:variant
     * @method GET
     * @access private
     */
    async fetchProduct_getVariants(sku: string, variant: string): Promise<IBaseApiResponse<IProductVariant[]>> {
      try {
        this.product_variants = [];
        this.product_cartValues = [];
        const { data, error } = await useApiFetch<IBaseApiResponse<IProductVariant[]>>(
          `${PRODUCT_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}/${variant}`,
          {
            method: 'GET',
            onResponse: async ({ response }) => {
              const result: IProductDetailVariant[] = response._data.data?.map((i: IProductVariant) => ({
                ...i,
                color: variant,
              }));
              let allVariants = [];

              allVariants = Array.from([...this.product_variants, ...result]);

              // Using filter with a Set to track unique SKUs
              const uniQueVariants = new Set();
              const filteredArray = allVariants.filter(item => {
                if (!uniQueVariants.has(item.article_id)) {
                  uniQueVariants.add(item.article_id);
                  return true;
                }
                return false;
              });

              // set product variants

              this.product_variants = filteredArray;

              const _cartValues = filteredArray.map(item => ({
                article: item.article_id,
                qty: item.cart.count,
                color: item.color,
                stock: item.stock,
                product_size_c: item.product_size_c,
                price: item.price.amount,
              }));

              // set cart values
              this.product_cartValues = _cartValues;
            },
          },
        );
        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data.value as IBaseApiResponse<IProductVariant[]>);
      } catch (error) {
        this.product_variants = [];
        this.product_cartValues = [];
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get product detail variant.
     * @url /products/detail/:sku/:variant
     * @method GET
     * @access private
     */
    async fetchProduct_getCustomProductVariants(
      sku: string,
      variant: string,
    ): Promise<IBaseApiResponse<IProductVariant[]>> {
      try {
        const { data, error } = await useApiFetch<IBaseApiResponse<IProductVariant[]>>(
          `${PRODUCT_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}/${variant}`,
          {
            method: 'GET',
            onResponse: async ({ response }) => {
              const result: IProductDetailVariant[] = response._data.data?.map((i: IProductVariant) => ({
                ...i,
                color: variant,
              }));
              let allVariants = [];
              allVariants = Array.from([...this.product_customVariants, ...result]);
              const uniQueVariants = new Set();
              const filteredArray = allVariants.filter(item => {
                if (!uniQueVariants.has(item.article_id)) {
                  uniQueVariants.add(item.article_id);
                  return true;
                }
                return false;
              });
              this.product_customVariants = filteredArray;
              const _cartValues = filteredArray.map(item => ({
                article: item.article_id,
                qty: item.cart.count,
                color: item.color,
                stock: item.stock,
                product_size_c: item.product_size_c,
                price: item.price.amount,
              }));

              // set cart values
              this.product_customCartValues = _cartValues;
            },
          },
        );
        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data.value as IBaseApiResponse<IProductVariant[]>);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get product detail custom
     * @url /products/detail/:sku
     * @method GET
     * @access private
     */
    async fetchProduct_getProductDetailCustom(sku: string): Promise<IProduct> {
      try {
        this.product_checkedRequestOther = false;
        this.product_detailCustomIsLoading = true;
        this.product_detailCustomData = null;
        const { data, error } = await useApiFetch<IBaseApiResponse<IProduct>>(
          `${PRODUCT_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}`,
          {
            method: 'GET',
            onResponse: async ({ response }) => {
              const result: IProduct = response._data.data;
              this.product_detailCustomData = result;
              if (result?.variant?.length > 0) {
                const uniqueVariants = result?.variant?.filter(
                  (item, index, self) =>
                    index === self.findIndex(t => t.product_variant_c === item.product_variant_c),
                );
                if (uniqueVariants?.length > 0) {
                  return await Promise.all(
                    uniqueVariants.map(i => {
                      this.fetchProduct_getCustomProductVariants(result.sku_code_c, i.product_variant_c);
                    }),
                  );
                }
              }
            },
          },
        );

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data.value?.data as IProduct);
      } catch (error) {
        this.product_detailCustomData = null;
        return Promise.reject(new Error(error as string));
      } finally {
        this.product_detailCustomIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get product detail variant.
     * @url /products/detail/:sku/:variant
     * @method GET
     * @access private
     */
    async fetchProduct_getProductDetailVariant(sku: string, variant: string): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${PRODUCT_ENDPOINT_GET_PRODUCT_DETAIL}/${sku}/${variant}`, {
          method: 'GET',

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async () => {
            // this.product_variants[variant] = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get sizes of product.
     * @url /product-filter-list/size
     * @method GET
     * @access private
     */
    async fetchProduct_getProductSizes(params: IQueryParamsOfProductColorsAndSizes): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_GET_PRODUCT_SIZES, {
          method: 'GET',
          params,

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            this.product_listOfProductSizes = response._data.data;
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get products recommendation.
     * @url /products
     * @method GET
     * @access private
     */
    async fetchProduct_getProductsRecommendation(
      params: IQueryParamsOfProducts,
      isLoadMore: boolean = false,
    ): Promise<unknown> {
      try {
        this.product_listOfProductsRecommendationLoading = true;

        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_PRODUCTS, {
          method: 'GET',
          params,

          /**
           * @description We also can use cached data for avoiding unnecessary requests
           */
          getCachedData: function (key: string) {
            return useCachedData(key);
          },

          onResponse: async ({ response }) => {
            const result: IProductListWithPagination = response._data.data;

            if (isLoadMore) {
              this.product_listOfProductsRecommendation.data = [
                ...this.product_listOfProductsRecommendation.data,
                ...result.data,
              ];

              // ? Don't forget to remove the duplicate items
              // * We don't use Set method because the object is not primitive type. So, we need to use the JSON.stringify method to compare the object.
              const uniqueItems: IProductList[] = [];

              this.product_listOfProductsRecommendation.data.forEach((item: IProductList) => {
                // Check if this object is already in uniqueObjects
                if (!uniqueItems.some(uniqueItem => JSON.stringify(uniqueItem) === JSON.stringify(item))) {
                  uniqueItems.push(item);
                }
              });

              this.product_listOfProductsRecommendation.data = uniqueItems;
            } else {
              this.product_listOfProductsRecommendation = result;
            }
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.product_listOfProductsRecommendationLoading = false;
      }
    },

    /**
     * @description Handle fetch api get products (search).
     * @url /products
     * @method GET
     * @access private
     */
    async fetchProduct_searchProducts(searchQuery: string): Promise<unknown> {
      try {
        this.product_searchIsLoading = true;
        this.product_searchData = [];
        const params: IQueryParamsOfProducts = {
          activity: null,
          color: null,
          limit: 6,
          maxPrice: null,
          minPrice: null,
          order_by: 'newest',
          page: 1,
          reff: '',
          search: searchQuery,
          size: null,
          subcategory: null,
        };

        // this.product_saveSearchKeyword(searchQuery);

        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_PRODUCTS, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            const result: IProductList[] = response._data?.data?.data ?? [];
            if (result?.length > 0) {
              this.product_searchData = result;
            } else {
              this.product_searchData = [];
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.product_searchIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get search product recommendation.
     * @url /products/recommended
     * @method GET
     * @access private
     */
    async fetchProduct_getSearchProductRecommendation(searchQuery: string): Promise<unknown> {
      try {
        this.product_searchRecommendationIsLoading = true;
        this.product_searchRecommendationData = [];
        const params: Partial<IQueryParamsOfProducts> = {
          search: searchQuery,
        };
        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_GET_SEARCH_RECOMMENDATION, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            const result: IProductSearchRecommendationItem[] = response._data?.data ?? [];
            if (result?.length > 0) {
              this.product_searchRecommendationData = result;
            } else {
              this.product_searchRecommendationData = [];
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.product_searchRecommendationIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get products for homepage.
     */
    async fetchProduct_getProductHomepageCustom(isPopular: boolean): Promise<unknown> {
      try {
        if (isPopular) {
          this.product_homepagePopularIsLoading = true;
        } else {
          this.product_homepageCustomIsLoading = true;
        }
        this.product_homepageCustomData = [];
        const { data, error } = await useApiFetch(PRODUCT_ENDPOINT_LIST_PRODUCT_HOMEPAGE, {
          method: 'GET',
          params: {
            isPopular,
          },
          onResponse: async ({ response }) => {
            const result: IProductHomepage[] = Object.values(response._data?.data) ?? [];
            if (isPopular) {
              this.product_homepagePopularData = result;
            } else {
              this.product_homepageCustomData = result;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.product_homepageCustomIsLoading = false;
        this.product_homepagePopularIsLoading = false;
      }
    },

    /**
     * @description Handle upload s3
     * @method PUT
     * @access public
     */
    async fetchProduct_uploadAttachmentToS3(file: File, s3Url: string): Promise<unknown> {
      console.log('fetchProduct_uploadAttachmentToS3 file->', file);
      console.log('fetchProduct_uploadAttachmentToS3 s3Url->', s3Url);
      try {
        const response = await axios.put(s3Url, file, {
          headers: {
            'Content-Type': 'application/octet-stream',
          },
        });
        return response;
      } catch (error) {
        // Handle any errors that occur during the fetch
        console.error('Error upload file:', error);
        throw error;
      }
    },

    /**
     * @description Handle upload attachment product custom.
     * @url '/api/product/custom/upload'
     * @method POST
     * @access public
     */
    async fetchProduct_uploadAttachments(
      body: IReturnTypeUploadCustomAttachments,
      files: File[],
    ): Promise<IResponseUploadCustomAttachment[] | string> {
      try {
        const { data, error } = await useApiFetch<IBaseApiResponse<IResponseUploadCustomAttachment>>(
          PRODUCT_ENDPOINT_UPLOAD_CUSTOM_ATTACHMENT,
          {
            method: 'POST',
            body,
            onResponse: async ({ response }) => {
              const result: IResponseUploadCustomAttachment[] = response._data?.data ?? [];
              // console.log('fetchProduct_uploadAttachment result->', result);
              if (result?.length > 0) {
                const requestPromises = result.map((res, index) => {
                  this.fetchProduct_uploadAttachmentToS3(files[index], res.s3_url);
                });
                await Promise.all(requestPromises)
                  .then(function (values) {
                    console.log(requestPromises, values);
                  })
                  .catch(e => {
                    console.log('requestPromises e->', e);
                  })

                  .finally(() => {
                    console.log('FILALLY');
                  });
              }
            },
          },
        );
        if (error.value) {
          console.log('STOREEE ', error.value.data);
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          return Promise.resolve(error.value.data?.message);
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Promise.resolve(data.value?.data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle search history
     */
    product_saveSearchKeyword(val: string) {
      const mergedData = Array.from([...this.product_searchHistory, val]);
      const uniqueArray = [...new Set(mergedData)]?.filter(item => item)?.slice(0, 10) ?? [];
      this.product_searchHistory = uniqueArray;
    },

    product_removeSearchKeyword(val: string) {
      const filteredData = Array.from(this.product_searchHistory.filter(i => i !== val));
      this.product_searchHistory = filteredData;
    },
    product_clearSearchHistory() {
      this.product_searchHistory = [];
    },
  },
  persist: {
    storage: localStorage,
    pick: ['product_searchHistory'],
  },
});
