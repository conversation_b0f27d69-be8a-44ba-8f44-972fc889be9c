import CatalogFitGuide from '../../dashboard/components/catalog/CatalogFitGuide.vue';
import CatalogMeasurement from '../../dashboard/components/catalog/CatalogMeasurement.vue';
import CatalogMeasurementGuide from '../../dashboard/components/catalog/CatalogMeasurementGuide.vue';

export const FALLBACK_PRODUCT_IMAGE =
  'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';

export const PRODUCT_CUSTOM_TYPE = {
  IMAGE: 'Gambar',
  TEXT: 'Teks',
};

export const PRODUCT_LIST_OF_ACTIVITIES = [
  {
    label: 'College',
    value: 'College',
  },
  {
    label: 'LIfestyle',
    value: 'LIfestyle',
  },
  {
    label: 'Mountaineering',
    value: 'Mountaineering',
  },
  {
    label: 'Riding',
    value: 'Riding',
  },
  {
    label: 'Tactical',
    value: 'Tactical',
  },
  {
    label: 'Traveling',
    value: 'Traveling',
  },
];

export const PRODUCT_CUSTOM_TEXT_COLORS = [
  {
    label: 'Putih',
    value: '#FFFFFF',
  },
  {
    label: 'Hitam',
    value: '#25262A',
  },
  {
    label: 'Kuning',
    value: '#FFA500',
  },
  {
    label: 'Merah',
    value: '#D30F0F',
  },
  {
    label: 'Biru',
    value: '#0000FF',
  },
  {
    label: 'Hijau',
    value: '#008000',
  },
];

export const PRODUCT_LIST_OF_COLORS = [
  {
    label: 'Grey',
    value: 'Grey',
  },
  {
    label: 'Black',
    value: 'Black',
  },
  {
    label: 'Green',
    value: 'Green',
  },
  {
    label: 'Blue',
    value: 'Blue',
  },
];

export const PRODUCT_LIST_OF_COLUMNS_PRODUCT_VARIANT = [
  {
    key: 'product_size_c',
    label: 'Size',
  },
  {
    key: 'stock',
    label: 'Stock',
  },
  {
    key: 'total_order',
    label: 'Jumlah Order',
  },
  {
    key: 'price',
    label: 'Harga',
  },
];

export const PRODUCT_LIST_OF_COLUMNS_MEASUREMENT_BOTTOM = [
  {
    key: 'gender',
    label: "Men's",
  },
  {
    key: 'sizeS',
    label: 'S',
  },
  {
    key: 'sizeM',
    label: 'M',
  },
  {
    key: 'sizeL',
    label: 'L',
  },
  {
    key: 'sizeXL',
    label: 'XL',
  },
  {
    key: 'size2XL',
    label: '2XL',
  },
  {
    key: 'size3XL',
    label: '3XL',
  },
  {
    key: 'size4XL',
    label: '4XL',
  },
];

export const PRODUCT_LIST_OF_COLUMNS_MEASUREMENT_TOP = [
  {
    key: 'gender',
    label: "Men's",
  },
  {
    key: 'sizeS',
    label: 'S',
  },
  {
    key: 'sizeM',
    label: 'M',
  },
  {
    key: 'sizeL',
    label: 'L',
  },
  {
    key: 'sizeXL',
    label: 'XL',
  },
  {
    key: 'size2XL',
    label: '2XL',
  },
  {
    key: 'size3XL',
    label: '3XL',
  },
  {
    key: 'size4XL',
    label: '4XL',
  },
];

export const PRODUCT_LIST_OF_DUMMY_MEASUREMENT_BOTTOM = [
  {
    gender: 'Ukuran Pinggang',
    sizeS: '155-170',
    sizeM: '160-175',
    sizeL: '165-180',
    sizeXL: '170-185',
    size2XL: '175-190',
    size3XL: '180-195',
    size4XL: '185-200',
  },
];

export const PRODUCT_LIST_OF_DUMMY_MEASUREMENT_TOP = [
  {
    gender: 'Tinggi Badan',
    sizeS: '155-170',
    sizeM: '160-175',
    sizeL: '165-180',
    sizeXL: '170-185',
    size2XL: '175-190',
    size3XL: '180-195',
    size4XL: '185-200',
  },
  {
    gender: 'Panjang Badan',
    sizeS: '155-170',
    sizeM: '160-175',
    sizeL: '165-180',
    sizeXL: '170-185',
    size2XL: '175-190',
    size3XL: '180-195',
    size4XL: '185-200',
  },
  {
    gender: 'Dada',
    sizeS: '155-170',
    sizeM: '160-175',
    sizeL: '165-180',
    sizeXL: '170-185',
    size2XL: '175-190',
    size3XL: '180-195',
    size4XL: '185-200',
  },
  {
    gender: 'Pinggang',
    sizeS: '155-170',
    sizeM: '160-175',
    sizeL: '165-180',
    sizeXL: '170-185',
    size2XL: '175-190',
    size3XL: '180-195',
    size4XL: '185-200',
  },
];

export const PRODUCT_CATALOG_LIST_OF_DUMMY_PRODUCTS = [
  {
    size: 'L',
    stock: 100,
    quantity: 100,
    price: 'Rp. 3.500.000',
  },
  {
    size: 'XL',
    stock: 100,
    quantity: 100,
    price: 'Rp. 3.500.000',
  },
];

export const PRODUCT_CATALOG_LIST_OF_GENDERS = [
  {
    label: 'Unisex',
    value: 'Unisex',
  },
  {
    label: 'Pria',
    value: 'Pria',
  },
  {
    label: 'Wanita',
    value: 'Wanita',
  },
  {
    label: 'Anak',
    value: 'Anak',
  },
];

export const PRODUCT_LIST_OF_GUIDE_SIZES = [
  {
    component: CatalogMeasurement,
    label: 'Measurement',
    value: 'Measurement',
  },
  {
    component: CatalogMeasurementGuide,
    label: 'Measurement Guide',
    value: 'Measurement Guide',
  },
  {
    component: CatalogFitGuide,
    label: 'Fit Guide',
    value: 'Fit Guide',
  },
];

export const PRODUCT_LIST_OF_SIZES = [
  {
    label: '1L',
    value: '1L',
  },
  {
    label: '10L',
    value: '10L',
  },
  {
    label: '15L',
    value: '15L',
  },
  {
    label: '20L',
    value: '20L',
  },
];

export const PRODUCT_LIST_OF_SPECIFICATIONS = [
  {
    key: 'dimension',
    label: 'Dimensi',
  },
  {
    key: 'sku_code_c',
    label: 'SKU',
  },
  {
    key: 'activity',
    label: 'Aktivitas',
  },
  {
    key: 'weight',
    label: 'Berat Satuan',
  },
];

export const PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION = ['Deskripsi', 'Spesifikasi'];

export const PRODUCT_STATE_PRODUCT_DETAIL: IProduct = {
  article: '************',
  article_description: 'R.EXELCIOR-65L, RED, NOS',
  sku_code_c: '910000013',
  product_name_c: 'R.EXELCIOR-65L',
  product_variant_c: 'RED',
  product_size_c: 'NOS',
  product_description: '',
  product_style: '',
  product_feature: '',
  product_tag: ['BACKPACK'],
  product_type: 'Rucksack',
  product_gender: 'Male',
  product_material: '',
  product_status: null,
  product_category: 'BACKPACK',
  wholesales_published_date: '2023-09-21',
  b2b_published_date: '2023-09-21',
  main_image: 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
  specification: {
    dimension: '',
    activity: '',
    weight: '0.00',
    uomweight: 'G',
    material: '',
    is_custom_logo: false,
  },
  media: [],
  variantmedia: [],
  product_price: {
    sku_code_c: '910000013',
    amount: '1300000.00',
    valid_from: '2016-12-01',
    valid_to: '9999-12-31',
    created_date: '2023-12-19T07:55:54.000000Z',
    created_by: 'SYSTEM',
    modified_date: '2023-12-19T07:55:54.000000Z',
    modified_by: 'SYSTEM',
  },
  variant: [
    {
      product_variant_c: 'RED',
      cross_site: '910000013.RED',
    },
  ],
  variantcustomer: [],
  custom_placement: [],
};

export const PRODUCT_STATE_PRODUCT_DETAIL_VARIANT: IProductDetailVariant[] = [
  {
    article_id: '************',
    article_description: 'R.EXELCIOR-65L, RED, NOS',
    product_size_c: 'NOS',
    product_name: 'R.EXELCIOR-65L',
    product_images: [],
    article_category: null,
    weight: '0.00',
    dimension: '',
    stock: 0,
    stock_toko: {
      total_sum: 0,
      store_list: [],
    },
    price: {
      id: '0933e11f-9e44-11ee-8524-06c39b9216e2',
      sku_code_c: '910000013',
      amount: '1300000.00',
      currency: 'IDR',
      valid_from: '2016-12-01',
      valid_to: '9999-12-31',
      created_date: '2023-12-19T07:55:54.000000Z',
      created_by: 'SYSTEM',
      modified_date: '2023-12-19T07:55:54.000000Z',
      modified_by: 'SYSTEM',
    },
    created_date: '2023-09-21T11:54:40.000000Z',
    created_by: 'SYSTEM',
    modified_date: '2023-09-21T11:54:40.000000Z',
    modified_by: 'SYSTEM',
    min_qty: 0,
    cart: {
      count: 0,
    },
  },
];

export const PRODUCT_STATE_LIST_OF_PRODUCTS: IProductListWithPagination = {
  data: [],
  lastPage: false,
  showPage: 0,
  total: 0,
};

export const PRODUCT_STATE_LIST_OF_PRODUCTS_RECOMMENDATION: IProductListWithPagination = {
  data: [],
  lastPage: false,
  showPage: 0,
  total: 0,
};

export const PRODUCT_COLOR_VALUES: Record<string, string> = {
  BLACK: '#000000',
  BLUE: '#0763fa',
  GREY: '#ababab',
  OLIVE: '#808000',
  RED: '#ff0101',
  'NOC NO COLOUR': '#fbfbfb',
  'NO COLOUR': '#fbfbfb',
  CAMOUFLAGE: '#9e9a75',
  CREAM: '#FFFDD0',
  PURPLE: '#800080',
  KHAKI: '#c3b091',
  WHITE: '#fbfbfb',
  YELLOW: '#ffce01',
  ORANGE: '#ff531a',
  PINK: '#ff24a4',
  GOLD: '#e99c63',
  COFFEE: '#7b5d47',
  'CAMOUFLAGE BLACK': '#000000',
  JADE: '#00A36C',
  OCEAN: '#064273',
  'DARK BLUE': '#064273',
  CHERRY: '#D2042D',
  ARMY: '#4b5320',
  AQUA: '#00FFFF',
  CHARCOAL: '#36454F',
  'DARK BROWN': '#5C4033',
  TERRACOTTA: '#E2725B',
  CYAN: '#00FFFF',
  'ETHNIC HORIZON': '#86B3D1',
  'TRANSPARENT BLACK': '#000000',
  'SKY BLUE': '#87CEEB',
  PEACH: '#FFE5B4',
  TAN: '#D2B48C',
  'JET BLACK': '#444444',
  'LIGHT GREY': '#D3D3D3',
  FUCHSIA: '#CA2C92',
  'DARK PURPLE': '#301934',
  TAUPE: '#483C32',
  BIRCH: '#61693e',
  'ETHNIC AUTUMN': '#000000',
  MUSTARD: '#000000',
  STRIPE: '#000000',
  SILVER: '#C0C0C0',
  WATERMELON: '#D23B68',
  'MISTY GREY': '#BCC2C2',
  'ETHNIC DESERT': '#000000',
  'OFF WHITE': '#000000',
  'BLACK COFFEE': '#000000',
  'DEEP OLIVE': '#556b2f',
  'PACIFIC BLUE': '#000000',
  MAGENTA: '#FF00FF',
  CAMEL: '#C19A6B',
  COBALT: '#0047AB',
  'DARK OLIVE': '#556b2f',
  'CAMOUFLAGE BLUE': '#000000',
  'CAMOUFLAGE GREY': '#000000',
  NAVY: '#000080',
  'BLUE MARINE': '#27AEB9',
  'ETHNIC BROWN': '#000000',
  ZODIAC: '#000000',
  'STRETCH LIMO': '#000000',
  TOBACCO: '#000000',
  'ETHNIC CORAL': '#000000',
  'DENIM BLUE': '#000000',
  'FLAME ORANGE': '#000000',
  BEIGE: '#000000',
  'ETHNIC PURPLE': '#000000',
  BROWN: '#966919',
  GREEN: '#008000',
  'DARK GREEN': '#006e1c',
  TURQUOIS: '#000000',
  'ETHNIC CARBON': '#000000',
  'ETHNIC SOUTHWEST': '#000000',
  'ETHNIC CHEVRON': '#000000',
  BLUISH: '#000000',
  'MAGMA ORANGE': '#000000',
  'BLACK TOPO': '#000000',
  'CAMOUFLAGE GREEN': '#000000',
  'ETHNIC WOODS': '#000000',
  BRONZE: '#CD7F32',
  FROST: '#000000',
  'LIGHT BLUE': '#ADD8E6',
  'LAGUNA GREEN': '#000000',
  BURGUNDY: '#800020',
  'ORANGE POP': '#000000',
  'ETHNIC TEAL': '#069494',
  'HIGH RISK RED': '#000000',
  MAROON: '#800000',
  'STEEL GREY': '#000000',
  'LIGHT BROWN': '#000000',
  LIME: '#32CD32',
  TOSCA: '#4d918f',
  LILAC: '#000000',
  SALEM: '#000000',
  RUST: '#f7621f',
};

export enum PRODUCT_CUSTOM_EDIT_MODE {
  EDIT = 'EDIT',
  ADD_TO_CART = 'ADD_TO_CART',
}
