<script setup lang="ts">
// Components
import CartToastAddToCart from '../../cart/components/CartToastAddToCart.vue';
import { useCartService } from '../../cart/services/cart.service';
import { useCatalog } from '../../dashboard/services/catalog.service';
import ProductDetailProductInformation from '../components/product-detail/ProductDetailProductInformation.vue';
import ProductDialogGuideSizeProduct from '../components/product-detail/ProductDialogGuideSizeProduct.vue';
import ProductBottomSheetGuideSizeProduct from '../components/product-detail/ProductBottomSheetGuideSizeProduct.vue';

// Services
import { useProductService } from '../services/product.service';
import ProductDetailBreadcrumbs from '../components/product-detail/ProductDetailBreadcrumbs.vue';
import FloatingMobileProductCustomDetail from '../components/product-custom/FloatingMobileProductCustomDetail.vue';
import { useEditorService } from '../services/editor.service';
import type { ISAPProductStock } from '../interfaces/product.interface';
// import { useAuthenticationStore } from '../../authentication/stores/authentication.store';

const route = useRoute();

interface IWebsocketProductStock {
  status: boolean;
  data: ISAPProductStock[];
}

/**
 * @description Injecting dependencies
 */
const { $websocket } = useNuxtApp();

// const authStore = useAuthenticationStore();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  product_activeMainImageUrl,
  product_fetchProductDetail,
  product_onLoadMoreProductsRecommendation,
  product_isLoading,
  product_isOpenDialogGuideSizeProduct,
  product_onChangeMainImage,
  product_onOpenDialogGuideSizeProduct,
  product_productDetail,
  product_productDetailIsLoading,
  product_variants,
  product_cartValues,
  product_variantsIsLoading,
  product_selectedTabGuideSizeProduct,
  product_selectedTabProductVariant,
  product_selectedTabProductInformation,
  product_listOfProductsRecommendation,
  product_listOfProductsRecommendationLoading,
  product_onOpenBottomSheetGuideSizeProduct,
  proudct_isOpenBottomSheetGuideSizeProduct,
  window_size,
  product_isLoadingProductStock,
  syncStockProducts,
} = useProductService();
const { catalog_isOpenDialogAddToCart } = useCatalog();
const { cart_addToCart, cart_getList, cart_toastData, cart_showToast, cart_addToCartBulk, cart_addToCartLoading } =
  useCartService();
const { editor_mode } = useEditorService();

/**
 * @description Provide all the data and methods what we need
 */
provide('product', {
  product_activeMainImageUrl,
  product_isLoading,
  product_isOpenDialogGuideSizeProduct,
  product_onChangeMainImage,
  product_onOpenDialogGuideSizeProduct,
  product_productDetail,
  product_productDetailIsLoading,
  product_variants,
  product_cartValues,
  product_variantsIsLoading,
  product_selectedTabGuideSizeProduct,
  product_selectedTabProductVariant,
  product_selectedTabProductInformation,
  product_listOfProductsRecommendation,
  product_listOfProductsRecommendationLoading,
  product_onLoadMoreProductsRecommendation,
  product_onOpenBottomSheetGuideSizeProduct,
  proudct_isOpenBottomSheetGuideSizeProduct,
  window_size,
  product_isLoadingProductStock,
  syncStockProducts,
});

provide('dashboardCatalog', {
  catalog_isOpenDialogAddToCart,
});

provide('cart', {
  cart_addToCart,
  cart_getList,
  cart_toastData,
  cart_showToast,
  cart_addToCartBulk,
  cart_addToCartLoading,
});
provide('editor', {
  editor_mode,
});

const breadcrumbs = computed<IBreadcrumb[]>(() => [
  {
    label: 'Home',
    path: '/',
  },
  {
    label: 'Katalog',
    path: '/catalog',
  },
  {
    label: 'Detail',
    path: null,
  },
]);

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(() => {
  const sku = route.params.sku;
  if (sku) {
    product_fetchProductDetail(String(sku));
  }
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onBeforeMount(async () => {
  setTimeout(() => {
    if (route.params.sku) {
      $websocket
        .channel(`article.${String(route.params.sku)}`)
        .on('pusher:subscription_succeeded', () => {})
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        .listen('.stock.update', callback => {
          const result: IWebsocketProductStock = callback;
          if (result?.status) {
            syncStockProducts(result.data ?? []);
          }
          product_isLoadingProductStock.value = false;
        });

      $websocket.connector.pusher.connection.bind('disconnected', function () {
        product_isLoadingProductStock.value = false;
      });
      $websocket.connector.pusher.connection.bind('unavailable', function () {
        product_isLoadingProductStock.value = false;
      });
    }
  }, 1500);
});
</script>
<template>
  <MainLayout>
    <section id="catalog-detail" class="flex flex-col relative inset-0 z-0 gap-6 mt-5">
      <div v-if="product_productDetailIsLoading" class="flex items-center gap-2">
        <PrimeVueSkeleton class="!w-[60px] !h-[20px]" />
        <PrimeVueSkeleton class="!w-[60px] !h-[20px]" />
        <PrimeVueSkeleton class="!w-[60px] !h-[20px]" />
      </div>
      <ProductDetailBreadcrumbs v-else :data="breadcrumbs" />
      <ProductDetailProductInformation />
      <ProductDialogGuideSizeProduct />
      <ProductBottomSheetGuideSizeProduct />
      <CartToastAddToCart />
      <FloatingMobileProductCustomDetail v-if="product_productDetail?.specification?.is_custom_logo" />
    </section>
  </MainLayout>
</template>
