<script setup lang="ts">
// Components
import CartToastAddToCart from '../../cart/components/CartToastAddToCart.vue';
import ProductDetailBreadcrumbs from '../components/product-detail/ProductDetailBreadcrumbs.vue';
import ProductCustomDetail from '../components/product-custom/ProductCustomDetail.vue';
import ProductCustomBackButton from '../components/product-custom/ProductCustomBackButton.vue';
import DialogCustomProduct from '../components/product-custom/DialogCustomProduct.vue';
import ProductDialogGuideSizeProduct from '../components/product-detail/ProductDialogGuideSizeProduct.vue';

// Services
import { useCartService } from '../../cart/services/cart.service';
import { useCatalog } from '../../dashboard/services/catalog.service';
import { useProductService } from '../services/product.service';
import { useEditorService } from '../services/editor.service';

import { Context as CanvasContext } from '../services/canvas.service';
import { PRODUCT_CUSTOM_EDIT_MODE } from '../constants/product.constant';
import BottomSheetProductCustom from '../components/product-custom/BottomSheetProductCustom.vue';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  product_customActiveMainImageUrl,
  product_fetchProductDetailCustom,
  product_onLoadMoreProductsRecommendation,
  product_isLoading,
  product_isOpenDialogGuideSizeProduct,
  product_onChangeMainImage,
  product_onOpenDialogGuideSizeProduct,
  product_detailCustomData,
  product_detailCustomIsLoading,
  product_customVariants,
  product_customCartValues,
  product_customVariantsIsLoading,
  product_selectedTabGuideSizeProduct,
  product_customSelectedTabProductVariant,
  product_selectedTabProductInformation,
  product_listOfProductsRecommendation,
  product_listOfProductsRecommendationLoading,
  product_onOpenBottomSheetGuideSizeProduct,
  proudct_isOpenBottomSheetGuideSizeProduct,
  window_size,
  product_selectedCustomImageVariant,
  product_checkedRequestOther,
  product_canAddToCartProductCustom,
  product_openBottomSheetProductCustom,
  product_uploadAttachments,
} = useProductService();
const { catalog_isOpenDialogAddToCart } = useCatalog();
const {
  cart_addToCart,
  cart_getList,
  cart_postTextAttachments,
  cart_toastData,
  cart_showToast,
  cart_customAddToCartBulk,
  cart_addToCartLoading,
} = useCartService();

const {
  editor_mode,
  layers,
  editor_backgroundLayers,
  editor_selectedLayer,
  editor_selectedCustomType,
  editor_listOfImageAssets,
  editor_listOfTextAssets,
  selectedModel,
  controlTab,
  editor_canFinishEdit,
  editor_currentBackgroundImageId,
  editor_isLoading,
  editor_width,
  setSelectedLayer,
  syncLayers,
  editor_getSelectedLayerInitialProps,
  captureAllBackgroundLayers,
  editor_selectedTextColor,
  editor_matchMobileView,
  editor_isOpenBottomSheetToolbar,
  editor_prepareAddToCart,
} = useEditorService();

/**
 * @description Provide all the data and methods what we need
 */
provide('product', {
  product_customActiveMainImageUrl,
  product_isLoading,
  product_isOpenDialogGuideSizeProduct,
  product_onChangeMainImage,
  product_onOpenDialogGuideSizeProduct,
  product_detailCustomData,
  product_detailCustomIsLoading,
  product_customVariants,
  product_customCartValues,
  product_customVariantsIsLoading,
  product_selectedTabGuideSizeProduct,
  product_customSelectedTabProductVariant,
  product_selectedTabProductInformation,
  product_listOfProductsRecommendation,
  product_listOfProductsRecommendationLoading,
  product_onLoadMoreProductsRecommendation,
  product_onOpenBottomSheetGuideSizeProduct,
  proudct_isOpenBottomSheetGuideSizeProduct,
  window_size,
  product_selectedCustomImageVariant,
  product_checkedRequestOther,
  product_canAddToCartProductCustom,
  product_openBottomSheetProductCustom,
  product_uploadAttachments,
});

provide('dashboardCatalog', {
  catalog_isOpenDialogAddToCart,
});

provide('cart', {
  cart_addToCart,
  cart_getList,
  cart_postTextAttachments,
  cart_toastData,
  cart_showToast,
  cart_customAddToCartBulk,
  cart_addToCartLoading,
});

provide('editor', {
  editor_mode,
  layers,
  editor_backgroundLayers,
  editor_selectedLayer,
  editor_selectedCustomType,
  editor_listOfImageAssets,
  editor_listOfTextAssets,
  selectedModel,
  controlTab,
  editor_canFinishEdit,
  editor_currentBackgroundImageId,
  editor_isLoading,
  editor_width,
  setSelectedLayer,
  syncLayers,
  editor_getSelectedLayerInitialProps,
  captureAllBackgroundLayers,
  editor_selectedTextColor,
  editor_matchMobileView,
  editor_isOpenBottomSheetToolbar,
  product_uploadAttachments,
  editor_prepareAddToCart,
});

provide('ctx', CanvasContext);

const breadcrumbs = computed<IBreadcrumb[]>(() => [
  {
    label: 'Home',
    path: '/',
  },
  {
    label: product_detailCustomData.value?.product_name_c ?? '-',
    path: `/product/detail/${product_detailCustomData.value?.sku_code_c ?? '-'}`,
  },
  {
    label: 'Kustomisasi',
    path: null,
  },
]);

const isOpenDialog = ref(false);

const handleFetchProductCustom = () => {
  const sku = route.params.sku as string;
  product_fetchProductDetailCustom(String(sku)).finally(() => {
    setTimeout(() => {
      editor_isLoading.value = false;
    }, 1000);

    setTimeout(() => {
      if (editor_matchMobileView.value) {
        product_openBottomSheetProductCustom.value = true;
      } else {
        isOpenDialog.value = true;
      }
    }, 1750);
  });
};

const hasSignificantChange = (prevWidth: number, newWidth: number) => {
  return Math.abs(newWidth - prevWidth) > 100;
};

// reside window handler
const windowResizeHandler = useDebounce(async () => {
  if (hasSignificantChange(editor_width.value, window.innerWidth)) {
    // handleFetchProductCustom();
    window.location.reload();
  }
}, 350);

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(() => {
  const sku = route.params.sku;
  if (sku) {
    editor_mode.value = PRODUCT_CUSTOM_EDIT_MODE.EDIT;
    editor_isLoading.value = true;

    // if (editor_mode.value === PRODUCT_CUSTOM_EDIT_MODE.EDIT) {
    // Set editor width
    const screenWidth = window.innerWidth;

    if (screenWidth <= 768) {
      editor_width.value = window.innerWidth - 20;
    } else if (screenWidth > 1000 && screenWidth < 1240) {
      editor_width.value = 672;
    } else if (screenWidth > 1100 && screenWidth < 1240) {
      editor_width.value = 700;
    } else if (screenWidth > 1240 && screenWidth < 1300) {
      editor_width.value = 720;
    } else if (screenWidth > 1300 && screenWidth < 1500) {
      editor_width.value = 740;
    } else if (screenWidth > 1500) {
      editor_width.value = 800;
    }
    // }

    handleFetchProductCustom();

    window.addEventListener('resize', windowResizeHandler);
  }
});
</script>

<template>
  <MainLayout :required-auth="true">
    <section id="catalog-detail" class="flex flex-col relative inset-0 z-0 gap-6 mt-5">
      <div v-if="product_detailCustomIsLoading" class="flex items-center gap-2">
        <PrimeVueSkeleton class="!w-[60px] !h-[20px]" />
        <PrimeVueSkeleton class="!w-[60px] !h-[20px]" />
        <PrimeVueSkeleton class="!w-[60px] !h-[20px]" />
      </div>
      <ProductDetailBreadcrumbs v-else :data="breadcrumbs" />

      <ProductCustomBackButton />

      <ProductCustomDetail />

      <CartToastAddToCart />
      <DialogCustomProduct v-if="!editor_matchMobileView && isOpenDialog" />
      <BottomSheetProductCustom v-if="editor_matchMobileView" />
      <ProductDialogGuideSizeProduct />

      <button
        class="custom-shadow-sm rounded-full block md:hidden fixed bottom-[60px] right-[40px] cursor-pointer z-10 h-[60px] w-[60px] items-center justify-center p-0 border-[#ACB1B4]"
        @click="editor_isOpenBottomSheetToolbar = true"
      >
        <NuxtImg src="/icons/customize-icon.svg" class="h-[60px] w-[60px]" alt="customize" />
      </button>
    </section>
  </MainLayout>
</template>
