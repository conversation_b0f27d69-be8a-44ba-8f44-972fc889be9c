export interface IAddToCartValue {
  article: string;
  qty: number;
  color: string;
  stock: number;
  product_size_c: string;
  price: string;
}

export interface IReturnTypeUploadCustomAttachments {
  sku_code_c: string;
  files: string[];
}

export interface IResponseUploadCustomAttachment {
  filepath: string;
  s3_url: string;
}

export interface ISAPProductStock {
  article: string;
  qty: number;
}

export interface IProductProvided {
  product_activeMainImageUrl: Ref<string>;
  product_fetchProductDetail: (sku?: string) => Promise<void>;
  product_fetchProductDetailCustom: (sku?: string) => Promise<IProduct>;
  product_fetchProductRecommendation: () => Promise<void>;
  product_onLoadMoreProductsRecommendation: () => Promise<void>;
  product_isLoading: Ref<boolean>;
  product_isOpenDialogGuideSizeProduct: Ref<boolean>;
  proudct_isOpenBottomSheetGuideSizeProduct: Ref<boolean>;
  product_onChangeMainImage: (url: string | undefined) => void;
  product_onOpenDialogGuideSizeProduct: () => void;
  product_onOpenBottomSheetGuideSizeProduct: () => void;
  product_productDetailIsLoading: Ref<boolean>;
  product_productDetail: Ref<IProduct | null>;
  product_variants: Ref<IProductDetailVariant[]>;
  product_cartValues: Ref<IAddToCartValue[]>;
  product_variantsIsLoading: Ref<boolean>;
  product_selectedTabGuideSizeProduct: Ref<string>;
  product_selectedTabProductVariant: Ref<string>;
  product_customSelectedTabProductVariant: Ref<string>;
  product_selectedTabProductInformation: Ref<string>;
  product_listOfProductsRecommendationLoading: Ref<boolean>;
  product_listOfProductsRecommendation: Ref<IProductListWithPagination>;
  window_size: Ref<number>;
  product_searchData: Ref<IProductList[]>;
  product_searchRecommendationData: Ref<IProductSearchRecommendationItem[]>;
  product_searchRecommendationIsLoading: Ref<boolean>;
  product_searchHistory: Ref<string[]>;
  product_searchIsLoading: Ref<boolean>;
  product_saveSearchHistory: (query: string) => void;
  product_clearSearchHistory: () => void;
  product_searchProducts: (searchQuery: string) => Promise<void>;
  product_getSearchProductRecommendation: (searchQuery: string) => Promise<void>;
  product_fetchProductHomepageCustom: () => Promise<void>;
  product_fetchProductHomepagePopular: () => Promise<void>;
  product_homepageCustomData: Ref<IProductHomepage[]>;
  product_homepageCustomIsLoading: Ref<boolean>;
  product_homepagePopularData: Ref<IProductHomepage[]>;
  product_homepagePopularIsLoading: Ref<boolean>;
  // product_fetchProductHomepage: () => Promise<void>;
  product_homepageData: Ref<IProductList[]>;
  product_homepageIsLoading: Ref<boolean>;
  product_detailCustomData: Ref<IProduct | null>;
  product_detailCustomIsLoading: Ref<boolean>;
  product_customVariants: Ref<IProductDetailVariant[]>;
  product_customCartValues: Ref<IAddToCartValue[]>;
  product_customVariantsIsLoading: Ref<boolean>;
  product_checkedRequestOther: Ref<boolean>;
  // product_selectedTextColor: Ref<string>;
  product_selectedCustomImageVariant: Ref<string>;
  product_customActiveMainImageUrl: Ref<string>;
  product_canAddToCartProductCustom: ComputedRef<boolean>;
  product_openBottomSheetProductCustom: Ref<boolean>;
  product_uploadAttachments: (
    body: IReturnTypeUploadCustomAttachments,
    files: File[],
  ) => Promise<IResponseUploadCustomAttachment[]>;
  product_isLoadingProductStock: Ref<boolean>;
  syncStockProducts: (payload: ISAPProductStock[]) => void;
}

export interface IProductStoreState {
  product_isLoading: boolean;
  product_listOfProductActivities: string[];
  product_listOfProductCategories: string[];
  product_listOfProductColors: IProductColor[];
  product_listOfProductSizes: string[];
  product_productDetailIsLoading: boolean;
  product_productDetail: IProduct | null;
  product_variants: IProductDetailVariant[];
  product_cartValues: IAddToCartValue[];
  product_variantsIsLoading: boolean;
  product_listOfProductsRecommendationLoading: boolean;
  product_listOfProductsRecommendation: IProductListWithPagination;
  product_searchData: IProductList[];
  product_searchRecommendationData: IProductSearchRecommendationItem[];
  product_searchRecommendationIsLoading: boolean;
  product_searchHistory: string[];
  product_searchIsLoading: boolean;
  product_homepageCustomData: IProductHomepage[];
  product_homepageCustomIsLoading: boolean;
  product_homepagePopularData: IProductHomepage[];
  product_homepagePopularIsLoading: boolean;
  product_homepageData: IProductList[];
  product_homepageIsLoading: boolean;
  product_detailCustomData: IProduct | null;
  product_detailCustomIsLoading: boolean;
  product_customVariants: IProductDetailVariant[];
  product_customCartValues: IAddToCartValue[];
  product_customVariantsIsLoading: boolean;
  product_checkedRequestOther: boolean;
  product_isLoadingProductStock: boolean;
}
export interface IProductItem {
  id: string;
  image: string;
  image_variant?: string;
  name: string;
  sku: string | null;
  total_variant: number;
  price: string | null;
  stock: number;
  flag: string[];
  cart: {
    count: number;
  };
}
