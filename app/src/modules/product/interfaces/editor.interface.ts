import type { PRODUCT_CUSTOM_EDIT_MODE } from '../constants/product.constant';
import type { IReturnTypeUploadCustomAttachments } from './product.interface';

export interface IEditorSelectedLayer {
  imageId: string;
  backgroundId: string;
  layerId: string;
  layerType: string;
  text: string | null;
  textLabel: string | null;
  fill: string | null;
  fontSize: string | null;
  fontWeight: string;
  fontStyle: string;
  underline: boolean;
  fontFamily: string | null;
  angle: number;
  flipX: boolean;
  flipY: boolean;
  scaleAndZoom: number;
  dirty?: boolean;
}

export interface IEditorImageAssets {
  file: File;
  imageId: string;
  img: string | ArrayBuffer | null;
}

export interface IEditorBackgroundLayer {
  backgroundId: string;
  imageUrl: string;
  hasLayers: boolean;
}

export interface IReturnTypePrepareAddToCart {
  body: IReturnTypeUploadCustomAttachments;
  files: File[];
}

export interface IEditorProvided {
  editor_mode: Ref<PRODUCT_CUSTOM_EDIT_MODE>;
  editor_backgroundLayers: Ref<IEditorBackgroundLayer[]>;
  // editor_currentSequenceNumber: Ref<number>;
  editor_currentBackgroundImageId: Ref<string>;
  layers: Ref<IEditorSelectedLayer[]>;
  editor_selectedLayer: Ref<IEditorSelectedLayer>;
  selectedModel: Ref<string | null>;
  controlTab: Ref<string | null>;
  editor_canFinishEdit: ComputedRef<boolean>;
  setSelectedLayer: (layer: IEditorSelectedLayer) => void;
  syncLayers: (payload: IEditorSelectedLayer[]) => void;
  editor_isLoading: Ref<boolean>;
  editor_width: Ref<number>;
  editor_selectedCustomType: Ref<string>;
  editor_listOfTextAssets: Ref<string[]>;
  editor_listOfImageAssets: Ref<IEditorImageAssets[]>;
  editor_selectedTextColor: Ref<string>;
  editor_matchMobileView: ComputedRef<boolean>;
  editor_isOpenBottomSheetToolbar: Ref<boolean>;
  editor_getSelectedLayerInitialProps: () => IEditorSelectedLayer;
  captureAllBackgroundLayers: () => Promise<void>;
  editor_prepareAddToCart: (skuCodeC: string) => IReturnTypePrepareAddToCart;
}

export interface IEditorStoreState {
  editor_mode: PRODUCT_CUSTOM_EDIT_MODE;
  mode: string;
  editor_backgroundLayers: IEditorBackgroundLayer[];
  // editor_currentSequenceNumber: number;
  editor_currentBackgroundImageId: string;
  layers: IEditorSelectedLayer[];
  editor_selectedLayer: IEditorSelectedLayer;
  selectedModel: string | null;
  controlTab: string | null;
  editor_isLoading: boolean;
  editor_width: number;
}
