// Constants
import {
  PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION,
  PRODUCT_LIST_OF_GUIDE_SIZES,
} from '../constants/product.constant';

// Interfaces
import type {
  IProductProvided,
  IResponseUploadCustomAttachment,
  IReturnTypeUploadCustomAttachments,
  ISAPProductStock,
} from '../interfaces/product.interface';
// Store
import { storeToRefs } from 'pinia';
import { useProductStore } from '../stores/product.store';
import { useEditorService } from './editor.service';
import CanvasService from '../services/canvas.service';
import { makeId } from '~/app/src/core/helpers/text.helper';
import type { IEditorBackgroundLayer } from '../interfaces/editor.interface';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useProductService = (): IProductProvided => {
  /**
   * @description Injected variables
   */
  const route = useRoute(); // Instance of the router
  const store = useProductStore(); // Instance of the store
  const {
    product_isLoading,
    product_productDetailIsLoading,
    product_productDetail,
    product_variants,
    product_cartValues,
    product_variantsIsLoading,
    product_listOfProductsRecommendationLoading,
    product_listOfProductsRecommendation,
    product_searchData,
    product_searchIsLoading,
    product_searchHistory,
    product_searchRecommendationData,
    product_searchRecommendationIsLoading,
    product_homepageCustomData,
    product_homepageCustomIsLoading,
    product_homepagePopularData,
    product_homepagePopularIsLoading,
    product_homepageData,
    product_homepageIsLoading,
    product_detailCustomData,
    product_detailCustomIsLoading,
    product_customVariants,
    product_customCartValues,
    product_customVariantsIsLoading,
    product_checkedRequestOther,
    product_isLoadingProductStock,
  } = storeToRefs(store);

  const { editor_backgroundLayers, editor_currentBackgroundImageId } = useEditorService();

  /**
   * @description Reactive data binding
   */
  const window_size = ref<number>(window.innerWidth);
  const product_activeMainImageUrl = ref<string>('');
  const product_isOpenDialogGuideSizeProduct = ref<boolean>(false);
  const proudct_isOpenBottomSheetGuideSizeProduct = ref<boolean>(false);
  const product_selectedTabGuideSizeProduct = ref<string>(PRODUCT_LIST_OF_GUIDE_SIZES[0].value);
  const product_selectedTabProductInformation = ref<string>(PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION[0]);
  const product_selectedTabProductVariant = ref<string>('');
  const product_isLoadMoreProductRecommendation = ref<boolean>(false);

  const product_customActiveMainImageUrl = ref<string>('');
  const product_customSelectedTabProductVariant = ref<string>('');
  // const product_selectedTextColor = ref('');
  const product_selectedCustomImageVariant = ref<string>('');
  const product_openBottomSheetProductCustom = ref<boolean>(false);

  const product_queryParamsOfProductRecommendation = reactive<IQueryParamsOfProducts>({
    activity: null,
    color: null,
    maxPrice: null,
    minPrice: null,
    limit: 4,
    order_by: 'newest',
    page: 1,
    reff: '',
    search: null,
    size: null,
    subcategory: null,
  });

  const toast = useToast();

  const product_canAddToCartProductCustom = computed(
    () => product_customCartValues.value.filter(i => i.qty > 0)?.length > 0,
  );

  /**
   * @description Handle fetch api get detail product. We call the fetchProduct_getProductDetail function from the store to handle the request.
   */
  const product_fetchProductDetail = async (sku?: string) => {
    const params = sku ? sku : (route.params.sku as string);
    await store.fetchProduct_getProductDetail(params);

    // ? After we've got the product detail, we can set the selected tab of product variant and the main image
    product_activeMainImageUrl.value = product_productDetail.value?.main_image as string;
    product_selectedTabProductVariant.value = product_productDetail.value?.variant?.[0]
      .product_variant_c as string;
  };
  /**
   * @description Handle fetch api get detail product. We call the fetchProduct_getProductDetailCustom function from the store to handle the request.
   */
  const product_fetchProductDetailCustom = async (sku?: string): Promise<IProduct> => {
    const params = sku ? sku : (route.params.sku as string);
    const result = await store.fetchProduct_getProductDetailCustom(params);

    // const MAIN_IMAGE = product_detailCustomData.value?.main_image as string;
    const MAIN_IMAGE: string =
      product_detailCustomData?.value?.variantmedia?.[0]?.url ??
      (product_detailCustomData.value?.main_image as string);

    product_customActiveMainImageUrl.value = MAIN_IMAGE;
    product_customSelectedTabProductVariant.value = product_detailCustomData.value?.variant?.[0]
      .product_variant_c as string;

    // product_selectedTextColor.value = PRODUCT_CUSTOM_TEXT_COLORS[0].value;

    CanvasService.drawSelectedModel(MAIN_IMAGE);

    if (product_detailCustomData?.value && product_detailCustomData?.value?.variantmedia?.length > 0) {
      const mapBackgroundLayers: IEditorBackgroundLayer[] = product_detailCustomData?.value?.variantmedia.map(
        i => ({
          imageUrl: i.url,
          backgroundId: makeId(20),
          hasLayers: false,
        }),
      );
      editor_backgroundLayers.value = mapBackgroundLayers;
      editor_currentBackgroundImageId.value = mapBackgroundLayers[0].backgroundId;
    }
    return result;
  };

  /**
   * @description Handle fetch api get detail product variant. We call the fetchProduct_getProductDetailVariant function from the store to handle the request.
   */
  const product_fetchProductDetailVariant = async () => {
    await store.fetchProduct_getProductDetailVariant(
      route.params.sku as string,
      product_selectedTabProductVariant.value,
    );
  };

  /**
   * @description Handle fetch api get search products. We call the fetchProduct_searchProducts function from the store to handle the request.
   */
  const product_searchProducts = async (searchQuery: string) => {
    await store.fetchProduct_searchProducts(searchQuery);
  };

  /**
   * @description Handle fetch api get search products. We call the fetchProduct_getSearchProductRecommendation function from the store to handle the request.
   */
  const product_getSearchProductRecommendation = async (searchQuery: string) => {
    await store.fetchProduct_getSearchProductRecommendation(searchQuery);
  };

  /**
   * @description Handle fetch api get product custom for homepage. We call the fetchProduct_getProductHomepageCustom function from the store to handle the request.
   */
  const product_fetchProductHomepageCustom = async () => {
    await store.fetchProduct_getProductHomepageCustom(false);
  };

  /**
   * @description Handle fetch api get product popular for homepage. We call the fetchProduct_getProductHomepageCustom function from the store to handle the request.
   */
  const product_fetchProductHomepagePopular = async () => {
    await store.fetchProduct_getProductHomepageCustom(true);
  };

  /**
   * @description Handle fetch api get detail recommendation. We call the fetchProduct_getProductsRecommendation function from the store to handle the request.
   */
  const product_fetchProductRecommendation = async () => {
    await store.fetchProduct_getProductsRecommendation(
      product_queryParamsOfProductRecommendation,
      product_isLoadMoreProductRecommendation.value,
    );
    product_isLoadMoreProductRecommendation.value = false;
  };

  /**
   * @description Handle fetch api get detail recommendation. We call the fetchProduct_getProductsRecommendation function from the store to handle the request.
   */
  const product_uploadAttachments = async (
    body: IReturnTypeUploadCustomAttachments,
    files: File[],
  ): Promise<IResponseUploadCustomAttachment[] | string> => {
    try {
      const result = await store.fetchProduct_uploadAttachments(body, files);
      if (typeof result === 'string') {
        if (result?.includes('Your account has not been verified')) {
          toast.add({
            severity: 'error',
            summary: 'Akun belum diverifikasi. Tidak dapat menambahkan produk ke keranjang.',
            detail: null,
            life: 3000,
          });
        }
        return result;
      }
      return result;
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      toast.add({
        severity: 'error',
        summary: 'Failed to add to cart',
        detail: null,
        life: 3000,
      });
      return false;
    }
  };

  /**
   * @description Handle business logic for load more products recommendation
   */
  const product_onLoadMoreProductsRecommendation = async (): Promise<void> => {
    product_isLoadMoreProductRecommendation.value = true;
    product_queryParamsOfProductRecommendation.page += 1;
    product_queryParamsOfProductRecommendation.limit = 4;
  };

  /**
   * @description Handle business logic when we want to change the main image of the product
   */
  const product_onChangeMainImage = (url: string | undefined) => {
    product_activeMainImageUrl.value = url || '';
  };

  /**
   * @description Handle business logic to open the dialog guide size product
   */
  const product_onOpenDialogGuideSizeProduct = () => {
    product_isOpenDialogGuideSizeProduct.value = true;
  };

  /**
   * @description Handle business logic to open the dialog guide size product for mobile
   */
  const product_onOpenBottomSheetGuideSizeProduct = () => {
    proudct_isOpenBottomSheetGuideSizeProduct.value = true;
  };

  const product_saveSearchHistory = (query: string) => {
    store.product_saveSearchKeyword(query);
  };

  const product_clearSearchHistory = () => {
    store.product_clearSearchHistory();
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    product_selectedTabProductVariant,
    async value => {
      if (value) {
        await product_fetchProductDetailVariant();
      }
    },
    {
      immediate: true,
    },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch([() => product_queryParamsOfProductRecommendation.page], async () => {
    await product_fetchProductRecommendation();
  });

  /**
   * Sync stock values in variant based on matching articles in websocket payload
   * @param {Array} payload - Array of objects with article and stock to update
   * @returns {void}
   */
  const syncStockProducts = (payload: ISAPProductStock[]): void => {
    const stockMap: { [key: string]: number } = {};

    payload.forEach(item => {
      stockMap[item.article] = item.qty;
    });

    // Update variants with new stock values
    const updatedVariants = [...product_variants.value].map(item => {
      if (typeof stockMap?.[String(item.article_id)] === 'number') {
        return {
          ...item,
          stock: stockMap[item.article_id],
        };
      } else {
        // If no match is found, return the item unchanged
        return item;
      }
    });

    // also update state cart values
    const cartValues = updatedVariants.map(item => ({
      article: item.article_id,
      qty: item.cart.count,
      color: item.color,
      stock: item.stock,
      product_size_c: item.product_size_c,
      price: item.price.amount,
    }));

    product_variants.value = updatedVariants;
    product_cartValues.value = cartValues;
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    product_activeMainImageUrl,
    product_fetchProductDetail,
    product_fetchProductDetailCustom,
    product_fetchProductRecommendation,
    product_onLoadMoreProductsRecommendation,
    product_uploadAttachments,
    product_isLoading: product_isLoading,
    product_isOpenDialogGuideSizeProduct,
    proudct_isOpenBottomSheetGuideSizeProduct,
    product_onChangeMainImage,
    product_onOpenDialogGuideSizeProduct,
    product_onOpenBottomSheetGuideSizeProduct,
    product_productDetailIsLoading,
    product_productDetail,
    product_variants,
    product_cartValues,
    product_variantsIsLoading,
    product_selectedTabGuideSizeProduct,
    product_selectedTabProductVariant,
    product_customSelectedTabProductVariant,
    product_selectedTabProductInformation,
    product_listOfProductsRecommendationLoading,
    product_listOfProductsRecommendation,
    window_size,
    product_searchData,
    product_searchIsLoading,
    product_searchHistory,
    product_searchRecommendationData,
    product_searchRecommendationIsLoading,
    product_searchProducts,
    product_getSearchProductRecommendation,
    product_fetchProductHomepageCustom,
    product_fetchProductHomepagePopular,
    product_saveSearchHistory,
    product_clearSearchHistory,
    product_homepageCustomData,
    product_homepageCustomIsLoading,
    product_homepagePopularData,
    product_homepagePopularIsLoading,
    product_homepageData,
    product_homepageIsLoading,
    product_detailCustomData,
    product_detailCustomIsLoading,
    product_customActiveMainImageUrl,
    product_customVariants,
    product_customCartValues,
    product_customVariantsIsLoading,
    product_selectedCustomImageVariant,
    product_checkedRequestOther,
    product_canAddToCartProductCustom,
    product_openBottomSheetProductCustom,
    product_isLoadingProductStock,
    syncStockProducts,
  };
};
