import { fabric } from 'fabric';

// import MediaService from './media.service';
import { cloneProxy } from '../utils/product.util';
import type { IEditorSelectedLayer } from '../interfaces/editor.interface';
import { dataURLtoBlob, makeId } from '~/app/src/core/helpers/text.helper';
import type { Object, StaticCanvas } from 'fabric/fabric-impl';

export interface IEditorCanvasContext {
  canvas: StaticCanvas;
}

export const Context: IEditorCanvasContext = {
  canvas: null as unknown as StaticCanvas,
};

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
class CanvasService {
  static fontSize = 32;
  static scale = 1;

  static drawSelectedModel(bgUrl: string) {
    const center = Context.canvas.getCenter();
    Context.canvas.setBackgroundImage(
      bgUrl,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      data => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        Context.canvas.backgroundImage.scaleX = (Context.canvas.getWidth() / data.width) * CanvasService.scale;
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        Context.canvas.backgroundImage.scaleY = (Context.canvas.getHeight() / data.height) * CanvasService.scale;
        return Context.canvas.renderAll();
      },
      {
        // backgroundImageOpacity: 1,
        // backgroundImageStretch: false,
        top: center.top,
        left: center.left,
        originX: 'center',
        originY: 'center',
        crossOrigin: 'anonymous',
      },
    );
    Context.canvas.renderAll();
  }

  static addTextLayer(text: string, textLabel: string, color: string, backgroundId: string) {
    const center = Context.canvas.getCenter();
    const textLayer = new fabric.Text(text, {
      layerId: makeId(), 
      backgroundId,
      layerType: 'text',
      fill: color ?? '#000000',
      textLabel,
      fontSize: CanvasService.fontSize,
      fontWeight: 700,
      fontFamily: 'Arial',
      scaleX: 1,
      scaleY: 1,
      top: center.top,
      left: center.left,
      originX: 'center',
      originY: 'center',
      borderColor: '#fbd444',
      cornerColor: '#fbd444',
      strokeWidth: 20,
      cornerSize: 15,
      transparentCorners: false,
      globalCompositeOperation: 'source-atop',
      _controlsVisibility: {
        mt: false,
        mb: false,
        ml: false,
        mr: false,
      },
    } as unknown as Object);
    Context.canvas.add(textLayer);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    Context.canvas.setActiveObject(textLayer);
    Context.canvas.renderAll();
  }

  static updateTextLayer(layer: IEditorSelectedLayer, color: string) {
    const newTextLayer = new fabric.Text(
      layer.text as string,
      {
        ...layer,
        fill: color,
      } as unknown as Object,
    );

    Context.canvas.getObjects().forEach(object => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (object.layerId == layer.layerId) {
        Context.canvas.remove(object);
        Context.canvas.renderAll();
      }
    });

    Context.canvas.add(newTextLayer);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    Context.canvas.setActiveObject(newTextLayer);
    Context.canvas.renderAll();
  }

  static addImageLayer(url: string, imageId: string, backgroundId: string) {
    fabric.Image.fromURL(
      url,
      imageLayer => {
        imageLayer.set({
          backgroundId,
          imageId,
          layerId: makeId(),
          layerType: 'image',
          mode: '',
          borderColor: '#fbd444',
          cornerColor: '#fbd444',
          strokeWidth: 20,
          cornerSize: 15,
          transparentCorners: false,
          _controlsVisibility: {
            mt: false,
            mb: false,
            ml: false,
            mr: false,
          },
        } as unknown as keyof Object);
        const imageWidth = Context.canvas.getWidth() * 0.2;
        imageLayer.scaleToWidth(imageWidth);
        Context.canvas.add(imageLayer);
        Context.canvas.centerObject(imageLayer);
        imageLayer.setCoords();

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        Context.canvas.setActiveObject(imageLayer);
        Context.canvas.renderAll();
      },
      {
        globalCompositeOperation: 'source-atop',
        crossOrigin: 'anonymous',
      },
    );
  }

  static changeMode(backgroundId: string) {
    Context.canvas.getObjects().forEach(object => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      object.visible = object.backgroundId === backgroundId;
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    Context.canvas.discardActiveObject();
    Context.canvas.renderAll();
  }

  static changeBackgroundLayer(backgroundId: string) {
    console.log('changeBackgroundLayer->backgroundId', backgroundId);
    // Context.canvas.getObjects().forEach(object => {
    //   object.visible = object.sequenceNumber != sequenceNumber ? false : true;
    // });
    // // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // // @ts-ignore
    // Context.canvas.discardActiveObject();
    // Context.canvas.renderAll();
  }

  static selectLayer(layer: IEditorSelectedLayer, callback = () => {}) {
    Context.canvas.getObjects().forEach(object => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (object.layerId == layer.layerId) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        Context.canvas.setActiveObject(object);
        Context.canvas.renderAll();
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        callback(object);
      }
    });
  }

  static removeLayer(layer: IEditorSelectedLayer) {
    Context.canvas.getObjects().forEach(object => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (object.layerId == layer.layerId) {
        Context.canvas.remove(object);
        Context.canvas.renderAll();
      }
    });
  }

  static duplicateLayer(layer: IEditorSelectedLayer) {
    Context.canvas.getObjects().forEach(object => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (object.layerId == layer.layerId) {
        const clone = cloneProxy(object);
        clone.set({
          layerId: makeId(),
          left: clone.left + 10,
          top: clone.top + 10,
        });
        Context.canvas.add(clone);
        Context.canvas.renderAll();
      }
    });
  }

  static toJSON() {
    return Context.canvas.toJSON(['backgroundId', 'layerId', 'layerType', '_controlsVisibility']);
  }

  static loadFromJSON(data: unknown, callback = () => {}) {
    Context.canvas.loadFromJSON(data, callback);
  }

  static deleteAllLayers() {
    Context.canvas.getObjects().forEach(object => {
      Context.canvas.remove(object);
    });
  }

  static async takeVirtualCapture(backgroundId: string, backgroundImageUrl: string, productName: string) {
    const extension = 'jpg';
    const canvas = new fabric.Canvas(null, {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      width: Context.canvas.width,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      height: Context.canvas.height,
    });

    return new Promise(resolve => {
      canvas.loadFromJSON(this.toJSON(), () => {
        canvas.getObjects().forEach(object => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          object.visible = object.backgroundId === backgroundId;
        });

        const center = canvas.getCenter();

        canvas.setBackgroundImage(
          backgroundImageUrl,
          data => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            canvas.backgroundImage.scaleX = (canvas.getWidth() / data.width) * CanvasService.scale;
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            canvas.backgroundImage.scaleY = (canvas.getHeight() / data.height) * CanvasService.scale;

            canvas
              .renderAll()
              .getElement()
              .toBlob(
                blob => {
                  resolve(blob);
                },
                'image/png',
                1,
              );

            const link = document.createElement('a');
            const imgData = canvas.toDataURL({ format: extension, quality: 5 });
            const blob = dataURLtoBlob(imgData);
            const objectUrl = URL.createObjectURL(blob);
            link.download = `${productName} - ${makeId()}.${extension}`;
            link.href = objectUrl;
            link.click();
          },
          {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            backgroundImageOpacity: 1,
            backgroundImageStretch: false,
            scaleX: 0.35,
            scaleY: 0.35,
            top: center.top,
            left: center.left,
            originX: 'center',
            originY: 'center',
            crossOrigin: 'anonymous',
          },
        );
      });
    });
  }

  static async capture(backgroundId: string, backgroundImageUrl: string, productName: string) {
    const result = await this.takeVirtualCapture(backgroundId, backgroundImageUrl, productName);
    console.log('result', result);
  }

  static prepareCrop(e) {
    const i = new fabric.Rect({
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      id: 'crop-rect',
      top: e.top,
      left: e.left,
      angle: e.angle,
      width: e.getScaledWidth(),
      height: e.getScaledHeight(),
      stroke: 'rgb(42, 67, 101)',
      strokeWidth: 2,
      strokeDashArray: [5, 5],
      fill: 'rgba(255, 255, 255, 1)',
      globalCompositeOperation: 'overlay',
      lockRotation: true,
    });

    const a = new fabric.Rect({
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      id: 'overlay-rect',
      top: e.top,
      left: e.left,
      angle: e.angle,
      width: e.getScaledWidth(),
      height: e.getScaledHeight(),
      selectable: false,
      selection: false,
      fill: 'rgba(0, 0, 0, 0.5)',
      lockRotation: true,
    });

    const s = e.cropX,
      o = e.cropY,
      c = e.width,
      l = e.height;

    e.set({
      cropX: null,
      cropY: null,
      left: e.left - s * e.scaleX,
      top: e.top - o * e.scaleY,
      width: e._originalElement.naturalWidth,
      height: e._originalElement.naturalHeight,
      dirty: false,
    });
    i.set({
      left: e.left + s * e.scaleX,
      top: e.top + o * e.scaleY,
      width: c * e.scaleX,
      height: l * e.scaleY,
      dirty: false,
    });
    a.set({
      left: e.left,
      top: e.top,
      width: e.width * e.scaleX,
      height: e.height * e.scaleY,
      dirty: false,
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    i.oldScaleX = i.scaleX;
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    i.oldScaleY = i.scaleY;

    Context.canvas.add(a);
    Context.canvas.add(i);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    Context.canvas.discardActiveObject();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    Context.canvas.setActiveObject(i);
    Context.canvas.renderAll();

    //
    i.on('moving', function () {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      (i.top < e.top || i.left < e.left) &&
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        ((i.left = i.left < e.left ? e.left : i.left), (i.top = i.top < e.top ? e.top : i.top)),
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        (i.top + i.getScaledHeight() > e.top + e.getScaledHeight() ||
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          i.left + i.getScaledWidth() > e.left + e.getScaledWidth()) &&
          ((i.top =
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            i.top + i.getScaledHeight() > e.top + e.getScaledHeight()
              ? e.top + e.getScaledHeight() - i.getScaledHeight()
              : i.top),
          (i.left =
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            i.left + i.getScaledWidth() > e.left + e.getScaledWidth()
              ? e.left + e.getScaledWidth() - i.getScaledWidth()
              : i.left));
    });

    i.on('deselected', function () {
      CanvasService.cropImage(i, e);
      Context.canvas.remove(a);
    });
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  static cropImage(i, e) {
    Context.canvas.remove(i);

    const s = (i.left - e.left) / e.scaleX,
      o = (i.top - e.top) / e.scaleY,
      c = (i.width * i.scaleX) / e.scaleX,
      l = (i.height * i.scaleY) / e.scaleY;

    e.set({
      cropX: s,
      cropY: o,
      width: c,
      height: l,
      top: e.top + o * e.scaleY,
      left: e.left + s * e.scaleX,
      selectable: true,
      cropped: 1,
    });

    Context.canvas.renderAll();
  }
}

export default CanvasService;
