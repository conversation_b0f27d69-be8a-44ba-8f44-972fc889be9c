// Store
import { storeToRefs } from 'pinia';
import type {
  IEditorImageAssets,
  IEditorProvided,
  IEditorSelectedLayer,
  IReturnTypePrepareAddToCart,
} from '../interfaces/editor.interface';
import { useEditorStore } from '../stores/editor.store';
import { PRODUCT_CUSTOM_TEXT_COLORS, PRODUCT_CUSTOM_TYPE } from '../constants/product.constant';
import CanvasService from './canvas.service';
import { useProductStore } from '../stores/product.store';
// import type { IReturnTypeUploadCustomAttachments } from '../interfaces/product.interface';

//Swiper

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useEditorService = (): IEditorProvided => {
  /**
   * @description Injected variables
   */
  const store = useEditorStore(); // Instance of the store
  const productStore = useProductStore(); // Instance of the store
  const {
    editor_mode,
    editor_selectedLayer,
    editor_backgroundLayers,
    editor_currentBackgroundImageId,
    layers,
    selectedModel,
    controlTab,
    editor_isLoading,
    editor_width,
  } = storeToRefs(store);

  const editor_selectedCustomType = ref(PRODUCT_CUSTOM_TYPE.IMAGE);
  const editor_listOfTextAssets = ref<string[]>([]);
  const editor_listOfImageAssets = ref<IEditorImageAssets[]>([]);
  const editor_selectedTextColor = ref(PRODUCT_CUSTOM_TEXT_COLORS[1].value);
  const editor_isOpenBottomSheetToolbar = ref(false);

  const editor_getSelectedLayerInitialProps = () => ({
    backgroundId: '',
    layerId: '',
    layerType: '',
    text: null,
    textLabel: null,
    fill: null,
    fontSize: null,
    fontWeight: 'normal',
    fontStyle: 'normal',
    underline: false,
    fontFamily: null,
    angle: 0,
    flipX: false,
    flipY: false,
    scaleAndZoom: 0,
  });

  const setSelectedLayer = (layer: IEditorSelectedLayer) => {
    controlTab.value = 'object';
    editor_selectedLayer.value = layer;
  };

  const syncLayers = (payload: IEditorSelectedLayer[]) => {
    layers.value = payload;
    if (payload?.length > 0) {
      editor_backgroundLayers.value.forEach((bg, bgIndex) => {
        const hasExistLayerThisBackground = payload.find(layer => layer.backgroundId === bg.backgroundId);
        if (hasExistLayerThisBackground) {
          editor_backgroundLayers.value[bgIndex].hasLayers = true;
        } else {
          editor_backgroundLayers.value[bgIndex].hasLayers = false;
        }
      });
    } else {
      editor_backgroundLayers.value.forEach((_, bgIndex) => {
        editor_backgroundLayers.value[bgIndex].hasLayers = false;
      });
    }
  };

  const captureAllBackgroundLayers = async (): Promise<void> => {
    console.log('OK captureAllBackgroundLayers');
    const productName = productStore.product_detailCustomData?.product_name_c as string;
    try {
      const allBackgroundLayers = editor_backgroundLayers.value.forEach(bg => {
        if (bg.hasLayers) {
          CanvasService.capture(bg.backgroundId, bg.imageUrl, productName);
        }
      });

      console.log('allBackgroundLayers', allBackgroundLayers);
    } catch (e) {
      console.log('E', e);
    }
  };

  const editor_prepareAddToCart = (skuCodeC: string): IReturnTypePrepareAddToCart => {
    // const layersLogo = layers.value.filter(t => t.layerType === 'image');
    return {
      body: {
        files: editor_listOfImageAssets.value.map(i => i.file.name) as string[],
        sku_code_c: skuCodeC,
      },
      files: editor_listOfImageAssets.value.map(file => file.file) as File[],
    };
  };

  const editor_canFinishEdit = computed<boolean>(() => {
    return editor_backgroundLayers.value.filter(i => i.hasLayers).length > 0;
  });

  const editor_matchMobileView = computed(() => {
    return window.innerWidth < 1000;
  });

  /**
   * @description Return everything what we need into an object
   */
  return {
    editor_mode,
    editor_selectedLayer,
    editor_backgroundLayers,
    editor_currentBackgroundImageId,
    editor_selectedCustomType,
    editor_listOfTextAssets,
    editor_listOfImageAssets,
    editor_selectedTextColor,
    editor_isOpenBottomSheetToolbar,
    editor_canFinishEdit,
    layers,
    selectedModel,
    controlTab,
    editor_isLoading,
    editor_width,
    setSelectedLayer,
    syncLayers,
    editor_matchMobileView,
    editor_getSelectedLayerInitialProps:
      editor_getSelectedLayerInitialProps as unknown as () => IEditorSelectedLayer,
    captureAllBackgroundLayers,
    editor_prepareAddToCart,
  };
};
