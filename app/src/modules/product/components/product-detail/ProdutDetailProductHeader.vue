<script setup lang="ts">
// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { product_productDetail } = inject<IProductProvided>('product')!;

const data = computed<IProduct>(() => {
  return product_productDetail?.value as IProduct;
});
</script>

<template>
  <section v-if="product_productDetail" id="catalog-detail-product-header" class="flex flex-col gap-3">
    <header class="flex flex-col gap-1">
      <section id="title" class="flex items-center gap-2">
        <h1 class="font-bold font-druk text-[22px] sm:text-[28px] text-black">
          {{ data.product_name_c }}
        </h1>
      </section>
      <span id="price" class="font-bold text-xl text-black">
        {{ useCurrencyFormat(+data.product_price.amount) }}
      </span>
    </header>
  </section>
</template>
