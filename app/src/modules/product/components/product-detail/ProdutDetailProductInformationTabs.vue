<script setup lang="ts">
// Constants
import {
  PRODUCT_LIST_OF_SPECIFICATIONS,
  PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION,
} from '../../constants/product.constant';

// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { product_productDetail, product_selectedTabProductInformation } = inject<IProductProvided>('product')!;

const product = computed<IProduct>(() => product_productDetail.value as IProduct);
</script>
<template>
  <section v-if="product_productDetail" id="catalog-detail-product-information-tabs" class="mb-5">
    <PrimeVueTabs v-model:value="product_selectedTabProductInformation">
      <PrimeVueTabList
        class="w-fit"
        :pt="{
          activeBar: '!bg-header-orange',
        }"
      >
        <PrimeVueTab
          v-for="(tab, tabIndex) in PRODUCT_LIST_OF_TABS_PRODUCT_INFORMATION"
          :key="`tab-${tabIndex}`"
          :value="tab"
          :pt="{
            root: `text-sm ${useActiveTab(product_selectedTabProductInformation, tab)}`,
          }"
        >
          {{ tab }}
        </PrimeVueTab>
      </PrimeVueTabList>
      <PrimeVueTabPanels class="!px-0 !pb-0 !pt-6">
        <PrimeVueTabPanel value="Deskripsi">
          <p class="text-base text-black overflow-hidden text-ellipsis">
            {{ product?.article_description }}
          </p>
        </PrimeVueTabPanel>
        <PrimeVueTabPanel value="Spesifikasi">
          <section id="specification-informations" class="grid grid-rows-1 grid-cols-12 gap-4">
            <section
              v-for="(specification, specificationIndex) in PRODUCT_LIST_OF_SPECIFICATIONS"
              id="specification"
              :key="`specification-${specificationIndex}`"
              class="col-span-6 flex items-center gap-3 w-full"
            >
              <span class="text-black w-50 text-sm"> {{ specification.label }}: </span>

              <template v-if="specification.label === 'SKU'">
                <span class="border-b border-solid border-input-gray text-base text-black pb-1 w-full">
                  {{ product?.[specification.key as keyof IProduct] || '-' }}
                </span>
              </template>

              <template v-else-if="specification.label === 'Berat Satuan'">
                <span class="border-b border-solid border-input-gray text-black pb-1 w-full">
                  {{ product.specification?.[specification.key as keyof IProductSpecification] || '-' }}
                  {{ product.specification.uomweight ?? '-' }}
                </span>
              </template>

              <template v-else>
                <span class="border-b border-solid border-input-gray text-base text-black pb-1 w-full">
                  {{ product?.specification?.[specification.key as keyof IProductSpecification] || '-' }}
                </span>
              </template>
            </section>
          </section>
        </PrimeVueTabPanel>
      </PrimeVueTabPanels>
    </PrimeVueTabs>
  </section>
</template>
