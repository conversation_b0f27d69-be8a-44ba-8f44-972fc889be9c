<script setup lang="ts">
// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';
import PrimeVueGalleria from 'primevue/galleria';
/**
 * @description Injecting dependencies
 */
const { product_activeMainImageUrl, product_onChangeMainImage, product_productDetail, window_size } =
  inject<IProductProvided>('product')!;

const data = computed<IProduct>(() => {
  return product_productDetail?.value as IProduct;
});

function updateWidth() {
  window_size.value = window.innerWidth;
}

onMounted(() => {
  window.addEventListener('resize', updateWidth);
});

const activeIndex = ref(0);

const next = () => {
  const variantMedia = product_productDetail?.value?.variantmedia;
  if (!variantMedia || !variantMedia.length) return;
  
  activeIndex.value = 
    activeIndex.value >= variantMedia.length - 1
      ? variantMedia.length - 1
      : activeIndex.value + 1;
  
  const newImageUrl = variantMedia[activeIndex.value]?.url;
  if (newImageUrl) {
    product_onChangeMainImage(newImageUrl);
    if (product_activeMainImageUrl && typeof product_activeMainImageUrl === 'object') {
      product_activeMainImageUrl.value = newImageUrl;
    }
  }
};

const prev = () => {
  const variantMedia = product_productDetail?.value?.variantmedia;
  if (!variantMedia || !variantMedia.length) return;
  
  activeIndex.value = activeIndex.value <= 0 ? 0 : activeIndex.value - 1;
  
  const newImageUrl = variantMedia[activeIndex.value]?.url;
  if (newImageUrl) {
    product_onChangeMainImage(newImageUrl);
    if (product_activeMainImageUrl && typeof product_activeMainImageUrl === 'object') {
      product_activeMainImageUrl.value = newImageUrl;
    }
  }
};

</script>
<template>
  <section class="flex flex-col w-full">
    <section v-if="product_productDetail" id="catalog-detail-product-galleries" class="w-full">
      <PrimeVueGalleria
        v-model:active-index="activeIndex"
        :value="product_productDetail?.variantmedia"
        :circular="true"
        :thumbnails-position="window_size < 1024 ? 'bottom' : 'left'"
        :num-visible="5"
        :show-item-navigators="true"
        :pt="{
          root: '!border-none',
          items: '!relative',
          thumbnails: '!border-[#E5E6E8] border rounded-md h-fit',
          thumbnailItems: '!gap-5',
          thumbnailPrevButton: '!w-full !mb-2 !border-b  !rounded-none !border-gray-300 sm:!block !hidden',
          thumbnailNextButton: '!w-full !mt-2 !border-t  !rounded-none !border-gray-300 sm:!block !hidden',
        }"
      >
        <template #item="slotProps">
          <div class="relative">
            <NuxtImg :src="slotProps.item.url" alt="product" class="gallery-img w-full rounded-sm" />
            <div class="inline-flex absolute bottom-0 right-0">
              <button class="bg-white shadow rounded-full p-2 m-2" @click="prev">
                <NuxtImg src="/icons/chevron-right.svg" alt="" class="rotate-180" />
              </button>
              <button class="bg-white shadow rounded-full p-2 m-2" @click="next">
                <NuxtImg src="/icons/chevron-right.svg" alt="" class="" />
              </button>
            </div>
          </div>
        </template>
        <template #previousthumbnailicon>
          <div class="w-full flex justify-center items-center" @click.stop>
            <button
              type="button"
              @click="prev"
              class="w-full flex justify-center items-center"
            >
              <NuxtImg src="/icons/chevron-top.svg" alt="" class="w-[2em] h-[2em] object-contain" />
            </button>
          </div>
        </template>
        <template #nextthumbnailicon>
          <div class="w-full flex justify-center items-center" @click.stop>
            <button
              type="button"
              @click="next"
              class="w-full flex justify-center items-center"
            >
              <NuxtImg src="/icons/chevron-bottom.svg" alt="" class="w-[2em] h-[2em] object-contain" />
            </button>
          </div>
        </template>
        <template #thumbnail="slotProps">
            <NuxtImg
            :src="slotProps.item.url"
            alt="product"
            class="w-15 h-15 rounded-sm cursor-pointer object-cover"
            :class="{ 'border-2 border-header-orange': slotProps.item.url === product_activeMainImageUrl }"
            @click="product_onChangeMainImage(slotProps.item.url)"
            >
          </NuxtImg>
        </template>
      </PrimeVueGalleria>
    </section>
    <section class="mt-10 sm:block hidden">
      <PrimeVueAccordion value="0">
        <PrimeVueAccordionPanel value="0">
          <PrimeVueAccordionHeader class="!text-black">Tentang Produk</PrimeVueAccordionHeader>
          <PrimeVueAccordionContent>
            <p class="m-0">
              {{ data?.article_description ? data?.article_description : '-' }}
            </p>
          </PrimeVueAccordionContent>
        </PrimeVueAccordionPanel>
        <PrimeVueAccordionPanel value="1">
          <PrimeVueAccordionHeader class="!text-black">Detail Teknis</PrimeVueAccordionHeader>
          <PrimeVueAccordionContent>
            <div class="flex flex-col">
              <div class="py-2 px-1 flex justify-between hover:bg-gray-50 hover:border-y border-gray-200">
                <span class="text-muted">Ukuran</span>
                <span class="font-bold text-black">{{ data?.specification?.dimension ? data?.specification?.dimension : '-' }}</span>
              </div>
              <div class="py-2 px-1 flex justify-between hover:bg-gray-50 hover:border-y border-gray-200">
                <span class="text-muted">Berat</span>
                <span class="font-bold text-black"
                  >{{ data?.specification?.weight ?? '-' }} {{ data?.specification?.uomweight ? data?.specification?.uomweight : '-' }}</span
                >
              </div>
              <div class="py-2 px-1 flex justify-between hover:bg-gray-50 hover:border-y border-gray-200">
                <span class="text-muted">Material</span>
                <span class="font-bold text-black">{{ data?.specification?.material ? data?.specification?.material : '-'  }}</span>
              </div>
            </div>
          </PrimeVueAccordionContent>
        </PrimeVueAccordionPanel>
        <PrimeVueAccordionPanel value="2">
          <PrimeVueAccordionHeader class="!text-black">Fitur</PrimeVueAccordionHeader>
          <PrimeVueAccordionContent>
            <div class="">
              {{ data?.product_feature ? data?.product_feature : '-' }}
            </div>
          </PrimeVueAccordionContent>
        </PrimeVueAccordionPanel>
      </PrimeVueAccordion>
    </section>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-galleria-next-button.p-galleria-nav-button) {
  display: none !important;
}
/* :deep(.p-galleria-item) { 
  width: 718px !important;
  height: 718px !important;
}
:deep(.p-galleria-item .gallery-img img) {
  width: 718px !important;
  height: 718px !important;
} */
</style>
