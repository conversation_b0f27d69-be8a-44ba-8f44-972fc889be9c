<script setup lang="ts">
// Components
import type { IProductProvided } from '../../interfaces/product.interface';
import ProductDetailProductColorVariants from './ProductDetailProductColorVariants.vue';
import ProductDetailProductColorVariantsSkeleton from './ProductDetailProductColorVariantsSkeleton.vue';
import ProductDetailProductGalleries from './ProductDetailProductGalleries.vue';
import ProductDetailProductHeader from './ProdutDetailProductHeader.vue';

/**
 * @description Injecting dependencies
 */
const { product_productDetailIsLoading: loading } = inject<IProductProvided>('product')!;
</script>

<template>
  <section id="catalog-detail-product-information" class="grid grid-cols-1 lg:grid-cols-[1fr_536px] gap-5">
    <ProductDetailProductGalleries />
    <section id="product-custom-sidebar" class="relative rounded-[16px] sm:p-5 sm:px-6 p-2 custom-shadow-md">
      <ProductDetailProductHeader />
      <ProductDetailProductColorVariantsSkeleton v-if="loading" />
      <ProductDetailProductColorVariants v-else />
    </section>
  </section>
</template>
