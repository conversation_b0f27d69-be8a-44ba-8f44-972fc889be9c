<script setup lang="ts">
// Constants
import { PRODUCT_LIST_OF_GUIDE_SIZES } from '../../constants/product.constant';

// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { proudct_isOpenBottomSheetGuideSizeProduct, product_selectedTabGuideSizeProduct } =
  inject<IProductProvided>('product')!;
</script>

<template>
  <section id="dialog-guide-size-product" class="relative inset-0 z-0">
    <PrimeVueDrawer
      v-model:visible="proudct_isOpenBottomSheetGuideSizeProduct"
      position="bottom"
      class="!h-[50em] !rounded-t-lg"
    >
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-bold font-druk text-3xl text-black">Panduan Ukuran</h4>
        </header>
      </template>

      <template #default>
        <section id="content" class="flex flex-col gap-3">
          <PrimeVueTabs v-model:value="product_selectedTabGuideSizeProduct">
            <PrimeVueTabList
              class="w-fit"
              :pt="{
                activeBar: '!bg-header-orange',
              }"
            >
              <PrimeVueTab
                v-for="(guideSize, guideSizeIndex) in PRODUCT_LIST_OF_GUIDE_SIZES"
                :key="`guideSize-${guideSizeIndex}`"
                :value="guideSize.value"
                :pt="{
                  root: `text-sm ${useActiveTab(product_selectedTabGuideSizeProduct, guideSize.value)}`,
                }"
              >
                {{ guideSize.label }}
              </PrimeVueTab>
            </PrimeVueTabList>
            <PrimeVueTabPanels class="!px-0 !pb-0 !pt-6">
              <PrimeVueTabPanel
                v-for="(guideSize, guideSizeIndex) in PRODUCT_LIST_OF_GUIDE_SIZES"
                :key="`guideSize-panel-${guideSizeIndex}`"
                :value="guideSize.value"
              >
                <component :is="guideSize.component" />
              </PrimeVueTabPanel>
            </PrimeVueTabPanels>
          </PrimeVueTabs>
        </section>
      </template>
    </PrimeVueDrawer>
  </section>
</template>
