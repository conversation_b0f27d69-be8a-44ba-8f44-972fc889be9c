<script setup lang="ts">
// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { product_listOfProductsRecommendation, product_onLoadMoreProductsRecommendation } =
  inject<IProductProvided>('product')!;
</script>

<template>
  <section id="catalog-detail-product-recommendations" class="flex flex-col gap-6">
    <section id="product-recommendations" class="flex flex-col gap-4 pt-2">
      <h1 class="font-bold font-druk text-4xl text-black tracking-[-0.03em]">Rekomendasi produk sejenis</h1>

      <section id="products" class="grid grid-rows-1 grid-cols-12 gap-6">
        <section
          v-for="(product, index) in product_listOfProductsRecommendation.data"
          id="product"
          :key="index"
          class="col-span-6 md:col-span-3"
        >
          <BaseProductCard :product="product" />
        </section>
      </section>
    </section>

    <section
      v-if="product_listOfProductsRecommendation.data.length < product_listOfProductsRecommendation.total"
      id="load-more"
      class="flex justify-center w-full cursor-pointer"
      @click="product_onLoadMoreProductsRecommendation"
    >
      <section id="content" class="flex flex-col items-center gap-3">
        <span class="font-medium text-base text-black"> Muat Lebih Banyak (+12) </span>
        <NuxtImg src="/icons/chevron-bottom.svg" alt="chevron-bottom" class="w-6 h-6" />
      </section>
    </section>
  </section>
</template>
