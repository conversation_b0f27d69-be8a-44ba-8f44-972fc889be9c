<script setup lang="ts">
const arraySkeleton = ref(new Array(4));
</script>

<template>
  <section
    id="product-colors-variants"
    class="border border-solid border-input-gray flex flex-col gap-4 rounded-lg p-4 overflow-hidden"
  >
    <section>
      <div class="flex items-center gap-4">
        <PrimeVueSkeleton class="!w-[100px] !h-[22px] mb-2" />
        <PrimeVueSkeleton class="!w-[100px] !h-[22px] mb-2" />
      </div>

      <PrimeVueTabPanels class="!px-0 !pb-0 !pt-6">
        <div class="w-full">
          <div class="mt-1">
            <table class="min-w-full table-auto border-[#ACB1B4]">
              <thead>
                <tr class="border-t-1 border-b-1 border-[#ACB1B4]">
                  <th class="py-2 px-4 text-left text-sm w-[100px]">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </th>
                  <th class="py-2 px-4 text-left text-sm w-[100px]">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </th>
                  <th class="py-2 px-4 text-left text-sm w-[120px]">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </th>
                  <th class="py-2 px-4 text-left text-sm w-[120px]">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </th>
                </tr>
              </thead>
              <tbody class="border-b border-gray-300">
                <tr v-for="(item, index) in arraySkeleton" :key="String(index)">
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </td>
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </td>
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </td>
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton class="!w-[100px] !h-[30px] mb-2" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </PrimeVueTabPanels>
    </section>

    <div class="h-[50px] flex items-center justify-center gap-4 w-full">
      <PrimeVueSkeleton class="!w-[150px] !h-[44px] mb-2" />
    </div>
    <PrimeVueSkeleton class="!w-[100px] !h-[20px] mb-2" />
  </section>
</template>
