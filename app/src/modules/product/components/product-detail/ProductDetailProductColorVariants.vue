<script setup lang="ts">
// Interfaces
import BaseInquiryForm from '~/app/src/core/components/base/BaseInquiryForm.vue';
import { useAuthenticationStore } from '../../../authentication/stores/authentication.store';
import type { ICartProvided } from '../../../cart/interfaces/cart.interface';
import { FALLBACK_PRODUCT_IMAGE } from '../../constants/product.constant';
import type { IProductProvided } from '../../interfaces/product.interface';
import type { IAuthenticationProvided } from '../../../authentication/interfaces/authentication.interface';
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
const { MIN_CUSTOM_ORDER_QTY } = inject<IConfigurationProvided>('configurations')!;

const authStore = useAuthenticationStore();

/**
 * @description Injecting dependencies
 */
const {
  product_onOpenBottomSheetGuideSizeProduct,
  product_onOpenDialogGuideSizeProduct,
  product_productDetail,
  product_selectedTabProductVariant,
  product_cartValues,
  product_variants,
  product_isLoadingProductStock,
} = inject<IProductProvided>('product')!;
const { cart_addToCartBulk, cart_getList, cart_showToast, cart_toastData, cart_addToCartLoading } =
  inject<ICartProvided>('cart')!;
const { authentication_isOpenModalLogin, authentication_isLoggedIn } = inject<IAuthenticationProvided>('auth')!;
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

const product_uniqueVariant = computed(() => {
  return product_productDetail.value?.variant.filter(
    (item, index, self) => index === self.findIndex(t => t.product_variant_c === item.product_variant_c),
  );
});

const router = useRouter();

const hasQtyChanges = ref(false);
const isOpenDialogOutOfStock = ref(false);
const isOpenModalInquiryForm = ref(false);

const hasQty = computed(() => {
  return product_variants.value.filter(v => v.stock > 0)?.length > 0;
});

const data = computed<IProduct>(() => {
  return product_productDetail?.value as IProduct;
});

const onClickAddToCart = async () => {
  if (authentication_isLoggedIn.value) {
    const result = await cart_addToCartBulk({
      sku_code_c: product_productDetail.value?.sku_code_c as string,
      items: product_cartValues.value.filter(i => i.qty > 0),
    });
    if (result) {
      cart_toastData.value.count = product_cartValues.value.filter(i => i.qty > 0).length;
      cart_toastData.value.variants = [product_selectedTabProductVariant.value];
      cart_showToast.value = true;
      setTimeout(() => {
        cart_showToast.value = false;
        cart_toastData.value.count = 0;
        cart_toastData.value.variants = [];
      }, 3000);
      cart_getList();
    }
  } else {
    authentication_isOpenModalLogin.value = true;
  }
};

const onChangeQuantity = () => {
  hasQtyChanges.value = true;
};

const disabledAddToCart = computed(() => {
  return hasQtyChanges.value === false || product_cartValues.value.filter(i => i.qty > 0)?.length === 0;
});

const onClickCustom = () => {
  if (authentication_isLoggedIn.value) {
    if (hasQty.value) {
      if (authStore.authentication_accessToken && authStore.authentication_userData?.owner_name) {
        router.push({
          name: 'product.custom.detail',
          params: {
            sku: product_productDetail.value?.sku_code_c,
          },
        });
      } else {
        const productId = product_productDetail?.value?.sku_code_c as unknown as string;
        const redirectUrl = `/product/custom/${productId}`;
        router.push({
          name: 'login',
          params: {
            redirectUrl,
          },
        });
      }
    } else {
      isOpenDialogOutOfStock.value = true;
    }
  } else {
    authentication_isOpenModalLogin.value = true;
  }
};

const onClickInquiryForm = () => {
  isOpenDialogOutOfStock.value = false;
  isOpenModalInquiryForm.value = true;
};

const onSubmitInquiryForm = () => {
  isOpenModalInquiryForm.value = false;
};

const disabledCustomize = computed(() => {
  return product_variants?.value?.every(i => i.stock === 0);
});
</script>

<template>
  <div class="my-2"><span class="text-muted">Warna</span> : {{ product_selectedTabProductVariant }}</div>
  <div class="bg-[#D4EAFF] px-3 py-3 my-4 flex gap-2 ˝text-xs items-center">
    <NuxtImg src="/icons/info-blue.svg" class="h-[19px] w-[19px]" />
    <span class="text-[#18191A] text-[14px]"> Hanya menampilkan barang dengan ukuran yang tersedia. </span>
  </div>
  <section id="product-colors-variants" class="flex flex-col gap-4 rounded-lg h-fit">
    <PrimeVueTabs v-model:value="product_selectedTabProductVariant">
      <PrimeVueTabList
        class="w-fit product-variant-img-tabs"
        :pt="{
          activeBar: '!border-white border',
          root: '!border-white border',
        }"
      >
        <PrimeVueTab
          v-for="(variant, variantIndex) in product_uniqueVariant"
          :key="`variant-${variantIndex}`"
          :value="variant.product_variant_c"
          :pt="{
            activeBar: '!border-white border',
            root: `!border-white border !py-0 !px-0 !mr-2`,
          }"
        >
          <NuxtImg
            :src="product_productDetail?.media[variantIndex]?.url ?? FALLBACK_PRODUCT_IMAGE"
            alt="product variant thumbnail"
            class="h-[48px] w-[48px] border-2 rounded"
            :class="
              product_selectedTabProductVariant == variant.product_variant_c ? 'border-black' : 'border-[#ececec]'
            "
          />
        </PrimeVueTab>
      </PrimeVueTabList>

      <PrimeVueTabPanels class="!px-0 !pb-0 !pt-6 !border-b-0">
        <div class="w-full">
          <div class="mt-1">
            <table class="min-w-full table-auto border-[#ACB1B4]">
              <thead>
                <tr class="border-t-1 border-b-1 border-[#ACB1B4]">
                  <th class="py-2 px-4 text-left text-sm w-[100px]">Size</th>
                  <th class="py-2 px-4 text-left text-sm w-[100px]">Stock</th>
                  <th class="py-2 px-4 text-left text-sm w-[120px]">Jumlah Order</th>
                  <th class="py-2 px-4 text-left text-sm w-[120px]">Harga</th>
                </tr>
              </thead>
              <tbody class="!border-none">
                <tr
                  v-for="(item, index) in product_cartValues?.filter(
                    v => v.color === product_selectedTabProductVariant,
                  )"
                  :key="String(index)"
                >
                  <td class="py-2 px-4 text-left text-sm">{{ item.product_size_c }}</td>
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton v-if="product_isLoadingProductStock" class="!w-[90px] !h-[22px] mb-2" />
                    <span v-else>
                      {{ item.stock }}
                    </span>
                  </td>
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton v-if="product_isLoadingProductStock" class="!w-[90px] !h-[22px] mb-2" />
                    <PrimeVueInputNumber
                      v-else
                      v-model="item.qty"
                      input-id="horizontal-buttons"
                      show-buttons
                      button-layout="horizontal"
                      :step="1"
                      :min="0"
                      :max="item.stock"
                      fluid
                      class="input-cart-qty !w-fit"
                      :pt="{
                        pcInputText: {
                          root: '!min-w-[24px] !text-center !text-[12px] !font-semibold !p-0',
                        },
                      }"
                      @update:model-value="onChangeQuantity"
                    >
                      <template #decrementicon>
                        <NuxtImg src="/icons/minus-flat-black.svg" alt="plus" class="w-4 h-4" />
                      </template>
                      <template #incrementicon>
                        <NuxtImg src="/icons/plus-flat-black.svg" alt="plus" class="w-4 h-4" />
                      </template>
                    </PrimeVueInputNumber>
                  </td>
                  <td class="py-2 px-4 text-left text-sm">
                    <PrimeVueSkeleton v-if="product_isLoadingProductStock" class="!w-[90px] !h-[22px] mb-2" />
                    <span v-else>
                      {{ item?.price ? useCurrencyFormat(parseInt(item.price)) : '-' }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </PrimeVueTabPanels>
      <div class="sm:flex items-center gap-1 mt-5 hidden">
        <NuxtImg class="h-[15px] w-[15px]" src="/icons/color-swatch.svg" />
        <PrimeVueButton
          label="Panduan Ukuran"
          variant="link"
          class="!text-sm font-medium !text-black underline !w-fit !p-0"
          @click="product_onOpenDialogGuideSizeProduct"
        />
      </div>
      <div class="flex items-center gap-1 mt-5 sm:hidden">
        <NuxtImg class="h-[15px] w-[15px]" src="/icons/color-swatch.svg" />
        <PrimeVueButton
          label="Panduan Ukuran"
          variant="link"
          class="!text-sm font-medium !text-black underline !w-fit !p-0"
          @click="product_onOpenBottomSheetGuideSizeProduct"
        />
      </div>
      <div class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 mt-3">
        <div class="bg-header-orange rounded-full">
          <NuxtImg src="/icons/free-delivery.svg" class="!w-[3.5em] !h-[2.2em] aspect-square self-start" />
        </div>
        <div class="text-xs">
          Free ongkir untuk pembelian minimal
          <b>{{ MIN_CUSTOM_ORDER_QTY }} Pcs atau nominal transaksi Rp 10 juta </b> di wilayah
          <b> Jawa dan Bali </b>
        </div>
      </div>
    </PrimeVueTabs>

    <BaseLoaderBoxed v-if="cart_addToCartLoading" size="xs" :height="50" />
    <div v-else class="w-full">
      <PrimeVueButton
        type="button"
        size="large"
        class="!bg-white !rounded-lg !w-full !border-gray-800"
        :disabled="disabledAddToCart"
        :class="{
          '!bg-gray-400 !text-white': disabledAddToCart,
          '!bg-black': !disabledAddToCart,
        }"
        @click="onClickAddToCart"
      >
        <template #default>
          <div id="content" class="flex items-center justify-center gap-2">
            <span class="font-medium text-base text-center text-black w-full"> Tambah Keranjang </span>
          </div>
        </template>
      </PrimeVueButton>
    </div>

    <template v-if="data?.specification?.is_custom_logo">
      <PrimeVueDivider align="center" type="solid" class="!my-0">
        <span class="text-[16px] text-[#686F72]">Custom For Your Journey</span>
      </PrimeVueDivider>

      <PrimeVueButton
        :disabled="disabledCustomize"
        type="button"
        size="large"
        class="!rounded-lg !w-full"
        :class="
          disabledCustomize ? '!border-gray-800 !bg-black !cursor-not-allowed' : ' !border-gray-800 !bg-black'
        "
        @click="onClickCustom"
      >
        <template #default>
          <div id="content" class="flex items-center justify-center gap-2">
            <span class="font-medium text-base text-center text-white w-full"> Kustomisasi Produk </span>
          </div>
        </template>
      </PrimeVueButton>
    </template>
  </section>
  <section class="mt-10 sm:hidden block">
    <PrimeVueAccordion value="0">
      <PrimeVueAccordionPanel value="0">
        <PrimeVueAccordionHeader class="!text-black">Tentang Produk</PrimeVueAccordionHeader>
        <PrimeVueAccordionContent>
          <p class="m-0">
            {{ data?.product_description ? data?.product_description : '-' }}
          </p>
        </PrimeVueAccordionContent>
      </PrimeVueAccordionPanel>
      <PrimeVueAccordionPanel value="1">
        <PrimeVueAccordionHeader class="!text-black">Detail Teknis</PrimeVueAccordionHeader>
        <PrimeVueAccordionContent>
          <div class="flex flex-col">
            <div class="py-2 px-1 flex justify-between hover:bg-gray-50 hover:border-y border-gray-200">
              <span class="text-muted">Ukuran</span>
              <span class="font-bold text-black">{{
                data?.specification?.dimension ? data?.specification?.dimension : '-'
              }}</span>
            </div>
            <div class="py-2 px-1 flex justify-between hover:bg-gray-50 hover:border-y border-gray-200">
              <span class="text-muted">Berat</span>
              <span class="font-bold text-black"
                >{{ data?.specification?.weight ? data?.specification?.weight : '-' }}
                {{ data?.specification?.uomweight ? data?.specification?.uomweight : '-' }}</span
              >
            </div>
            <div class="py-2 px-1 flex justify-between hover:bg-gray-50 hover:border-y border-gray-200">
              <span class="text-muted">Material</span>
              <span class="font-bold text-black">{{ data?.specification?.material }}</span>
            </div>
          </div>
        </PrimeVueAccordionContent>
      </PrimeVueAccordionPanel>
      <PrimeVueAccordionPanel value="2">
        <PrimeVueAccordionHeader class="!text-black">Fitur</PrimeVueAccordionHeader>
        <PrimeVueAccordionContent>
          <div class="">
            {{ data?.product_feature ? data?.product_feature : '-' }}
          </div>
        </PrimeVueAccordionContent>
      </PrimeVueAccordionPanel>
    </PrimeVueAccordion>

    <!-- Dialog request other sized -->
    <PrimeVueDialog
      v-model:visible="isOpenDialogOutOfStock"
      modal
      class="!relative h-fit w-full md:w-[400px] modal-super"
      :pt="{
        root: '!p-0',
        header: '!hidden',
        content: '!px-5 !pt-6',
      }"
      :draggable="false"
    >
      <template #default>
        <section class="flex flex-col">
          <div class="mx-auto mb-4">
            <NuxtImg src="/images/info-black-icon.png" class="h-[72px] w-[72px]" alt="info icon" />
          </div>
          <p v-if="!config_hideInquiryForm" class="text-[16px] text-[#18191A] mb-5">
            Maaf stok tidak tersedia silahkan mengisi inquiry form untuk melanjutkan pesanan.
            <span class="text-[#086bff] font-semibold underline cursor-pointer" @click="onClickInquiryForm">
              Inquiry form</span
            >
          </p>
          <PrimeVueButton
            type="button"
            size="large"
            class="!rounded-lg !w-full !border-none !bg-black !text-white"
            @click="isOpenDialogOutOfStock = false"
          >
            <template #default>
              <div id="content" class="flex items-center justify-center gap-2">
                <span class="font-medium text-base text-center text-white w-full"> Tutup </span>
              </div>
            </template>
          </PrimeVueButton>
        </section>
      </template>
    </PrimeVueDialog>

    <!-- Modal inquiry form -->
    <PrimeVueDialog
      v-if="!config_hideInquiryForm"
      v-model:visible="isOpenModalInquiryForm"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :pt="{
        header: '!px-5 sm:!px-10',
        content: '!px-5 sm:!px-10',
      }"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-druk text-[28px] md:text-[42px] text-black font-bold">B2B Inquiry Form</h4>
        </header>
      </template>
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDialog>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-tablist-tab-list) {
  border: none !important;
  padding-bottom: 2px !important;
}
</style>
