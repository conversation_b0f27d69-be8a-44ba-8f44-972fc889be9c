<script setup lang="ts">
// Constants
import { PRODUCT_LIST_OF_COLUMNS_PRODUCT_VARIANT } from '../../constants/product.constant';

// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { product_productDetail } = inject<IProductProvided>('product')!;
</script>

<template>
  <div>
    <PrimeVueDataTable :value="product_productDetail?.variant" table-style="min-width: 200px">
      <PrimeVueColumn
        v-for="(column, columnIndex) in PRODUCT_LIST_OF_COLUMNS_PRODUCT_VARIANT"
        :key="`column-${columnIndex}`"
        :field="column.key"
        :header="column.label"
      >
        <template v-if="column.label === 'Jumlah Order'" #body="{ data }">
          <PrimeVueInputNumber
            v-model="data.cart.count"
            input-id="horizontal-buttons"
            show-buttons
            button-layout="horizontal"
            :step="1"
            :min="1"
            :max="item.stock"
            fluid
            class="!w-fit"
            :pt="{
              pcInputText: {
                root: '!max-w-10',
              },
            }"
          >
            <template #decrementicon>
              <NuxtImg src="/icons/minus-flat-black.svg" alt="plus" class="w-4 h-4" />
            </template>
            <template #incrementicon>
              <NuxtImg src="/icons/plus-flat-black.svg" alt="plus" class="w-4 h-4" />
            </template>
          </PrimeVueInputNumber>
        </template>

        <template v-else-if="column.label === 'Harga'" #body="{ data }">
          <span class="text-sm text-black">
            {{ useCurrencyFormat(+data[column.key].amount) }}
          </span>
        </template>

        <template v-else #body="{ data }">
          <span class="text-sm text-black">
            {{ data[column.key] }}
          </span>
        </template>
      </PrimeVueColumn>
    </PrimeVueDataTable>
  </div>
</template>
