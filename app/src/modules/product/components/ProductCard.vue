<script setup lang="ts">
import type { IProductItem } from '../interfaces/product.interface';

/**
 * @description Define the props interface
 */
interface IProps {
  product: IProductItem;
}

/**
 * @description Define props with default values and interfaces
 */
const props = defineProps<IProps>();

/**
 * @description router
 */
const displayImage = ref<string | null>(props.product.image);

const outOffStock = computed(() => props.product.stock === 0);

const onClickDetail = () => {
  window.location.href = `/product/detail/${props.product.sku}`;
};
</script>

<template>
  <section
    :id="'product-card-' + product.id"
    class="flex flex-col w-full h-fit rounded-lg gap-3 border border-[#F0F2F4]"
  >
    <div class="px-4 py-4">
      <div class="w-full h-full max-h-80 rounded-lg relative flex items-center justify-center mb-3">
        <NuxtImg
          :src="(displayImage as string)"
          alt="product"
          class="w-full h-full max-h-80 rounded"
          :class="[outOffStock && 'opacity-70']"
          loading="lazy"
          @click="onClickDetail"
        />
        <div v-if="outOffStock" class="absolute bg-[#18191A] text-[#fbfbfb] rounded-lg px-3 py-2.5 opacity-70">
          <span class="text-[14px]">Stok Tidak Ada</span>
        </div>
      </div>
      <section id="information" class="flex flex-col">
        <div class="flex gap-3 mb-3">
          <div class="bg-[#05964C] rounded text-white leading-none px-3 py-2 text-sm">PLN</div>
          <div class="bg-[#3B3C3E] rounded text-white leading-none px-3 py-2 text-sm">Kustom</div>
        </div>
        <div class="h-[22px] overflow-hidden truncate">
          <h6 class="font-medium text-base text-black">
            {{ product.name }}
          </h6>
        </div>
      </section>
    </div>
  </section>
</template>
