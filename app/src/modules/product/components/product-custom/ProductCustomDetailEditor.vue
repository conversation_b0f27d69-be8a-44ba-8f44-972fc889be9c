<script setup lang="ts">
// Components
import { fabric } from 'fabric';
import CanvasService from '../../services/canvas.service';
import type { IEditorProvided, IEditorSelectedLayer } from '../../interfaces/editor.interface';
import type { IEditorCanvasContext } from '../../services/canvas.service.js';
import type { IProductProvided } from '../../interfaces/product.interface.js';

/**
 * @description Injecting dependencies
 */
// const { product_customActiveMainImageUrl } = inject<IProductProvided>('product')!;

/**
 * @description Injecting dependencies
 */
const { product_detailCustomData } = inject<IProductProvided>('product')!;
const {
  editor_selectedLayer,
  controlTab,
  setSelectedLayer,
  syncLayers,
  editor_getSelectedLayerInitialProps,
  editor_backgroundLayers,
  editor_isLoading,
  editor_width,
  editor_currentBackgroundImageId,
} = inject<IEditorProvided>('editor')!;
const ctx: IEditorCanvasContext = inject<IEditorProvided>('ctx') as unknown as IEditorCanvasContext;

// The important part: the name of the variable needs to be equal to the ref's name of the canvas element in the template
const canvasRef = ref<HTMLCanvasElement | null>(null);

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const handleLayerSelection = event => {
  const layer: IEditorSelectedLayer = event.selected[0];
  if (editor_selectedLayer.value.layerId != layer.layerId) {
    setSelectedLayer(layer);
    controlTab.value = 'object';
  }
};

const onClickCapture = () => {
  const findBackgroundImageUrl = editor_backgroundLayers.value.find(
    bg => bg.backgroundId === editor_currentBackgroundImageId.value,
  );

  CanvasService.capture(
    editor_currentBackgroundImageId.value,
    findBackgroundImageUrl?.imageUrl as string,
    product_detailCustomData.value?.product_name_c as string,
  );
};

const onClickCanvas = () => {
  // editor_isOpenBottomSheetToolbar.value = true;
};

onMounted(() => {
  ctx.canvas = new fabric.Canvas(canvasRef.value, {
    selection: false,
    width: editor_width.value,
    height: editor_width.value,
    allowTouchScrolling: true,
  });

  // call when background clicked
  ctx.canvas.on('before:selection:cleared', () => {
    editor_selectedLayer.value = editor_getSelectedLayerInitialProps();
  });

  ctx.canvas.on('selection:created', event => {
    console.log("on('selection:created");
    handleLayerSelection(event);
  });

  ctx.canvas.on('selection:updated', event => {
    console.log("on('selection:updated");
    handleLayerSelection(event);
  });

  ctx.canvas.on('object:modified', event => {
    if (event.action === 'scale' || event.action === 'rotate') {
      editor_selectedLayer.value.dirty = true;
    }
  });

  ctx.canvas.on('object:added', () => syncLayers(ctx.canvas.getObjects() as unknown as IEditorSelectedLayer[]));

  ctx.canvas.on('object:removed', () => syncLayers(ctx.canvas.getObjects() as unknown as IEditorSelectedLayer[]));

  ctx.canvas.on('mouse:dblclick', ({ target }) => {
    if (target && target.type === 'image') {
      // CanvasService.prepareCrop(target);
    }
  });

  document.addEventListener('keydown', e => {
    if (['Delete', 'Backspace'].includes(e.key) && editor_selectedLayer.value.layerId) {
      // if (document.querySelector('.layer-text-field')?.matches(':focus')) return;
      CanvasService.removeLayer(editor_selectedLayer.value);
    }
  });
});
</script>

<template>
  <section :id="`product-detail-custom-editor`" class="relative w-full mb-6">
    <BaseLoaderBoxed v-if="editor_isLoading" :height="editor_width" :style="`width:${editor_width}`" />
    <div
      class="flex flex-center canvas-wrapper"
      :style="`visibility:${editor_isLoading ? 'hidden' : 'visible'}`"
      @click="onClickCanvas"
    >
      <canvas ref="canvasRef"></canvas>
    </div>

    <div class="hidden sm:flex mt-2 bottom-[40px] w-full items-center justify-center">
      <div class="bg-[#FFF7EC] rounded p-2 text-center">
        <p class="text-[12px] md:text-[14px]">
          Proses kustomisasi ini hanya sebagai ilustrasi. Untuk detail informasi lebih lanjut, tim sales akan
          menghubungi Anda.
        </p>
      </div>
    </div>

    <!-- floating capture button -->
    <div v-if="!editor_isLoading" class="hidden absolute !bottom-4 !right-4">
      <PrimeVueButton variant="text" rounded class="!bg-[#3366FF]" @click="onClickCapture">
        <template #icon>
          <NuxtImg src="/icons/capture-icon.svg" class="h-[24px] w-[24px]" />
        </template>
      </PrimeVueButton>
    </div>
  </section>
</template>

<style lang="css"></style>
