<script setup lang="ts">
// Interfaces
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { product_openBottomSheetProductCustom } = inject<IProductProvided>('product')!;
const { MIN_CUSTOM_ORDER_AMOUNT, MIN_CUSTOM_ORDER_QTY } = inject<IConfigurationProvided>('configurations')!;
</script>

<template>
  <section id="dialog-guide-size-product" class="relative inset-0 z-0">
    <PrimeVueDrawer
      v-model:visible="product_openBottomSheetProductCustom"
      position="bottom"
      class="!h-[372px] !rounded-t-2xl"
      :pt="{
        header: '!pt-2 !pb-2 !px-6 !mt-5 !border-b !border-b-[#DCE0E4]',
        content: '!px-6 !pt-4',
      }"
    >
      <template #closeicon>
        <PrimeVueButton variant="text">
          <template #icon>
            <NuxtImg src="/icons/close-circle-icon.svg" class="!h-[22px] !w-[22px]" alt="close icon" />
          </template>
        </PrimeVueButton>
      </template>

      <template #header>
        <header class="flex items-center justify-between">
          <!-- Handler -->
          <div class="absolute top-0 left-0 w-full flex items-center justify-center h-[30px]">
            <div class="h-[4px] w-[72px] rounded-md bg-[#E5E5E5]"></div>
          </div>

          <div class="flex items-center justify-between">
            <h4 class="font-bold font-druk text-[22px]">Pemesanan Kustom</h4>
          </div>
        </header>
      </template>

      <template #default>
        <section class="dialog-custom-content flex flex-col">
          <p class="text-[14px] text-[#686F72] mb-4">Berikut syarat untuk memesan produk kustomisasi yaitu</p>
          <ul class="pl-5">
            <li>
              <p v-if="MIN_CUSTOM_ORDER_AMOUNT" class="text-[#686F72] text-[14px] mb-1">
                Nilai pesanan minimum <strong>{{ useCurrencyFormat(MIN_CUSTOM_ORDER_AMOUNT) }}</strong>
              </p>
            </li>
            <li>
              <p class="text-[#686F72] text-[14px] mb-1">
                Estimasi pengerjaan pemesaanan kustom adalah <strong>x minggu</strong> setelah pemesanan
              </p>
            </li>
            <li>
              <p class="text-[#686F72] text-[14px] mb-1">
                Permintaan ukuran khusus dapat dilakukan untuk pesanan minimum
                <strong>1000</strong> pcs
              </p>
            </li>
          </ul>

          <div class="pt-8">
            <PrimeVueButton
              type="button"
              size="large"
              class="!rounded-lg !w-full mb-3 !border-none !bg-black !text-white"
              @click="product_openBottomSheetProductCustom = false"
            >
              <template #default>
                <div id="content" class="flex items-center justify-center gap-2">
                  <span class="font-medium text-base text-center text-white w-full"> Mengerti </span>
                </div>
              </template>
            </PrimeVueButton>
          </div>
        </section>
      </template>
    </PrimeVueDrawer>
  </section>
</template>

<style lang="css" scoped>
.dialog-custom-content ul {
  list-style-type: numeric;
}
</style>
