<script setup lang="ts">
import { PRODUCT_CUSTOM_EDIT_MODE } from '../../constants/product.constant';
import type { IEditorProvided } from '../../interfaces/editor.interface';
import type { IProductProvided } from '../../interfaces/product.interface';

const router = useRouter();

/**
 * @description Injecting dependencies
 */
const {
  editor_mode,
  editor_canFinishEdit,
  editor_isOpenBottomSheetToolbar /*, captureAllBackgroundLayers, editor_isOpenBottomSheetToolbar */,
} = inject<IEditorProvided>('editor')!;
const { product_detailCustomData } = inject<IProductProvided>('product')!;

const onClickBack = () => {
  if (editor_mode.value === PRODUCT_CUSTOM_EDIT_MODE.ADD_TO_CART) {
    editor_mode.value = PRODUCT_CUSTOM_EDIT_MODE.EDIT;
  } else {
    // router.back();
    router.push({
      name: 'product.detail',
      params: {
        sku: product_detailCustomData.value?.sku_code_c as string,
      },
    });
  }
};

const onClickFinish = () => {
  editor_mode.value = PRODUCT_CUSTOM_EDIT_MODE.ADD_TO_CART;
  editor_isOpenBottomSheetToolbar.value = true;
  // captureAllBackgroundLayers().finally(() => (editor_isOpenBottomSheetToolbar.value = true));
};
</script>

<template>
  <div class="w-full sm:w-[150px] flex items-center absolute z-50">
    <div class="hidden sm:flex">
      <PrimeVueButton
        class="!bg-white !border-none !text-black !text-base !font-medium !rounded-full mt-[60px] custom-shadow-lg"
        @click="onClickBack"
      >
        <NuxtImg src="/icons/arrow-back.svg" alt="question-mark" class="w-[18px] h-[18px]" />
        <span class="hidden md:block text-[14px]">Kembali</span>
      </PrimeVueButton>
    </div>
    <div class="w-full flex sm:hidden mt-[50px]">
      <div class="w-full flex items-center justify-between">
        <button
          class="cursor-pointer bg-white !rounded-full border-2 border-[#E5E6E8] h-[40px] w-[40px] flex gap-2 items-center justify-center opacity-80"
          @click="onClickBack"
        >
          <NuxtImg src="/icons/arrow-back.svg" alt="question-mark" class="w-[18px] h-[18px]" />
        </button>
        <div class="ml-auto">
          <button
            :disabled="!editor_canFinishEdit"
            class="bg-white !rounded-full border-2 border-[#E5E6E8] h-[40px] px-4 flex gap-2 items-center justify-center"
            :class="[editor_canFinishEdit ? 'opacity-80 cursor-pointer' : 'opacity-30 cursor-not-allowed']"
            @click="onClickFinish"
          >
            <NuxtImg src="/icons/check-icon.svg" alt="check icon" class="w-[18px] h-[18px]" />
            <span class="text-[14px] font-semibold">Selesai</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
