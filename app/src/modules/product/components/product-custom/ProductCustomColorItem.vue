<script setup lang="ts">
import { PRODUCT_COLOR_VALUES } from '../../constants/product.constant';

interface Props {
  color: string;
}

defineProps<Props>();

const getColor = (variantName: string): string => {
  const matchColor = PRODUCT_COLOR_VALUES?.[variantName];
  return matchColor ?? '#3b3b3b';
};
</script>

<template>
  <div class="flex items-center justify-center h-[48px] w-[48px] rounded bg-white p-1">
    <div class="h-[40px] w-[40px]" :style="`background-color: ${getColor(color)}`"></div>
  </div>
</template>
