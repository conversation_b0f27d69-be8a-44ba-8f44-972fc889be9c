<script setup lang="ts">
import { NuxtImg } from '#components';
import type { IAuthenticationProvided } from '../../../authentication/interfaces/authentication.interface';
import { useAuthenticationStore } from '../../../authentication/stores/authentication.store';
import type { ICartProvided } from '../../../cart/interfaces/cart.interface';
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
import type { IProductProvided } from '../../interfaces/product.interface';
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

const router = useRouter();

const authStore = useAuthenticationStore();

const isOpenDialogOutOfStock = ref(false);
const isOpenModalInquiryForm = ref(false);

/**
 * @description Injecting dependencies
 */
const { authentication_isOpenModalLogin, authentication_isLoggedIn } = inject<IAuthenticationProvided>('auth')!;
const { product_variants, product_productDetail, product_cartValues, product_selectedTabProductVariant } =
  inject<IProductProvided>('product')!;
const { cart_addToCartBulk, cart_getList, cart_showToast, cart_toastData } = inject<ICartProvided>('cart')!;

const hasQty = computed(() => {
  return product_variants.value.filter(v => v.stock > 0)?.length > 0;
});

const onClickCustom = () => {
  if (authentication_isLoggedIn.value) {
    if (hasQty.value) {
      if (authStore.authentication_accessToken && authStore.authentication_userData?.owner_name) {
        router.push({
          name: 'product.custom.detail',
          params: {
            sku: product_productDetail.value?.sku_code_c,
          },
        });
      } else {
        const productId = product_productDetail?.value?.sku_code_c as unknown as string;
        const redirectUrl = `/product/custom/${productId}`;
        router.push({
          name: 'login',
          params: {
            redirectUrl,
          },
        });
      }
    } else {
      isOpenDialogOutOfStock.value = true;
    }
  } else {
    authentication_isOpenModalLogin.value = true;
  }
};

const onClickAddToCart = async () => {
  if (authentication_isLoggedIn.value) {
    const result = await cart_addToCartBulk({
      sku_code_c: product_productDetail.value?.sku_code_c as string,
      items: product_cartValues.value.filter(i => i.qty > 0),
    });
    if (result) {
      cart_toastData.value.count = product_cartValues.value.filter(i => i.qty > 0).length;
      cart_toastData.value.variants = [product_selectedTabProductVariant.value];
      cart_showToast.value = true;
      setTimeout(() => {
        cart_showToast.value = false;
        cart_toastData.value.count = 0;
        cart_toastData.value.variants = [];
      }, 3000);
      cart_getList();
    }
  } else {
    authentication_isOpenModalLogin.value = true;
  }
};

const onSubmitInquiryForm = () => {
  isOpenModalInquiryForm.value = false;
};

const disabledAddToCart = computed(() => {
  return product_cartValues.value.filter(i => i.qty > 0)?.length === 0;
});

const disabledCustomize = computed(() => {
  return product_variants?.value?.every(i => i.stock === 0);
});
</script>

<template>
  <div class="flex sm:hidden fixed bottom-0 left-0 w-full z-50 bg-white px-6 py-4 custom-shadow-lg">
    <div class="w-full grid grid-cols-[52px_1fr] gap-3">
      <PrimeVueButton
        :disabled="disabledAddToCart"
        type="button"
        size="large"
        class="!rounded-[8px] !px-0 !py-0 !w-[48px] !h-[48px] !bg-white !border-[#E5E6E8]"
        :class="{
          '!opacity-50': disabledAddToCart,
          '!opacity-100': !disabledAddToCart,
        }"
        @click="onClickAddToCart"
      >
        <NuxtImg src="/icons/cart-black.svg" class="h-[18px] w-[18px]" alt="cart icons" />
      </PrimeVueButton>
      <PrimeVueButton
        :disabled="disabledCustomize"
        type="button"
        size="large"
        class="!rounded-[8px] !w-full !h-[48px] !border-none"
        :class="disabledCustomize ? '!bg-black !cursor-not-allowed' : '!bg-black'"
        @click="onClickCustom"
      >
        <template #default>
          <div id="content" class="flex items-center justify-center gap-2">
            <span class="font-medium text-base text-center text-white w-full"> Kustomisasi Produk </span>
          </div>
        </template>
      </PrimeVueButton>
    </div>

    <!-- Modal inquiry form -->
    <PrimeVueDialog
      v-if="!config_hideInquiryForm"
      v-model:visible="isOpenModalInquiryForm"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :pt="{
        header: '!px-5 sm:!px-10',
        content: '!px-5 sm:!px-10',
      }"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-druk text-[28px] md:text-[42px] text-black font-bold">B2B Inquiry Form</h4>
        </header>
      </template>
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDialog>
  </div>
</template>
