<script setup lang="ts">
// Interfaces
import type { IProductProvided } from '../../interfaces/product.interface';

/**
 * @description Injecting dependencies
 */
const { product_detailCustomData: data } = inject<IProductProvided>('product')!;
</script>

<template>
  <section v-if="data" id="catalog-detail-product-header" class="flex flex-col gap-3">
    <BaseBreadcrumb />
    <header class="flex flex-col gap-1">
      <section id="title" class="flex items-center gap-2">
        <h1 class="font-bold font-druk text-4xl text-black tracking-[-0.03em]">
          {{ data?.product_name_c }}
        </h1>
      </section>
      <span id="price" class="font-bold text-xl text-black">
        {{ useCurrencyFormat(+data.product_price.amount) }}
      </span>
    </header>
  </section>
</template>
