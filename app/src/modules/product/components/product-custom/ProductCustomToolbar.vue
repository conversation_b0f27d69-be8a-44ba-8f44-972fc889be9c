<script setup lang="ts">
// Components
import { formatSizeUnits, makeId } from '~/app/src/core/helpers/text.helper';
import type { IProductProvided } from '../../interfaces/product.interface';

// components
import ProductCustomColorItem from './ProductCustomColorItem.vue';

// constants
import {
  FALLBACK_PRODUCT_IMAGE,
  PRODUCT_CUSTOM_EDIT_MODE,
  PRODUCT_CUSTOM_TEXT_COLORS,
  PRODUCT_CUSTOM_TYPE,
} from '../../constants/product.constant';

import CanvasService from '../../services/canvas.service';
import type { IEditorBackgroundLayer, IEditorProvided } from '../../interfaces/editor.interface';
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

/**
 * @description Injecting dependencies
 */
const { product_customSelectedTabProductVariant, product_detailCustomData } = inject<IProductProvided>('product')!;

/**
 * @description Injecting dependencies
 */
const {
  editor_currentBackgroundImageId,
  editor_backgroundLayers,
  editor_isLoading,
  editor_selectedCustomType,
  editor_listOfImageAssets,
  editor_listOfTextAssets,
  editor_canFinishEdit,
  editor_mode,
  editor_selectedTextColor,
  editor_selectedLayer,
  layers,
} = inject<IEditorProvided>('editor')!;

const toast = useToast();

const inputRef = ref(null);
const input = undefined;
const isDragging = ref(false);
const uploadedCurrentFile = ref<File | null>(null);
// const previewFile = ref<string | ArrayBuffer | null>(null);
const maxFileSize = ref<number>(1e6);
const acceptFile = ref<string>('.jpg, .png, .jpeg');
const tabCustomTypes = ref([PRODUCT_CUSTOM_TYPE.IMAGE, PRODUCT_CUSTOM_TYPE.TEXT]);
const isOpenModalInquiryForm = ref(false);

// default black
const customTextValue = ref('');

const product_uniqueVariant = computed(() => {
  return product_detailCustomData.value?.variant.filter(
    (item, index, self) => index === self.findIndex(t => t.product_variant_c === item.product_variant_c),
  );
});

const isValidFile = async (file: File, showToast = true) => {
  if (file?.size && file.size > maxFileSize.value) {
    if (showToast) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'File lebih dari 2MB',
        life: 3000,
      });
    }
    return false;
  }

  // File type checking
  if (!acceptFile.value.split(',').some(ext => file.name.includes(ext?.trim()))) {
    if (showToast) {
      toast.add({
        severity: 'error',
        summary: 'Success',
        detail: 'File tidak sesuai.',
        life: 3000,
      });
    }
    return false;
  }

  return true;
};

const onClickUpload = () => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  inputRef?.value?.click();
};

const onDragEnter = () => {
  isDragging.value = true;
};

const onDragLeave = () => {
  isDragging.value = false;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onDragover = e => {
  e.preventDefault();
  isDragging.value = true;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onDrop = async e => {
  const file = e.dataTransfer?.files?.[0];
  if (file) {
    if (!(await isValidFile(file))) return;

    const reader = new FileReader();
    reader.onload = e => {
      if (e?.target?.result) {
        const mergedImageAssets = [...editor_listOfImageAssets.value];
        mergedImageAssets.push({ img: e.target.result, file, imageId: makeId() });
        editor_listOfImageAssets.value = mergedImageAssets;
      }
    };
    reader.readAsDataURL(file);
  }
  uploadedCurrentFile.value = file;
  isDragging.value = false;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onFileChange = async e => {
  const file = e.target.files[0];

  if (file) {
    if (!(await isValidFile(file))) return;

    const reader = new FileReader();

    reader.onload = e => {
      if (e?.target?.result) {
        const mergedImageAssets = [...editor_listOfImageAssets.value];
        mergedImageAssets.push({ img: e.target.result, file, imageId: makeId() });
        editor_listOfImageAssets.value = mergedImageAssets;
      }
    };
    reader.readAsDataURL(file);
    uploadedCurrentFile.value = file;
  }
};

const onSelectTextColor = (color: string) => {
  editor_selectedTextColor.value = color;

  if (editor_selectedLayer.value.layerType == 'text' && editor_selectedLayer.value.fill !== color) {
    CanvasService.updateTextLayer(editor_selectedLayer.value, color);
  }
};

const onSelectVariantProduct = (bgLayer: IEditorBackgroundLayer) => {
  editor_currentBackgroundImageId.value = bgLayer.backgroundId;
  CanvasService.drawSelectedModel(bgLayer.imageUrl);
  // CanvasService.changeBackgroundLayer(bgLayer.imageUrl);
  CanvasService.changeMode(bgLayer.backgroundId);

  editor_isLoading.value = true;

  setTimeout(() => {
    editor_isLoading.value = false;
  }, 750);
};

const onSelectVariantColor = (variant: string) => {
  product_customSelectedTabProductVariant.value = variant;
};

const onSubmitCustomText = () => {
  const val = customTextValue.value;
  if (val) {
    const isAlreadyInHistory = editor_listOfTextAssets.value.find(i => i === val);

    if (!isAlreadyInHistory) {
      const data = Array.from([val, ...editor_listOfTextAssets.value]).slice(0, 10);
      editor_listOfTextAssets.value = data;
    }

    customTextValue.value = '';
  }
};

const onClickRemoveTextItem = (text: string) => {
  const filteredCustomText = editor_listOfTextAssets.value.filter(t => t !== text);
  editor_listOfTextAssets.value = filteredCustomText;

  const selectedLayers = layers.value.filter(layer => layer.text === text);

  selectedLayers.forEach(layer => {
    CanvasService.removeLayer(layer);
  });

  // CanvasService.removeLayer(editor_selectedLayer.value);
};

const onClickRemoveImageAsset = (imageId: string) => {
  const newImageAssets = editor_listOfImageAssets.value.filter(i => i.imageId !== imageId);
  editor_listOfImageAssets.value = newImageAssets;

  const selectedLayers = layers.value.filter(layer => layer.imageId === imageId);

  selectedLayers.forEach(layer => {
    CanvasService.removeLayer(layer);
  });
};

const onClickAddImageToLayer = (imgUrl: string, imageId: string) => {
  CanvasService.addImageLayer(imgUrl, imageId, editor_currentBackgroundImageId.value);

  const findIndex = editor_backgroundLayers.value.findIndex(
    i => i.backgroundId === editor_currentBackgroundImageId.value,
  );
  editor_backgroundLayers.value[findIndex].hasLayers = true;
};

const onClickAddTextToLayer = (text: string) => {
  const textLabel = PRODUCT_CUSTOM_TEXT_COLORS.find(item => item.value === editor_selectedTextColor.value)
    ?.label as string;
  CanvasService.addTextLayer(
    text,
    textLabel,
    editor_selectedTextColor.value,
    editor_currentBackgroundImageId.value,
  );
  const findIndex = editor_backgroundLayers.value.findIndex(
    i => i.backgroundId === editor_currentBackgroundImageId.value,
  );
  editor_backgroundLayers.value[findIndex].hasLayers = true;
};

const onClickFinish = () => {
  editor_mode.value = PRODUCT_CUSTOM_EDIT_MODE.ADD_TO_CART;
};

const onSubmitInquiryForm = () => {
  isOpenModalInquiryForm.value = false;
};

const onClickContactUs = () => {
  isOpenModalInquiryForm.value = true;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onChangeCustomInputText = e => {
  // const cleaned = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
  // customTextValue.value = cleaned;
  const cleaned = e.target.value;
  if (cleaned) {
    customTextValue.value = cleaned;
  }
};

const onClickAddText = () => {
  onSubmitCustomText();
};
</script>

<template>
  <section :style="{ display: editor_mode === PRODUCT_CUSTOM_EDIT_MODE.EDIT ? 'block' : 'none' }">
    <!-- product variant media -->
    <section id="product-image-variants" class="flex flex-col gap-4 rounded-lg h-fit mb-5">
      <div class="flex flex-col gap-4 rounded-lg h-fit mb-2">
        <div class="w-full flex flex-wrap gap-2">
          <button
            v-for="(background, index) in editor_backgroundLayers"
            :key="index"
            class="cursor-pointer flex items-center justify-center rounded h-[48px] w-[48px] overflow-hidden border-2 bg-white !p-0"
            :class="
              editor_currentBackgroundImageId === background.backgroundId ? 'border-black' : 'border-[#e8e8e8]'
            "
            @click="onSelectVariantProduct(background)"
          >
            <NuxtImg
              :src="background?.imageUrl ?? FALLBACK_PRODUCT_IMAGE"
              alt="product background thumbnail"
              class="h-[44px] w-[44px]"
            />
          </button>
        </div>
      </div>
    </section>

    <!------------- Product variant colors ------------>
    <section id="product-variant-colors" class="flex flex-col rounded-lg h-fit">
      <p class="text-[14px] mb-3">
        <span class="text-muted">Warna</span> :
        <span text="text-black"> {{ product_customSelectedTabProductVariant }}</span>
      </p>
      <div class="flex flex-col gap-4 rounded-lg h-fit mb-2">
        <div class="w-full flex gap-2">
          <button
            v-for="(variant, index) in product_uniqueVariant"
            :key="index"
            class="cursor-pointer flex items-center justify-center rounded h-[48px] w-[48px] overflow-hidden border-2 bg-white !p-0"
            :class="
              product_customSelectedTabProductVariant === variant.product_variant_c
                ? 'border-black'
                : 'border-[#e8e8e8]'
            "
            @click="onSelectVariantColor(variant.product_variant_c)"
          >
            <ProductCustomColorItem :color="variant.product_variant_c" />
          </button>
        </div>
      </div>
    </section>

    <!------------- Product custom types ------------>
    <section id="product-custom-types" class="flex flex-col gap-4 rounded-lg h-fit mb-4">
      <PrimeVueTabs v-model:value="editor_selectedCustomType">
        <PrimeVueTabList
          class="w-fit"
          :pt="{
            activeBar: '!bg-header-orange',
          }"
        >
          <PrimeVueTab
            v-for="(item, index) in tabCustomTypes"
            :key="`variant-${index}`"
            :value="item"
            :pt="{
              root: `text-sm ${useActiveTab(editor_selectedCustomType, item)}`,
            }"
          >
            {{ useCapitalize(item) }}
          </PrimeVueTab>
        </PrimeVueTabList>
      </PrimeVueTabs>
    </section>

    <section>
      <!------------- Custom Image ------------>
      <div v-if="editor_selectedCustomType === PRODUCT_CUSTOM_TYPE.IMAGE" class="mt-2">
        <p class="text-[15px] mb-2">Masukkan Gambar <span class="text-[#E9151D]">*</span></p>
        <div class="w-full mb-8">
          <div
            v-if="uploadedCurrentFile?.name"
            class="flex items-center justify-center flex-col h-[200px] border border-dashed border-[#05964C] rounded-[10px] bg-[#EEFFF5]"
            @dragenter.prevent="onDragEnter"
            @dragover.prevent="onDragover"
            @dragleave.prevent="onDragLeave"
            @drop.prevent="onDrop"
          >
            <NuxtImg src="/icons/checklist-rounded-green.svg" class="mx-auto h-[32px] w-[32px] mb-3" />
            <div class="text-center px-4">
              <p class="text-[14px] font-semibold mb-1">{{ uploadedCurrentFile?.name }}</p>
              <div class="flex items-center justify-center gap-3 mb-2">
                <p class="text-[12px] text-[#686F72]">
                  {{ formatSizeUnits(uploadedCurrentFile.size) }}
                </p>
              </div>
              <div class="flex items-center justify-center cursor-pointer py-4" @click="onClickUpload">
                <NuxtImg src="/icons/add.svg" class="h-[20px] w-[20px] mr-2" />
                <span class="text-black text-[14px] font-semibold underline">Klik untuk menambah aset</span>
              </div>
            </div>
          </div>

          <div
            v-else
            class="flex items-center justify-center flex-col h-[200px] border border-dashed border-[#ACB1B4] rounded-[10px] bg-white"
            @dragenter.prevent="onDragEnter"
            @dragover.prevent="onDragover"
            @dragleave.prevent="onDragLeave"
            @drop.prevent="onDrop"
          >
            <NuxtImg src="/icons/upload_doc.svg" class="mx-auto h-[32px] w-[32px] mb-3" />
            <div class="text-center px-4">
              <p class="text-[14px] font-semibold mb-1">Masukkan Foto Aset</p>
              <p class="text-[12px] text-[#686F72] mb-2">
                Dapat menambahkan dokumen dengan format .jpg dan .png (maksimal 1 Mb)
              </p>
              <div class="cursor-pointer flex items-center justify-center py-4" @click="onClickUpload">
                <NuxtImg src="/icons/add.svg" class="h-[20px] w-[20px] mr-2" />
                <span class="text-black text-[14px] font-semibold underline">Klik untuk menambah aset</span>
              </div>
            </div>
          </div>
          <input
            id="imageUpload"
            ref="inputRef"
            :value="input"
            class="hidden"
            type="file"
            :accept="acceptFile"
            @change="onFileChange"
          />
        </div>

        <div class="bg-[#F5F6F6] px-4 py-3 rounded-md flex items-center justify-center mb-4">
          <NuxtImg src="/icons/hand-icon.svg" class="h-[18px] w-[18px] mr-2" />
          <span class="text-[#18191A] text-[14px]">Seret logo yang diunggah ke lokasi yang diinginkan.</span>
        </div>

        <div class="mb-10">
          <p class="mb-6 text-semibold text-[#18191A]">Penyimpanan Aset</p>
          <div v-if="editor_listOfImageAssets.length > 0" class="grid grid-cols-3 md:grid-cols-4 gap-4">
            <div v-for="(item, index) in editor_listOfImageAssets" :key="index">
              <div
                class="relative flex flex-col items-center justify-center cursor-cell"
                @click="onClickAddImageToLayer(item.img as string, item.imageId)"
              >
                <button
                  class="flex items-center justify-center rounded-full bg-transparent outline-none !w-[30px] !h-[30px] absolute top-[-5px] right-1 cursor-pointer"
                  @click.stop="onClickRemoveImageAsset(item.imageId)"
                >
                  <NuxtImg src="/icons/remove-red.svg" alt="remove icon" class="h-[24px] w-[24px]" />
                </button>
                <div
                  class="h-[60px] w-[60px] bg-[#F5F6F6] flex items-center justify-center rounded-md overflow-hidden"
                >
                  <NuxtImg :src="String(item.img)" :alt="item.file.name" class="h-[40px] w-auto" />
                </div>
                <div class="overflow-hidden text-center w-[100px] h-[20px]">
                  <p class="text-[14px]">{{ item.file.name }}</p>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="px-5 pt-12 text-[#686F72] text-center font-medium text-[14px] w-full md:w-[360px] mx-auto"
          >
            <p>Anda belum menambahkan aset apa pun di sini. Silakan unggah aset yang diinginkan.</p>
          </div>
        </div>
      </div>

      <!------------- Custom Text ------------>
      <div v-if="editor_selectedCustomType === PRODUCT_CUSTOM_TYPE.TEXT" class="mt-2">
        <p class="text-[15px] mb-2 underline">Teks Aset <span class="text-[#E9151D]">*</span></p>
        <div class="w-full mb-8">
          <PrimeVueInputText
            :value="customTextValue"
            class="!text-[16px] block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted !mb-2"
            placeholder="Masukkan Teks Aset"
            type="text"
            size="large"
            maxlength="12"
            @keydown.enter="onSubmitCustomText"
            @input="onChangeCustomInputText"
          />
          <p class="text-[#686F72] text-[14px]">Maksimal 12 Karakter</p>

          <PrimeVueButton
            :disabled="customTextValue.length < 1"
            type="button"
            size="large"
            class="!rounded-lg !w-full mb-3 mt-2 !border-none"
            :class="[customTextValue.length < 1 ? '!bg-gray-300 !text-gray-700' : '!bg-black !text-white']"
            @click="onClickAddText"
          >
            <template #default>
              <div id="content" class="flex items-center justify-center gap-2">
                <span class="font-medium text-base text-center text-white w-full"> Tambah Texts </span>
              </div>
            </template>
          </PrimeVueButton>

          <div class="mt-6">
            <p class="my-2 text-[14px]">
              <span class="text-muted">Warna Teks</span> :
              <span text="text-black">
                {{
                  PRODUCT_CUSTOM_TEXT_COLORS?.find(item => item.value === editor_selectedTextColor)?.label
                }}</span
              >
            </p>
            <div class="w-full flex gap-2" @click.stop="() => {}">
              <button
                v-for="(item, index) in PRODUCT_CUSTOM_TEXT_COLORS"
                :key="index"
                class="cursor-pointer flex items-center justify-center rounded-full h-[38px] w-[38px] overflow-hidden border-2 bg-white !p-0"
                :class="editor_selectedTextColor === item.value ? 'border-black' : 'border-[#e8e8e8]'"
                @click.stop="onSelectTextColor(item.value)"
              >
                <div class="h-[30px] w-[30px] rounded-full" :style="`background-color: ${item.value}`"></div>
              </button>
            </div>
          </div>
        </div>

        <div class="bg-[#F5F6F6] px-4 py-3 rounded-md flex items-center justify-center mb-4">
          <NuxtImg src="/icons/hand-icon.svg" class="h-[18px] w-[18px] mr-2" />
          <span class="text-[#18191A] text-[14px]">Seret logo yang diunggah ke lokasi yang diinginkan.</span>
        </div>

        <div class="mb-10">
          <div class="mb-6 flex items-center">
            <p class="text-semibold text-[#18191A]">Penyimpanan Aset</p>
            <NuxtImg src="/icons/custom-info.svg" class="h-[18px] w-[18px] ml-2" />
          </div>
          <div v-if="editor_listOfTextAssets.length > 0" class="w-full flex flex-col gap-3">
            <div v-for="(item, index) in editor_listOfTextAssets" :key="index">
              <div
                class="relative w-full flex flex-col items-center justify-center cursor-cell"
                @click="onClickAddTextToLayer(item)"
              >
                <div
                  class="h-[40px] w-full bg-[#F5F6F6] flex items-center rounded-md overflow-hidden px-4 text-[16px]"
                >
                  {{ index + 1 }}. {{ item }}
                </div>
                <button
                  class="flex items-center justify-center rounded-full bg-transparent outline-none !w-[30px] !h-[30px] absolute top-[6px] right-2 cursor-pointer"
                  @click.stop="onClickRemoveTextItem(item)"
                >
                  <NuxtImg src="/icons/remove-red.svg" alt="remove icon" class="h-[24px] w-[24px]" />
                </button>
              </div>
            </div>
          </div>
          <div
            v-else
            class="px-5 pt-12 text-[#686F72] text-center font-medium text-[14px] w-full md:w-[360px] mx-auto"
          >
            <p>Anda belum menambahkan aset apa pun di sini. Silakan unggah aset yang diinginkan.</p>
          </div>
        </div>
      </div>

      <div class="w-full mt-12">
        <PrimeVueButton
          :disabled="!editor_canFinishEdit"
          type="button"
          size="large"
          class="!rounded-lg !w-full mb-3 !border-none"
          :class="[editor_canFinishEdit ? '!bg-black !text-white' : '!bg-gray-300 !text-gray-700']"
          @click="onClickFinish"
        >
          <template #default>
            <div id="content" class="flex items-center justify-center gap-2">
              <span class="font-medium text-base text-center text-white w-full"> Selesai </span>
            </div>
          </template>
        </PrimeVueButton>

        <div v-if="!config_hideInquiryForm" class="text-center">
          <p class="text-[#686F72] text-[14px] mb-6">Apakah Anda ingin menambahkan yang lain?</p>
          <PrimeVueButton
            variant="text"
            label="Hubungi Kami"
            class="!text-black !text-[16px] !px-4 !py-1"
            @click="onClickContactUs"
          />
        </div>
      </div>
    </section>

    <!-- Modal inquiry form -->
    <PrimeVueDialog
      v-if="!config_hideInquiryForm"
      v-model:visible="isOpenModalInquiryForm"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :pt="{
        header: '!px-5 sm:!px-10',
        content: '!px-5 sm:!px-10',
        mask: 'sm:!flex !hidden',
      }"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-druk text-[28px] md:text-[42px] text-black font-bold">B2B Inquiry Form</h4>
        </header>
      </template>
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDialog>

    <!-- Bottom Drawer B2B -->
    <PrimeVueDrawer
      v-if="!config_hideInquiryForm"
      id="drawer-login-mobile"
      v-model:visible="isOpenModalInquiryForm"
      position="bottom"
      class="!h-[90vh] !rounded-t-2xl sm:!hidden !flex"
      :pt="{
        header: '!hidden',
        content: '!px-6 !pt-4',
        mask: '!flex sm:!hidden',
      }"
    >
      <template #header> </template>

      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDrawer>
  </section>
</template>

<style lang="css" scoped>
:deep(.product-variant-tabs .p-tablist-tab-list) {
  flex-wrap: wrap;
  border: none !important;
}
:deep(.product-variant-tabs .p-tablist-tab-list .p-tab) {
  margin-bottom: 6px;
}
</style>
