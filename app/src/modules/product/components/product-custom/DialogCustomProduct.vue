<script setup lang="ts">
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';

const openDialog = ref(true);
const { MIN_CUSTOM_ORDER_AMOUNT, MIN_CUSTOM_ORDER_QTY } = inject<IConfigurationProvided>('configurations')!;
</script>

<template>
  <PrimeVueDialog
    v-model:visible="openDialog"
    modal
    class="!relative h-fit w-full md:w-[450px] modal-super"
    :pt="{
      header: '!pt-4 !pb-1',
      content: '!py-4',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/close.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <header class="!z-50 flex items-center justify-between">
        <h4 class="font-bold text-[28px] font-druk text-[#18191A]"><PERSON><PERSON><PERSON><PERSON></h4>
      </header>
    </template>

    <template #default>
      <div class="absolute -z-1 top-0 right-0">
        <div
          class="h-[80px] w-[80px] bg-[url(/images/modal/modal-super-1.png)] bg-contain bg-right bg-no-repeat"
        />
        <div
          class="h-[80px] w-[80px] bg-[url(/images/modal/modal-super-2.png)] bg-contain bg-right bg-no-repeat"
        />
        <div
          class="h-[80px] w-[80px] bg-[url(/images/modal/modal-super-3.png)] bg-contain bg-right bg-no-repeat"
        />
      </div>
      <section class="dialog-custom-content flex flex-col">
        <p class="text-[14px] text-[#686F72] mb-4">Berikut syarat untuk memesan produk kustomisasi yaitu</p>
        <ul class="pl-5">
          <li>
            <p v-if="MIN_CUSTOM_ORDER_AMOUNT" class="text-[#686F72] text-[14px] mb-1">
              Nilai pesanan minimum <strong>{{ useCurrencyFormat(MIN_CUSTOM_ORDER_AMOUNT) }}</strong>
            </p>
          </li>
          <li>
            <p class="text-[#686F72] text-[14px] mb-1">
              Estimasi pengerjaan pemesaanan kustom adalah <strong>x minggu</strong> setelah pemesanan
            </p>
          </li>
          <li>
            <p class="text-[#686F72] text-[14px] mb-1">
              Permintaan ukuran khusus dapat dilakukan untuk pesanan minimum
              <strong>1000</strong> pcs
            </p>
          </li>
        </ul>
      </section>
    </template>
  </PrimeVueDialog>
</template>

<style lang="css" scoped>
.dialog-custom-content ul {
  list-style-type: numeric;
}
</style>
