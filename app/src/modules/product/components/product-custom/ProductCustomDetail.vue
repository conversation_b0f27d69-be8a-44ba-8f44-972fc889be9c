<script setup lang="ts">
// Components
import type { IProductProvided } from '../../interfaces/product.interface';

// components
import ProductCustomDetailEditor from './ProductCustomDetailEditor.vue';
import ProductCustomDetailInfo from './ProductCustomDetailInfo.vue';

import ProductCustomToolbar from './ProductCustomToolbar.vue';
import ProductCustomDetailVariants from './ProductCustomDetailVariants.vue';
import type { IEditorProvided } from '../../interfaces/editor.interface';

/**
 * @description Injecting dependencies
 */
const { product_detailCustomData } = inject<IProductProvided>('product')!;
const { editor_isOpenBottomSheetToolbar, editor_selectedLayer, editor_getSelectedLayerInitialProps } =
  inject<IEditorProvided>('editor')!;

const onClickSidebar = () => {
  editor_selectedLayer.value = editor_getSelectedLayerInitialProps();
};
</script>

<template>
  <section id="custom-product-detail-section" class="grid gap-4">
    <section>
      <ProductCustomDetailEditor v-if="product_detailCustomData" />
      <ProductCustomDetailInfo />
    </section>

    <!-- Desktop Sidebar toolbar -->
    <section
      id="product-custom-sidebar"
      class="hidden sm:block relative rounded-[16px] sm:p-5 sm:px-6 p-4 custom-shadow-md"
      @click="onClickSidebar"
    >
      <ProductCustomToolbar />
      <ProductCustomDetailVariants />
    </section>

    <!-- Mobile bottom sheet toolbar -->
    <PrimeVueDrawer
      v-model:visible="editor_isOpenBottomSheetToolbar"
      position="bottom"
      class="!h-[80vh] !rounded-t-2xl"
      :pt="{
        root: 'product-custom-toolbar-drawer',
        header: '!pt-1 !pb-1 !px-6 !mt-5 !border-b !border-b-[#DCE0E4]',
        content: '!px-6 !pt-8',
        close: '!hidden',
      }"
    >
      <template #header>
        <header class="flex items-center justify-between">
          <!-- Handler -->
          <div class="absolute top-0 left-0 w-full flex items-center justify-center h-[30px]">
            <div class="h-[4px] w-[72px] rounded-md bg-[#E5E5E5]"></div>
          </div>
        </header>
      </template>

      <template #default>
        <ProductCustomToolbar />
        <ProductCustomDetailVariants />
      </template>
    </PrimeVueDrawer>
  </section>
</template>

<style lang="css" scoped>
#custom-product-detail-section {
  width: 100%;
  @media (min-width: 768px) {
    grid-template-columns: 1fr 320px;
  }
  @media (min-width: 1000px) {
    grid-template-columns: 1fr 410px;
  }
  @media (min-width: 1300px) {
    grid-template-columns: 1fr 440px;
  }
  @media (min-width: 1400px) {
    grid-template-columns: 1fr 500px;
  }
  @media (min-width: 1500px) {
    grid-template-columns: 1fr 536px;
  }
}
</style>
