<script setup lang="ts">
interface Props {
  data: IBreadcrumb[];
}

const props = defineProps<Props>();

// refs
const breadcrumbs = computed<IBreadcrumb[]>(() => {
  return (
    props.data.map(item => ({
      label: item.label,
      url: item.path ?? null,
    })) ?? []
  );
});
</script>

<template>
  <section id="product-detail-breadcrumbs" class="relative inset-0 z-0 !-mb-1">
    <PrimeVueBreadcrumb
      :model="breadcrumbs"
      class="!p-0"
      :pt="{
        item: '!text-[12px]',
        itemLink: '!text-[12px] !text-[#686F72]',
      }"
    >
      <template #separator> /</template>
    </PrimeVueBreadcrumb>
  </section>
</template>
