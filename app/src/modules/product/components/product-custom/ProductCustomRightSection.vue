<script setup lang="ts">
// Components
// import ProductDetailProductColorVariants from './ProductDetailProductColorVariants.vue';
// import ProductDetailProductGalleries from './ProductDetailProductGalleries.vue';
// import ProductDetailProductHeader from './ProdutDetailProductHeader.vue';
</script>

<template>
  <section id="catalog-detail-product-information" class="grid sm:grid-cols-[65fr_35fr] grid-cols-1 gap-5">
    <button class="rounded-xl shadow-xl px-2 py-1">
      <NuxtImg src="/icons/arrow-back.svg" />
      <span>Back</span>
    </button>
    <!-- <section id="product-information" class="rounded-md mt-1 sm:p-5 p-2 sm:shadow-md shadow-none">
      <ProductDetailProductHeader />
      <ProductDetailProductColorVariants /> 
    </section> -->
  </section>
</template>
