<script setup lang="ts">
// Interfaces
import BaseInquiryForm from '~/app/src/core/components/base/BaseInquiryForm.vue';
import type { ICartProvided, IPayloadPostCartAttachments } from '../../../cart/interfaces/cart.interface';
import { PRODUCT_CUSTOM_EDIT_MODE } from '../../constants/product.constant';
import type { IEditorProvided } from '../../interfaces/editor.interface';
import type { IProductProvided } from '../../interfaces/product.interface';
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
const { CUSTOM_ORDER_LOGO_PRICE, CUSTOM_ORDER_TEXT_PRICE } = inject<IConfigurationProvided>('configurations')!;

const openDialogRequestOtherSize = ref(false);
// const router = useRouter();

/**
 * @description Injecting dependencies
 */
const { editor_mode, editor_prepareAddToCart, layers, editor_listOfImageAssets } =
  inject<IEditorProvided>('editor')!;
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

const {
  product_onOpenDialogGuideSizeProduct,
  product_detailCustomData,
  product_customCartValues,
  product_customSelectedTabProductVariant,
  // product_checkedRequestOther,
  product_canAddToCartProductCustom,
  product_uploadAttachments,
} = inject<IProductProvided>('product')!;
const { cart_customAddToCartBulk, cart_postTextAttachments } = inject<ICartProvided>('cart')!;

// const router = useRouter();

const hasQtyChanges = ref(false);
const isOpenModalInquiryForm = ref(false);
const isLoadingAddToCart = ref(false);

const data = computed<IProduct>(() => {
  return product_detailCustomData?.value as IProduct;
});

const onChangeQuantity = () => {
  hasQtyChanges.value = true;
};

const customProductName = computed<string[]>(() => {
  const _customName = [];
  const layersText: string[] = layers.value.filter(t => t.layerType === 'text').map(t => t.text) as string[];

  if (editor_listOfImageAssets.value.length > 0) {
    _customName.push('Logo');
  }
  if (layersText.length > 0) {
    _customName.push('Text');
  }

  return _customName;
});

const calculateEstimatePrice = computed<number>(() => {
  let customPrice = 0;
  const layersText: string[] = layers.value.filter(t => t.layerType === 'text').map(t => t.text) as string[];

  if (layersText?.length > 0) {
    customPrice += layersText.length * Number(CUSTOM_ORDER_TEXT_PRICE.value);
  }

  if (editor_listOfImageAssets.value.length > 0) {
    customPrice += editor_listOfImageAssets.value.length * Number(CUSTOM_ORDER_LOGO_PRICE.value);
  }

  return customPrice;
});

/**
 * Sum sub total cart
 */
const subTotalOrder = computed<number>(() => {
  return product_customCartValues.value.reduce((total, item) => {
    return total + item.qty * parseInt(item.price);
  }, 0);
});

/**
 * Sum total order
 */
const totalProduct = computed<number>(() => {
  return product_customCartValues.value.reduce((total, item) => {
    return total + item.qty;
  }, 0);
});

const onClickAddToCart = async () => {
  isLoadingAddToCart.value = true;
  const skuCodeC = product_detailCustomData.value?.sku_code_c as string;
  try {
    // const captures = await captureAllBackgroundLayers();
    const prepareData = editor_prepareAddToCart(skuCodeC);
    const layersText = layers.value
      .filter(t => t.layerType === 'text')
      .map(item => ({
        value: item.text,
        color: item.textLabel,
      })) as IPayloadPostCartAttachments['text'];
    if (prepareData) {
      try {
        const attachmentResults = await product_uploadAttachments(prepareData.body, prepareData.files);
        if (attachmentResults?.length > 0) {
          setTimeout(async () => {
            const response = await cart_postTextAttachments({ sku_code_c: skuCodeC, text: layersText });
            if (response.attachments_group_id) {
              setTimeout(() => {
                cart_customAddToCartBulk({
                  items: product_customCartValues.value.filter(i => i.qty > 0),
                  attachments_group_id: response.attachments_group_id,
                }).then(() => {
                  isLoadingAddToCart.value = false;
                  // navigate to cart page
                  window.location.href = '/cart';
                });
              }, 2500);
            } else {
              isLoadingAddToCart.value = false;
            }
          }, 2500);
        } else {
          isLoadingAddToCart.value = false;
        }
      } catch (e) {
        console.log('e->', e);
        isLoadingAddToCart.value = false;
      }
    }
  } catch (e) {
    console.log('E', e);
    isLoadingAddToCart.value = false;
  }
};

// const onChangeRequestOther = (value: boolean) => {
//   if (value) {
//     isOpenModalInquiryForm.value = true;
//   } else {
//     isOpenModalInquiryForm.value = false;
//   }
// };

const onSubmitInquiryForm = () => {
  isOpenModalInquiryForm.value = false;
};

onUnmounted(() => {
  console.log('onUnmounted call me->');
});
</script>

<template>
  <section :style="{ display: editor_mode === PRODUCT_CUSTOM_EDIT_MODE.ADD_TO_CART ? 'block' : 'none' }">
    <!-- header -->
    <header class="flex flex-col gap-1 mb-4">
      <section id="">
        <h1 class="font-bold font-druk text-[22px] sm:text-[28px] text-black">
          {{ data?.product_name_c }}
        </h1>
      </section>
      <span id="price" class="font-bold text-[20px]">
        {{ data && useCurrencyFormat(+data?.product_price?.amount) }}
      </span>
    </header>

    <section id="product-custom-selected-variant">
      <div class="mb-1 text-[14px]">
        <span class="text-[#686F72]">Warna</span> :
        <span>{{ product_customSelectedTabProductVariant }}</span>
      </div>
      <div class="text-[14px] mb-3">
        <span class="text-[#686F72]">Custom</span> :
        <span>{{ customProductName?.toString() }}</span>
      </div>
    </section>

    <section id="product-custom-list-variant" class="flex flex-col g ap-4 rounded-lg h-fit mb-2">
      <div class="w-full">
        <div class="mt-1">
          <table class="min-w-full table-auto !border-none">
            <thead>
              <tr class="border-t-1 border-b-1 border-[#ACB1B4]">
                <th class="py-2 px-4 text-left text-sm w-[100px]">Size</th>
                <th class="py-2 px-4 text-left text-sm w-[100px]">Stock</th>
                <th class="py-2 px-4 text-left text-sm w-[120px]">Jumlah Order</th>
                <th class="py-2 px-4 text-left text-sm w-[120px]">Harga</th>
              </tr>
            </thead>
            <tbody class="!border-none">
              <tr
                v-for="(item, index) in product_customCartValues?.filter(
                  v => v.color === product_customSelectedTabProductVariant,
                )"
                :key="String(index)"
              >
                <td class="py-2 px-4 text-left text-sm">{{ item.product_size_c }}</td>
                <td class="py-2 px-4 text-left text-sm">{{ item.stock }}</td>
                <td class="py-2 px-4 text-left text-sm">
                  <PrimeVueInputNumber
                    v-model="item.qty"
                    input-id="horizontal-buttons"
                    show-buttons
                    button-layout="horizontal"
                    :step="1"
                    :min="0"
                    :max="item.stock"
                    fluid
                    class="!w-[100px] !p-0 !border-none"
                    :pt="{
                      pcInputText: {
                        root: '!text-center !text-[14px] !px-[1px] !py-[2px] !w-10 !font-bold',
                      },
                      decrementButton: {
                        class: 'h-7 !w-7',
                      },
                      incrementButton: {
                        class: 'h-7 !w-7',
                      },
                    }"
                    @update:model-value="onChangeQuantity"
                  >
                    <template #decrementicon>
                      <NuxtImg src="/icons/minus-flat-black.svg" alt="plus" class="w-4 h-4" />
                    </template>
                    <template #incrementicon>
                      <NuxtImg src="/icons/plus-flat-black.svg" alt="plus" class="w-4 h-4" />
                    </template>
                  </PrimeVueInputNumber>
                </td>
                <td class="py-2 px-4 text-left text-sm">
                  {{ item?.price ? useCurrencyFormat(parseInt(item.price)) : '-' }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <section id="product-custom-hint-toolbar" class="w-full flex items-center mb-5">
      <div class="w-full">
        <PrimeVueButton
          variant="text"
          class="!px-[2px] !py-[1px] !leading-none !gap-1"
          @click="product_onOpenDialogGuideSizeProduct"
        >
          <NuxtImg class="h-[18px] w-[18px]" src="/icons/color-swatch.svg" />
          <p class="text-[14px] !text-black underline font-medium">Panduan Ukuran</p>
        </PrimeVueButton>
      </div>
      <!-- <div class="w-full flex items-center justify-right">
        <div class="w-full flex items-center gap-2">
          <PrimeVueCheckbox
            v-model="product_checkedRequestOther"
            input-id="requestOtherSize"
            name="requestOtherSize"
            binary
            class="w-[20px] h-[20px]"
            @update:model-value="onChangeRequestOther"
          />
          <label for="requestOtherSize" class="text-[14px]">Request Ukuran Lainnya</label>
        </div>
        <div>
          <PrimeVueButton
            variant="text"
            size="small"
            rounded
            class="!h-[30px] !w-[30px] !p-0"
            @click="openDialogRequestOtherSize = true"
          >
            <NuxtImg class="h-[15px] w-[15px]" src="/icons/custom-info-blue-icon.svg" />
          </PrimeVueButton>
        </div>
      </div> -->
    </section>

    <section id="product-custom-cart-summary" class="mb-6">
      <p class="font-medium text-[16px] mb-2">Ringkasan Pesanan</p>
      <div class="pb-3 border-b border-b-[#CDD3DA]">
        <p class="text-[14px]">Produk</p>
        <div class="pl-2 flex flex-col gap-1.5">
          <div class="flex items-center justify-between">
            <p class="text-[14px]">Jumlah Order</p>
            <p class="text-[14px]">{{ totalProduct }}</p>
          </div>
          <div class="flex items-center justify-between">
            <p class="text-[14px]">Subtotal</p>
            <p class="text-[14px]">{{ useCurrencyFormat(subTotalOrder) }}</p>
          </div>
          <div class="flex items-center justify-between">
            <p class="text-[14px]">Estimasi Biaya Kustomisasi</p>
            <p class="text-[14px]">{{ useCurrencyFormat(calculateEstimatePrice * totalProduct) }}</p>
          </div>
        </div>
      </div>
      <div class="pt-2 flex items-center justify-between">
        <p class="text-[14px] font-medium">TOTAL HARGA</p>
        <p class="text-[20px] font-bold">
          {{ useCurrencyFormat(subTotalOrder + calculateEstimatePrice * totalProduct) }}
        </p>
      </div>
    </section>

    <section id="product-custom-footer">
      <PrimeVueButton
        :disabled="isLoadingAddToCart || !product_canAddToCartProductCustom"
        type="button"
        size="large"
        class="!rounded-lg !w-full mb-3 !border-none"
        :class="[
          product_canAddToCartProductCustom && !isLoadingAddToCart
            ? '!bg-black !text-white'
            : '!bg-gray-300 !text-gray-700',
        ]"
        @click="onClickAddToCart"
      >
        <NuxtImg v-if="isLoadingAddToCart" src="/icons/loading-loop.svg" class="!h-[18px] !w-[18px]" />
        <div id="content" class="flex items-center justify-center gap-2">
          <span class="font-medium text-base text-center text-white w-full"> Pesan Sekarang </span>
        </div>
      </PrimeVueButton>
    </section>

    <!-- Dialog request other sized -->
    <PrimeVueDialog
      v-model:visible="openDialogRequestOtherSize"
      modal
      class="!relative h-fit w-full md:w-[400px] modal-super"
      :pt="{
        root: '!p-0',
        header: '!hidden',
        content: '!px-5 !pt-6',
      }"
      :draggable="false"
    >
      <template #default>
        <section class="flex flex-col">
          <div class="mx-auto mb-4">
            <NuxtImg src="/images/info-black-icon.png" class="h-[72px] w-[72px]" alt="info icon" />
          </div>
          <p class="text-[18px] text-[#18191A] font-semibold mb-2">Request Ukuran</p>
          <p class="text-[14px] mb-5">
            Anda dapat melakukan permintaan untuk ukuran khusus (contoh: XXL, 3XL, dst) apabila pesanan Anda
            melebihi <strong>1000 pcs</strong>. Silakan tambahkan pesanan Anda untuk melakukan permintaan ukuran
            khusus
          </p>
          <PrimeVueButton
            type="button"
            size="large"
            class="!rounded-lg !w-full !border-none !bg-black !text-white"
            @click="openDialogRequestOtherSize = false"
          >
            <template #default>
              <div id="content" class="flex items-center justify-center gap-2">
                <span class="font-medium text-base text-center text-white w-full"> Tutup </span>
              </div>
            </template>
          </PrimeVueButton>
        </section>
      </template>
    </PrimeVueDialog>

    <!-- Modal inquiry form -->
    <PrimeVueDialog
      v-if="!config_hideInquiryForm"
      v-model:visible="isOpenModalInquiryForm"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :pt="{
        header: '!px-5 sm:!px-10',
        content: '!px-5 sm:!px-10',
      }"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-druk text-[28px] md:text-[42px] text-black font-bold">B2B Inquiry Form</h4>
        </header>
      </template>
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDialog>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-tablist-tab-list) {
  border: none !important;
  padding-bottom: 2px !important;
}
</style>
