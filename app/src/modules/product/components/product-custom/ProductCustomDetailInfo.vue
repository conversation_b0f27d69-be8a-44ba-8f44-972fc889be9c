<script setup lang="ts">
// Components
// import type { IEditorProvided } from '../../interfaces/editor.interface';
import type { IConfigurationProvided } from '../../../configurations/interfaces/configurations.interface';
import type { IProductProvided } from '../../interfaces/product.interface';
const { MIN_CUSTOM_ORDER_QTY, MIN_CUSTOM_ORDER_AMOUNT } = inject<IConfigurationProvided>('configurations')!;

/**
 * @description Injecting dependencies
 */
const { product_detailCustomData: data } = inject<IProductProvided>('product')!;
</script>

<template>
  <section id="product-custom-info" class="grid grid-cols-1 md:grid-cols-[300px_1fr]">
    <header class="flex flex-col gap-1 border-r-2 border-r-[#E5E6E8]">
      <section id="">
        <h1 class="font-bold font-druk text-[28px] text-[#18191A] tracking-[-0.03em]">
          {{ data?.product_name_c }}
        </h1>
        <div class="flex items-center">
          <div class="bg-[#F5F6F6] px-2 py-1 mb-2 text-[12px] rounded">Biaya belum termasuk biaya kustomisasi</div>
        </div>
      </section>
      <span id="price" class="font-bold text-[20px]">
        {{ data && useCurrencyFormat(+data?.product_price?.amount) }}
      </span>
    </header>
    <section class="px-5 py-4 relative">
      <!-- <button
        class="block md:hidden absolute cursor-pointer z-10 top-6 right-4 h-[60px] w-[60px] items-center justify-center p-0 border-[#ACB1B4]"
        @click="editor_isOpenBottomSheetToolbar = true"
      >
        <NuxtImg src="/icons/customize-icon.svg" class="h-[60px] w-[60px]" alt="customize" />
      </button> -->
      <h4 class="font-semibold text-[20px] mb-2">Informasi Kustomisasi Produk</h4>
      <div class="product-custom-info-detail">
        <ul>
          <li>
            <p class="font-semibold text-[14px]">Apa saja jenis kustomisasi yang tersedia?</p>
            <p>
              Anda dapat menambahkan logo dan teks. Lalu untuk bahan tertentu kami hanya menyediakan sablon biasa
              dan sablon DTF
            </p>
          </li>
          <li>
            <p class="font-semibold text-[14px]">Apa saja ketentuan untuk menambah logo?</p>
            <ol>
              <li></li>
            </ol>
          </li>
          <li>
            <p class="font-semibold text-[14px]">Apa saja syarat memesan produk kustomisasi?</p>
            <ol>
              <li v-if="MIN_CUSTOM_ORDER_AMOUNT">
                Minimal pesanan <strong>{{ useCurrencyFormat(MIN_CUSTOM_ORDER_AMOUNT) }}</strong> atau
                <strong>{{ MIN_CUSTOM_ORDER_QTY }} pcs</strong>.
              </li>
              <li>
                Estimasi pengerjaan pemesanan kustom kurang lebih <strong>5-7 hari</strong> setelah pemesanan
              </li>
              <li>
                Permintaan ukuran khusus dapat dilakukan untuk pesanan minumum
                <strong>1000 pcs</strong>
              </li>
            </ol>
          </li>
          <li>
            <p class="font-semibold text-[14px]">Bagaimana cara memesan produk kustomisasi?</p>
            <ol>
              <li>Isi inquiry form yang tersedia dengan kebutuhan Anda, lalu kirimkan.</li>
              <li>Setelahya dari tim kami akan menghubungi Anda untuk proses selanjutnya</li>
            </ol>
          </li>
          <li>
            <p class="font-semibold text-[14px]">
              Berapa lama proses pengerjaan sampai barang siap untuk dikirim?
            </p>
            <p>
              Proses pengerjaan produk custom bergantung pada jumlah produk dan kompleksitas desain custom yang
              dipesan
            </p>
          </li>
        </ul>
      </div>
    </section>
  </section>
</template>

<style lang="css" scoped>
:deep(.product-custom-info-detail *) {
  font-size: 16px;
}
:deep(.product-custom-info-detail li),
:deep(.product-custom-info-detail p) {
  margin-bottom: 6px;
}
:deep(.product-custom-info-detail > ul) {
  list-style-type: numeric;
  padding-left: 1.2rem;
}
:deep(.product-custom-info-detail > ul li ol) {
  list-style-type: disc;
  padding-left: 1.2rem;
}
</style>
