<script setup lang="ts">
// import type { IProductItem } from '../interfaces/product.interface';

/**
 * @description Define the props interface
 */
interface IProps {
  product: IProductHomepage;
}

/**
 * @description Define props with default values and interfaces
 */
const props = defineProps<IProps>();

/**
 * @description router
 */
const displayImage = ref<string | null>(props.product.main_image);

// const outOffStock = computed(() => props.product.stock === 0);

const onClickDetail = () => {
  window.location.href = `/product/detail/${props.product.sku_code_c}`;
};
</script>

<template>
  <section
    :id="'product-card-' + product.sku_code_c"
    class="flex flex-col w-full h-fit rounded-lg gap-3 border border-[#F0F2F4]"
  >
    <div class="px-4 py-4">
      <div class="w-full h-full max-h-80 rounded-lg relative flex items-center justify-center mb-3">
        <NuxtImg
          :src="(displayImage as string)"
          alt="product"
          class="w-full h-full max-h-80 rounded"
          loading="lazy"
          @click="onClickDetail"
        />
      </div>
      <section id="information" class="flex flex-col">
        <div class="flex gap-3 mb-3">
          <div
            v-for="(item, index) in product.flag"
            :key="index"
            class="rounded text-white leading-none px-3 py-2 text-sm"
            :class="index === 0 ? 'bg-[#05964C]' : 'bg-[#3B3C3E]'"
          >
            {{ item }}
          </div>
        </div>
        <div class="h-[22px] overflow-hidden truncate">
          <h6 class="font-medium text-base text-black">
            {{ product.product_name_c }}
          </h6>
        </div>
      </section>
    </div>
  </section>
</template>
