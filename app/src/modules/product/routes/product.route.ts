// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/product',
    component: AppBaseWrapper,
    children: [
      {
        path: 'detail/:sku',
        name: 'product.detail',
        component: () => import('../views/ProductDetailUI.vue'),
      },
      {
        path: 'custom/:sku',
        name: 'product.custom.detail',
        component: () => import('../views/CustomProductUI.vue'),
      },
    ],
  },
];

export default routes;
