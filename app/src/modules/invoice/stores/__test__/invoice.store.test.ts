import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useInvoiceStore } from '../invoice.store';

describe('Invoice store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useInvoiceStore();

      expect(store.invoice_listIsLoading).toBe(false);
      expect(store.invoice_listData).toMatchObject([]);
      expect(store.invoice_listPagination).toMatchObject({
        total_data: 0,
        size: 0,
        active_page: 0,
        total_page: 0,
      });
      expect(store.invoice_detailHeaderData).toBeNull();
      expect(store.invoice_detailHeaderLoading).toBe(false);
      expect(store.invoice_detailProductListData).toMatchObject([]);
      expect(store.invoice_detailProductIsLoading).toBe(false);
      expect(store.invoice_detailProductListPagination).toMatchObject({
        total_data: 0,
        size: 12,
        active_page: 0,
        total_page: 0,
      });
    });

    it('has correct initial computed state', () => {
      const store = useInvoiceStore();
      expect(store.invoice_listIsLoading).toBe(false);
      expect(store.invoice_detailProductIsLoading).toBe(false);
    });
  });

  it('Invoice list data should be empty', () => {
    const store = useInvoiceStore();
    expect(store.invoice_listData).toMatchObject([]);
  });

  it('Invoice detail product list data should be null', () => {
    const store = useInvoiceStore();
    expect(store.invoice_detailProductListData).toMatchObject([]);
  });
});
