<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';

// Services
import { useInvoiceService } from '../services/invoice.service';
import InvoiceSummary from '../components/InvoiceSummary.vue';
import InvoiceProductList from '../components/InvoiceProductList.vue';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  invoice_getList,
  invoice_detailHeaderData,
  invoice_detailHeaderLoading,
  invoice_getDetailHeader,
  invoice_detailProductListData,
  invoice_detailProductIsLoading,
  invoice_detailProductListPagination,
  invoice_getDetailProductList,
  invoice_detailProductsListQueryParams,
  invoice_download,
} = useInvoiceService();

/**
 * @description Provide all the data and methods what we need
 */
provide('invoice', {
  invoice_getList,
  invoice_detailHeaderData,
  invoice_detailHeaderLoading,
  invoice_detailProductListData,
  invoice_detailProductIsLoading,
  invoice_detailProductListPagination,
  invoice_getDetailProductList,
  invoice_detailProductsListQueryParams,
  invoice_download,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  const invoiceNumber = route.params.invoiceNumber;
  if (invoiceNumber) {
    await Promise.allSettled([
      invoice_getDetailHeader(String(invoiceNumber)),
      invoice_getDetailProductList(String(invoiceNumber)),
    ]);
  }
});
</script>

<template>
  <MainLayout page-title="Detail Tagihan">
    <section id="invoice-detail" class="flex flex-col w-full h-full relative inset-0 z-0 pt-6">
      <!-- <BaseLoaderBoxed v-if="invoice_detailHeaderLoading" :height="300" /> -->
      <InvoiceSummary :data="invoice_detailHeaderData ?? null" />
      <InvoiceProductList />
    </section>
  </MainLayout>
</template>
