<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import InvoiceForm from '../components/InvoiceForm.vue'
// Services
import { useInvoiceTaxService } from '../../tax-invoice/services/invoice-tax.service';
// import { useInvoiceTaxService } from '../services/invoice-tax.service';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  taxInvoice_formData,
  taxInvoice_formValidations,
  taxInvoice_onSubmit,
  taxInvoice_isShowModal
} = useInvoiceTaxService();

/**
 * @description Provide all the data and methods what we need
 */

 provide('invoiceTax', {
  taxInvoice_formData,
  taxInvoice_formValidations,
  taxInvoice_onSubmit,
  taxInvoice_isShowModal
});

</script>

<template>
  <MainLayout page-title="Faktur <PERSON>jak">
    <section id="invoice-detail" class="flex flex-col w-full h-full relative inset-0 z-0 pt-6">
      <InvoiceForm/>
    </section>
  </MainLayout>
</template>
