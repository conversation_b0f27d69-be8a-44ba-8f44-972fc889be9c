<script setup lang="ts">
// Components
// import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import InvoiceListFilters from '../components/InvoiceListFilters.vue';
import InvoiceListTab from '../components/InvoiceListTab.vue';
import InvoiceListItem from '../components/InvoiceListItem.vue';

// Services
import { useInvoiceService } from '../services/invoice.service';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  invoice_getList,
  invoice_listIsLoading,
  invoice_listData,
  invoice_listPagination,
  invoice_listQueryParams,
  invoice_listOrderActiveTab,
} = useInvoiceService();

/**
 * @description Provide all the data and methods what we need
 */
provide('invoice', {
  invoice_getList,
  invoice_listIsLoading,
  invoice_listData,
  invoice_listPagination,
  invoice_listQueryParams,
  invoice_listOrderActiveTab,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  await Promise.allSettled([invoice_getList()]);
});
</script>

<template>
  <MainLayout page-title="Tagihan">
    <section id="order-list" class="flex flex-col w-full h-full relative inset-0 z-0 pt-6">
      <InvoiceListFilters />
      <InvoiceListTab />
      <InvoiceListItem />
    </section>
  </MainLayout>
</template>
