// Constants
import { INVOICE_LIST_PAGINATION_SIZE, INVOICE_PAGINATION_SIZE, INVOICE_STATUS_TABS } from '../constants';

// Interfaces
import type {
  IInvoiceProvided,
  IQueryParamsGetListInvoice,
  IQueryParamsGetDetailInvoiceProducts,
} from '../interfaces/invoice.interface';

// Store
import { storeToRefs } from 'pinia';
import { useInvoiceStore } from '../stores/invoice.store';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useInvoiceService = (): IInvoiceProvided => {
  /**
   * @description Injected variables
   */
  const store = useInvoiceStore(); // Instance of the store
  const {
    invoice_listIsLoading,
    invoice_listData,
    invoice_listPagination,
    invoice_detailHeaderData,
    invoice_detailHeaderLoading,
    invoice_detailProductListData,
    invoice_detailProductIsLoading,
    invoice_detailProductListPagination,
  } = storeToRefs(store);

  /**
   * @description Reactive data binding
   */
  const invoice_listOrderActiveTab = ref<string>(INVOICE_STATUS_TABS[0].value);

  const invoice_listQueryParams = reactive<IQueryParamsGetListInvoice>({
    page: 1,
    per_page: INVOICE_LIST_PAGINATION_SIZE[0],
    status: INVOICE_STATUS_TABS[0].value,
    text: null,
    date_from: null,
    date_to: null,
  });

  const invoice_detailProductsListQueryParams = reactive<IQueryParamsGetDetailInvoiceProducts>({
    page: 1,
    per_page: INVOICE_PAGINATION_SIZE[0],
    text: null,
  });

  /**
   * @description Handle fetch api get order list. We call the fetchInvoice_getList function from the store to handle the request.
   */
  const invoice_getList = async () => {
    await store.fetchInvoice_getList(invoice_listQueryParams);
  };

  /**
   * @description Handle fetch api get invoice header. We call the fetchInvoice_getInvoiceHeader function from the store to handle the request.
   */
  const invoice_getDetailHeader = async (invoiceNumber: string) => {
    await store.fetchInvoice_getInvoiceHeader(invoiceNumber);
  };

  /**
   * @description Handle fetch api get detail product list. We call the fetchInvoice_getListProduct function from the store to handle the request.
   */
  const invoice_getDetailProductList = async (orderNumber: string) => {
    await store.fetchInvoice_getListProduct(orderNumber, invoice_detailProductsListQueryParams);
  };

  /**
   * @description Handle download invoice. We call the fetchInvoice_download function from the store to handle the request.
   */
  const invoice_download = async (invoiceNumber: string) => {
    try {
      await store.fetchInvoice_download(invoiceNumber);
    } catch  {
      console.error('Error downloading invoice:');
    }
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    invoice_listQueryParams,
    () => {
      invoice_getList();
    },
    { deep: true },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    invoice_detailProductsListQueryParams,
    () => {
      const invoiceId = invoice_detailHeaderData?.value?.order?.billing;

      if (invoiceId) {
        invoice_getDetailProductList(invoiceId);
      }
    },
    { deep: true, immediate: true },
  );

  return {
    invoice_getList,
    invoice_getDetailHeader,
    invoice_getDetailProductList,
    invoice_download,
    invoice_listIsLoading,
    invoice_listData,
    invoice_listPagination,
    invoice_listQueryParams,
    invoice_detailHeaderData,
    invoice_detailHeaderLoading,
    invoice_detailProductsListQueryParams,
    invoice_listOrderActiveTab,
    invoice_detailProductListData,
    invoice_detailProductIsLoading,
    invoice_detailProductListPagination,
  };
};
