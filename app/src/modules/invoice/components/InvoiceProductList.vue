<script setup lang="ts">
// Interface
import type { IInvoiceProduct, IInvoiceProvided } from '../interfaces/invoice.interface';

// constants
import { INVOICE_LIST_PAGINATION_SIZE } from '../constants';

/**
 * @description Injecting dependencies
 */
const {
  invoice_detailProductListData,
  invoice_detailProductIsLoading,
  invoice_detailProductListPagination,
  invoice_detailProductsListQueryParams,
} = inject<IInvoiceProvided>('invoice')!;

/**
 * @description Skeleton looping number
 */
const products = ref(new Array(5));

const handleSearch = (value: string) => {
  invoice_detailProductsListQueryParams.text = value;
};

const productList = computed<IInvoiceProduct[]>(() => invoice_detailProductListData.value as IInvoiceProduct[]);

const onChangePerPage = (value: number) => {
  invoice_detailProductsListQueryParams.per_page = value;
  invoice_detailProductsListQueryParams.page = 1;
};
const onClickPrev = () => {
  invoice_detailProductsListQueryParams.page = invoice_detailProductsListQueryParams.page - 1;
};
const onClickNext = () => {
  invoice_detailProductsListQueryParams.page = invoice_detailProductsListQueryParams.page + 1;
};
</script>

<template>
  <section id="bill-order-list" class="mb-10">
    <section id="tittle-bill-order-list" class="text-xl font-semibold my-5">Daftar Pesanan</section>

    <section id="list-filter">
      <BaseSearchBar placeholder="Masukan Keyword" :handle-search="handleSearch" />
    </section>

    <section id="table-list-order" class="mt-5">
      <template v-if="invoice_detailProductIsLoading">
        <PrimeVueDataTable :value="products">
          <PrimeVueColumn field="product_name" header="Nama Produk" class="!w-3">
            <template #body>
              <PrimeVueSkeleton width="1rem" class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="product_name" header="Nama Produk">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="sku" header="SKU">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="product_size" header="Ukuran">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="qty" header="Jumlah Barang">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="price" header="Harga Satuan">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="sub_total" header="Sub Total">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="discounts" header="Potongan Harga">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="net_price" header="NETT">
            <template #body>
              <PrimeVueSkeleton class="!h-6"></PrimeVueSkeleton>
            </template>
          </PrimeVueColumn>
        </PrimeVueDataTable>
      </template>
      <template v-else>
        <PrimeVueDataTable :value="productList" table-style="min-width: 50rem">
          <template #empty>
            <div class="flex items-center justify-center py-8">
              <p class="text-center">Tidak ada data</p>
            </div>
          </template>
          <PrimeVueColumn header="No.">
            <template #body="{ index }">
              <div class="py-0">
                {{
                  (invoice_detailProductListPagination.active_page - 1) *
                    invoice_detailProductListPagination.size +
                  index +
                  1
                }}
              </div>
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="product_name" header="Nama Produk"></PrimeVueColumn>
          <PrimeVueColumn field="sku" header="SKU"></PrimeVueColumn>
          <PrimeVueColumn field="product_size" header="Ukuran"></PrimeVueColumn>
          <PrimeVueColumn field="qty" header="Jumlah Barang"></PrimeVueColumn>
          <PrimeVueColumn field="price" header="Harga Satuan">
            <template #body="slotProps">
              {{ useCurrencyFormat(slotProps.data.price, true) }}
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="sub_total" header="Sub Total">
            <template #body="slotProps">
              {{ useCurrencyFormat(slotProps.data.sub_total, true) }}
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="discount" header="Potongan Harga">
            <template #body="slotProps">
              {{ useCurrencyFormat(slotProps.data.discount, true) }}
            </template>
          </PrimeVueColumn>
          <PrimeVueColumn field="net_price" header="NETT">
            <template #body="slotProps">
              {{ useCurrencyFormat(slotProps.data.net_price, true) }}
            </template>
          </PrimeVueColumn>
        </PrimeVueDataTable>
      </template>
    </section>
    <section id="pagination-product" class="my-5">
      <BasePagination
        :data="invoice_detailProductListPagination"
        :click-action-next="onClickNext"
        :click-action-prev="onClickPrev"
        :click-action-per-page="onChangePerPage"
        :sizes="INVOICE_LIST_PAGINATION_SIZE"
        :loading="invoice_detailProductIsLoading"
      />
    </section>
  </section>
</template>
