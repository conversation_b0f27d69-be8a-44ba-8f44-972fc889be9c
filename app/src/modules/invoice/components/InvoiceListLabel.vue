<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script lang="ts" setup>
import { capitalizeFirstLetter } from '~/app/src/core/helpers/text.helper';

/**
 * interfaces
 */
interface IInvoiceListLabel {
  text: string;
  status: string;
  onClick?: () => void;
}

const props = defineProps<IInvoiceListLabel>();

const invoiceStatus = computed(() => props.status?.toLocaleLowerCase()?.trim());

const emit = defineEmits<{
  (e: 'click'): void;
}>();

const handleClick = () => {
  emit('click');
};
</script>

<template>
  <div
    class="inline-flex px-3 py-1.5 leading-none text-xs font-bold rounded-full"
    :class="[
      invoiceStatus === 'lunas' && ' !bg-[#EEFFF5] text-[#05964C]',
      (invoiceStatus === 'belum dibayar' || invoiceStatus === 'belum lunas') && ' !bg-[#FFF1F2] text-[#E9151D]',
    ]"
    @click="handleClick"
  >
    {{ capitalizeFirstLetter(text) }}
  </div>
</template>

<style scoped></style>
