<script setup lang="ts">
import { capitalizeFirstLetter } from '~/app/src/core/helpers/text.helper';
import type { IInvoiceDetailHeader, IInvoiceProvided } from '../interfaces/invoice.interface';

const props = defineProps<{ data: IInvoiceDetailHeader | null}>();

/**
 * @description Injecting dependencies
 */
const { invoice_download, invoice_detailProductIsLoading } = inject<IInvoiceProvided>('invoice')!;

const toast = useToast();

const onClickDownload = () => {
  if (props.data && props.data.order && (props.data?.order?.billing || props.data?.order?.ref)) {
    invoice_download(props.data?.order?.billing ?? props.data?.order?.ref as string);
  }
};

const onClickCopy = (waybill: string) => {
  if (waybill) {
    navigator.clipboard.writeText(waybill);
    toast.add({
      severity: 'success',
      summary: 'Sukses',
      detail: 'Nomor resi di salin ke clipboard',
      life: 3000,
    });
  }
};
</script>

<template>
  <section v-if="invoice_detailProductIsLoading" id="invoice-header">
    <PrimeVueSkeleton class="!h-[230px]"></PrimeVueSkeleton>
  </section>
  <section v-else id="invoice-header">
    <div id="header-bill-summary" class="flex items-center justify-between">
      <div id="tittle-bill-order-list" class="text-[18px] font-medium">Ringkasan Tagihan</div>
      <PrimeVueButton
        v-if="data?.order?.status === 'BELUM DIBAYAR'"
        class="!bg-white md:!bg-black !border-[#E5E6E8] md:!border-black !px-3 md:!px-5 !py-3 !justify-center !items-center !text-center !flex"
        @click="onClickDownload"
      >
        <span class="hidden md:block">
          Unduh Tagihan
        </span>
        <span>
          <NuxtImg src="/icons/icon-export.svg" alt="warning-icon" class="md:hidden w-[20px] h-[20px]" />
        </span>
      </PrimeVueButton>
    </div>
    <div id="card-bill-sumary" class="my-5">
      <PrimeVueCard class="!bg-[#F5F6F6] !rounded-none">
        <template #content>
          <div id="content-card" class="flex flex-col md:flex-row text-black">
            <div id="detail" class="w-full md:w-1/3 mb-5 md:mb-3">
              <h4 class="font-semibold mb-2">Detail</h4>
              <div class="flex flex-col gap-y-2 text-sm">
                <div class="flex flex-row justify-between">
                  <p>Order</p>
                  <p>
                    {{ data?.order?.order_no ?? '-' }}
                  </p>
                </div>
                <div id="dn" class="flex flex-row justify-between">
                  <p>DN</p>
                  <p>
                    {{ data?.order?.dn ?? '-' }}
                  </p>
                </div>
                <div id="billing" class="flex flex-row justify-between">
                  <p>Biling</p>
                  <div class="text-right">
                    <p>
                      {{ data?.order?.billing ?? '-' }}
                    </p>
                    <p v-if="data?.order?.proforma_invoice_no" class="text-[#686F72]">
                      (Nomor billing sementara)
                    </p>
                  </div>
                </div>

                <div class="flex flex-row justify-between gap-x-2">
                  <div>Tanggal Pesanan</div>
                  <div class="text-right">
                    {{ data?.order?.order_date ? `${useFormatDateTime(data?.order?.order_date)}` : '-' }}
                  </div>
                </div>
                <div id="gi-date" class="flex flex-row justify-between">
                  <div>Tanggal GI</div>
                  <div>
                    {{ data?.order?.gi_date ? `${useFormatDateTime(data?.order?.gi_date)}` : '-' }}
                  </div>
                </div>
                <div
                  v-show="data?.order?.status?.toLocaleLowerCase()?.trim() !== 'lunas' && data?.order?.due_date"
                  id="due-date"
                  class="flex flex-row justify-between font-semibold text-[#E9151D]"
                >
                  <p class="uppercase font-semibold">Jatuh Tempo Tagihan</p>
                  <div>
                    {{ data?.order?.due_date ? `${useFormatDateTime(data?.order?.due_date)}` : '-' }}
                  </div>
                </div>
              </div>
            </div>

            <PrimeVueDivider
              class="border-input-gray border-1 !hidden md:!block"
              :pt="{
                root: '!mx-6',
              }"
              layout="vertical"
            />

            <div id="delivery" class="w-full md:w-1/3 mb-5 md:mb-3">
              <h4 class="font-semibold mb-2">Pengiriman</h4>
              <div class="text-sm">
                <div id="receipt" class="flex flex-row">
                  <p class="w-1/4">No. Resi</p>
                  <p class="px-2">:</p>
                  <div class="w-3/4 flex flex-row items-center">
                    <span>
                      {{ data?.shipment?.nomor_resi?.[0]?.noresi ?? '-' }}
                    </span>
                    <PrimeVueButton
                      v-if="data?.shipment?.nomor_resi?.[0]?.noresi"
                      class="!bg-transparent !border-transparent !text-[#147FFF] !py-0 !px-1"
                      :class="[
                        false
                          ? '!bg-gray-300 !text-gray-500 !no-underline cursor-not-allowed'
                          : '!bg-transparent !border-transparent !text-[#147FFF] !underline',
                      ]"
                      @click="onClickCopy(data?.shipment?.nomor_resi?.[0]?.noresi)"
                    >
                      Salin
                    </PrimeVueButton>
                  </div>
                </div>
                <div id="address" class="flex flex-row justify-between">
                  <p class="w-1/4">Alamat</p>
                  <p class="px-2">:</p>
                  <div class="w-3/4">
                    <p>
                      {{ data?.shipment?.address?.company_name ?? '-' }}
                    </p>
                    <p class="mb-3">{{ data?.shipment?.address?.phone}}</p>
                    <p>
                      {{ data?.shipment?.address?.address }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <PrimeVueDivider
              class="border-input-gray border-1 !hidden md:!block"
              :pt="{
                root: '!mx-6',
              }"
              layout="vertical"
            />

            <div id="order-summary" class="w-full md:w-1/3">
              <div class="flex justify-between">
                <h4 class="font-semibold mb-2">Ringkasan Pesanan</h4>
                <PrimeVueChip
                  v-if="data?.order?.status"
                  class="!text-sm"
                  :class="
                    data?.order?.status?.trim()?.toLocaleLowerCase() === 'lunas'
                      ? '!bg-[#05964C] !text-white !rounded-md !py-1'
                      : '!bg-red-500 !text-white !rounded-md !py-1'
                  "
                >
                  {{ capitalizeFirstLetter(data?.order?.status ?? '') ?? '-' }}
                </PrimeVueChip>
              </div>
              <div class="flex flex-col mt-4 h-full relative text-sm">
                <div id="sub-total" class="flex flex-row justify-between">
                  <div class="flex flex-col md:flex-row gap-x-1">
                    <p>Sub-Total</p>
                    <p>({{ data?.order?.qty_items ?? '-' }} BARANG)</p>
                  </div>
                  <p class="text-right">
                    {{ data?.order?.sub_total ? useCurrencyFormat(data?.order?.sub_total) : '-' }}
                  </p>
                </div>
                <div id="discounts" class="flex flex-row justify-between">
                  <p>Diskon</p>
                  <p class="text-right">
                    {{ data?.order?.discount ? useCurrencyFormat(data?.order?.discount) : '-' }}
                  </p>
                </div>
                <div class="mt-10">
                  <PrimeVueDivider class="border-input-gray border-1" />
                  <div class="flex flex-row justify-between">
                    <p class="font-semibold">Total</p>
                    <p class="text-lg font-bold text-right">
                      {{ data?.order?.total ? useCurrencyFormat(data?.order?.total) : '-' }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </PrimeVueCard>
    </div>
  </section>
</template>
