<script setup lang="ts">
</script>

<template>
  <section>
    <div class="!w-full !grid !grid-cols-[64px_1fr] md:!grid-cols-[159px_1fr] !gap-4 !mb-3">
      <PrimeVueSkeleton class="!w-[64px] !h-[64px] md:!w-[159px] md:!h-[159px] !rounded" />

      <div class="!w-full">
        <div class="!flex !items-center !gap-3">
          <PrimeVueSkeleton class="!w-[60px] !h-[20px] !rounded-lg" />
          <PrimeVueSkeleton class="!w-[100px] !h-[20px] !rounded" />
        </div>

        <PrimeVueSkeleton class="!w-[200px] !h-[28px] md:!h-[36px] !mt-3 !rounded !mb-2" />

        <div class="!flex !items-center !gap-3 !mb-2">
          <PrimeVueSkeleton class="!w-[80px] !h-[20px] !rounded-xl" />
        </div>

        <div class="!flex !w-full !items-center !justify-between !mb-2">
          <PrimeVueSkeleton class="!w-[120px] !h-[20px] !rounded" />
          <PrimeVueSkeleton class="!w-[80px] !h-[20px] !rounded" />
        </div>

        <PrimeVueSkeleton class="!w-full !h-[40px] !rounded" />
      </div>
    </div>
  </section>
</template>
