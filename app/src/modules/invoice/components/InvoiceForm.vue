
<script setup lang="ts">
/**
 * @description Injecting dependencies
 */
const {
    taxInvoice_formData,
    taxInvoice_formValidations,
    taxInvoice_onSubmit,
    taxInvoice_isShowModal
} = inject<IAuthenticationLoginProvide>('invoiceTax')!;

const type = ref([
    '020','010','000'
])

</script>

<template>
    <PrimeVueDialog
        v-model:visible="taxInvoice_isShowModal"
        modal
        :dismissableMask="true"
        :closable="false"
        :style="{ width: '50vw' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      >
        <template #default>
            <div class="flex items-center justify-center flex-col p-6">
                <NuxtImg src="/icons/send-mail.svg" alt="close-circle" class="w-[5em] h-[5em]" />
                <div class="font-bold text-[18px] text-[#18191A]">Invoice Berhasil Dikirim!</div>
                <div class="font-regular text-[14px] text-[#686F72] my-4">
                    Nomor invoice belanja Anda telah berhasil diinput. Faktur pajak akan segera diproses, dan invoice akan dikirim ke email yang terdaftar.
                    <br> <br>
                    Jika tidak menerima email dalam beberapa saat, silakan periksa folder spam atau hubungi tim support kami.
                </div>
                <PrimeVueButton
                label="Kembali"
                type="submit"
                class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
                />
            </div>
        </template>
    </PrimeVueDialog>
  <section id="authentication-login-form" class="flex flex-col items-center justify-center h-full w-full">
    <BaseAlert>
      <template #icon-prefix>
        <NuxtImg src="/icons/warning-danger.svg" alt="warning-icon" class="w-4 h-4" />
      </template>
    </BaseAlert>
    <form class="w-full" @submit.prevent="taxInvoice_onSubmit">
        <div class="bg-[#D4EAFF] px-3 py-3 my-4 flex gap-2 justify-start text-xs items-center">
            <img src="/icons/info-blue.svg"/>
            Masukkan nomor invoice belanja untuk memproses faktur pajak anda
        </div>
        <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="jenis_pajak"
            name="Jenis Pajak"
            spacing-bottom="mb-0"
            :validators="taxInvoice_formValidations.type"
            >
        <BaseSelectInput 
            v-model:selected="taxInvoice_formData.type" 
            :class="{ ...classes }"
            placeholder="Pilih Jenis Pajak"
            :disable="false"
            :options="type"
        />
        </BaseFormGroup>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mt-4"
          is-name-as-label
          label-for="invoice"
          name="Nomor Invoice Belanja"
          :validators="taxInvoice_formValidations.invoice_no"
        >
          <input
            id="invoice"
            v-model="taxInvoice_formData.invoice_no"
            v-bind="{ ...useBindStateForm('Silahkan Masukkan Invoice ') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            v-on="useListenerForm(taxInvoice_formValidations, 'invoice_no')"
          />
        </BaseFormGroup>

        <PrimeVueButton
          :disabled="taxInvoice_formValidations.$invalid"
          label="Submit"
          type="submit"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
        />
      </form>
  </section>
</template>
