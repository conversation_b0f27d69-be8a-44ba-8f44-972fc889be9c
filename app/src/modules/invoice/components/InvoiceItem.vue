<script setup lang="ts">
// import { FALLBACK_PRODUCT_IMAGE } from '../../product/constants/product.constant';

/**
 * Interfaces
 */
import InvoiceListLabel from './InvoiceListLabel.vue';
import type { IInvoiceListItem } from '../interfaces/invoice.interface';

const props = defineProps<{ invoice: IInvoiceListItem }>();
const router = useRouter();

const FALLBACK_PRODUCT_IMAGE =
  'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';

/**
 * Navigate to invoice detail
 */
const onClickDetailBill = () => {
  if (props.invoice.invoice_no) {
    if (props.invoice.invoice_status?.trim()?.toLowerCase() === 'menunggu konfirmasi') {
      router.push({
        name: 'bill.detail',
        params: {
          invoiceNumber: props.invoice.invoice_no,
        },
      });
    } else {
      router.push({
        name: 'bill.detail',
        params: {
          invoiceNumber: props.invoice.invoice_no,
        },
      });
    }
  }
};
</script>

<template>
  <section :id="'order-item' + invoice.invoice_no">
    <div class="w-full grid grid-cols-[64px_1fr] md:grid-cols-[159px_1fr] gap-4 mb-3">
      <div class="w-[64px] h-[64px] md:w-[159px] md:h-[159px] bg-grey-200">
        <NuxtLink>
          <NuxtImg
            :src="invoice.image ? invoice.image : FALLBACK_PRODUCT_IMAGE"
            alt="product"
            class="w-full h-full object-cover"
          />
        </NuxtLink>
      </div>
      <div class="w-full">
        <div class="flex items-center gap-3">
          <InvoiceListLabel :status="invoice.invoice_status" :text="invoice.invoice_status" />
          <p
            v-if="invoice.invoice_status?.toLocaleLowerCase()?.trim() !== 'lunas'"
            class="text-[#E9151D] font-semibold uppercase text-[11px] md:text-sm"
          >
            Jatuh tempo {{ invoice.jatuh_tempo ? useFormatDateTime(invoice.jatuh_tempo) : '-' }}
          </p>
        </div>
        <h4 class="font-bold font-druk text-lg md:text-[28px] leading-none mb-2 mt-3">
          {{ invoice?.product_name ?? '-' }}
        </h4>
        <div class="flex items-center gap-3 mb-2">
          <div class="border border-[#ACB1B4] rounded-full inline-flex px-2.5 py-1 leading-none mb-2">
            <p class="text-xs font-medium">{{ invoice.total_produk }} Produk</p>
          </div>
        </div>

        <div class="flex w-full items-center justify-between mb-2">
          <p class="font-semibold">Total Tagihan</p>
          <p
            class="font-bold text-lg"
            :class="[invoice.invoice_status?.toLocaleLowerCase()?.trim() !== 'lunas' && 'text-[#E9151D]']"
          >
            {{
              invoice.invoice_status?.toLocaleLowerCase()?.trim() === 'lunas'
                ? '-'
                : useCurrencyFormat(parseInt(invoice.total_tagihan)) ?? '-'
            }}
          </p>
        </div>
        <div class="w-full">
          <PrimeVueButton
            type="button"
            label="Detail Tagihan"
            variant="outlined"
            severity="secondary"
            class="flex gap-2 w-full !text-black !text-sm font-medium !border-[#ACB1B4] !px-7"
            @click="onClickDetailBill"
          >
            <span> Detail Tagihan</span>
            <NuxtImg src="/icons/chevron-right.svg" alt="product" class="w-[16px] h-[16px]" />
          </PrimeVueButton>
        </div>
      </div>
    </div>
  </section>
</template>
