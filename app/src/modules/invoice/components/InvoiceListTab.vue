<script setup lang="ts">
// interfaces
import type { IInvoiceProvided } from '../interfaces/invoice.interface';

// constants
import { INVOICE_STATUS_TABS } from '../constants/invoice.constant';

/**
 * @description Injecting dependencies
 */
const { invoice_listQueryParams } = inject<IInvoiceProvided>('invoice')!;

const onChangeTab = (val: string | number) => {
  invoice_listQueryParams.status = String(val);
  invoice_listQueryParams.page = 1;
};
</script>

<template>
  <section id="order-product-list" class="flex flex-col gap-4 mb-6">
    <div class="flex flex-col w-full items-center justify-between gap-3">
      <div class="w-full">
        <div class="mt-2">
          <PrimeVueTabs
            :value="invoice_listQueryParams.status || INVOICE_STATUS_TABS[0].value"
            @update:value="onChangeTab"
          >
            <PrimeVueTabList
              class="w-fit"
              :pt="{
                activeBar: '!bg-header-orange',
              }"
            >
              <PrimeVueTab
                v-for="(tab, tabIndex) in INVOICE_STATUS_TABS"
                :key="`tab-${tabIndex}`"
                :value="tab.value"
                :pt="{
                  root: `text-sm !px-3 !py-2 ${useActiveTab(invoice_listQueryParams.status, tab.value)}`,
                }"
              >
                {{ tab.label }}
              </PrimeVueTab>
            </PrimeVueTabList>
          </PrimeVueTabs>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped></style>
