<script setup lang="ts">
import BaseRangeDatePicker from '~/app/src/core/components/base/BaseRangeDatePicker.vue';
import type { IInvoiceProvided } from '../interfaces/invoice.interface';

/**
 * @description Injecting dependencies
 */
const { invoice_listQueryParams } = inject<IInvoiceProvided>('invoice')!;

// refs
const searchValue = ref('');

const onClickClearSearchQuery = () => {
  searchValue.value = '';
  invoice_listQueryParams.text = null;
};

const onChangeDatePicker = (value1: string, value2: string) => {
  const valueStartDate = convertToPayloadDate(value1);
  const valueEndDate = convertToPayloadDate(value2);

  if (valueStartDate && valueEndDate) {
    invoice_listQueryParams.date_from = valueStartDate;
    invoice_listQueryParams.date_to = valueEndDate;
  }
};

const onResetDate = () => {
  invoice_listQueryParams.date_from = null;
  invoice_listQueryParams.date_to = null;
};

/**
 * @description Handle side effect when reactive data binding is changed
 */
watch(
  searchValue,
  useDebounce(searchQuery => {
    invoice_listQueryParams.text = searchQuery as string;
  }, 500),
);
</script>

<template>
  <header id="order-filter" class="flex items-center justify-between gap-4 py-2 mb-4">
    <div class="flex items-center border border-gray-300 has-[input:focus-within]:outline-1 has-[input:focus-within]:-outline-offset-1 rounded-lg has-[input:focus-within]:outline-black sm:w-[320px] w-full">
      <div id="icon-search" class="w-10 justify-items-center">
        <NuxtImg src="/icons/magnifying-glass.svg" alt="search-icon" class="w-5 h-5" />
      </div>
      <div class="relative w-full">
        <input
          v-model="searchValue"
          placeholder="Masukan nomor order"
          type="text"
          class="outline-none h-[45px] px-1 w-full"
        />
        <button
          v-if="searchValue?.length > 0"
          class="absolute h-[45px] w-[32px] rounded-full top-0 right-0 cursor-pointer"
          @click="onClickClearSearchQuery"
        >
          <NuxtImg src="/icons/close.svg" alt="close" class="w-4 h-4" />
        </button>
      </div>
    </div>
    <div>
      <BaseRangeDatePicker :on-change="onChangeDatePicker" :on-reset="onResetDate"/>
    </div>
  </header>
</template>