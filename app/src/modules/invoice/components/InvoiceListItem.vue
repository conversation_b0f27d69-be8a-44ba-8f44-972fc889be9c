<script setup lang="ts">
// interfaces
import type { IInvoiceProvided } from '../interfaces/invoice.interface';

// constants
import { INVOICE_LIST_PAGINATION_SIZE } from '../constants';

// components
import InvoiceItem from './InvoiceItem.vue';
import InvoiceItemSkeleton from './InvoiceItemSkeleton.vue';

/**
 * @description Injecting dependencies
 */
const {
  invoice_listData,
  invoice_listPagination,
  invoice_listQueryParams,
  invoice_listIsLoading
} = inject<IInvoiceProvided>('invoice')!;

const onChangePerPage = (value: number) => {
  invoice_listQueryParams.per_page = value;
  invoice_listQueryParams.page = 1;
};
const onClickPrev = () => {
  invoice_listQueryParams.page = invoice_listQueryParams.page - 1;
};
const onClickNext = () => {
  invoice_listQueryParams.page = invoice_listQueryParams.page + 1;
};
</script>

<template>
  <section id="bill-list-container">
    <div v-if="invoice_listIsLoading" >
      <InvoiceItemSkeleton v-for="i in 3" :key="i"/>
    </div>
    <div v-else-if="invoice_listData.length > 0" id="order-product-list" class="flex flex-col gap-4">
      <div class="flex flex-col w-full gap-3">
        <InvoiceItem v-for="invoice in invoice_listData" :key="invoice.invoice_no" :invoice="invoice" />
      </div>
      <div class="border-y border-y-[#CED1D3] py-2">
        <BasePagination
          :data="invoice_listPagination"
          :click-action-prev="onClickPrev"
          :click-action-next="onClickNext"
          :click-action-per-page="onChangePerPage"
          :sizes="INVOICE_LIST_PAGINATION_SIZE"
        />
      </div>
    </div>
    <BaseEmptyState v-else title="Tagihan tidak ditemukan" subtitle="" />
  </section>
</template>
