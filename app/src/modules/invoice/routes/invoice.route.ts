// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/bill',
    component: AppBaseWrapper,
    children: [
      {
        path: ':invoiceNumber',
        name: 'bill.detail',
        component: () => import('../views/InvoiceDetailUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            {
              label: 'Beranda',
              path: '/dashboard',
            },
            {
              label: 'Tagihan',
              path: '/bill',
            },
            {
              label: 'Detail Tagihan',
              path: null,
            },
          ],
        },
      },
      {
        path: '',
        name: 'bill',
        component: () => import('../views/InvoiceListUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            {
              label: 'Beranda',
              path: '/',
            },
            {
              label: '<PERSON>ihan',
              path: null,
            },
          ],
        },
      },
    ],
  },
  {
    path: '/invoice-tax',
    name: 'invoice-tax',
    component: () => import('../views/InvoiceTaxUI.vue'),
    meta: {
      breadcrumbs: [
        {
          label: 'Beranda',
          path: '/',
        },
        {
          label: 'Faktur Pajak',
          path: null,
        },
      ],
    },
  },
];

export default routes;
