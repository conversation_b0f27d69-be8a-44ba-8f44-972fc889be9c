// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/authentication',
    component: AppBaseWrapper,
    children: [
      {
        path: 'create-password',
        name: 'create-password',
        component: () => import('../views/AuthenticationCreatePasswordUI.vue'),
      },
      {
        path: 'register-form',
        name: 'register-form',
        component: () => import('../views/AuthenticationRegistrationUI.vue'),
      },
      {
        path: 'register-mobile',
        name: 'register-mobile',
        component: () => import('../views/AuthenticationRegistrationMobileUI.vue'),
      },
      {
        path: 'password-reset',
        name: 'password-reset',
        component: () => import('../views/AuthenticationCreatePasswordUI.vue'),
      },
      {
        path: 'register-detail',
        name: 'register-detail',
        component: () => import('../views/AuthenticationRegistrationDetailUI.vue'),
      },
      {
        path: 'forgot-password',
        name: 'forgot-password',
        component: () => import('../views/AuthenticationForgotPasswordUI.vue'),
      },
      {
        path: 'login',
        name: 'login',
        component: () => import('../views/AuthenticationLoginUI.vue'),
      },
      {
        path: 'register-success',
        name: 'register-success',
        component: () => import('../views/AuthenticationRegistrationSuccessUI.vue'),
      },
      {
        path: 'activation',
        name: 'activation',
        component: () => import('../views/ActivationAccountUI.vue'),
      },
    ],
  },
];

export default routes;
