<script setup lang="ts">
// Constants
import { AUTHENTICATION_PASSWORD_VALIDATIONS } from '../constants/authentication.constant';

// Interfaces
import type { IAuthenticationCreatePasswordProvided } from '../interfaces/authentication-create-password.interface';

/**
 * @description Injecting dependencies
 */
const {
  authenticationCreatePassword_formData,
  authenticationCreatePassword_formValidations,
  authenticationCreatePassword_isCustomValidationsValid,
  authenticationCreatePassword_isLoading,
  authenticationCreatePassword_onSubmit,
  authenticationCreatePassword_verifyEmail,
  authenticationCreatePassword_setDynamicClassLabelValidation,
  authenticationCreatePassword_setDynamicPathIcon,
} = inject<IAuthenticationCreatePasswordProvided>('authenticationCreatePassword')!;
console.log(authenticationCreatePassword_verifyEmail)
</script>

<template>
  <section
    id="authentication-create-password form"
    class="w-full h-full relative inset-0 z-0 flex flex-col items-center justify-center"
  >
    <BaseLoader v-show="authenticationCreatePassword_isLoading" />
    <BaseAlert />

    <div class="mx-auto w-full max-w-lg p-6 border border-hero-light-gray">
      <header class="mb-5">
        <h1 class="font-druk text-4xl text-black mb-2">Sandi Baru</h1>
        <p class="text-base text-muted leading-6">Silakan masukan sandi terbaru</p>
      </header>
      <!-- {{ }} -->
      <form class="flex flex-col gap-4" @submit.prevent="authenticationCreatePassword_onSubmit">
        <fieldset class="flex flex-col gap-4 mb-4">
          <legend class="font-medium text-sm text-black">Email</legend>
          <p class="font-bold text-base text-black leading-6">{{authenticationCreatePassword_verifyEmail}}</p>
        </fieldset>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Sandi Baru"
          spacing-bottom="mb-0"
          :validators="authenticationCreatePassword_formValidations.password"
        >
          <BaseInputPassword
            id="password"
            v-model="authenticationCreatePassword_formData.password"
            :class="{ ...classes }"
            :readonly="authenticationCreatePassword_isLoading"
            type="password"
            placeholder="********"
            v-on="useListenerForm(authenticationCreatePassword_formValidations, 'password')"
          />
        </BaseFormGroup>

        <section
          id="password-validation"
          class="flex flex-col gap-2 py-2 px-4 rounded-lg"
          :class="[authenticationCreatePassword_isCustomValidationsValid ? 'bg-light-success' : 'bg-light-gray']"
        >
          <h6
            class="text-sm"
            :class="[
              authenticationCreatePassword_isCustomValidationsValid ? 'text-success' : 'text-muted-secondary',
            ]"
          >
            Password harus memiliki setidaknya:
          </h6>

          <template
            v-for="(validation, validationIndex) in AUTHENTICATION_PASSWORD_VALIDATIONS"
            :key="`password-validation-${validationIndex}`"
          >
            <section id="validation" class="flex items-center gap-2">
              <template v-if="authenticationCreatePassword_formData.password">
                <NuxtImg
                  :src="authenticationCreatePassword_setDynamicPathIcon(validation.key)"
                  alt="icon-password-validation"
                  class="w-4 h-4"
                />
              </template>

              <template v-else>
                <NuxtImg src="/icons/min-circle-muted.svg" alt="icon-min-circle-muted" class="w-4 h-4" />
              </template>

              <span
                class="text-sm"
                :class="[authenticationCreatePassword_setDynamicClassLabelValidation(validation.key)]"
              >
                {{ validation.label }}
              </span>
            </section>
          </template>
        </section>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password_confirmation"
          name="Konfirmasi Sandi Baru"
          spacing-bottom="mb-0"
          :validators="authenticationCreatePassword_formValidations.password_confirmation"
        >
          <BaseInputPassword
            id="password_confirmation"
            v-model="authenticationCreatePassword_formData.password_confirmation"
            :class="{ ...classes }"
            :readonly="authenticationCreatePassword_isLoading"
            type="password"
            placeholder="********"
            v-on="useListenerForm(authenticationCreatePassword_formValidations, 'password_confirmation')"
          />
        </BaseFormGroup>

        <button
          class="block w-full bg-hero-black font-sans text-base text-white h-10 font-bebas py-2 rounded-lg"
          :class="{ 'opacity-50': authenticationCreatePassword_isLoading }"
          :disabled="authenticationCreatePassword_isLoading"
          type="submit"
        >
          Sandi Baru
        </button>
      </form>
    </div>
  </section>
</template>
