<script setup lang="ts">
// Constants
import { AUTHENTICATION_PASSWORD_VALIDATIONS } from '../constants/authentication.constant';
// Interfaces
import type { IAuthenticationRegisterProvided } from '../interfaces/authentication-register.interface';

/**
 * @description Injecting dependencies
 */

const {
  authenticationRegister_formData,
  authenticationRegister_formValidations,
  authenticationRegister_isCustomValidationsValid,
  authenticationRegister_isLoading,
  authenticationRegister_onSubmit,
  authenticationRegister_setDynamicClassLabelValidation,
  authenticationRegister_setDynamicPathIcon,
} = inject<IAuthenticationRegisterProvided>('authenticationRegister')!;

const onSubmit = () => {
  console.log('authenticationRegister_formValidations', authenticationRegister_formValidations);
  authenticationRegister_onSubmit();
};
</script>

<template>
  <section
    id="authentication-create-password form"
    class="w-full h-full relative inset-0 z-0 flex flex-col items-center justify-center"
  >
    <BaseLoader v-show="authenticationRegister_isLoading" />
    <BaseAlert class="my-[1em]" />
    <div class="mx-auto w-full max-w-lg p-6 border border-hero-light-gray">
      <header class="mb-5">
        <h1 class="font-druk text-4xl text-black mb-2">Pendaftaran</h1>
        <p class="text-base text-muted leading-6">Silakan daftarkan diri Anda</p>
      </header>
      <form class="flex flex-col gap-4">
        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="email"
          name="Email"
          :validators="authenticationRegister_formValidations.email"
          :is-mandatory="true"
        >
          <input
            id="email"
            v-model="authenticationRegister_formData.email"
            v-bind="{ ...useBindStateForm('<EMAIL>') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            :readonly="authenticationRegister_isLoading"
            v-on="useListenerForm(authenticationRegister_formValidations, 'email')"
          />
        </BaseFormGroup>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Kata Sandi"
          spacing-bottom="mb-0"
          :validators="authenticationRegister_formValidations.password"
          :is-mandatory="true"
        >
          <BaseInputPassword
            id="password"
            v-model="authenticationRegister_formData.password"
            class="rounded-lg"
            :class="{ ...classes }"
            :readonly="authenticationRegister_isLoading"
            type="password"
            placeholder="********"
            v-on="useListenerForm(authenticationRegister_formValidations, 'password')"
          />
        </BaseFormGroup>
        <section
          id="password-validation"
          class="flex flex-col gap-2 py-2 px-4 rounded-lg"
          :class="[authenticationRegister_isCustomValidationsValid ? 'bg-light-success' : 'bg-light-gray']"
        >
          <h6
            class="text-sm"
            :class="[authenticationRegister_isCustomValidationsValid ? 'text-success' : 'text-muted-secondary']"
          >
            Password harus memiliki setidaknya:
          </h6>

          <template
            v-for="(validation, validationIndex) in AUTHENTICATION_PASSWORD_VALIDATIONS"
            :key="`password-validation-${validationIndex}`"
          >
            <section id="validation" class="flex items-center gap-2">
              <template v-if="authenticationRegister_formData.password">
                <NuxtImg
                  :src="authenticationRegister_setDynamicPathIcon(validation.key)"
                  alt="icon-password-validation"
                  class="w-4 h-4"
                />
              </template>

              <template v-else>
                <NuxtImg src="/icons/min-circle-muted.svg" alt="icon-min-circle-muted" class="w-4 h-4" />
              </template>

              <span
                class="text-sm"
                :class="[authenticationRegister_setDynamicClassLabelValidation(validation.key)]"
              >
                {{ validation.label }}
              </span>
            </section>
          </template>
        </section>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password_confirmation"
          name="Konfirmasi Sandi Baru"
          spacing-bottom="mb-0"
          :validators="authenticationRegister_formValidations.password_confirmation"
          :is-mandatory="true"
        >
          <BaseInputPassword
            id="password_confirmation"
            v-model="authenticationRegister_formData.password_confirmation"
            :class="{ ...classes }"
            class="rounded-lg"
            :readonly="authenticationRegister_isLoading"
            type="password"
            placeholder="********"
            v-on="useListenerForm(authenticationRegister_formValidations, 'password_confirmation')"
          />
        </BaseFormGroup>
        <PrimeVueButton
          :disabled="authenticationRegister_isLoading || authenticationRegister_formValidations.$invalid"
          label="Lanjut"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
          @click.prevent="onSubmit"
        />
      </form>
    </div>
  </section>
</template>
