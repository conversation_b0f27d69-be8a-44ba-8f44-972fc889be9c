<script setup lang="ts">
// Interfaces
import type { IAuthenticationRegisterDetailProvided } from '../interfaces/authentication-register-detail.interface';

// Vuelidate
import type { BaseValidation, ErrorObject } from '@vuelidate/core';

// helper
import type { ITermsAndConditions, TncSection } from '../interfaces/authentication-register-mobile.interface';
/**
 * @description Injecting dependencies
 */

const {
  authenticationRegisterDetail_formData,
  authenticationRegisterDetail_isLoading,
  authenticationRegisterDetail_formValidations,
  authenticationRegisterDetail_onSubmitForm,
  authenticationRegisterDetail_onSubmitTnc,
  authenticationRegisterDetail_attachment,
  authenticationRegisterDetail_fileValidations,
  authenticationRegisterDetail_isShowModalTnc,
  authenticationRegisterDetail_validateNPWP,
  authenticationRegisterDetail_validateKTP,
  authenticationRegisterDetail_tncBox_1,
  authenticationRegisterDetail_tncBox_2,
  authenticationRegisterDetail_toggleInoviceBill,
  authenticationRegisterDetail_isNeedInvoiceBill,
  // ktp
  authentication_isUploadKtpError,
  authentication_isUploadingKtp,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  authentication_uploadKtpFileName,
  // npwp
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
  // api
  authentication_listProvince,
  authentication_listCities,
  authentication_listSubdistrict,
  authentication_listNpwpProvince,
  authentication_listNpwpCities,
  authentication_listNpwpSubdistrict,
  authentication_taxType,
  authentication_termAndCondition,
  authentication_isExistNPWP,
  authentication_isExistKTP,
} = inject<IAuthenticationRegisterDetailProvided>('authenticationRegisterDetail')!;

const isShowModalChangeIdentity = ref<boolean>(false);
const isShowModalChangeNpwp = ref<boolean>(false);
const dragging = ref<boolean>(false);
const selectedFile = ref<File | null>(null);
const invoice_number = ref(['02', '03', '04', '07']);

// function to handle select file
// return: none
const handleFileSelected = (event: Event, field: 'file_identity' | 'file_npwp') => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    selectedFile.value = file;
    authenticationRegisterDetail_attachment[field] = file;
  }
};

// function to handle drop file
// return: none
const handleDrop = (event: DragEvent, field: 'file_identity' | 'file_npwp') => {
  event.preventDefault();
  dragging.value = false;
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0];
    console.log(file);
    selectedFile.value = file;
    authenticationRegisterDetail_attachment[field] = file;
  }
};

// function to open file picker
// return: none
const openFilePicker = (field: 'file_identity' | 'file_npwp') => {
  const fileInput = document.getElementById(`fileInput-${field}`) as HTMLInputElement;
  fileInput.click();
};

// function to get error
// return: error value
const getFileError = (fieldName: 'file_identity' | 'file_npwp'): ComputedRef<ErrorObject | null> =>
  computed(() => {
    const fieldValidation = authenticationRegisterDetail_fileValidations.value[fieldName] as BaseValidation;
    if (!fieldValidation || fieldValidation.$errors.length === 0) return null;
    console.log(fieldValidation.$errors[0]);
    return fieldValidation.$errors[0];
  });

//function to filter section in data tnc
//return: data tnc
const sections = computed(() =>
  authentication_termAndCondition.value
    ? Object.entries(authentication_termAndCondition.value)
        .filter(([key, value]) => key !== 'footer' && typeof value !== 'string')
        .map(([_, value]) => value as TncSection)
    : [],
);

const errorKTP = getFileError('file_identity');
const errorNPWP = getFileError('file_npwp');

const onChangeValidateNPWP = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length === 16) {
    authenticationRegisterDetail_validateNPWP(value);
  }
}, 500);

const onChangeValidateKTP = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length === 16) {
    authenticationRegisterDetail_validateKTP(value);
  }
}, 500);

const getIsValidNPWPMessage = computed(() => {
  if (
    authenticationRegisterDetail_formData?.npwp_number?.length >= 15 &&
    authentication_isExistNPWP.value == true
  ) {
    return 'Nomor NPWP Sudah terapakai';
  } else {
    return '';
  }
});

const getIsValidKTPMessage = computed(() => {
  if (authenticationRegisterDetail_formData?.ktp?.length >= 16 && authentication_isExistKTP.value == true) {
    return 'Nomor KTP Sudah terpakai';
  } else {
    return '';
  }
});

const onChangeShipmentProvince = () => {
  authenticationRegisterDetail_formData.shipment_city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  authenticationRegisterDetail_formData.shipment_sub_district = {
    name: '',
    code: '',
  };
};

const onChangeShipmentCity = () => {
  authenticationRegisterDetail_formData.shipment_sub_district = {
    name: '',
    code: '',
  };
};

const onChangeNpwpProvince = () => {
  authenticationRegisterDetail_formData.npwp_city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  authenticationRegisterDetail_formData.npwp_sub_district = {
    name: '',
    code: '',
  };
};

const onChangeNpwpCity = () => {
  authenticationRegisterDetail_formData.npwp_sub_district = {
    name: '',
    code: '',
  };
};

const onSubmitForm = () => {
  console.log('authenticationRegisterDetail_formValidations', authenticationRegisterDetail_formValidations);
  authenticationRegisterDetail_onSubmitForm();
};
</script>
<template>
  <section
    id="authentication-create-password form"
    class="relative inset-0 z-0 flex flex-col items-center justify-center"
  >
    <hr />
    <BaseLoader v-show="authenticationRegisterDetail_isLoading" />
    <BaseAlert />
    <div class="mx-12 w-5/6 p-6 border border-hero-light-gray my-[5em]">
      <header class="mb-5">
        <h1 class="font-druk text-4xl text-black mb-2">Pendaftaran</h1>
        <p class="text-base text-muted leading-6">Silakan daftarkan diri Anda</p>
      </header>
      <h1 class="text-muted font-bold mt-2 mb-4 text-xl">Informasi Diri</h1>
      <form class="flex flex-col gap-4" @submit.prevent>
        <section class="grid sm:grid-cols-2 grid-cols-1 sm:gap-5 gap-0 sm:gap-y-4 gap-y-2">
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="nama_lengkap"
            name="Nama Lengkap"
            :validators="authenticationRegisterDetail_formValidations.full_name"
            :is-mandatory="true"
          >
            <input
              id="nama_lengkap"
              v-model="authenticationRegisterDetail_formData.full_name"
              v-bind="{ ...useBindStateForm('Tambahkan Nama Lengkap') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'full_name')"
            />
          </BaseFormGroup>
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="email"
            name="Email"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.email"
          >
            <input
              id="email"
              v-model="authenticationRegisterDetail_formData.email"
              v-bind="{ ...useBindStateForm('Tambahkan Email') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted bg-gray-100"
              :class="{ ...classes }"
              disabled
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'email')"
            />
          </BaseFormGroup>
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="no_telp"
            name="Nomor Telepon"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.phone_number"
          >
            <PrimeVueInputGroup class="h-[3.1em]">
              <PrimeVueInputGroupAddon class="!bg-[#F9FAFB]">
                <p class="text-[#18191A] text-base font-normal">+62</p>
              </PrimeVueInputGroupAddon>
              <PrimeVueInputText
                id="nomor_telp"
                v-model="authenticationRegisterDetail_formData.phone_number"
                v-bind="{ ...useBindStateForm('Masukkan nomor handphone') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                :class="{ ...classes }"
                type="text"
                inputmode="numeric"
                v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'phone_number')"
              />
            </PrimeVueInputGroup>
          </BaseFormGroup>
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="perusahaan"
            name="Perusahaan"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.company"
          >
            <input
              id="perusahaan"
              v-model="authenticationRegisterDetail_formData.company"
              v-bind="{ ...useBindStateForm('Tambahkan Perusahaan') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'company')"
            />
          </BaseFormGroup>
        </section>
        <section class="grid grid-cols-1">
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="shipping_address"
            name="Alamat Pengiriman"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.shipping_address"
          >
            <textarea
              id="shipping_address"
              v-model="authenticationRegisterDetail_formData.shipping_address"
              rows="4"
              v-bind="{ ...useBindStateForm('Masukkan alamat pengiriman anda') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'shipping_address')"
            />
          </BaseFormGroup>
        </section>
        <section class="grid sm:grid-cols-2 grid-cols-1 sm:gap-5 gap-0 sm:gap-y-4 gap-y-4">
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="province"
            name="Provinsi"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.shipment_province"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.shipment_province"
              :class="{ ...classes }"
              class="rounded-lg"
              placeholder="Pilih Provinsi"
              :disable="false"
              :options="authentication_listProvince"
              @update:selected="onChangeShipmentProvince"
            />
          </BaseFormGroup>
          <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="City"
            name="Kota"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.shipment_city"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.shipment_city"
              placeholder="Pilih Kota"
              class="rounded-lg"
              :disable="false"
              :options="authentication_listCities"
              @update:selected="onChangeShipmentCity"
            />
          </BaseFormGroup>

          <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="Kecamatan"
            name="Kecamatan"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.shipment_sub_district"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.shipment_sub_district"
              placeholder="Pilih Kecamatan"
              class="rounded-lg"
              :disable="false"
              :options="authentication_listSubdistrict"
            />
          </BaseFormGroup>

          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="zip_code"
            name="Kode Pos"
            :validators="authenticationRegisterDetail_formValidations.shipment_zip_code"
            :is-mandatory="true"
          >
            <input
              id="zip_code"
              v-model="authenticationRegisterDetail_formData.shipment_zip_code"
              v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
              class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'shipment_zip_code')"
            />
          </BaseFormGroup>
        </section>
        <hr class="h-px my-8 bg-gray-200 border-0" />
        <h1 class="text-muted font-bold">Data NPWP</h1>
        <section class="grid grid-cols-1 gap-y-4">
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            :is-error-message="getIsValidKTPMessage"
            label-for="ktp"
            name="Nomor KTP"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.ktp"
          >
            <input
              id="ktp"
              v-model="authenticationRegisterDetail_formData.ktp"
              v-bind="{ ...useBindStateForm('Tambahkan Nomor KTP') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              :maxlength="16"
              :minlength="16"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'ktp')"
              @update:model-value="(value : string) => onChangeValidateKTP(value)"
            />
          </BaseFormGroup>
          <div class="font-medium text-black text-[14px]">Upload KTP</div>
          <!-- upload file KTP  -->
          <div
            v-if="authentication_isUploadingKtp"
            class="border-2 border-dashed border-[#CCD2D9] p-6 w-full justify-center h-[232px] pl-12 flex flex-col rounded-xl"
          >
            <p v-if="authentication_uploadKtpProgress === 100" class="mb-2 text-black font-semibold">
              Sukses! {{ authentication_uploadKtpProgress }}%
            </p>
            <PrimeVueProgressBar
              :value="authentication_uploadKtpProgress"
              class="!w-full !max-w-full !self-stretch"
              :pt="{
                root: '!bg-white !rounded-full',
                value: '!bg-header-green-500 !rounded-full',
              }"
            >
              <span></span>
            </PrimeVueProgressBar>
            <p class="mt-2">{{ authentication_uploadedKtpSize }} mb/{{ authentication_totalKtpSize }} mb</p>
          </div>
          <div v-else>
            <div
              v-if="authentication_uploadKtpProgress === 100"
              class="border-2 border-green-500 bg-green-100 p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-lg"
            >
              <div class="w-full justify-items-center my-5">
                <NuxtImg src="/icons/checklist-rounded-green.svg" alt="upload-icon" class="w-15 h-15 mx-auto" />
                <div class="mt-6 text-md font-medium text-green-700 text-center">Data Berhasil Terupload</div>
                <div v-if="authentication_uploadKtpFileName" class="mt-1 text-lg font-semibold text-black">
                  {{ authentication_uploadKtpFileName }}
                </div>
              </div>
              <input
                id="fileInput-file_identity"
                type="file"
                data-testid="bulk-file-input"
                accept=".pdf, .jpg, .jpeg, .png"
                class="hidden"
                @change="e => handleFileSelected(e, 'file_identity')"
              />
            </div>
            <div
              v-else
              class="border-2 border-dashed p-6 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 rounded-lg"
              :class="dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-gray-400'"
              data-testid="bulk-file-input-draggable"
              @dragover.prevent="dragging = true"
              @dragleave="dragging = false"
              @drop="handleDrop($event, 'file_identity')"
              @click="() => openFilePicker('file_identity')"
            >
              <div class="w-full justify-items-center my-5">
                <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10 mx-auto" />
              </div>
              <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
              <div class="mt-6 text-sm text-gray-500 mx-auto text-center">
                Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
              </div>
              <input
                id="fileInput-file_identity"
                accept=".pdf, .jpg, .jpeg, .png"
                type="file"
                class="hidden"
                @change="e => handleFileSelected(e, 'file_identity')"
              />
            </div>
          </div>
          <div
            v-if="authenticationRegisterDetail_fileValidations.file_identity.$errors[0]"
            class="bg-red-50 rounded-md p-1 px-3"
          >
            <PrimeVueMessage severity="error" variant="simple" size="small" class="">
              {{ errorKTP?.$message }}
            </PrimeVueMessage>
          </div>
          <div v-if="authentication_isUploadKtpError" class="bg-red-50 rounded-md p-1 px-3">
            <PrimeVueMessage severity="error" variant="simple" size="small"> Gagal Upload Data </PrimeVueMessage>
          </div>

          <div v-if="authentication_uploadKtpProgress === 100" class="flex w-full justify-end mt-4 gap-10">
            <PrimeVuebutton
              data-testid="btn-bulk-change-file"
              class="!text-white !font-semibold !bg-black !border-black"
              @click="isShowModalChangeIdentity = true"
              >Ubah File
            </PrimeVuebutton>
          </div>
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            :is-error-message="getIsValidNPWPMessage"
            label-for="nomor_npwp"
            name="Nomor NPWP"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.npwp_number"
          >
            <input
              id="nomor_npwp"
              v-model="authenticationRegisterDetail_formData.npwp_number"
              v-bind="{ ...useBindStateForm('Tambahkan Nomor NPWP') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              :maxlength="16"
              :minlength="15"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'npwp_number')"
              @update:model-value="(value : string) => onChangeValidateNPWP(value)"
            />
          </BaseFormGroup>
          <!-- ==================Upload File NPWP -->
          <div class="font-medium text-black text-[14px]">
            Upload NPWP <span class="text-[14px] text-[#E9151D]">*</span>
          </div>
          <div
            v-if="authentication_isUploadingNpwp"
            class="border-2 border-dashed border-[#CCD2D9] p-6 w-full justify-center h-[232px] pl-12 flex flex-col rounded-xl"
          >
            <p v-if="authentication_uploadNpwpProgress === 100" class="mb-2 text-black font-semibold">
              Sukses! {{ authentication_uploadNpwpProgress }}%
            </p>
            <PrimeVueProgressBar
              :value="authentication_uploadNpwpProgress"
              class="!w-full !max-w-full !self-stretch"
              :pt="{
                root: '!bg-white !rounded-full',
                value: '!bg-header-green-500 !rounded-full',
              }"
            >
              <span></span>
            </PrimeVueProgressBar>
            <p class="mt-2">{{ authentication_uploadedNpwpSize }} mb/{{ authentication_totalNpwpSize }} mb</p>
          </div>
          <div v-else>
            <div
              v-if="authentication_uploadNpwpProgress === 100"
              class="border-2 border-green-500 bg-green-100 p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-xl"
            >
              <div class="w-full justify-items-center my-5">
                <NuxtImg src="/icons/checklist-rounded-green.svg" alt="upload-icon" class="w-15 h-15 mx-auto" />
                <div class="mt-6 text-md font-medium text-green-700 text-center">Data Berhasil Terupload</div>
                <div v-if="authentication_uploadNpwpFileName" class="mt-1 text-lg font-semibold text-black">
                  {{ authentication_uploadNpwpFileName }}
                </div>
              </div>
              <input
                id="fileInput-file_npwp"
                type="file"
                data-testid="bulk-file-input"
                accept=".pdf, .jpg, .jpeg, .png"
                class="hidden"
                @change="e => handleFileSelected(e, 'file_npwp')"
              />
            </div>
            <div
              v-else
              class="border-2 border-dashed p-6 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 rounded-xl"
              :class="dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-gray-400'"
              data-testid="bulk-file-input-draggable"
              @dragover.prevent="dragging = true"
              @dragleave="dragging = false"
              @drop="e => handleDrop(e, 'file_npwp')"
              @click="() => openFilePicker('file_npwp')"
            >
              <div class="w-full justify-items-center my-5">
                <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10 mx-auto" />
              </div>
              <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
              <div class="mt-6 text-sm text-gray-500 mx-auto text-center">
                Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
              </div>
              <input
                id="fileInput-file_npwp"
                accept=".pdf, .jpg, .jpeg, .png"
                type="file"
                class="hidden"
                @change="e => handleFileSelected(e, 'file_npwp')"
              />
            </div>
          </div>
          <div
            v-if="authenticationRegisterDetail_fileValidations.file_npwp.$errors[0]"
            class="bg-red-50 rounded-md p-1 px-3"
          >
            <PrimeVueMessage severity="error" variant="simple" size="small">
              {{ errorNPWP?.$message }}
            </PrimeVueMessage>
          </div>
          <PrimeVueMessage v-if="authentication_isUploadNpwpError" severity="error" variant="simple" size="small">
            Gagal Upload Data
          </PrimeVueMessage>

          <div v-if="authentication_uploadNpwpProgress === 100" class="flex w-full justify-end mt-4 gap-10">
            <PrimeVuebutton
              data-testid="btn-bulk-change-file"
              class="!text-white !font-semibold !bg-black !border-black"
              @click="isShowModalChangeNpwp = true"
              >Ubah File</PrimeVuebutton
            >
          </div>

          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="jenis_pajak"
            name="Jenis Pajak"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.tax_type"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.tax_type"
              :class="{ ...classes }"
              class="rounded-lg"
              placeholder="Pilih Jenis Pajak"
              :disable="false"
              :options="authentication_taxType"
            />
          </BaseFormGroup>

          <div class="grid sm:grid-cols-2 grid-cols-1">
            <div class="flex items-center gap-1">
              <PrimeVueCheckbox
                input-id="term_tax"
                name="cart-check-all"
                class="mr-2"
                binary
                @click="authenticationRegisterDetail_toggleInoviceBill"
                @update:model-value="() => authenticationRegisterDetail_formValidations.invoice_number.$touch()"
              />
              <label for="term_tax" class="font-medium">Saya akan membutuhkan faktur pajak</label>
            </div>
            <BaseFormGroup
              v-slot="{ classes }"
              class-label="font-medium text-sm text-black block mb-2"
              :is-name-as-label="false"
              label-for="faktur_pajak"
              name="Tipe Faktur Pajak"
              spacing-bottom="mb-0"
              :validators="authenticationRegisterDetail_formValidations.invoice_number"
            >
              <BaseSelectInput
                v-model:selected="authenticationRegisterDetail_formData.invoice_number"
                :class="{ ...classes }"
                class="rounded-lg"
                placeholder="Pilih Tipe Faktur Pajak"
                :disable="authenticationRegisterDetail_isNeedInvoiceBill"
                :options="invoice_number"
              />
            </BaseFormGroup>
          </div>
          <hr class="h-px my-8 bg-gray-200 border-0" />
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="nama_npwp"
            name="Nama Sesuai NPWP"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.npwp_name"
          >
            <input
              id="nama_npwp"
              v-model="authenticationRegisterDetail_formData.npwp_name"
              v-bind="{ ...useBindStateForm('Tambahkan Sesuai NPWP') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :is-mandatory="true"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'npwp_name')"
            />
          </BaseFormGroup>
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="company_address"
            name="Alamat Perusahaan"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.npwp_address"
          >
            <textarea
              id="company_address"
              v-model="authenticationRegisterDetail_formData.npwp_address"
              rows="4"
              v-bind="{ ...useBindStateForm('Masukan alamat pengiriman anda') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'npwp_address')"
            />
          </BaseFormGroup>
        </section>
        <section class="grid sm:grid-cols-2 grid-cols-1 sm:gap-5 gap-0 sm:gap-y-4 gap-y-4">
          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="province"
            name="Provinsi"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.npwp_province"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.npwp_province"
              :class="{ ...classes }"
              class="rounded-lg"
              placeholder="Pilih Provinsi"
              :disable="false"
              :options="authentication_listNpwpProvince"
              @update:selected="onChangeNpwpProvince"
            />
          </BaseFormGroup>
          <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="City"
            name="Kota"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.npwp_city"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.npwp_city"
              placeholder="Pilih Kota"
              class="rounded-lg"
              :disable="!authenticationRegisterDetail_formData.npwp_province.code"
              :options="authentication_listNpwpCities"
              @update:selected="onChangeNpwpCity"
            />
          </BaseFormGroup>

          <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="Kecamatan"
            name="Kecamatan"
            spacing-bottom="mb-0"
            :is-mandatory="true"
            :validators="authenticationRegisterDetail_formValidations.npwp_sub_district"
          >
            <BaseSelectInput
              v-model:selected="authenticationRegisterDetail_formData.npwp_sub_district"
              placeholder="Pilih Kecamatan"
              class="rounded-lg"
              :disable="!authenticationRegisterDetail_formData.npwp_city.code"
              :options="authentication_listNpwpSubdistrict"
            />
          </BaseFormGroup>

          <BaseFormGroup
            v-slot="{ classes }"
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="zip_code"
            name="Kode Pos"
            :validators="authenticationRegisterDetail_formValidations.npwp_zip_code"
            :is-mandatory="true"
          >
            <input
              id="zip_code"
              v-model="authenticationRegisterDetail_formData.npwp_zip_code"
              v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
              class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              :readonly="authenticationRegisterDetail_isLoading"
              v-on="useListenerForm(authenticationRegisterDetail_formValidations, 'npwp_zip_code')"
            />
          </BaseFormGroup>
        </section>
        <div class="grid grid-cols-1 gap-2 mt-10">
          <PrimeVueButton
            :disabled="authenticationRegisterDetail_isLoading || authenticationRegisterDetail_formValidations.$invalid"
            label="Buat Akun"
            type="submit"
            class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
            @click.prevent="onSubmitForm"
          />
        </div>
      </form>
    </div>
  </section>
  <!--Modal Dialog Identity/KTP  -->
  <PrimeVueDialog
    v-model:visible="isShowModalChangeIdentity"
    modal
    class="h-fit w-full md:w-[532px]"
    :pt="{
      header: '!py-2 border-b border-b-[#E5E6E8]',
      content: '!py-4',
      footer: '!py-3 border-t border-t-[#E5E6E8]',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[18px] text-black">Konfirmasi Ganti File KTP</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col gap-4">
        <span class="text-base text-black">Apakah Anda yakin ingin mengganti File KTP? </span>
      </section>
    </template>

    <template #footer>
      <section id="btn-actions" class="flex items-center justify-end gap-4">
        <PrimeVueButton
          type="button"
          label="Batalkan"
          class="!bg-white !text-black !px-3 !py-2 !rounded-lg !border-gray-300"
          @click="isShowModalChangeIdentity = false"
        />
        <PrimeVueButton
          type="button"
          label="Ganti File KTP"
          class="!bg-[#18191A] !text-white !px-3 !py-2 !rounded-lg !border-[#18191A]"
          @click="
            {
              isShowModalChangeIdentity = false;
              openFilePicker('file_identity');
            }
          "
        />
      </section>
    </template>
  </PrimeVueDialog>
  <!-- Modal Dialog NPWP -->
  <PrimeVueDialog
    v-model:visible="isShowModalChangeNpwp"
    modal
    class="h-fit w-full md:w-[532px]"
    :pt="{
      header: '!py-2 border-b border-b-[#E5E6E8]',
      content: '!py-4',
      footer: '!py-3 border-t border-t-[#E5E6E8]',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[18px] text-black">Konfirmasi Ganti File NPWP</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col gap-4">
        <span class="text-base text-black">Apakah Anda yakin ingin mengganti File NPWP? </span>
      </section>
    </template>

    <template #footer>
      <section id="btn-actions" class="flex items-center justify-end gap-4">
        <PrimeVueButton
          type="button"
          label="Batalkan"
          class="!bg-white !text-black !px-3 !py-2 !rounded-lg !border-gray-300"
          @click="isShowModalChangeNpwp = false"
        />
        <PrimeVueButton
          type="button"
          label="Ganti File NPWP"
          class="!bg-[#18191A] !text-white !px-3 !py-2 !rounded-lg !border-[#18191A]"
          @click="
            {
              isShowModalChangeNpwp = false;
              openFilePicker('file_npwp');
            }
          "
        />
      </section>
    </template>
  </PrimeVueDialog>
  <PrimeVueDialog
    v-model:visible="authenticationRegisterDetail_isShowModalTnc"
    class="w-[50em]"
    @update:visible="
      val => {
        authenticationRegisterDetail_isShowModalTnc = val;
        if (!val) {
          authenticationRegisterDetail_tncBox_1 = false;
          authenticationRegisterDetail_tncBox_2 = false;
        }
      }
    "
  >
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[18px] text-black">Syarat dan Ketentuan</h4>
      </header>
    </template>
    <template #default>
      <div class="p-2 text-black h-[40em]">
        <div v-for="(section, key_section) in sections" :key="key_section" class="grid grid-cols-1 gap-y-4">
          <div class="my-3">
            <div class="font-bold ml-2 my-2">{{ section.section_name }}. {{ section.value }}</div>
            <div class="grid grid-cols-1 gap-y-2">
              <div
                v-for="(tncDetail, key_tnc) in section.tnc_detail"
                :key="key_tnc"
                class="grid grid-cols-[3fr_97fr]"
              >
                <div class="">{{ key_tnc + 1 }}.</div>
                <div>
                  {{ tncDetail.detail_value }}
                  <div
                    v-for="(tncPoint, key_point) in tncDetail.tnc_point"
                    :key="key_point"
                    class="grid grid-cols-[3fr_97fr] my-4"
                  >
                    <div class="">{{ key_point + 1 }}.</div>
                    <div class="">{{ tncPoint.point_value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex flex-col mt-5">
        <div class="">
          {{ (authentication_termAndCondition as ITermsAndConditions).footer }}
        </div>
        <div class="mt-5">
          <div class="flex items-center gap-2">
            <PrimeVueCheckbox
              v-model="authenticationRegisterDetail_tncBox_1"
              input-id="term_1"
              name="cart-check-all"
              class="mr-2"
              binary
            />
            <label for="term_1" class="text-[14px] font-medium"
              >Saya Menyetujui Syarat dan ketentuan berlaku</label
            >
          </div>
          <div class="flex items-center gap-2">
            <PrimeVueCheckbox
              v-model="authenticationRegisterDetail_tncBox_2"
              input-id="term_2"
              name="cart-check-all"
              class="mr-2"
              binary
            />
            <label for="term_2" class="text-[14px] font-medium">
              Saya menyetujui menyetujui bahwa data yang diunggah akan digunakan oleh tim EIGER. Data akan dihapus
              setelah Anda tidak lagi menjadi member.
            </label>
          </div>
        </div>
        <PrimeVueButton
          label="Submit"
          :disabled="!(authenticationRegisterDetail_tncBox_1 && authenticationRegisterDetail_tncBox_2)"
          type="submit"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted my-2"
          @click="authenticationRegisterDetail_onSubmitTnc"
        />
      </div>
    </template>
  </PrimeVueDialog>
</template>
``