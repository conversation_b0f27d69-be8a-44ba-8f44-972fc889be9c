<script setup lang="ts">
// Interfaces
import type { IAuthenticationForgotPasswordProvided } from '../interfaces/authentication-forgot-password.interface';

/**
 * @description Injecting dependencies
 */
const {
  authenticationForgotPassword_formData,
  authenticationForgotPassword_formValidations,
  authenticationForgotPassword_isLoading,
  authenticationForgotPassword_onSubmit,
} = inject<IAuthenticationForgotPasswordProvided>('authenticationForgotPassword')!;
</script>

<template>
  <section id="authentication-login-form" class="flex flex-col sm:items-center sm:justify-center items-start justify-start h-full">
    <BaseLoader v-show="authenticationForgotPassword_isLoading" />
    <BaseAlert class="sm:hidden block"/>
    <div class="mx-auto w-full max-w-lg p-6 border sm:border-hero-light-gray border-white rounded-md">
      <div class="mb-5">
        <h1 class="font-druk text-4xl text-black mb-2">Lupa Kata Sand<PERSON></h1>
        <p class="text-base text-muted leading-6">
          Silahkan masukkan e-mail Anda. Kami akan mengirimkan pesan untuk mengatur ulang kata sandi Anda
        </p>
      </div>

      <form @submit.prevent="authenticationForgotPassword_onSubmit">
        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="email"
          name="Email"
          :validators="authenticationForgotPassword_formValidations.email"
        >
          <input
            id="email"
            v-model="authenticationForgotPassword_formData.email"
            v-bind="{ ...useBindStateForm('Masukkan Email Anda') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            :readonly="authenticationForgotPassword_isLoading"
            v-on="useListenerForm(authenticationForgotPassword_formValidations, 'email')"
          />
        </BaseFormGroup>

        <section id="btn-actions" class="flex flex-col gap-4">
          <button class="cursor-pointer text-sm text-end font-medium text-[#147FFF] underline" type="submit">
            Kirim Ulang Pesan
          </button>

          <button
            class="block w-full bg-hero-black font-sans text-base text-white h-12 font-bebas py-2 rounded-lg cursor-pointer"
            :class="{ 'opacity-50': authenticationForgotPassword_isLoading }"
            :disabled="authenticationForgotPassword_isLoading"
            type="submit"
          >
            Kirim
          </button>

          <NuxtLink
            :to="{
              name: 'login',
            }"
            class="block w-full bg-transparent font-sans text-base text-center text-black h-10 font-medium py-2 rounded-lg"
            :class="{ 'opacity-50': authenticationForgotPassword_isLoading }"
            :disabled="authenticationForgotPassword_isLoading"
          >
            Kembali
          </NuxtLink>
          <BaseAlert class="sm:block hidden"/>
        </section>
      </form>
    </div>
  </section>
</template>
