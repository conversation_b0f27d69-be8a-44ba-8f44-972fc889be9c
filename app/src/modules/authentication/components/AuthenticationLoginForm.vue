<script setup lang="ts">
import type { IAuthenticationLoginProvided } from '../interfaces/authentication-login.interface';
import { useRememberPassword } from '../stores/remember-password.store';
/**
 * @description Injecting dependencies
 */

const width = ref(window.innerWidth);
const router = useRouter();
const checkedRememberMe = ref(false);

const rememberStore = useRememberPassword();

const handleResize = () => {
  width.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

// function handle register click
// return: none
const handleRegisterClick = () => {
  if (width.value < 640) {
    router.push({ name: 'register-mobile' });
  } else {
    router.push({ name: 'register-form' });
  }
};

// inject dependencies
const {
  authenticationLogin_formData,
  authenticationLogin_formValidations,
  authenticationLogin_isLoading,
  authenticationLogin_onSubmit,
} = inject<IAuthenticationLoginProvided>('authenticationLogin')!;

const onSubmitLogin = () => {
  console.log('authenticationLogin_formValidations', authenticationLogin_formValidations);
  authenticationLogin_onSubmit(checkedRememberMe.value);
};

onMounted(() => {
  if (rememberStore.email && rememberStore.password) {
    authenticationLogin_formData.email = rememberStore.email;
    authenticationLogin_formData.password = rememberStore.password;
  }
});
</script>

<template>
  <section
    id="authentication-login-form"
    class="flex flex-col items-center sm:justify-center justify-start pt-5 h-full"
  >
    <BaseLoader v-show="authenticationLogin_isLoading" />
    <BaseAlert>
      <template #icon-prefix>
        <NuxtImg src="/icons/warning-danger.svg" alt="warning-icon" class="w-4 h-4" />
      </template>
    </BaseAlert>

    <div class="mx-auto w-full max-w-lg p-6 border sm:border-hero-light-gray border-white rounded-lg">
      <div class="mb-5">
        <h1 class="font-druk text-4xl text-black mb-2">Masuk</h1>
        <p class="text-[16px] font-regular text-[#686F72]">
          Masukkan Email dan Kata Sandi Anda yang sudah terdaftar!
        </p>
      </div>

      <form>
        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="email"
          name="Email"
          :is-mandatory="true"
          :validators="authenticationLogin_formValidations.email"
        >
          <input
            id="email"
            v-model="authenticationLogin_formData.email"
            v-bind="{ ...useBindStateForm('Masukkan Email Anda') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            :readonly="authenticationLogin_isLoading"
            v-on="useListenerForm(authenticationLogin_formValidations, 'email')"
          />
        </BaseFormGroup>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Kata Sandi"
          :is-mandatory="true"
          :validators="authenticationLogin_formValidations.password"
        >
          <BaseInputPassword
            id="password"
            v-model="authenticationLogin_formData.password"
            :class="{ ...classes }"
            :readonly="authenticationLogin_isLoading"
            placeholder="Masukkan Kata Sandi Anda"
            type="password"
            v-on="useListenerForm(authenticationLogin_formValidations, 'password')"
          />
        </BaseFormGroup>
        <section id="additional-actions" class="flex items-center justify-between mb-8">
          <div class="flex items-center gap-2">
            <PrimeVueCheckbox
              v-model="checkedRememberMe"
              input-id="rememberMe"
              name="rememberMe"
              binary
              class="w-[24px] h-[24px]"
            />
            <label for="rememberMe">Ingatkan Saya</label>
          </div>

          <NuxtLink :to="{ name: 'forgot-password' }" class="text-sm font-medium text-hero-link"
            >Lupa Kata Sandi</NuxtLink
          >
        </section>

        <PrimeVueButton
          :disabled="authenticationLogin_isLoading || authenticationLogin_formValidations.$invalid"
          label="Lanjut"
          type="submit"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
          @click.prevent="onSubmitLogin"
        />
        <div class="text-center mt-7">
          <div class="text-muted">Belum Punya Akun ?</div>
          <NuxtLink class="underline font-medium text-header-orange cursor-pointer" @click="handleRegisterClick">
            Buat Akun Eiger B2B
          </NuxtLink>
        </div>
      </form>
    </div>
  </section>
</template>
