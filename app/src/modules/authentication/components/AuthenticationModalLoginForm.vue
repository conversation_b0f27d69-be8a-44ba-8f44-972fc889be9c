<script setup lang="ts">
import type { IAuthenticationProvided } from '../interfaces/authentication.interface';
import { useRememberPassword } from '../stores/remember-password.store';
/**
 * @description Injecting dependencies
 */
const width = ref(window.innerWidth);
const router = useRouter();
const checkedRememberMe = ref(false);

const rememberStore = useRememberPassword();

const { authentication_isOpenModalLogin } = inject<IAuthenticationProvided>('auth')!;

// function handle register click
// return: none
const handleRegisterClick = () => {
  if (width.value < 500) {
    router.push({ name: 'register-mobile' });
  } else {
    router.push({ name: 'register-form' });
  }
};

// inject dependencies
const {
  authentication_loginFormData,
  authentication_loginFormValidations,
  authentication_loginIsLoading,
  authentication_onSubmitLogin,
} = inject<IAuthenticationProvided>('auth')!;

const onSubmitLogin = () => {
  authentication_onSubmitLogin(checkedRememberMe.value);
};

onMounted(() => {
  if (rememberStore.email && rememberStore.password) {
    authentication_loginFormData.email = rememberStore.email;
    authentication_loginFormData.password = rememberStore.password;
  }
});
</script>

<template>
  <section
    id="authentication-modal-login-form"
    class="flex flex-col items-center sm:justify-center justify-start pt-5 h-full"
  >
    <BaseLoader v-show="authentication_loginIsLoading" />
    <BaseAlert>
      <template #icon-prefix>
        <NuxtImg src="/icons/warning-danger.svg" alt="warning-icon" class="w-4 h-4" />
      </template>
    </BaseAlert>

    <div class="mx-auto w-full max-w-lg p-2 rounded-lg">
      <div class="mb-5">
        <h1 class="font-druk text-4xl text-black mb-2">Masuk</h1>
        <p class="text-[16px] font-regular text-[#686F72]">
          Masukkan Email dan Kata Sandi Anda yang sudah terdaftar!
        </p>
      </div>

      <form @submit.prevent="onSubmitLogin">
        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="email"
          name="Email"
          :is-mandatory="true"
          :validators="authentication_loginFormValidations.email"
        >
          <input
            id="email"
            v-model="authentication_loginFormData.email"
            v-bind="{ ...useBindStateForm('Masukkan Email Anda') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            :readonly="authentication_loginIsLoading"
            @keydown.enter.prevent
            v-on="useListenerForm(authentication_loginFormValidations, 'email')"
          />
        </BaseFormGroup>

        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Kata Sandi"
          :is-mandatory="true"
          :validators="authentication_loginFormValidations.password"
        >
          <BaseInputPassword
            id="password"
            v-model="authentication_loginFormData.password"
            :class="{ ...classes }"
            :readonly="authentication_loginIsLoading"
            placeholder="Masukkan Kata Sandi Anda"
            type="password"
            @keydown.enter.prevent
            v-on="useListenerForm(authentication_loginFormValidations, 'password')"
          />
        </BaseFormGroup>
        <section id="additional-actions" class="flex items-center justify-between mb-8">
          <div class="flex items-center gap-2">
            <PrimeVueCheckbox
              v-model="checkedRememberMe"
              input-id="rememberMe"
              name="rememberMe"
              binary
              class="w-[24px] h-[24px]"
            />
            <label for="rememberMe">Ingatkan Saya</label>
          </div>

          <NuxtLink :to="{ name: 'forgot-password' }" class="text-sm font-medium text-hero-link"
            >Lupa Kata Sandi</NuxtLink
          >
        </section>

        <PrimeVueButton
          :disabled="authentication_loginIsLoading || authentication_loginFormValidations.$invalid"
          label="Lanjut"
          type="submit"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
          @click="onSubmitLogin"
        />
        <div class="text-center mt-7">
          <div class="text-muted">Belum Punya Akun ?</div>
          <NuxtLink @click="handleRegisterClick" class="underline font-medium text-header-orange cursor-pointer"
            >Buat Akun Eiger B2B</NuxtLink
          >
        </div>
      </form>
    </div>
  </section>
</template>
