<script setup lang="ts">
// Constants
import { AUTHENTICATION_PASSWORD_VALIDATIONS } from '../constants/authentication.constant';
// Interfaces
import type {
  IAuthenticationRegisterMobileProvided,
  ITermsAndConditions,
  TncSection,
} from '../interfaces/authentication-register-mobile.interface';
// Vuelidate
import type { BaseValidation, ErrorObject } from '@vuelidate/core';
// helper
import { replaceParams } from '../../../core/helpers/replace-parameters.helper';

// inject dependetion
const {
  // authenticationRegisterMobile_formData,
  // authenticationRegisterMobile_formValidations,
  authenticationRegisterMobile_fileValidations,
  authenticationRegisterMobile_attachment,
  authenticationRegisterMobile_isCustomValidationsValid,
  authenticationRegisterMobile_isLoading,
  authenticationRegisterMobile_setDynamicClassLabelValidation,
  authenticationRegisterMobile_setDynamicPathIcon,
  authenticationRegisterMobile_tncBox,
  authenticationRegisterMobile_onSubmit,
  authenticationRegisterMobile_isNeedInvoiceBill,
  authenticationRegisterMobile_toggleInoviceBill,
  // KTP
  authentication_uploadKtpFileName,
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  // NPWP
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
  //
  authentication_listProvince,
  authentication_listCities,
  authentication_listSubdistrict,
  authentication_taxType,
  authentication_termAndCondition,

  authentication_listNpwpProvince,
  authentication_listNpwpCities,
  authentication_listNpwpSubdistrict,

  // SECTION 1
  auhtenticationRegisterMobile_formData_Section_1,
  authenticationRegisterMobile_formValidations_Section_1,
  authenticationRegisterMobile_onSubmitSection1,
  // SECTION 2
  auhtenticationRegisterMobile_formData_Section_2,
  authenticationRegisterMobile_formValidations_Section_2,
  // SECTION 3
  authenticationRegisterMobile_formValidations_Section_3,
  auhtenticationRegisterMobile_formData_Section_3,
  // SECTION 3
  // authenticationRegisterMobile_formValidations_Section_4,
  // auhtenticationRegisterMobile_formData_Section_4,
  authentication_isExistNPWP,
  authentication_isExistKTP,
  authenticationRegisterMobile_validateNPWP,
  authenticationRegisterMobile_validateKTP,
} = inject<IAuthenticationRegisterMobileProvided>('authenticationRegisterMobile')!;

// const isNeedFakturPajak = ref(false);
const isShowModalChangeIdentity = ref<boolean>(false);
const isShowModalChangeNpwp = ref<boolean>(false);
const selectedStep = ref<number>(1);
const dragging = ref<boolean>(false);
const selectedFile = ref<File | null>(null);
const currentActivateCallback = ref<((value: string | number) => void) | null>(null);
const invoice_number = ref(['02', '03', '04', '07']);

//function to filter section in data tnc
//return: data tnc
const sections = computed(() =>
  authentication_termAndCondition.value
    ? Object.entries(authentication_termAndCondition.value)
        .filter(([key, value]) => key !== 'footer' && typeof value !== 'string')
        .map(([_, value]) => value as TncSection)
    : [],
);

// // function to handle change section in check customer
// // return: none
const handleCheckCustomer = async () => {
  const result = await authenticationRegisterMobile_onSubmitSection1();
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  if (result?._rawValue?.error == true) {
    currentActivateCallback.value!('2');
    selectedStep.value = 2;
  }
};

// function to handle select step
// reutrn: none

// function to handle select file
// return: none
const handleFileSelected = (event: Event, field: 'file_identity' | 'file_npwp') => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    selectedFile.value = file;
    authenticationRegisterMobile_attachment[field] = file;
  }
};

// function to handle drop file
// return: none
const handleDrop = (event: DragEvent, field: 'file_identity' | 'file_npwp') => {
  event.preventDefault();
  dragging.value = false;
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0];
    selectedFile.value = file;
    authenticationRegisterMobile_attachment[field] = file;
  }
};

// function to open file picker
// return: none
const openFilePicker = (field: 'file_identity' | 'file_npwp') => {
  const fileInput = document.getElementById(`fileInput-${field}`) as HTMLInputElement;
  fileInput.click();
};

// function to get error
// return: error value
const getFileError = (fieldName: 'file_identity' | 'file_npwp'): ComputedRef<ErrorObject | null> =>
  computed(() => {
    const fieldValidation = authenticationRegisterMobile_fileValidations.value[fieldName] as BaseValidation;
    if (!fieldValidation || fieldValidation.$errors.length === 0) return null;
    console.log(fieldValidation.$errors[0]);
    return fieldValidation.$errors[0];
  });

// function to get message error
// return: error value
const getFileErrorMessage = (
  errorRef: ComputedRef<ErrorObject | null>,
  attributeName = 'File',
  max = '15 MB',
): ComputedRef<string> =>
  computed(() => {
    const validator = errorRef.value?.$validator;
    if (!validator) return '';
    return replaceParams(VALIDATION_MESSAGE[validator] ?? `Error: ${validator}`, {
      attribute: attributeName,
      max,
      ...errorRef.value?.$params,
    });
  });

// function to toggle faktur pajak
// return: none

const errorKTP = getFileError('file_identity');
const errorNPWP = getFileError('file_npwp');
const messageNPWP = getFileErrorMessage(errorNPWP, 'NPWP');

const stepClass = (value: number): string => {
  if (selectedStep.value === value) {
    return "!w-[20px] !aspect-square !flex !items-center !justify-center !border-[1.2px] !border-orange-500 !rounded-full !text-transparent relative before:content-[''] before:absolute before:w-3 before:h-3 before:bg-orange-500 before:rounded-full";
  } else if (selectedStep.value < value) {
    return "!w-[20px] !aspect-square !flex !items-center !justify-center !border-[1.2px] !border-gray-400 !rounded-full !text-transparent relative before:content-[''] before:absolute before:w-3 before:h-3 before:bg-gray-400 before:rounded-full";
  } else {
    return "!w-[20px] !aspect-square !flex !items-center !justify-center !border-[1.2px] !border-orange-500 !rounded-full !text-transparent relative before:content-[''] before:absolute before:w-3 before:h-3 before:bg-orange-500 before:rounded-full";
  }
};

const onChangeValidateKTP = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length === 16) {
    authenticationRegisterMobile_validateKTP(value);
  }
}, 500);

const onChangeValidateNPWPKTP = useDebounce((value: string) => {
  if (typeof value === 'string' && value?.length === 16) {
    authenticationRegisterMobile_validateNPWP(value);
  }
}, 500);

const getIsValidNPWPMessage = computed(() => {
  if (authentication_isExistNPWP.value == true) {
    return 'Nomor NPWP Sudah terapakai';
  } else {
    return '';
  }
});

const getIsValidKTPMessage = computed(() => {
  if (authentication_isExistKTP.value == true) {
    return 'Nomor KTP Sudah terpakai';
  } else {
    return '';
  }
});

const onChangeShipmentProvince = () => {
  auhtenticationRegisterMobile_formData_Section_2.shipment_city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  auhtenticationRegisterMobile_formData_Section_2.shipment_sub_district = {
    name: '',
    code: '',
  };
};

const onChangeShipmentCity = () => {
  auhtenticationRegisterMobile_formData_Section_2.shipment_sub_district = {
    name: '',
    code: '',
  };
};

const onChangeNpwpProvince = () => {
  auhtenticationRegisterMobile_formData_Section_3.npwp_city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  auhtenticationRegisterMobile_formData_Section_3.npwp_sub_district = {
    name: '',
    code: '',
  };
};

const onChangeNpwpCity = () => {
  auhtenticationRegisterMobile_formData_Section_3.npwp_sub_district = {
    name: '',
    code: '',
  };
};
</script>

<template>
  <section
    id="authentication-create-password form"
    class="w-full h-full relative inset-0 z-0 flex flex-col items-center sm:mt-24 mt-2"
  >
    <BaseLoader v-show="authenticationRegisterMobile_isLoading" />
    <div class="w-full">
      <PrimeVueStepper value="1" class="basis-[50rem] mt-10">
        <!-- Step List -->
        <PrimeVueStepList class="!pl-4 !pr-7">
          <PrimeVueStep
            disabled
            value="1"
            :pt="{
              root: '!ml-0',
              number: {
                class: stepClass(1),
              },
            }"
          >
          </PrimeVueStep>
          <PrimeVueStep
            disabled
            value="2"
            :pt="{
              number: {
                class: stepClass(2),
              },
            }"
          />
          <PrimeVueStep
            disabled
            value="3"
            :pt="{
              number: {
                class: stepClass(3),
              },
            }"
          />
          <PrimeVueStep
            disabled
            value="4"
            :pt="{
              number: {
                class: stepClass(4),
              },
            }"
          />
          <!-- <PrimeVueStep
            value="5"
            :pt="{
              root: '!mr-6',
              number: {
                class: stepClass(5),
              },
            }"
          /> -->
        </PrimeVueStepList>
        <!-- Step Panels -->
        <PrimeVueStepPanels class="!px-4 !pt-8">
          <!-- Panel ! -->
          <PrimeVueStepPanel v-slot="{ activateCallback }" value="1" class="">
            <div @vue:mounted="currentActivateCallback = activateCallback">
              <header class="mb-5">
                <h1 class="font-druk text-4xl text-black mb-2">Pendaftaran</h1>
                <p class="text-base text-muted leading-6">Silakan daftarkan diri Anda</p>
              </header>
              <div class="grid gric-cols-1 gap-y-2">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="email"
                  name="Email"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_1.email"
                >
                  <input
                    id="email"
                    v-model="auhtenticationRegisterMobile_formData_Section_1.email"
                    v-bind="{ ...useBindStateForm('Masukkan Email Anda') }"
                    class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    :readonly="authenticationRegisterMobile_isLoading"
                    v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_1, 'email')"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="password"
                  name="Sandi Baru"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_1.password"
                >
                  <BaseInputPassword
                    id="password"
                    v-model="auhtenticationRegisterMobile_formData_Section_1.password"
                    :class="{ ...classes }"
                    :readonly="authenticationRegisterMobile_isLoading"
                    type="password"
                    placeholder="********"
                    v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_1, 'password')"
                  />
                </BaseFormGroup>
                <section
                  id="password-validation"
                  class="flex flex-col gap-2 py-2 px-4 rounded-lg my-4"
                  :class="[
                    authenticationRegisterMobile_isCustomValidationsValid ? 'bg-light-success' : 'bg-light-gray',
                  ]"
                >
                  <h6
                    class="text-sm"
                    :class="[
                      authenticationRegisterMobile_isCustomValidationsValid
                        ? 'text-success'
                        : 'text-muted-secondary',
                    ]"
                  >
                    Password harus memiliki setidaknya:
                  </h6>

                  <template
                    v-for="validation in AUTHENTICATION_PASSWORD_VALIDATIONS"
                    :key="`password-validation-${validation.key}`"
                  >
                    <section id="validation" class="flex items-center gap-2">
                      <template v-if="auhtenticationRegisterMobile_formData_Section_1.password">
                        <NuxtImg
                          :src="authenticationRegisterMobile_setDynamicPathIcon(validation.key)"
                          alt="icon-password-validation"
                          class="w-4 h-4"
                        />
                      </template>

                      <template v-else>
                        <NuxtImg src="/icons/min-circle-muted.svg" alt="icon-min-circle-muted" class="w-4 h-4" />
                      </template>

                      <span
                        class="text-sm"
                        :class="[authenticationRegisterMobile_setDynamicClassLabelValidation(validation.key)]"
                      >
                        {{ validation.label }}
                      </span>
                    </section>
                  </template>
                </section>

                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="password_confirmation"
                  name="Konfirmasi Sandi Baru"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_1.password_confirmation"
                >
                  <BaseInputPassword
                    id="password_confirmation"
                    v-model="auhtenticationRegisterMobile_formData_Section_1.password_confirmation"
                    :class="{ ...classes }"
                    :readonly="authenticationRegisterMobile_isLoading"
                    type="password"
                    @paste.prevent
                    placeholder="********"
                    v-on="
                      useListenerForm(
                        authenticationRegisterMobile_formValidations_Section_1,
                        'password_confirmation',
                      )
                    "
                  />
                </BaseFormGroup>
                <PrimeVueButton
                  :disabled="
                    authenticationRegisterMobile_isLoading ||
                    authenticationRegisterMobile_formValidations_Section_1.$invalid
                  "
                  label="Lanjut"
                  type="submit"
                  class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                  @click="handleCheckCustomer"
                />
              </div>
            </div>
          </PrimeVueStepPanel>
          <!-- Panels 2 -->
          <PrimeVueStepPanel v-slot="{ activateCallback }" value="2" class="p-2 h-full">
            <header class="">
              <h1 class="font-druk text-4xl text-black mb-2">Pendaftaran</h1>
              <p class="text-base text-muted leading-6">Silakan daftarkan diri Anda</p>
            </header>
            <h2 class="font-bold text-[#686F72] my-4 text-[18px]">Informasi Diri</h2>
            <BaseFormGroup
              v-slot="{ classes }"
              class-label="font-medium text-sm text-black block mb-2"
              is-name-as-label
              label-for="bana-lengkap"
              name="Nama Lengkap"
              :is-mandatory="true"
              :validators="authenticationRegisterMobile_formValidations_Section_2.full_name"
            >
              <input
                id="nama-lengkap"
                v-model="auhtenticationRegisterMobile_formData_Section_2.full_name"
                v-bind="{ ...useBindStateForm('Tambahkan Nama Lengkap') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                :class="{ ...classes }"
                type="text"
                :readonly="authenticationRegisterMobile_isLoading"
                v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_2, 'full_name')"
              />
            </BaseFormGroup>
            <BaseFormGroup
              v-slot="{ classes }"
              class-label="font-medium text-sm text-black block mb-2"
              is-name-as-label
              label-for="email"
              name="Email"
              :is-mandatory="true"
              :validators="authenticationRegisterMobile_formValidations_Section_2.email"
            >
              <input
                id="email"
                v-model="auhtenticationRegisterMobile_formData_Section_2.email"
                v-bind="{ ...useBindStateForm('Tambahkan Email') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted bg-gray-100"
                :class="{ ...classes }"
                type="text"
                disabled
                :readonly="authenticationRegisterMobile_isLoading"
                v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_2, 'email')"
              />
            </BaseFormGroup>
            <BaseFormGroup
              v-slot="{ classes }"
              class-label="font-medium text-sm text-black block mb-2"
              is-name-as-label
              label-for="password"
              name="Nomor HP"
              :validators="authenticationRegisterMobile_formValidations_Section_2.phone_number"
            >
              <PrimeVueInputGroup class="h-[3.1em]">
                <PrimeVueInputGroupAddon class="!bg-[#F9FAFB]">
                  <p class="text-[#18191A] text-base font-normal">+62</p>
                </PrimeVueInputGroupAddon>
                <PrimeVueInputText
                  id="no_telp"
                  v-model="auhtenticationRegisterMobile_formData_Section_2.phone_number"
                  v-bind="{ ...useBindStateForm('Masukan nomor handphone') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  inputmode="numeric"
                  v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_2, 'phone_number')"
                />
              </PrimeVueInputGroup>
            </BaseFormGroup>
            <BaseFormGroup
              v-slot="{ classes }"
              class-label="font-medium text-sm text-black block mb-2"
              is-name-as-label
              label-for="company"
              name="Perusahaan"
              :is-mandatory="true"
              :validators="authenticationRegisterMobile_formValidations_Section_2.company_name"
            >
              <input
                id="company"
                v-model="auhtenticationRegisterMobile_formData_Section_2.company_name"
                v-bind="{ ...useBindStateForm('Tambahkan Nama Instansi') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                :class="{ ...classes }"
                type="text"
                :readonly="authenticationRegisterMobile_isLoading"
                v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_2, 'company_name')"
              />
            </BaseFormGroup>
            <!-- <div class="mb-2">
              <div
                v-if="authentication_isUploadingKtp"
                class="border-2 border-dashed border-[#CCD2D9] p-6 w-full justify-center h-[232px] pl-12 flex flex-col rounded-xl"
              >
                <p v-if="authentication_uploadKtpProgress === 100" class="mb-2 text-black font-semibold">
                  Sukses! {{ authentication_uploadKtpProgress }}%
                </p>
                <PrimeVueProgressBar
                  :value="authentication_uploadKtpProgress"
                  class="!w-full !max-w-full !self-stretch"
                  :pt="{
                    root: '!bg-white !rounded-full',
                    value: '!bg-header-green-500 !rounded-full',
                  }"
                >
                  <span></span>
                </PrimeVueProgressBar>
                <p class="mt-2">{{ authentication_uploadedKtpSize }} mb/{{ authentication_totalKtpSize }} mb</p>
              </div>
              <div v-else>
                <div
                  v-if="authentication_uploadKtpProgress === 100"
                  class="border-2 border-green-500 bg-green-100 p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-lg"
                >
                  <div class="w-full justify-items-center my-5">
                    <NuxtImg src="/icons/checklist-rounded-green.svg" alt="upload-icon" class="w-15 h-15" />
                    <div class="mt-6 text-md font-medium text-green-700 text-center">Data Berhasil Terupload</div>
                    <div v-if="authentication_uploadKtpFileName" class="mt-1 text-lg font-semibold text-black">
                      {{ authentication_uploadKtpFileName }}
                    </div>
                  </div>
                  <input
                    id="fileInput-file_identity"
                    type="file"
                    data-testid="bulk-file-input"
                    accept=".pdf, .jpg, .jpeg, .png"
                    class="hidden"
                    @change="e => handleFileSelected(e, 'file_identity')"
                  />
                </div>
                <div
                  v-else
                  class="border-2 border-dashed p-6 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 rounded-lg"
                  :class="dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-gray-400'"
                  data-testid="bulk-file-input-draggable"
                  @dragover.prevent="dragging = true"
                  @dragleave="dragging = false"
                  @drop="e => handleDrop(e, 'file_identity')"
                  @click="() => openFilePicker('file_identity')"
                >
                  <div class="w-full justify-items-center my-5">
                    <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10" />
                  </div>
                  <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
                  <div class="mt-6 text-sm text-gray-500 mx-auto text-center">
                    Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
                  </div>
                  <input
                    id="fileInput-file_identity"
                    type="file"
                    accept=".pdf, .jpg, .jpeg, .png"
                    class="hidden"
                    @change="e => handleFileSelected(e, 'file_identity')"
                  />
                </div>
              </div>
              <div
                v-if="authenticationRegisterMobile_fileValidations.file_identity.$errors[0]"
                class="bg-red-50 rounded-md p-1 px-3"
              >
                <PrimeVueMessage severity="error" variant="simple" size="small" class="">
                  {{ errorKTP?.$message }}
                </PrimeVueMessage>
              </div>
              <div v-if="authentication_isUploadKtpError" class="bg-red-50 rounded-md p-1 px-3">
                <PrimeVueMessage severity="error" variant="simple" size="small"> Gagal Upload Data </PrimeVueMessage>
              </div>
  
              <div v-if="authentication_uploadKtpProgress === 100" class="flex w-full justify-end mt-4 gap-10">
                <PrimeVuebutton
                  data-testid="btn-bulk-change-file"
                  class="!text-white !font-semibold !bg-black !border-black"
                  @click="isShowModalChangeIdentity = true"
                  >Ubah File
                </PrimeVuebutton>
              </div>
            </div> -->

            <section class="grid grid-cols-1">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password_confirmation"
                name="Alamat"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_2.shipment_address"
              >
                <textarea
                  id="perusahaan"
                  v-model="auhtenticationRegisterMobile_formData_Section_2.shipment_address"
                  rows="4"
                  v-bind="{ ...useBindStateForm('Masukan alamat pengiriman anda') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="authenticationRegisterMobile_isLoading"
                  v-on="
                    useListenerForm(authenticationRegisterMobile_formValidations_Section_2, 'shipment_address')
                  "
                />
              </BaseFormGroup>
            </section>
            <section class="grid grid-cols-1 gap-y-3 my-4">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="province"
                name="Provinsi"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_2.shipment_province"
              >
                <BaseSelectInput
                  v-model:selected="auhtenticationRegisterMobile_formData_Section_2.shipment_province"
                  :class="{ ...classes }"
                  class="rounded-lg"
                  placeholder="Pilih Provinsi"
                  :disable="false"
                  :options="authentication_listProvince"
                  @update:selected="onChangeShipmentProvince"
                />
              </BaseFormGroup>
              <BaseFormGroup
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="City"
                name="Kota"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_2.shipment_city"
              >
                <BaseSelectInput
                  v-model:selected="auhtenticationRegisterMobile_formData_Section_2.shipment_city"
                  placeholder="Pilih Kota"
                  class="rounded-lg"
                  :disable="!auhtenticationRegisterMobile_formData_Section_2.shipment_province.code"
                  :options="authentication_listCities"
                  @update:selected="onChangeShipmentCity"
                />
              </BaseFormGroup>

              <BaseFormGroup
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="Kecamatan"
                name="Kecamatan"
                spacing-bottom="mb-0"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_2.shipment_sub_district"
              >
                <BaseSelectInput
                  v-model:selected="auhtenticationRegisterMobile_formData_Section_2.shipment_sub_district"
                  placeholder="Pilih Kecamatan"
                  class="rounded-lg"
                  :disable="!auhtenticationRegisterMobile_formData_Section_2.shipment_city.code"
                  :options="authentication_listSubdistrict"
                />
              </BaseFormGroup>

              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="zip_code"
                name="Kode Pos"
                :validators="authenticationRegisterMobile_formValidations_Section_2.shipment_zip_code"
                :is-mandatory="true"
              >
                <input
                  id="zip_code"
                  v-model="auhtenticationRegisterMobile_formData_Section_2.shipment_zip_code"
                  v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
                  class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="authenticationRegisterMobile_isLoading"
                  v-on="
                    useListenerForm(authenticationRegisterMobile_formValidations_Section_2, 'shipment_zip_code')
                  "
                />
              </BaseFormGroup>
            </section>

            <div class="grid grid-cols-1 gap-3">
              <PrimeVueButton
                :disabled="
                  authenticationRegisterMobile_isLoading ||
                  authenticationRegisterMobile_formValidations_Section_2.$invalid
                "
                label="Lanjut"
                type="submit"
                class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                @click="
                  activateCallback('3');
                  selectedStep = 3;
                "
              />
              <PrimeVueButton
                label="Kembali"
                severity="secondary"
                icon="pi pi-arrow-left"
                class="!w-full !bg-white !border !border-black !text-base !text-black !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                @click="
                  activateCallback('1');
                  selectedStep = 1;
                "
              />
            </div>
          </PrimeVueStepPanel>
          <!-- Panel 3 -->
          <PrimeVueStepPanel v-slot="{ activateCallback }" value="3">
            <div class="p-2">
              <header class="">
                <h1 class="font-druk text-4xl text-black mb-2">Pendaftaran</h1>
                <p class="text-base text-muted leading-6">Silakan daftarkan diri Anda</p>
              </header>
              <h2 class="font-bold text-[#686F72] my-4 text-[18px]">Data NPWP</h2>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                :is-error-message="getIsValidKTPMessage"
                label-for="ktp"
                name="Nomor KTP"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_3.ktp"
              >
                <input
                  id="ktp"
                  v-model="auhtenticationRegisterMobile_formData_Section_3.ktp"
                  v-bind="{ ...useBindStateForm('Tambahkan Nomor KTP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="authenticationRegisterMobile_isLoading"
                  :maxlength="16"
                  :minlength="16"
                  v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_3, 'ktp')"
                  @update:model-value="(value : string) => onChangeValidateKTP(value)"
                />
              </BaseFormGroup>
              <div class="mb-2">
                <div class="font-medium text-black text-[14px] mb-2">Upload KTP</div>
                <div
                  v-if="authentication_isUploadingKtp"
                  class="border-2 border-dashed border-[#CCD2D9] p-6 w-full justify-center h-[232px] pl-12 flex flex-col rounded-xl"
                >
                  <p v-if="authentication_uploadKtpProgress === 100" class="mb-2 text-black font-semibold">
                    Sukses! {{ authentication_uploadKtpProgress }}%
                  </p>
                  <PrimeVueProgressBar
                    :value="authentication_uploadKtpProgress"
                    class="!w-full !max-w-full !self-stretch"
                    :pt="{
                      root: '!bg-white !rounded-full',
                      value: '!bg-header-green-500 !rounded-full',
                    }"
                  >
                    <span></span>
                  </PrimeVueProgressBar>
                  <p class="mt-2">{{ authentication_uploadedKtpSize }} mb/{{ authentication_totalKtpSize }} mb</p>
                </div>
                <div v-else>
                  <div
                    v-if="authentication_uploadKtpProgress === 100"
                    class="border-2 border-green-500 bg-green-100 p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-lg"
                  >
                    <div class="w-full justify-items-center my-5">
                      <NuxtImg src="/icons/checklist-rounded-green.svg" alt="upload-icon" class="w-15 h-15" />
                      <div class="mt-6 text-md font-medium text-green-700 text-center">
                        Data Berhasil Terupload
                      </div>
                      <div v-if="authentication_uploadKtpFileName" class="mt-1 text-lg font-semibold text-black">
                        {{ authentication_uploadKtpFileName }}
                      </div>
                    </div>
                    <input
                      id="fileInput-file_identity"
                      type="file"
                      data-testid="bulk-file-input"
                      accept=".pdf, .jpg, .jpeg, .png"
                      class="hidden"
                      @change="e => handleFileSelected(e, 'file_identity')"
                    />
                  </div>
                  <div
                    v-else
                    class="border-2 border-dashed p-6 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 rounded-lg"
                    :class="dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-gray-400'"
                    data-testid="bulk-file-input-draggable"
                    @dragover.prevent="dragging = true"
                    @dragleave="dragging = false"
                    @drop="e => handleDrop(e, 'file_identity')"
                    @click="() => openFilePicker('file_identity')"
                  >
                    <div class="w-full justify-items-center my-5">
                      <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10" />
                    </div>
                    <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
                    <div class="mt-6 text-sm text-gray-500 mx-auto text-center">
                      Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
                    </div>
                    <input
                      id="fileInput-file_identity"
                      type="file"
                      accept=".pdf, .jpg, .jpeg, .png"
                      class="hidden"
                      @change="e => handleFileSelected(e, 'file_identity')"
                    />
                  </div>
                </div>
                <div
                  v-if="authenticationRegisterMobile_fileValidations.file_identity.$errors[0]"
                  class="bg-red-50 rounded-md p-1 px-3"
                >
                  <PrimeVueMessage severity="error" variant="simple" size="small" class="">
                    {{ errorKTP?.$message }}
                  </PrimeVueMessage>
                </div>
                <div v-if="authentication_isUploadKtpError" class="bg-red-50 rounded-md p-1 px-3">
                  <PrimeVueMessage severity="error" variant="simple" size="small">
                    Gagal Upload Data
                  </PrimeVueMessage>
                </div>

                <div v-if="authentication_uploadKtpProgress === 100" class="flex w-full justify-end mt-4 gap-10">
                  <PrimeVuebutton
                    data-testid="btn-bulk-change-file"
                    class="!text-white !font-semibold !bg-black !border-black"
                    @click="isShowModalChangeIdentity = true"
                    >Ubah File
                  </PrimeVuebutton>
                </div>
              </div>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                :is-error-message="getIsValidNPWPMessage"
                is-name-as-label
                label-for="company"
                name="Nomor NPWP"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_number"
              >
                <input
                  id="company"
                  v-model="auhtenticationRegisterMobile_formData_Section_3.npwp_number"
                  v-bind="{ ...useBindStateForm('Tambahkan Nommor NPWP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted mb-2"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="authenticationRegisterMobile_isLoading"
                  :maxlength="16"
                  v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_3, 'npwp_number')"
                  @update:model-value="(value : string) => onChangeValidateNPWPKTP(value)"
                />
              </BaseFormGroup>

              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-4"
                is-name-as-label
                label-for="jenis_pajak"
                name="Jenis Pajak"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_3.tax_type"
              >
                <BaseSelectInput
                  v-model:selected="auhtenticationRegisterMobile_formData_Section_3.tax_type"
                  class="mb-4"
                  :class="{ ...classes }"
                  placeholder="Pilih Jenis Pajak"
                  :disable="false"
                  :options="authentication_taxType"
                />
              </BaseFormGroup>

              <div class="font-medium text-black text-[14px] mb-2">
                Upload NPWP <span class="text-[14px] text-[#E9151D]">*</span>
              </div>
              <div
                v-if="authentication_isUploadingNpwp"
                class="border-2 border-dashed border-[#CCD2D9] p-6 w-full justify-center h-[232px] pl-12 flex flex-col rounded-xl"
              >
                <p v-if="authentication_uploadNpwpProgress === 100" class="mb-2 text-black font-semibold">
                  Sukses! {{ authentication_uploadNpwpProgress }}%
                </p>
                <PrimeVueProgressBar
                  :value="authentication_uploadNpwpProgress"
                  class="!w-full !max-w-full !self-stretch"
                  :pt="{
                    root: '!bg-white !rounded-full',
                    value: '!bg-header-green-500 !rounded-full',
                  }"
                >
                  <span></span>
                </PrimeVueProgressBar>
                <p class="mt-2">{{ authentication_uploadedNpwpSize }} mb/{{ authentication_totalNpwpSize }} mb</p>
              </div>
              <div v-else>
                <div
                  v-if="authentication_uploadNpwpProgress === 100"
                  class="border-2 border-green-500 bg-green-100 p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-xl"
                >
                  <div class="w-full justify-items-center my-5">
                    <NuxtImg src="/icons/checklist-rounded-green.svg" alt="upload-icon" class="w-15 h-15" />
                    <div class="mt-6 text-md font-medium text-green-700 text-center">Data Berhasil Terupload</div>
                    <div v-if="authentication_uploadNpwpFileName" class="mt-1 text-lg font-semibold text-black">
                      {{ authentication_uploadNpwpFileName }}
                    </div>
                  </div>
                  <input
                    id="fileInput-file_npwp"
                    type="file"
                    data-testid="bulk-file-input"
                    accept=".pdf, .jpg, .jpeg, .png"
                    class="hidden"
                    @change="e => handleFileSelected(e, 'file_npwp')"
                  />
                </div>
                <div
                  v-else
                  class="border-2 border-dashed p-6 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 rounded-xl"
                  :class="dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-gray-400'"
                  data-testid="bulk-file-input-draggable"
                  @dragover.prevent="dragging = true"
                  @dragleave="dragging = false"
                  @drop="e => handleDrop(e, 'file_npwp')"
                  @click="() => openFilePicker('file_npwp')"
                >
                  <div class="w-full justify-items-center my-5">
                    <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10" />
                  </div>
                  <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
                  <div class="mt-6 text-sm text-gray-500 mx-auto text-center">
                    Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
                  </div>
                  <input
                    id="fileInput-file_npwp"
                    accept=".pdf, .jpg, .jpeg, .png"
                    type="file"
                    class="hidden"
                    @change="e => handleFileSelected(e, 'file_npwp')"
                  />
                </div>
              </div>
              <PrimeVueMessage
                v-if="authenticationRegisterMobile_fileValidations.file_npwp.$errors[0]"
                severity="error"
                variant="simple"
                size="small"
              >
                {{ messageNPWP }}
              </PrimeVueMessage>
              <PrimeVueMessage
                v-if="authentication_isUploadNpwpError"
                severity="error"
                variant="simple"
                size="small"
              >
                Gagal Upload Data
              </PrimeVueMessage>

              <div v-if="authentication_uploadNpwpProgress === 100" class="flex w-full justify-end mt-4 gap-10">
                <PrimeVuebutton
                  data-testid="btn-bulk-change-file"
                  class="!text-white !font-semibold !bg-black !border-black"
                  @click="isShowModalChangeNpwp = true"
                  >Ubah File</PrimeVuebutton
                >
              </div>
              <div class="grid sm:grid-cols-2 grid-cols-1 gap-y-4 mt-3">
                <div class="flex items-center gap-1">
                  <PrimeVueCheckbox
                    input-id="cart-check-all"
                    name="cart-check-all"
                    class="mr-2"
                    binary
                    @click="authenticationRegisterMobile_toggleInoviceBill"
                    @update:model-value="() => authenticationRegisterMobile_formValidations_Section_3.invoice_number.$touch()"
                  />
                  <label for="cart-check-all" class="font-medium">Saya akan membutuhkan faktur pajak</label>
                </div>
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  :is-name-as-label="false"
                  label-for="faktur_pajak"
                  name="Tipe Faktur Pajak"
                  spacing-bottom="mb-0"
                  :validators="authenticationRegisterMobile_formValidations_Section_3.invoice_number"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_3.invoice_number"
                    :class="{ ...classes }"
                    class="rounded-lg"
                    placeholder="Pilih Tipe Faktur Pajak"
                    :disable="authenticationRegisterMobile_isNeedInvoiceBill"
                    :options="invoice_number"
                  />
                </BaseFormGroup>
              </div>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block my-3"
                is-name-as-label
                label-for="npwp_name"
                name="Nama NPWP"
                :is-mandatory="true"
                :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_name"
              >
                <input
                  id="npwp_name"
                  v-model="auhtenticationRegisterMobile_formData_Section_3.npwp_name"
                  v-bind="{ ...useBindStateForm('Nama Pada NPWP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted mb-2"
                  :class="{ ...classes }"
                  type="text"
                  :readonly="authenticationRegisterMobile_isLoading"
                  v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_3, 'npwp_name')"
                />
              </BaseFormGroup>
              <section class="grid grid-cols-1">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="password_confirmation"
                  name="Alamat Perusahaan"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_address"
                >
                  <textarea
                    id="perusahaan"
                    v-model="auhtenticationRegisterMobile_formData_Section_3.npwp_address"
                    rows="4"
                    v-bind="{ ...useBindStateForm('Masukan alamat npwp anda') }"
                    class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    :readonly="authenticationRegisterMobile_isLoading"
                    v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_3, 'npwp_address')"
                  />
                </BaseFormGroup>
              </section>
              <section class="grid grid-cols-1 gap-y-3 my-4">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="province"
                  name="Provinsi"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_province"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_3.npwp_province"
                    :class="{ ...classes }"
                    class="rounded-lg"
                    placeholder="Pilih Provinsi"
                    :disable="false"
                    :options="authentication_listNpwpProvince"
                    @update:selected="onChangeNpwpProvince"
                  />
                </BaseFormGroup>
                <BaseFormGroup
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="City"
                  name="Kota"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_city"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_3.npwp_city"
                    placeholder="Pilih Kota"
                    class="rounded-lg"
                    :disable="!auhtenticationRegisterMobile_formData_Section_3.npwp_province.code"
                    :options="authentication_listNpwpCities"
                    @update:selected="onChangeNpwpCity"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="Kecamatan"
                  name="Kecamatan"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_sub_district"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_3.npwp_sub_district"
                    placeholder="Pilih Kecamatan"
                    class="rounded-lg"
                    :disable="!auhtenticationRegisterMobile_formData_Section_3.npwp_city.code"
                    :options="authentication_listNpwpSubdistrict"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="zip_code"
                  name="Kode Pos"
                  :validators="authenticationRegisterMobile_formValidations_Section_3.npwp_zip_code"
                  :is-mandatory="true"
                >
                  <input
                    id="zip_code"
                    v-model="auhtenticationRegisterMobile_formData_Section_3.npwp_zip_code"
                    v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
                    class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    :readonly="authenticationRegisterMobile_isLoading"
                    v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_3, 'npwp_zip_code')"
                  />
                </BaseFormGroup>
              </section>
              <div class="grid grid-cols-1 gap-3">
                <PrimeVueButton
                  :disabled="
                    authenticationRegisterMobile_isLoading ||
                    authenticationRegisterMobile_fileValidations.file_npwp.$invalid ||
                    authenticationRegisterMobile_formValidations_Section_3.$invalid
                  "
                  label="Lanjut"
                  type="submit"
                  class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                  @click="
                    activateCallback('4');
                    selectedStep = 3;
                  "
                />
                <PrimeVueButton
                  label="Kembali"
                  severity="secondary"
                  icon="pi pi-arrow-left"
                  class="!w-full !bg-white !border !border-black !text-base !text-black !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                  @click="
                    activateCallback('2');
                    selectedStep = 5;
                  "
                />
              </div>
            </div>
          </PrimeVueStepPanel>

          <!-- panel 4 -->
          <!-- <PrimeVueStepPanel v-slot="{ activateCallback }" value="4">
            <div class="p-2">
              <header class="">
                <h1 class="font-druk text-4xl text-black mb-2">Pendaftaran</h1>
                <p class="text-base text-muted leading-6">Silakan daftarkan diri Anda</p>
              </header>
              <section class="grid grid-cols-1 my-4 gap-y-4">
                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="province"
                  name="Provinsi"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_4.npwp_province"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_4.npwp_province"
                    :class="{ ...classes }"
                    placeholder="Pilih Provinsi"
                    :disable="false"
                    :options="authentication_listProvince"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="City"
                  name="Kota"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_4.npwp_city"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_4.npwp_city"
                    placeholder="Pilih Kota"
                    :disable="false"
                    :options="authentication_listCities"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="Kecamatan"
                  name="Kecamatan"
                  spacing-bottom="mb-0"
                  :is-mandatory="true"
                  :validators="authenticationRegisterMobile_formValidations_Section_4.npwp_sub_district"
                >
                  <BaseSelectInput
                    v-model:selected="auhtenticationRegisterMobile_formData_Section_4.npwp_sub_district"
                    placeholder="Pilih Kecamatan"
                    :disable="false"
                    :options="authentication_listSubdistrict"
                  />
                </BaseFormGroup>

                <BaseFormGroup
                  v-slot="{ classes }"
                  class-label="font-medium text-sm text-black block mb-2"
                  is-name-as-label
                  label-for="zip_code"
                  name="Kode Pos"
                  :validators="authenticationRegisterMobile_formValidations_Section_4.npwp_zip_code"
                  :is-mandatory="true"
                >
                  <input
                    id="zip_code"
                    v-model="auhtenticationRegisterMobile_formData_Section_4.npwp_zip_code"
                    v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
                    class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    :readonly="authenticationRegisterMobile_isLoading"
                    v-on="useListenerForm(authenticationRegisterMobile_formValidations_Section_4, 'npwp_zip_code')"
                  />
                </BaseFormGroup>
              </section>
              <div class="grid grid-cols-1 gap-3">
                <PrimeVueButton
                  :disabled="
                    authenticationRegisterMobile_isLoading ||
                    authenticationRegisterMobile_formValidations_Section_4.$invalid
                  "
                  label="Lanjut"
                  type="submit"
                  class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                  @click="
                    activateCallback('5');
                    selectedStep = 4;
                  "
                />
                <PrimeVueButton
                  label="Kembali"
                  severity="secondary"
                  icon="pi pi-arrow-left"
                  class="!w-full !bg-white !border !border-black !text-base !text-black !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted mt-5"
                  @click="
                    activateCallback('3');
                    selectedStep = 5;
                  "
                />
              </div>
            </div>
          </PrimeVueStepPanel> -->

          <!-- panel 5 -->
          <PrimeVueStepPanel value="4">
            <div class="p-2 text-black h-[40em] overflow-y-scroll">
              <div v-for="(section, key_section) in sections" :key="key_section" class="grid grid-cols-1 gap-y-4">
                <div class="my-3">
                  <div class="font-bold ml-2 my-2">{{ section.section_name }}. {{ section.value }}</div>
                  <div class="grid grid-cols-1 gap-y-2">
                    <div
                      v-for="(tncDetail, key_tnc) in section.tnc_detail"
                      :key="key_tnc"
                      class="grid grid-cols-[3fr_97fr]"
                    >
                      <div class="">{{ key_tnc + 1 }}.</div>
                      <div>
                        {{ tncDetail.detail_value }}
                        <div
                          v-for="(tncPoint, key_point) in tncDetail.tnc_point"
                          :key="key_point"
                          class="grid grid-cols-[3fr_97fr] my-4"
                        >
                          <div class="">{{ key_point + 1 }}.</div>
                          <div class="">{{ tncPoint.point_value }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="">
                {{ (authentication_termAndCondition as ITermsAndConditions).footer }}
              </div>
            </div>
            <div class="grid grid-cols-1 gap-2 my-5">
              <div class="mt-5">
                <div class="flex items-center gap-2">
                  <PrimeVueCheckbox
                    v-model="authenticationRegisterMobile_tncBox.tnc_1"
                    input-id="term_1"
                    name="cart-check-all"
                    class="mr-2"
                    binary
                  />
                  <label for="term_1" class="text-[14px] font-medium"
                    >Saya Menyetujui Syarat dan ketentuan berlaku</label
                  >
                </div>
                <div class="flex items-center gap-2">
                  <PrimeVueCheckbox
                    v-model="authenticationRegisterMobile_tncBox.tnc_2"
                    input-id="term_2"
                    name="cart-check-all"
                    class="mr-2"
                    binary
                  />
                  <label for="term_2" class="text-[14px] font-medium">
                    Saya menyetujui menyetujui bahwa data yang diunggah akan digunakan oleh tim EIGER. Data akan
                    dihapus setelah Anda tidak lagi menjadi member.
                  </label>
                </div>
              </div>
            </div>
            <PrimeVueButton
              label="Daftar Akun"
              type="submit"
              class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
              @click="authenticationRegisterMobile_onSubmit"
            />
          </PrimeVueStepPanel>
        </PrimeVueStepPanels>
      </PrimeVueStepper>
    </div>
    <BaseAlert />
  </section>
  <PrimeVueDialog
    v-model:visible="isShowModalChangeIdentity"
    modal
    class="h-fit w-full md:w-[532px]"
    :pt="{
      header: '!py-2 border-b border-b-[#E5E6E8]',
      content: '!py-4',
      footer: '!py-3 border-t border-t-[#E5E6E8]',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[18px] text-black">Konfirmasi Ganti File KTP</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col gap-4">
        <span class="text-base text-black">Apakah Anda yakin ingin mengganti File KTP? </span>
      </section>
    </template>

    <template #footer>
      <section id="btn-actions" class="flex items-center justify-end gap-4">
        <PrimeVueButton
          type="button"
          label="Batalkan"
          class="!bg-white !text-black !px-3 !py-2 !rounded-lg !border-gray-300"
          @click="isShowModalChangeIdentity = false"
        />
        <PrimeVueButton
          type="button"
          label="Ganti File KTP"
          class="!bg-[#18191A] !text-white !px-3 !py-2 !rounded-lg !border-[#18191A]"
          @click="
            {
              isShowModalChangeIdentity = false;
              openFilePicker('file_identity');
            }
          "
        />
      </section>
    </template>
  </PrimeVueDialog>
  <!-- Modal Dialog NPWP -->
  <PrimeVueDialog
    v-model:visible="isShowModalChangeNpwp"
    modal
    class="h-fit w-full md:w-[532px]"
    :pt="{
      header: '!py-2 border-b border-b-[#E5E6E8]',
      content: '!py-4',
      footer: '!py-3 border-t border-t-[#E5E6E8]',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[18px] text-black">Konfirmasi Ganti File NPWP</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col gap-4">
        <span class="text-base text-black">Apakah Anda yakin ingin mengganti File NPWP? </span>
      </section>
    </template>

    <template #footer>
      <section id="btn-actions" class="flex items-center justify-end gap-4">
        <PrimeVueButton
          type="button"
          label="Batalkan"
          class="!bg-white !text-black !px-3 !py-2 !rounded-lg !border-gray-300"
          @click="isShowModalChangeNpwp = false"
        />
        <PrimeVueButton
          type="button"
          label="Ganti File NPWP"
          class="!bg-[#18191A] !text-white !px-3 !py-2 !rounded-lg !border-[#18191A]"
          @click="
            {
              isShowModalChangeNpwp = false;
              openFilePicker('file_npwp');
            }
          "
        />
      </section>
    </template>
  </PrimeVueDialog>
</template>
<style lang="css" scoped>
.p-step-active {
  gap: 0px !important;
}
.p-step {
  gap: 0px !important;
}
:deep(.p-stepper .p-step-number),
:deep(.p-stepper .p-step-header) {
  height: 32px !important;
  width: 20px !important;
}
:deep(.p-stepper-separator) {
  margin-left: 22px !important;
  width: 42% !important;
  height: 3px !important;
  flex: none !important;
}
@media (max-width: 600px) {
  .p-steppanels {
    padding: 0 !important;
  }
  .p-step {
    padding: 0 !important;
  }
}
</style>
