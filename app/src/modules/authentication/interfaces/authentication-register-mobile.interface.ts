import type { Validation } from '@vuelidate/core';

export interface IAuthenticationRegisterMobileSection1 {
  email: string;
  password: string;
  password_confirmation: string;
}

export interface IAuthenticationRegisterData {
  email: string;
  password: string;
  password_confirmation: string;
  full_name: string;
  phone_number: string;
  company_name: string;
  identity_number: string;
  npwp_number: string;
  npwp_raw: string;
  tax_type: string;
  invoice_number: string;
  npwp_name: string;
  npwp_address: string;
  province: {
    code: string;
    name: string;
    island: string;
  };
  city: {
    id: string;
    name: string;
    code: string;
  };
  sub_district: {
    name: string;
    code: string;
  };
  zip_code: {
    name: string;
    code: string;
    postal_code: string;
  };
}

export interface IAuthenticationRegisterMobileFormData {
  file_npwp: string;
  file_identity: string;
  owner_name: string;
  password: string;
  password_confirmation: string;
  phone_number: string;
  shipment_address: string;
  shipment_province_code: string;
  shipment_province: string;
  shipment_city_code: string;
  shipment_city: string;
  shipment_district_code: string;
  shipment_district: string;
  shipment_zip_code: string;
  npwp_province_code: string;
  npwp_province: string;
  npwp_city_code: string;
  npwp_city: string;
  npwp_district_code: string;
  npwp_district: string;
  email: string;
  instance_name: string;
  npwp: string;
  npwp_name: string;
  tax_type: string;
  npwp_address: string;
  npwp_zip_code: string;
  tax_invoice: string;
}

export interface IAuthenticationRegisterMobileData_Section1 {
  email: string;
  password: string;
  password_confirmation: string;
}

export interface IAuthenticationRegisterMobileData_Section2 {
  full_name: string;
  email: string;
  phone_number: string;
  company_name: string;
  shipment_address: string;
  shipment_province: {
    code: string;
    name: string;
    island: string;
  };
  shipment_city: {
    id: string;
    code: string;
    name: string;
    city_name: string;
  };
  shipment_sub_district: {
    code: string;
    name: string;
  };
  shipment_zip_code: string;
}
export interface IAuthenticationRegisterMobileData_Section3 {
  tax_type: string;
  invoice_number: string;
  ktp: string;
  npwp_number: string;
  npwp_raw: string;
  npwp_name: string;
  npwp_address: string;
  npwp_province: {
    code: string;
    name: string;
    island: string;
  };
  npwp_city: {
    id: string;
    code: string;
    name: string;
    city_name: string;
  };
  npwp_sub_district: {
    code: string;
    name: string;
  };
  npwp_zip_code: string;
}

export interface IAuthenticationRegisterMobileData_Section4 {
  npwp_province: {
    code: string;
    name: string;
    island: string;
  };
  npwp_city: {
    id: string;
    code: string;
    name: string;
    city_name: string;
  };
  npwp_sub_district: {
    code: string;
    name: string;
  };
  npwp_zip_code: string;
}

export interface IAuthenticationRegisterMobileAttachment {
  file_identity: File | null;
  file_npwp: File | null;
}

export interface IAuthenticationRegisterMobileCitiesPayload {
  code: string;
  name: string;
  island: string;
}

export interface IAuthenticationRegisterMobileSubdistrictPayload {
  name: string;
  code: string;
  id: string;
}

export interface IAuthenticationRegisterMobileZipcodePayload {
  code: string;
  name: string;
}

export interface IAuthenticationRegisterValidations {
  isLengthValid: boolean;
  isContainLowerCase: boolean;
  isContainNumber: boolean;
  isContainSpecialCharacter: boolean;
  isContainUpperCase: boolean;
}

export interface IAuthenticationStoreStates {
  authentication_accessToken: string;
  authentication_isLoading: boolean;
  authentication_userData: null;
  authentication_profileData: null;
  authentication_isUploadingKtp: Ref<boolean>;
}

export interface IAuthenticationCheckCustomerResponse {
  email: string;
  password: string;
}

export interface TncPoint {
  point_sequence: number;
  point_value: string;
}

export interface TncDetail {
  sequence_no: number;
  detail_value: string;
  tnc_point: TncPoint[];
}

export interface TncSection {
  section_name: string;
  sequence_no: number;
  value: string;
  tnc_detail: TncDetail[];
}

export interface ITermsAndConditions {
  A: TncSection;
  B: TncSection;
  C: TncSection;
  D: TncSection;
  E: TncSection;
  F: TncSection;
  G: TncSection;
  H: TncSection;
  I: TncSection;
  J: TncSection;
  K: TncSection;
  footer: string;
}

export interface IAuthenticationRegisterMobileProvided {
  authenticationRegisterMobile_attachment: IAuthenticationRegisterMobileAttachment;
  authenticationRegisterMobile_customValidations: IAuthenticationRegisterValidations;
  authenticationRegisterMobile_isCustomValidationsValid: Ref<boolean>;
  authenticationRegisterMobile_isLoading: Ref<boolean>;
  authenticationRegisterMobile_fileValidations: globalThis.Ref<Validation>;
  authenticationRegisterMobile_fetchProvinces: () => Promise<void>;
  authenticationRegisterMobile_fetchTaxType: () => Promise<void>;
  authenticationRegisterMobile_fetchTermsAndCondition: () => Promise<void>;
  authenticationRegisterMobile_onSubmit: () => void;
  authenticationRegisterMobile_setDynamicClassLabelValidation: (
    key: keyof IAuthenticationRegisterValidations,
  ) => string;
  authenticationRegisterMobile_setDynamicPathIcon: (key: keyof IAuthenticationRegisterValidations) => string;
  authenticationRegisterMobile_tncBox: Ref<{ tnc_1: boolean; tnc_2: boolean }>;
  authenticationRegisterMobile_isNeedInvoiceBill: Ref<boolean>;
  authenticationRegisterMobile_toggleInoviceBill: () => void;
  // upload KTP
  authentication_isUploadingKtp: Ref<boolean>;
  authentication_isUploadKtpError: Ref<boolean>;
  authentication_uploadKtpProgress: Ref<number>;
  authentication_uploadedKtpSize: Ref<string>;
  authentication_totalKtpSize: Ref<string>;
  authentication_uploadKtpFileName: Ref<string | null>;
  // upload NPWP
  authentication_isUploadingNpwp: Ref<boolean>;
  authentication_isUploadNpwpError: Ref<boolean>;
  authentication_uploadNpwpProgress: Ref<number>;
  authentication_uploadedNpwpSize: Ref<string>;
  authentication_totalNpwpSize: Ref<string>;
  authentication_uploadNpwpFileName: Ref<string | null>;

  // SECTION 1
  authenticationRegisterMobile_onSubmitSection1: () => void;
  auhtenticationRegisterMobile_fetchCheckCustomer: () => void;
  auhtenticationRegisterMobile_formData_Section_1: IAuthenticationRegisterMobileData_Section1;
  authenticationRegisterMobile_formValidations_Section_1: globalThis.Ref<Validation>;
  // SECTION 2
  authenticationRegisterMobile_formValidations_Section_2: globalThis.Ref<Validation>;
  auhtenticationRegisterMobile_formData_Section_2: IAuthenticationRegisterMobileData_Section2;
  // SECTION 3
  authenticationRegisterMobile_formValidations_Section_3: globalThis.Ref<Validation>;
  auhtenticationRegisterMobile_formData_Section_3: IAuthenticationRegisterMobileData_Section3;
  // SECTION 4
  authenticationRegisterMobile_formValidations_Section_4: globalThis.Ref<Validation>;
  auhtenticationRegisterMobile_formData_Section_4: IAuthenticationRegisterMobileData_Section4;
  // API
  authentication_listProvince: Ref<[]>;
  authentication_listCities: Ref<[]>;
  authentication_listSubdistrict: Ref<[]>;
  authentication_listZipcode: Ref<[]>;
  authentication_listNpwpProvince: Ref<[]>;
  authentication_listNpwpCities: Ref<[]>;
  authentication_listNpwpSubdistrict: Ref<[]>;
  authentication_taxType: Ref<[]>;
  authentication_termAndCondition: Ref<object | null | ITermsAndConditions>;

  authenticationRegisterMobile_fileRules: unknown;
  authentication_isExistNPWP: Ref<boolean>;
  authentication_isExistKTP: Ref<boolean>;
  authenticationRegisterMobile_validateNPWP: (val: string) => Promise<void>;
  authenticationRegisterMobile_validateKTP: (val: string) => Promise<void>;
}
