// Interfaces
import type { Validation } from '@vuelidate/core';

export interface IAuthenticationForgotPasswordPayload {
  email: string;
  type: string;
  channel: string;
}

export interface IAuthenticationForgotPasswordProvided {
  authenticationForgotPassword_formData: IAuthenticationForgotPasswordPayload;
  authenticationForgotPassword_isLoading: Ref<boolean>;
  authenticationForgotPassword_formValidations: globalThis.Ref<Validation>;
  authenticationForgotPassword_onSubmit: () => void;
}
