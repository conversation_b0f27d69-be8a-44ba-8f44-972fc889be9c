import type { Validation } from '@vuelidate/core';
import type { ITermsAndConditions } from './authentication-register-mobile.interface';

export interface IAuthenticationRegisterDetailFormData { 
    full_name : string;
    email : string;
    phone_number : string;
    company : string;
    shipping_address : string;
    ktp: string;
    npwp_number: string;
    npwp_raw: string;
    tax_type: string;
    invoice_number : string;
    npwp_name: string;
    npwp_address : string; 
    npwp_province  : {
        code : string;
        name : string;
        island: string;
    },
    npwp_city : {
        id : string;
        name : string;
        code : string;
        city_name: string;
    };
    npwp_sub_district : {
        name : string;
        code : string;
    };
    npwp_zip_code : string;
    shipment_province  : {
        code : string;
        name : string;
        island: string;
    },
    shipment_city : {
        id : string;
        name : string;
        code : string;
        city_name: string;
    };
    shipment_sub_district : {
        name : string;
        code : string;
    };
    shipment_zip_code : string
}



export interface IAuthenticationRegisterDetailAttachment {
    file_npwp: File | null | Blob;
    file_identity: File | null | Blob;
}

export interface IAuthenticationRegisterDetailTncBox {
    tnc_1: boolean;
    tnc_2: boolean;
}


export interface IAuthentucationRegisterDetailPostFileAws{
    file: File | null | Blob;
    file_type : string;
    url: string;
}

export interface IAuthenticationRegisterDetailAttachment {
    file_identity: File | null | Blob;
    file_npwp: File | null | Blob;
}
  
export interface IAuthenticationRegisterDetailCitiesPayload {
    code: string;
    name: string;
    island: string;
}

export interface IAuthenticationRegisterDetailSubdistrictPayload {
    name: string;
    code: string;
    id: string;
}

export interface IAuthenticationRegisterDetailZipcodePayload {
    code : string;
    name : string;
}
export interface IAuthenticationRegisterDetailRegisterPayload {
    information : {
        owner_name: string,
        phone_number: string,
        instance_name: string,
        identity_number: string,
        tax_invoice_type: string,
        npwp: string,
        npwp_name: string,
        npwp_city: string,
        npwp_address: string,
        address_shipment: string,
        zip_code: string,
        tax_type:string,
        city_shipment: string,
        province_shipment: string,
        district: string,
    }
}



export type IAuthentucationRegisterDetailZipcodeList = IAuthenticationRegisterDetailZipcodePayload[]

export interface IAuthenticationRegisterDetailProvided {
    authenticationRegisterDetail_formData:IAuthenticationRegisterDetailFormData
    authenticationRegisterDetail_isLoading : Ref<boolean>,
    authenticationRegisterDetail_formValidations : globalThis.Ref<Validation>,
    authenticationRegisterDetail_fileValidations: globalThis.Ref<Validation>;
    authenticationRegisterDetail_onSubmitForm: () => void,
    authenticationRegisterDetail_onSubmitTnc:() => void,
    authenticationRegisterDetail_fetchProvinces: () => Promise<void>,
    authenticationRegisterDetail_fetchTaxType: () => Promise<void>,
    authenticationRegisterDetail_fetchTermsAndCondition: () => Promise<void>,
    authenticationRegisterDetail_validateNPWP: (val: string) => Promise<void>;
    authenticationRegisterDetail_validateKTP: (val: string) => Promise<void>;
    authenticationRegisterDetail_attachment: IAuthenticationRegisterDetailAttachment,
    authenticationRegisterDetail_isShowModalTnc: Ref<boolean>,
    authenticationRegisterDetail_tncBox_1: Ref<boolean>;
    authenticationRegisterDetail_tncBox_2: Ref<boolean>;
    authenticationRegisterDetail_toggleInoviceBill: () => void;
    authenticationRegisterDetail_isNeedInvoiceBill: Ref<boolean>;
    
    // API
    authentication_listProvince : Ref<[]>;
    authentication_listCities: Ref<[]>;
    authentication_listSubdistrict: Ref<[]>;
    authentication_listNpwpProvince : Ref<[]>;
    authentication_listNpwpCities: Ref<[]>;
    authentication_listNpwpSubdistrict: Ref<[]>;
    authentication_listZipcode: Ref<[]>;
    authentication_taxType: Ref<[]>;
    authentication_isExistNPWP: Ref<boolean>;
    authentication_isExistKTP: Ref<boolean>;
    authentication_termAndCondition: Ref<object | null | ITermsAndConditions>
    // upload KTP
    authentication_isUploadingKtp: Ref<boolean>;
    authentication_isUploadKtpError: Ref<boolean>;
    authentication_uploadKtpProgress: Ref<number>;
    authentication_uploadedKtpSize: Ref<string>;
    authentication_totalKtpSize: Ref<string>;
    authentication_uploadKtpFileName: Ref<string | null>;
    // upload NPWP
    authentication_isUploadingNpwp: Ref<boolean>;
    authentication_isUploadNpwpError: Ref<boolean>;
    authentication_uploadNpwpProgress: Ref<number>;
    authentication_uploadedNpwpSize: Ref<string>;
    authentication_totalNpwpSize: Ref<string>;
    authentication_uploadNpwpFileName: Ref<string | null>;
    authentication_emailRegister: Ref<string>
}