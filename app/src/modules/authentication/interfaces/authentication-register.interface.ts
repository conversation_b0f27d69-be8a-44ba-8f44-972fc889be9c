import type { Validation } from '@vuelidate/core';

export interface IAuthenticationRegisterPayload {
    email: string;
    password: string;
    password_confirmation: string;
}

export interface IAuthenticationRegisterValidations {
    isLengthValid: boolean;
    isContainLowerCase: boolean;
    isContainNumber: boolean;
    isContainSpecialCharacter: boolean;
    isContainUpperCase: boolean;
}


export interface IAuthenticationStoreStates {
    authentication_accessToken: string,
    authentication_isLoading: boolean,
    authentication_userData: null,
    authentication_profileData: null,
}

export interface IAuthenticationCheckCustomerResponse {
  email : string;
  password : string;
}


export interface IAuthenticationRegisterProvided {
    authenticationRegister_customValidations: IAuthenticationRegisterValidations;
    authenticationRegister_formData: IAuthenticationRegisterPayload;
    authenticationRegister_isCustomValidationsValid: Ref<boolean>;
    authenticationRegister_isLoading: Ref<boolean>;
    authenticationRegister_formValidations: globalThis.Ref<Validation>;
    authenticationRegister_onSubmit: () => void;
    authenticationRegister_setDynamicClassLabelValidation: (
        key: keyof IAuthenticationRegisterValidations,
    ) => string;
    authenticationRegister_setDynamicPathIcon: (key: keyof IAuthenticationRegisterValidations) => string;
    // upload KTP
    authentication_isUploadingKtp: Ref<boolean>;
    authentication_isUploadKtpError: Ref<boolean>;
    authentication_uploadKtpProgress: Ref<number>;
    authentication_uploadedKtpSize: Ref<string>;
    authentication_totalKtpSize: Ref<string>;
    authentication_uploadKtpFileName: Ref<string | null>;
    // upload NPWP
    authentication_isUploadingNpwp: Ref<boolean>;
    authentication_isUploadNpwpError: Ref<boolean>;
    authentication_uploadNpwpProgress: Ref<number>;
    authentication_uploadedNpwpSize: Ref<string>;
    authentication_totalNpwpSize: Ref<string>;
    authentication_uploadNpwpFileName: Ref<string | null>;
}


