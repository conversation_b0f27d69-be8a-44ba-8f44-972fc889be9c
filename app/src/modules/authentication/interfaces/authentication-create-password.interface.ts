import type { Validation } from '@vuelidate/core';

export interface IAuthenticationCreatePasswordPayload {
  token?: string;
  password: string;
  password_confirmation: string;
}

export interface IAuthenticationCreatePasswordValidations {
  isLengthValid: boolean;
  isContainLowerCase: boolean;
  isContainNumber: boolean;
  isContainSpecialCharacter: boolean;
  isContainUpperCase: boolean;
}

export interface IAuthenticationCreatePasswordProvided {
  authenticationCreatePassword_customValidations: IAuthenticationCreatePasswordValidations;
  authenticationCreatePassword_formData: IAuthenticationCreatePasswordPayload;
  authenticationCreatePassword_isCustomValidationsValid: Ref<boolean>;
  authenticationCreatePassword_isLoading: Ref<boolean>;
  authenticationCreatePassword_formValidations: globalThis.Ref<Validation>;
  authenticationCreatePassword_onSubmit: () => void;
  authenticationCreatePassword_setDynamicClassLabelValidation: (
    key: keyof IAuthenticationCreatePasswordValidations,
  ) => string;
  authenticationCreatePassword_setDynamicPathIcon: (key: keyof IAuthenticationCreatePasswordValidations) => string;
  authenticationCreatePassword_verify: (token: string) => Promise<void>;
  authenticationCreatePassword_verifyIsLoading: Ref<boolean>;
  authenticationCreatePassword_verifyEmail: Ref<string | null>;
  authenticationCreatePassword_changePasswordIsLoading: Ref<boolean>;
  authenticationCreatePassword_changePasswordIsSuccess: Ref<boolean>;
  
}
