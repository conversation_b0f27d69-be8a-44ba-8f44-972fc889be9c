import type { Validation } from '@vuelidate/core';

interface IAuthenticationResponse {
  access_token: string;
  token: string;
}

export interface IAuthProfileStore {
  customer_shipment_id: string;
  city: string;
  city_code: string;
  district: string;
  district_code: string;
  is_primary: string;
  name: string;
  province: string;
  province_code: string;
  zip_code: string;
  address: string;
  [key: string]: string;
}

export interface IAuthProfileVA {
  id: number;
  customer_id: string;
  bank_name: string;
  virtual_account_no: string;
  created_date: string;
  created_by: string;
  modified_date: string;
  modified_by: string;
}

interface IBank {
  id: string;
  company_id: string;
  bank_name: string;
  bank_account_name: string;
  bank_account_no: string;
  is_active: number;
  logo: string;
  created_date: string;
  created_by: string;
  modified_date: string;
  modified_by: string;
}

export interface IProfile {
  customer_id: string;
  owner_name: string;
  email: string;
  is_verified: boolean;
  phone_number: string;
  distribution_channel: string;
  npwp: string;
  npwp_name: string;
  npwp_province: string;
  npwp_province_code: string;
  npwp_city: string;
  npwp_city_code: string;
  npwp_district: string;
  npwp_district_code: string;
  npwp_zip_code: string;
  npwp_address: string;
  shipment_province: string;
  shipment_province_code: string;
  shipment_city: string;
  shipment_city_code: string;
  shipment_district: string;
  shipment_district_code: string;
  shipment_zip_code: string;
  national_id: string;
  address: string;
  top: string;
  credit_limit: string;
  credit_limit_used: string;
  credit_limit_used_percentage: string;
  credit_limit_remaining: string;
  credit_limit_currency: number;
  top_days: string;
  customer_type: string;
  is_active: boolean;
  is_pending_payment: boolean;
  is_change_password: boolean;
  store_list: IAuthProfileStore[];
  va_list: IAuthProfileVA[];
  bank_list: IBank[];
  rejection_reason: string | null;
  status: string;
  remarks: string;
  npwp_file: string;
  tax_type: string;
  tax_invoice: string;
}

export interface IUser {
  user_id: string;
  reference_id: string;
  reference_object: string;
  username: string;
  is_change_password: boolean;
  is_verified: boolean;
  is_active: boolean;
}

export interface IAuthenticationCheckCustomerResponse {
  email: string;
  password: string;
}

export interface IAuthenticationLoginResponse {
  auth: IAuthenticationResponse;
  profile: IProfile;
  user: IUser;
}

export interface IAuthenticationLoginPayload {
  email: string;
  password: string;
  type: string;
  reference: string;
}

export interface IAuthenticationLoginProvided {
  authenticationLogin_formData: IAuthenticationLoginPayload;
  authenticationLogin_isLoading: Ref<boolean>;
  authenticationLogin_formValidations: globalThis.Ref<Validation>;
  authenticationLogin_onSubmit: (checkedRememberMe?: boolean) => void;
}
