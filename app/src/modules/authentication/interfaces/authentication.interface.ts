import type { Reactive } from 'vue';
import type { IAuthenticationLoginPayload, IProfile, IUser } from './authentication-login.interface';
// import type { ITermsAndConditions } from './authentication-register-mobile.interface'

import type { Validation } from '@vuelidate/core';

export interface IAuthenticationUserData extends IProfile, IUser {}

interface ITncPoint {
  point_sequence: number;
  point_value: string;
}

// Interface for detail items within each section
interface ITncDetail {
  sequence_no: number;
  detail_value: string;
  tnc_point: ITncPoint[];
}

// Interface for each section (A, B, C, etc.)
interface ITncSection {
  section_name: string;
  sequence_no: number;
  value: string;
  tnc_detail: ITncDetail[];
}

// Root interface for the entire data structure
interface ITermsAndConditions {
  A: ITncSection;
  B: ITncSection;
  C: ITncSection;
  D: ITncSection;
  E: ITncSection;
  F: ITncSection;
  G: ITncSection;
  H: ITncSection;
  I: ITncSection;
  J: ITncSection;
  K: ITncSection;
  footer: string;
}
export interface IAuthenticationStoreStates {
  authentication_accessToken: string;
  authentication_isLoading: boolean;
  authentication_userData: IAuthenticationUserData | null;
  authentication_profileData: IProfile | null;
  authentication_emailRegister: string;
  authentication_passwordRegister: string;
  authentication_checkNPWPisLoading: boolean;
  authentication_checkKTPIsLoading: boolean;
  authentication_isExistNPWP: boolean;
  authentication_isExistKTP: boolean;
  // upload KTP
  authentication_isUploadingKtp: boolean;
  authentication_isUploadKtpError: boolean;
  authentication_uploadKtpProgress: number;
  authentication_uploadedKtpSize: string;
  authentication_totalKtpSize: string;
  authentication_uploadKtpFileName: string | null;
  // upload NPWP
  authentication_isUploadingNpwp: boolean;
  authentication_isUploadNpwpError: boolean;
  authentication_uploadNpwpProgress: number;
  authentication_uploadedNpwpSize: string;
  authentication_totalNpwpSize: string;
  authentication_uploadNpwpFileName: string | null;
  // SHIPMENT
  authentication_listProvince: [];
  authentication_listCities: [];
  authentication_listSubdistrict: [];
  // NPWP
  authentication_listNpwpProvince: [];
  authentication_listNpwpCities: [];
  authentication_listNpwpSubdistrict: [];

  authentication_listZipcode: [];
  authentication_taxType: [];
  authentication_termAndCondition: object | null | ITermsAndConditions;
  // RESET PASSWORD
  authentication_verifyResetPasswordIsLoading: boolean;
  authentication_verifyResetPasswordEmail: string | null;
  authentication_changePasswordIsLoading: boolean;
  authentication_changePasswordIsSuccess: boolean;

  // REDIRECTION STATE
  authentication_redirectionState: {
    path: string | null;
    payload?: unknown;
  };
  authentication_isOpenModalLogin: boolean;
  authentication_checkActivationLoading: boolean;
  authentication_resendIsLoading: boolean;
}

export interface IAuthenticationProvided {
  authentication_isOpenModalLogin: Ref<boolean>;
  authentication_isLoggedIn: ComputedRef<boolean>;
  authentication_loginFormData: Reactive<IAuthenticationLoginPayload>;
  authentication_loginIsLoading: Ref<boolean>;
  authentication_loginFormValidations: globalThis.Ref<Validation>;
  authentication_onSubmitLogin: (checkedRememberMe?: boolean) => void;
  authentication_checkActivationLoading: Ref<boolean>;
  authentication_resendIsLoading: Ref<boolean>;
  authentication_checkActivationStatus: (token: string, email: string) => Promise<unknown>;
  authentication_resendActivation: (email: string) => Promise<void>;
}

export interface IAuthenticationResponseUploadFile {
  filepath: string;
  s3_url: string;
}
