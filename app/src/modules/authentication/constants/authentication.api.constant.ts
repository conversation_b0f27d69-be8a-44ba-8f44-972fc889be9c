export const AUTHENTICATION_ENDPOINT_CREATE_PASSWORD = '/init-change-password';
export const AUTHENTICATION_ENDPOINT_FORGOT_PASSWORD = '/forgot-password';
export const AUTHENTICATION_ENDPOINT_VALIDATE_NPWPKTP = '/validateNPWPKTPUpdate';
export const AUTHENTICATION_ENDPOINT_LOGIN = '/login';
export const AUTHENTICATION_ENDPOINT_PROFILE = '/profile';
export const AUTHENTICATION_ENDPOINT_REGISTER = '/register';
export const AUTHENTICATION_ENDPOINT_UPLOAD_FILE = '/file';
export const AUTHENTICATION_ENDPOINT_VERIFY_RESET_PASSWORD = '/reset-password/verify';
export const AUTHENTICATION_ENDPOINT_CHANGE_PASSWORD = '/change-forgot-password';
export const AUTHENTICATION_CHECK_CUSTOMER = '/checkCustomer';
export const AUTHENTICATION_CHECK_NPWP_IS_EXIST = '/getNpwpExist';
export const AUTHENTICATION_GET_PRONIVCE = '/region?per_page=10000';
export const AUTHENTICATION_GET_CITY = '/region-city?per_page=10000&region_code=';
export const AUTHENTICATION_GET_SUBDISTRICT = '/region-district?per_page=10000&city_id=';
export const AUTHENTICATION_GET_ZIPCODE = '/region-subdistrict?per_page=10000&district_code=';
export const AUTHENTICATION_GET_TAX_TYPE = '/get-taxtypes';
export const AUTHENTICATION_GET_TNC = '/terms-and-conditions';
export const AUTHENTICATION_VALIDATE_NPWPKTP = '/validateNPWPKTP';
export const AUTHENTICATION_CHECK_ACTIVATION = '/activate';
export const AUTHENTICATION_RESEND_ACTIVATE = '/resend-activate';
