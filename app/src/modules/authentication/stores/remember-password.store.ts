// Pinia
import { defineStore } from 'pinia';
import type { IRememberPasswordStoreState } from '../interfaces/remember-password.interface.ts';

export const useRememberPassword = defineStore('rememberState', {
  state: (): IRememberPasswordStoreState => ({
    email: '',
    password: '',
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {},
  persist: {
    storage: localStorage,
    pick: ['email', 'password'],
  },
});
