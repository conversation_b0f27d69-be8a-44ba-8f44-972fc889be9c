// Constants
import {
  AUTHENTICATION_ENDPOINT_FORGOT_PASSWORD,
  AUTHENTICATION_ENDPOINT_LOGIN,
  AUTHENTICATION_ENDPOINT_PROFILE,
  AUTHENTICATION_ENDPOINT_VALIDATE_NPWPKTP,
  AUTHENTICATION_CHECK_CUSTOMER,
  AUTHENTICATION_GET_PRONIVCE,
  AUTHENTICATION_GET_CITY,
  AUTHENTICATION_GET_SUBDISTRICT,
  AUTHENTICATION_GET_ZIPCODE,
  AUTHENTICATION_GET_TNC,
  AUTHENTICATION_ENDPOINT_VERIFY_RESET_PASSWORD,
  AUTHENTICATION_GET_TAX_TYPE,
  AUTHENTICATION_ENDPOINT_REGISTER,
  AUTHENTICATION_ENDPOINT_CHANGE_PASSWORD,
  AUTHENTICATION_ENDPOINT_UPLOAD_FILE,
  AUTHENTICATION_CHECK_ACTIVATION,
  AUTHENTICATION_RESEND_ACTIVATE,
  AUTHENTICATION_VALIDATE_NPWPKTP,
} from '../constants/authentication.api.constant';

// Interfaces
import type { IAuthenticationCreatePasswordPayload } from '../interfaces/authentication-create-password.interface';
import type { IAuthenticationForgotPasswordPayload } from '../interfaces/authentication-forgot-password.interface';
import type { IAuthenticationRegisterPayload } from '../interfaces/authentication-register.interface';
import type {
  IAuthenticationRegisterDetailAttachment,
  IAuthenticationRegisterDetailCitiesPayload,
  IAuthenticationRegisterDetailFormData,
  IAuthenticationRegisterDetailSubdistrictPayload,
  IAuthenticationRegisterDetailZipcodePayload,
  IAuthentucationRegisterDetailPostFileAws,
} from '../interfaces/authentication-register-detail.interface';

import type { IAuthenticationRegisterMobileFormData } from '../interfaces/authentication-register-mobile.interface';

import type {
  IAuthenticationLoginPayload,
  IAuthenticationLoginResponse,
  IProfile,
} from '../interfaces/authentication-login.interface';
import type {
  IAuthenticationResponseUploadFile,
  IAuthenticationStoreStates,
} from '../interfaces/authentication.interface';

// Pinia
import { defineStore } from 'pinia';
// uuid
// import { v4 as uuidv4 } from 'uuid';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import CryptoJS from 'crypto-js';
// import shortUUID from 'short-uuid'

export const useAuthenticationStore = defineStore('authentication', {
  state: (): IAuthenticationStoreStates => ({
    authentication_accessToken: '',
    authentication_isLoading: false,
    authentication_userData: null,
    authentication_profileData: null,
    authentication_emailRegister: '',
    authentication_passwordRegister: '',
    authentication_checkNPWPisLoading: true,
    authentication_isExistNPWP: true,
    authentication_checkKTPIsLoading: true,
    authentication_isExistKTP: true,
    // ktp
    authentication_isUploadingKtp: false,
    authentication_isUploadKtpError: false,
    authentication_uploadKtpProgress: 0,
    authentication_uploadedKtpSize: '0.00',
    authentication_totalKtpSize: '0.00',
    authentication_uploadKtpFileName: null,
    // npwp
    authentication_isUploadingNpwp: false,
    authentication_isUploadNpwpError: false,
    authentication_uploadNpwpProgress: 0,
    authentication_uploadedNpwpSize: '0.00',
    authentication_totalNpwpSize: '0.00',
    authentication_uploadNpwpFileName: null,
    // API
    authentication_termAndCondition: {},
    authentication_taxType: [],
    // Shipping
    authentication_listProvince: [],
    authentication_listCities: [],
    authentication_listSubdistrict: [],
    // NPWP
    authentication_listNpwpProvince: [],
    authentication_listNpwpCities: [],
    authentication_listNpwpSubdistrict: [],
    authentication_listZipcode: [],
    // RESET PASSWORD
    authentication_verifyResetPasswordIsLoading: false,
    authentication_verifyResetPasswordEmail: null,
    authentication_changePasswordIsLoading: false,
    authentication_changePasswordIsSuccess: false,
    authentication_redirectionState: {
      path: null,
      payload: null,
    },
    authentication_isOpenModalLogin: false,
    authentication_checkActivationLoading: false,
    authentication_resendIsLoading: false,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    // function to hit post create password
    // return: response hit api create password
    async fetchAuthentication_createPassword(payload: IAuthenticationCreatePasswordPayload): Promise<unknown> {
      try {
        this.authentication_isLoading = true;

        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_CHANGE_PASSWORD, {
          body: payload,
          method: 'POST',
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.authentication_isLoading = false;
      }
    },
    // function to hit api forgot password
    // return: response forgot password
    async fetchAuthentication_forgotPassword(payload: IAuthenticationForgotPasswordPayload): Promise<unknown> {
      try {
        this.authentication_isLoading = true;

        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_FORGOT_PASSWORD, {
          body: payload,
          method: 'POST',
        });
        if (error.value?.statusCode == 400) {
          throw 'Maaf akun tidak terdaftar';
        }
        if (error.value?.statusCode == 500) {
          throw 'Terjadi Kesalah Pada Server silahkan coba lagi';
        }
        if (error.value) {
          throw new Error(error.value.message || 'Terjadi kesalahan.');
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.authentication_isLoading = false;
      }
    },
    //function to hit login api
    // return: response login api
    async fetchAuthentication_login(payload: IAuthenticationLoginPayload): Promise<unknown> {
      try {
        this.authentication_isLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_LOGIN, {
          body: payload,
          method: 'POST',
        });
        if (error.value) {
          const status = error.value.statusCode;
          if (status === 401) {
            throw new Error('Email Anda belum diverifikasi');
          } else if (status === 400) {
            throw new Error('Email atau kata sandi yang Anda masukan salah, silakan coba lagi');
          } else {
            throw new Error('Terjadi kesalahan saat proses login. Silakan coba lagi.');
          }
        }

        const responseData = (data.value as { data: IAuthenticationLoginResponse } | undefined)?.data;

        if (!responseData || !responseData.auth || !responseData.auth.access_token) {
          throw new Error('Data login tidak valid');
        }

        const { auth, profile, user } = responseData;

        this.authentication_accessToken = auth.access_token;
        this.authentication_userData = { ...profile, ...user };
        this.authentication_profileData = { ...profile, ...user };

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(error);
      } finally {
        this.authentication_isLoading = false;
      }
    },
    // function to verifiy token reset password
    // return: return reset password
    async fetchAuthentication_verifyTokenResetPassword(token: string): Promise<unknown> {
      try {
        this.authentication_verifyResetPasswordIsLoading = true;
        this.authentication_verifyResetPasswordEmail = '';

        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_VERIFY_RESET_PASSWORD, {
          method: 'POST',
          body: {
            token,
          },
          onResponse: async ({ response }) => {
            if (response.status == 404) {
              window.location.href = '/authentication/login';
            }
            const email = response._data.data?.email as string;
            this.authentication_verifyResetPasswordEmail = email ?? '';
          },
        });

        if (error.value) {
          if (error.value.statusCode == 404) {
            window.location.href = '/authentication/login';
          }
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        this.authentication_verifyResetPasswordEmail = '';
        return Promise.reject(new Error('Failed to fetch verify token reset password'));
      } finally {
        this.authentication_verifyResetPasswordIsLoading = false;
      }
    },
    // function to hit rest api profile
    // return: response api profile
    async fetchAuthentication_getProfile(): Promise<unknown> {
      try {
        this.authentication_isLoading = true;

        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_PROFILE, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const data = response._data.data as IProfile;
            this.authentication_profileData = data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Failed to fetch profile'));
      } finally {
        this.authentication_isLoading = false;
      }
    },
    // function to check customer
    // return: response available customer
    async fetchAuthentication_checkCustomer(payload: IAuthenticationRegisterPayload): Promise<unknown> {
      try {
        this.authentication_isLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_CHECK_CUSTOMER, {
          body: {
            email: payload.email,
          },
          method: 'POST',
          onResponse: async ({ response }) => {
            if (response._data.error == false) {
              this.authentication_emailRegister = payload.email;
              this.authentication_passwordRegister = payload.password;
            }
          },
        });
        if (error.value?.statusCode == 409) {
          return Promise.reject(new Error('Akun Anda Terdaftar, silakan gunakan email lain'));
        }
        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Email atau kata sandi yang Anda masukan salah, silakan coba lagi'));
      }
    },
    // funnction to get api province
    // return: response province
    async fetchAuthentication_getProvince(): Promise<unknown> {
      try {
        this.authentication_isLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_GET_PRONIVCE, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.authentication_listProvince = response._data.data.original.data;
            this.authentication_listNpwpProvince = response._data.data.original.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      } finally {
        this.authentication_isLoading = false;
      }
    },
    // function to get api type of tax
    // return: response api type of tax
    async fetchAuthentication_getTaxType(): Promise<unknown> {
      try {
        this.authentication_isLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_GET_TAX_TYPE, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.authentication_taxType = response._data.data;
            // this.province = response._data.data.original.data
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      } finally {
        this.authentication_isLoading = false;
      }
    },
    // function to get api term and condition
    // return:
    async fetchAuthentication_getTermsAndCondition(): Promise<unknown> {
      try {
        this.authentication_isLoading = true;
        const { data, error } = await useApiFetch(AUTHENTICATION_GET_TNC, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.authentication_termAndCondition = response._data.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      } finally {
        this.authentication_isLoading = false;
      }
    },
    // function to get api list ccities
    // return: response cities
    async fetchAuthentication_getCities(
      payload: IAuthenticationRegisterDetailCitiesPayload,
      type: string,
    ): Promise<unknown> {
      if (type == 'npwp') {
        try {
          this.authentication_isLoading = true;
          const { data, error } = await useApiFetch(`${AUTHENTICATION_GET_CITY}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.authentication_listNpwpCities = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }

          return Promise.resolve(data);
        } catch {
          return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
        } finally {
          this.authentication_isLoading = false;
        }
      } else {
        try {
          this.authentication_isLoading = true;
          const { data, error } = await useApiFetch(`${AUTHENTICATION_GET_CITY}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.authentication_listCities = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }

          return Promise.resolve(data);
        } catch {
          return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
        } finally {
          this.authentication_isLoading = false;
        }
      }
    },
    // function to get api  subdistrict
    // return:
    async fetchAuthentication_getSubdisctrict(
      payload: IAuthenticationRegisterDetailSubdistrictPayload,
      type: string,
    ): Promise<unknown> {
      if (type == 'npwp') {
        try {
          this.authentication_isLoading = true;
          const { data, error } = await useApiFetch(`${AUTHENTICATION_GET_SUBDISTRICT}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.authentication_listNpwpSubdistrict = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }

          return Promise.resolve(data);
        } catch {
          return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
        } finally {
          this.authentication_isLoading = false;
        }
      } else {
        try {
          this.authentication_isLoading = true;
          const { data, error } = await useApiFetch(`${AUTHENTICATION_GET_SUBDISTRICT}${payload.code}`, {
            method: 'GET',
            onResponse: async ({ response }) => {
              this.authentication_listSubdistrict = response._data.data.original.data;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }

          return Promise.resolve(data);
        } catch {
          return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
        } finally {
          this.authentication_isLoading = false;
        }
      }
    },
    // function to get api zip code
    // return: api zip code
    async fetchAuthentication_getZipcode(payload: IAuthenticationRegisterDetailZipcodePayload): Promise<unknown> {
      try {
        this.authentication_isLoading = true;
        const { data, error } = await useApiFetch(`${AUTHENTICATION_GET_ZIPCODE}${payload.code}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.authentication_listZipcode = response._data.data.original.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      } finally {
        this.authentication_isLoading = false;
      }
    },

    async authenticationRegister_uploadFile(file: File): Promise<{ filePath: string } | null> {
      try {
        let _filePath = null;
        const uid = crypto.randomUUID();
        const { error } = await useApiFetch<IBaseApiResponse<IAuthenticationResponseUploadFile>>(
          AUTHENTICATION_ENDPOINT_UPLOAD_FILE,
          {
            method: 'POST',
            body: { name: `${uid}_${file.name}` },
            headers: {},
            onResponse: async ({ response }) => {
              const filePath = response?._data.data?.filepath;
              const s3Url = response?._data.data?.s3_url;
              _filePath = filePath;
              if (filePath && s3Url) {
                return this.authenticationRegister_postAws({
                  file: file,
                  file_type: file.type,
                  url: s3Url,
                });
              } else {
                return Promise.reject(null);
              }
            },
          },
        );
        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(_filePath);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    async authenticationRegister_registerData(
      information: IAuthenticationRegisterDetailFormData,
      attachment: IAuthenticationRegisterDetailAttachment,
    ): Promise<unknown> {
      this.authentication_isLoading = true;
      let _result = false;
      try {
        if (attachment?.file_npwp) {
          let responseUploadKtp;
          if (attachment?.file_identity) {
            responseUploadKtp = await this.authenticationRegister_uploadFile(attachment?.file_identity as File);
          }
          const responseUploadNpwp = await this.authenticationRegister_uploadFile(attachment?.file_npwp as File);
          const payload = {
            // User information
            owner_name: information.full_name,
            password: this.authentication_passwordRegister,
            password_confirmation: this.authentication_passwordRegister,

            // Shipping information
            shipment_address: information.shipping_address,
            shipment_province_code: information.shipment_province?.code,
            shipment_province: information.shipment_province.name,
            shipment_city_code: information.shipment_city.code,
            shipment_city: information.shipment_city.city_name,
            shipment_district_code: information.shipment_sub_district.code,
            shipment_district: information.shipment_sub_district.name,
            shipment_zip_code: information.shipment_zip_code,

            // NPWP (Tax ID) information
            npwp_province_code: information.npwp_province.code,
            npwp_province: information.npwp_province.name,
            npwp_city_code: information.npwp_city.code,
            npwp_city: information.npwp_city.name,
            npwp_district_code: information.npwp_sub_district.code,
            npwp_district: information.npwp_sub_district.name,

            // Contact and company information
            phone_number: `0${information.phone_number}`,
            email: this.authentication_emailRegister,
            instance_name: information.company,

            // Tax information
            ktp: information.ktp,
            npwp: information.npwp_number,
            npwp_name: information.npwp_name,
            tax_type: information.tax_type,
            npwp_address: information.npwp_address,
            npwp_zip_code: information.npwp_zip_code,
            tax_invoice: information.invoice_number,

            npwp_file: responseUploadNpwp,
            ktp_file: responseUploadKtp,
          };
          const { error } = await useApiFetch(AUTHENTICATION_ENDPOINT_REGISTER, {
            method: 'POST',
            body: payload,
            onResponse: async ({ response }) => {
              if (response) {
                _result = true;
              }
            },
            onRequestError: async () => {
              _result = false;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }
        }
        return Promise.resolve(_result);
      } catch {
        this.authentication_isLoading = false;
        return Promise.reject(new Error('Failed To Register'));
      } finally {
        this.authentication_isLoading = false;
      }
    },

    // function to post data in mobile size
    // return: response api mobile
    async authenticationRegister_registerMobileData(
      information: IAuthenticationRegisterMobileFormData,
      attachment: IAuthenticationRegisterDetailAttachment,
    ): Promise<unknown> {
      this.authentication_isLoading = true;
      let _result = false;
      try {
        if (attachment?.file_npwp) {
          let responseUploadKtp;
          if (attachment?.file_identity) {
            responseUploadKtp = await this.authenticationRegister_uploadFile(attachment?.file_identity as File);
          }
          const responseUploadNpwp = await this.authenticationRegister_uploadFile(attachment?.file_npwp as File);
          const payload = {
            // User information
            owner_name: information.owner_name,
            password: this.authentication_passwordRegister,
            password_confirmation: this.authentication_passwordRegister,

            // Shipping information
            shipment_address: information.shipment_address,
            shipment_province_code: information.shipment_province_code,
            shipment_province: information.shipment_province,
            shipment_city_code: information.shipment_city_code,
            shipment_city: information.shipment_city,
            shipment_district_code: information.shipment_district_code,
            shipment_district: information.shipment_district,
            shipment_zip_code: information.shipment_zip_code,

            // NPWP (Tax ID) information
            npwp_province_code: information.npwp_province_code,
            npwp_province: information.npwp_province,
            npwp_city_code: information.npwp_city_code,
            npwp_city: information.npwp_city,
            npwp_district_code: information.npwp_district_code,
            npwp_district: information.npwp_district,

            // Contact and company information
            phone_number: `0${information.phone_number}`,
            email: this.authentication_emailRegister,
            instance_name: information.instance_name,

            // Tax information
            npwp: information.npwp,
            npwp_name: information.npwp_name,
            tax_type: information.tax_type,
            npwp_address: information.npwp_address,
            npwp_zip_code: information.npwp_zip_code,
            tax_invoice: information.tax_invoice,

            npwp_file: responseUploadNpwp,
            ktp_file: responseUploadKtp,
          };
          const { error } = await useApiFetch(AUTHENTICATION_ENDPOINT_REGISTER, {
            method: 'POST',
            body: payload,

            onResponse: async ({ response }) => {
              if (response) {
                _result = true;
              }
            },
            onRequestError: async () => {
              _result = false;
            },
          });

          if (error.value) {
            throw new Error(error.value?.message);
          }
        }
        return Promise.resolve(_result);
      } catch {
        this.authentication_isLoading = false;
        return Promise.reject(new Error('Failed To Register'));
      } finally {
        this.authentication_isLoading = false;
      }
    },

    // function to create sigmature for file aws
    // return: none
    generateCareSignature(filename: string, timestamp: string) {
      const config = useRuntimeConfig();
      const baseUrl = config.app.baseURL;
      const value = timestamp + baseUrl + filename;

      return CryptoJS.SHA256(value).toString(CryptoJS.enc.Hex);
    },

    async authenticationRegister_postAws(payload: IAuthentucationRegisterDetailPostFileAws) {
      const { file, file_type, url } = payload;
      if (!file) {
        throw new Error(`${file_type} is null`);
      }
      try {
        const res = await fetch(url, {
          method: 'PUT',
          headers: {
            'Content-Type': file.type,
          },
          body: file,
        });

        if (!res.ok) {
          throw new Error(`Failed to upload ${file_type}`);
        }

        return Promise.resolve(true);
      } catch {
        return Promise.reject(new Error(`Failed to upload ${file_type}`));
      }
    },
    // function to upload data
    // return: uppload data file profress etc
    async uploadData(payload: File, fileType: 'identity' | 'npwp'): Promise<unknown> {
      if (fileType === 'identity') {
        this.authentication_isUploadKtpError = false;
        this.authentication_uploadKtpProgress = 0;
        this.authentication_uploadedKtpSize = '0.00';
        this.authentication_totalKtpSize = '0.00';
      } else {
        this.authentication_isUploadNpwpError = false;
        this.authentication_uploadNpwpProgress = 0;
        this.authentication_uploadedNpwpSize = '0.00';
        this.authentication_totalNpwpSize = '0.00';
      }

      try {
        const file = payload;

        if (!file) {
          console.error(`No ${fileType} file selected`);
          if (fileType === 'identity') {
            this.authentication_isUploadKtpError = true;
          } else {
            this.authentication_isUploadNpwpError = true;
          }
          return Promise.reject(`No ${fileType} file selected`);
        }

        if (fileType === 'identity') {
          this.authentication_uploadKtpFileName = file.name;
        } else {
          this.authentication_uploadNpwpFileName = file.name;
        }

        const totalSize = (file.size / 1024 / 1024).toFixed(2);
        if (fileType === 'identity') {
          this.authentication_totalKtpSize = totalSize;
        } else {
          this.authentication_totalNpwpSize = totalSize;
        }

        if (fileType === 'identity') {
          this.authentication_isUploadingKtp = true;
        } else {
          this.authentication_isUploadingNpwp = true;
        }

        const animationDuration = 500;
        const startTime = Date.now();
        const updateProgress = () => {
          const elapsedTime = Date.now() - startTime;
          const progress = Math.min(100, Math.round((elapsedTime / animationDuration) * 100));

          if (fileType === 'identity') {
            this.authentication_uploadKtpProgress = progress;
            this.authentication_uploadedKtpSize = ((progress / 100) * parseFloat(totalSize)).toFixed(2);
          } else {
            this.authentication_uploadNpwpProgress = progress;
            this.authentication_uploadedNpwpSize = ((progress / 100) * parseFloat(totalSize)).toFixed(2);
          }

          if (progress < 100) {
            requestAnimationFrame(updateProgress);
          } else {
            setTimeout(() => {
              if (fileType === 'identity') {
                this.authentication_isUploadingKtp = false;
              } else {
                this.authentication_isUploadingNpwp = false;
              }
            }, 100);
          }
        };

        updateProgress();

        const formData = new FormData();
        formData.append('file', file);
        formData.append('fileType', fileType);

        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              success: true,
              fileType: fileType,
              fileName: file.name,
            });
          }, animationDuration);
        });
      } catch (err) {
        console.error(`Error occurred during ${fileType} upload`, err);

        if (fileType === 'identity') {
          this.authentication_uploadKtpProgress = 0;
          this.authentication_isUploadKtpError = true;
          this.authentication_isUploadingKtp = false;
        } else {
          this.authentication_uploadNpwpProgress = 0;
          this.authentication_isUploadNpwpError = true;
          this.authentication_isUploadingNpwp = false;
        }

        return Promise.reject(err);
      }
    },
    // function to hit change password
    // return: response api hit passowd
    async fetchAuthentication_changePassword(body: IAuthenticationCreatePasswordPayload): Promise<unknown> {
      try {
        this.authentication_changePasswordIsLoading = true;
        this.authentication_changePasswordIsSuccess = false;
        const { data, error } = await useApiFetch(AUTHENTICATION_ENDPOINT_CHANGE_PASSWORD, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            const email = response._data.data;
            if (email) {
              this.authentication_changePasswordIsSuccess = true;
            }
          },
        });

        if (error.value?.message) {
          this.authentication_changePasswordIsSuccess = false;
          return Promise.reject(error.value?.message as unknown as string);
        }

        return Promise.resolve(data);
      } catch {
        this.authentication_changePasswordIsSuccess = false;
        return Promise.reject(new Error('Failed to fetch verify token reset password'));
      } finally {
        this.authentication_changePasswordIsLoading = false;
      }
    },
    // function to validate NPWP KTP
    // return: resposne API
    async fetchAuthentication_validateNPWP(payload: string): Promise<unknown> {
      try {
        // this.profile_checkPasswordIsLoading = true;
        // this.profile_isMatchOldPassword = null;
        const { data, error } = await useApiFetch(AUTHENTICATION_VALIDATE_NPWPKTP, {
          method: 'POST',
          body: {
            type: 'npwp',
            number: payload,
          },
          onResponse: async ({ response }) => {
            console.log(response._data.error == false, 'check is exist');
            if (response._data.error == false) {
              this.authentication_isExistNPWP = false;
            } else {
              this.authentication_isExistNPWP = true;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Promise.resolve(data.value?.data?.isMatch);

        // return Promise.resolve(data);
      } catch (error) {
        // this.profile_isMatchOldPassword = false;
        return Promise.reject(new Error(error as string));
      } finally {
        // this.profile_checkPasswordIsLoading = false;
      }
    },
    // function to validate KTP
    // return: resposne API
    async fetchAuthentication_validateKTP(payload: string): Promise<unknown> {
      try {
        // this.profile_checkPasswordIsLoading = true;
        // this.profile_isMatchOldPassword = null;
        const { data, error } = await useApiFetch(AUTHENTICATION_VALIDATE_NPWPKTP, {
          method: 'POST',
          body: {
            type: 'ktp',
            number: payload,
          },
          onResponse: async ({ response }) => {
            console.log(response._data.error == false, 'check is exist');
            if (response._data.error == false) {
              this.authentication_isExistKTP = false;
            } else {
              this.authentication_isExistKTP = true;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Promise.resolve(data.value?.data?.isMatch);

        // return Promise.resolve(data);
      } catch (error) {
        // this.profile_isMatchOldPassword = false;
        return Promise.reject(new Error(error as string));
      } finally {
        // this.profile_checkPasswordIsLoading = false;
      }
    },

    // Check activation
    async fetchAuthentication_checkActivation(token: string, email: string): Promise<unknown> {
      try {
        this.authentication_checkActivationLoading = true;
        const { data, error } = await useApiFetch(`${AUTHENTICATION_CHECK_ACTIVATION}/${token}`, {
          params: {
            email: email,
          },
          method: 'GET',
        });

        if (
          data?.value &&
          typeof data.value === 'object' &&
          'redirect_to' in data.value &&
          (data.value as { redirect_to?: string }).redirect_to
        ) {
          await navigateTo('/authentication/login');
        }

        if (error.value) {
          throw new Error((error?.value?.data as { message?: string })?.message ?? 'Activation check failed');
        }

        return Promise.resolve(data.value);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        return Promise.reject(false);
      } finally {
        this.authentication_checkActivationLoading = false;
      }
    },

    // Resend activation
    async fetchAuthentication_resendActivation(email: string): Promise<unknown> {
      this.authentication_resendIsLoading = true;
      try {
        const { data, error } = await useApiFetch(AUTHENTICATION_RESEND_ACTIVATE, {
          method: 'POST',
          body: {
            email,
          },
          onResponse: async ({ response }) => {
            return response._data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data.value);
      } catch (error) {
        return Promise.reject(error as string);
      } finally {
        this.authentication_resendIsLoading = false;
      }
    },
  },

  persist: {
    storage: localStorage,
    pick: [
      'authentication_accessToken',
      'authentication_userData',
      'authentication_redirectionState',
      'authentication_listNpwpProvince',
      'authentication_listNpwpCities',
      'authentication_listNpwpSubdistrict',
      'authentication_listZipcode',
    ],
  },
});
