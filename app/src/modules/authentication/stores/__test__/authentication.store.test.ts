import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, create<PERSON><PERSON> } from 'pinia';
import { useAuthenticationStore } from '../authentication.store';

describe('Authentication store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useAuthenticationStore();
      expect(store.authentication_accessToken).toBe('');
      expect(store.authentication_isLoading).toBe(false);
      expect(store.authentication_userData).toBeNull();
      expect(store.authentication_profileData).toBeNull();
      expect(store.authentication_emailRegister).toBe('');
      expect(store.authentication_passwordRegister).toBe('');
      expect(store.authentication_checkNPWPisLoading).toBe(true);
      expect(store.authentication_isExistNPWP).toBe(true);
      expect(store.authentication_checkKTPIsLoading).toBe(true);
      expect(store.authentication_isExistKTP).toBe(true);
      expect(store.authentication_isUploadingKtp).toBe(false);
      expect(store.authentication_isUploadKtpError).toBe(false);
      expect(store.authentication_uploadKtpProgress).toBe(0);
      expect(store.authentication_uploadedKtpSize).toBe('0.00');
      expect(store.authentication_totalKtpSize).toBe('0.00');
      expect(store.authentication_uploadKtpFileName).toBeNull();
      expect(store.authentication_isUploadingNpwp).toBe(false);
      expect(store.authentication_isUploadNpwpError).toBe(false);
      expect(store.authentication_uploadNpwpProgress).toBe(0);
      expect(store.authentication_uploadedNpwpSize).toBe('0.00');
      expect(store.authentication_totalNpwpSize).toBe('0.00');
      expect(store.authentication_uploadNpwpFileName).toBeNull();
      expect(store.authentication_listProvince).toMatchObject([]);
      expect(store.authentication_listCities).toMatchObject([]);
      expect(store.authentication_listSubdistrict).toMatchObject([]);
      expect(store.authentication_listNpwpProvince).toMatchObject([]);
      expect(store.authentication_listNpwpCities).toMatchObject([]);
      expect(store.authentication_listNpwpSubdistrict).toMatchObject([]);
      expect(store.authentication_listZipcode).toMatchObject([]);
      expect(store.authentication_taxType).toMatchObject([]);
      expect(store.authentication_termAndCondition).toMatchObject({});
      expect(store.authentication_verifyResetPasswordIsLoading).toBe(false);
      expect(store.authentication_verifyResetPasswordEmail).toBeNull();
      expect(store.authentication_changePasswordIsLoading).toBe(false);
      expect(store.authentication_changePasswordIsSuccess).toBe(false);
      expect(store.authentication_redirectionState).toMatchObject({
        path: null,
        payload: null,
      });
      expect(store.authentication_isOpenModalLogin).toBe(false);
    });

    it('Has correct initial computed state', () => {
      const store = useAuthenticationStore();
      expect(store.authentication_isLoading).toBe(false);
    });
  });

  it('Tax type list data should be empty', () => {
    const store = useAuthenticationStore();
    expect(store.authentication_taxType).toMatchObject([]);
  });

  it('Term and condition data should be null', () => {
    const store = useAuthenticationStore();
    expect(store.authentication_termAndCondition).toMatchObject({});
  });

  it('Modal login state should be false', () => {
    const store = useAuthenticationStore();
    expect(store.authentication_isOpenModalLogin).toBe(false);
  });
});
