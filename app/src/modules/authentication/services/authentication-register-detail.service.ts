// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// import shortUUID from 'short-uuid'
// import CryptoJS from 'crypto-js'
// Interfaces
import type {
  IAuthenticationRegisterDetailFormData,
  IAuthenticationRegisterDetailCitiesPayload,
  IAuthenticationRegisterDetailSubdistrictPayload,
  IAuthenticationRegisterDetailAttachment,
} from '../interfaces/authentication-register-detail.interface';

// Primevue
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { helpers, required, maxLength, decimal } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationRegisterDetailService = () => {
  /**
   * @description Injected variables
   */
  const store = useAuthenticationStore(); // Instance of the store
  // const config = useRuntimeConfig();
  const { $bus } = useNuxtApp();
  const toast = useToast(); // Instance of the toast
  const {
    authentication_isLoading,
    // ktp
    authentication_isUploadingKtp,
    authentication_isUploadKtpError,
    authentication_uploadKtpProgress,
    authentication_uploadedKtpSize,
    authentication_totalKtpSize,
    authentication_uploadKtpFileName,
    // npwp
    authentication_isUploadingNpwp,
    authentication_isUploadNpwpError,
    authentication_uploadNpwpProgress,
    authentication_uploadedNpwpSize,
    authentication_totalNpwpSize,
    authentication_uploadNpwpFileName,
    // other
    authentication_listProvince,
    authentication_listCities,
    authentication_listSubdistrict,
    authentication_listNpwpProvince,
    authentication_listNpwpCities,
    authentication_listNpwpSubdistrict,
    authentication_listZipcode,
    authentication_taxType,
    authentication_termAndCondition,
    authentication_emailRegister,
    authentication_passwordRegister,
    authentication_isExistNPWP,
    authentication_isExistKTP,
  } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here
  const authenticationRegisterDetail_isShowModalTnc = ref<boolean>(false);

  const authenticationRegisterDetail_tncBox_1 = ref(false);
  const authenticationRegisterDetail_tncBox_2 = ref(false);

  /**
   * @description Reactive data binding
   */
  const authenticationRegisterDetail_formData = reactive<IAuthenticationRegisterDetailFormData>({
    full_name: '',
    email: authentication_emailRegister.value,
    phone_number: '',
    company: '',
    shipping_address: '',
    ktp: '',
    npwp_number: '',
    npwp_raw: '',
    tax_type: '',
    invoice_number: '',
    npwp_name: '',
    npwp_address: '',
    npwp_province: {
      code: '',
      name: '',
      island: '',
    },
    npwp_city: {
      code: '',
      id: '',
      name: '',
      city_name: '',
    },
    npwp_sub_district: {
      code: '',
      name: '',
    },
    npwp_zip_code: '',
    shipment_province: {
      code: '',
      name: '',
      island: '',
    },
    shipment_city: {
      id: '',
      name: '',
      code: '',
      city_name: '',
    },
    shipment_sub_district: {
      name: '',
      code: '',
    },
    shipment_zip_code: '',
  });

  const authenticationRegisterDetail_isNeedInvoiceBill = ref(true);

  const authenticationRegisterDetail_attachment = reactive<IAuthenticationRegisterDetailAttachment>({
    file_identity: null,
    file_npwp: null,
  });

  const noAngleBrackets = helpers.withMessage(
    'input tidak boleh mengandung karakter < atau >',
    (value: string) => {
      if (!value) return true;
      return !/[<>]/.test(value);
    },
  );

  /**
   * @description Form validations
   */
  const authenticationRegisterDetail_formRules: ComputedRef = computed(() => ({
    full_name: {
      required,
      maxLength: maxLength(255),
      invalid_full_name: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[a-zA-Z\s]+$/.test(value);
      }),
    },
    email: {
      required,
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
    },
    phone_number: {
      required,
      decimal,
      maxLength: maxLength(16),
      invalid_phone_number: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[1-9][0-9]*$/.test(value);
      }),
    },
    company: {
      required,
      noAngleBrackets,
      maxLength: maxLength(255),
    },
    shipping_address: {
      required,
      noAngleBrackets,
      maxLength: maxLength(1000),
    },
    ktp: {
      required,
      invalid_ktp_digits: helpers.withParams(
        { customMessage: 'KTP harus terdiri dari 16 digit' },
        (value: string) => {
          const onlyDigits = value?.replace(/\D/g, '') || '';
          return onlyDigits.length >= 15 && onlyDigits.length <= 16;
        },
      ),
      // valid_format: helpers.withParams(
      //   { customMessage: 'Format NPWP tidak sesuai (contoh: 12.345.678.9-012.345)' },
      //   (value: string) => {
      //     if (!value) return true
      //     return /^\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3}$/.test(value)
      //   }
      // )
    },
    npwp_number: {
      required,
      invalid_npwp_digits: helpers.withParams(
        { customMessage: 'NPWP harus terdiri dari 15 atau 16 digit' },
        (value: string) => {
          const onlyDigits = value?.replace(/\D/g, '') || '';
          return onlyDigits.length >= 15 && onlyDigits.length <= 16;
        },
      ),
      // valid_format: helpers.withParams(
      //   { customMessage: 'Format NPWP tidak sesuai (contoh: 12.345.678.9-012.345)' },
      //   (value: string) => {
      //     if (!value) return true
      //     return /^\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3}$/.test(value)
      //   }
      // )
    },
    tax_type: { required },
    invoice_number: {
      required: helpers.withMessage('Invoice number is required', value => {
        if (authenticationRegisterDetail_isNeedInvoiceBill.value) return true;
        return required.$validator(value, {}, null);
      }),
      maxLength: maxLength(255),
    },
    npwp_name: {
      required,
      maxLength: maxLength(255),
      invalid_full_name: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[a-zA-Z\s]+$/.test(value);
      }),
    },
    npwp_address: {
      required,
      noAngleBrackets,
      maxLength: maxLength(1000),
    },
    npwp_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    npwp_city: {
      id: { required },
      code: { required },
      name: { required },
    },
    npwp_sub_district: {
      code: { required },
      name: { required },
    },
    npwp_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams(
        { customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return /^[1-9][0-9]*$/.test(value);
        },
      ),
    },
    shipment_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    shipment_city: {
      id: { required },
      code: { required },
      name: { required },
    },
    shipment_sub_district: {
      code: { required },
      name: { required },
    },
    shipment_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams(
        { customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return /^[1-9][0-9]*$/.test(value);
        },
      ),
    },
  }));

  const authenticationRegisterDetail_formValidations = useVuelidate(
    authenticationRegisterDetail_formRules,
    authenticationRegisterDetail_formData,
    {
      $autoDirty: true,
    },
  );

  const authenticationRegisterDetail_fileRules: ComputedRef = computed(() => ({
    file_identity: {
      formatXLX: helpers.withMessage(
        'Format File tidak valid. Hanya file .pdf or .png .jpg .jpeg yang diperbolehkan',
        (value: File | null) => {
          if (!value) return true;
          const allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
          const fileExtension = value.name.split('.').pop()?.toLowerCase();
          return allowedExtensions.includes(fileExtension || '');
        },
      ),
      fileSizeMax: helpers.withMessage('Ukuran file tidak boleh lebih dari 2MB ', (value: File | null) => {
        if (!value) return true;
        const size = value.size;
        const minSizeInBytes = 1 * 1024; // 1KB
        const maxSizeInBytes = 2 * 1024 * 1024; // 2MB
        return size >= minSizeInBytes && size <= maxSizeInBytes;
      }),
    },
    file_npwp: {
      required: helpers.withMessage('File is required', required), // File is required
      formatXLX: helpers.withMessage(
        'Format File tidak valid. Hanya file .pdf or .png .jpg .jpeg yang diperbolehkan',
        (value: File | null) => {
          if (!value) return true;
          const allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
          const fileExtension = value.name.split('.').pop()?.toLowerCase();
          return allowedExtensions.includes(fileExtension || '');
        },
      ),
      fileSizeMax: helpers.withMessage('Ukuran file tidak boleh lebih dari 2MB', (value: File | null) => {
        if (!value) return true;
        const size = value.size;
        const minSizeInBytes = 1 * 1024; // 1KB
        const maxSizeInBytes = 2 * 1024 * 1024; // 2MB
        return size >= minSizeInBytes && size <= maxSizeInBytes;
      }),
    },
  }));

  const authenticationRegisterDetail_fileValidations = useVuelidate(
    authenticationRegisterDetail_fileRules,
    authenticationRegisterDetail_attachment,
    {
      $autoDirty: true,
    },
  );

  // watch
  watch(
    () => authenticationRegisterDetail_formData.npwp_province,
    async newVal => {
      await store.fetchAuthentication_getCities(newVal as IAuthenticationRegisterDetailCitiesPayload, 'npwp');
    },
  );

  watch(
    () => authenticationRegisterDetail_formData.npwp_city,
    async newVal => {
      await store.fetchAuthentication_getSubdisctrict(
        newVal as IAuthenticationRegisterDetailSubdistrictPayload,
        'npwp',
      );
    },
  );

  watch(
    () => authenticationRegisterDetail_formData.shipment_province,
    async newVal => {
      await store.fetchAuthentication_getCities(newVal as IAuthenticationRegisterDetailCitiesPayload, 'shipment');
    },
  );

  watch(
    () => authenticationRegisterDetail_formData.shipment_city,
    async newVal => {
      await store.fetchAuthentication_getSubdisctrict(
        newVal as IAuthenticationRegisterDetailSubdistrictPayload,
        'shipment',
      );
    },
  );

  // watch(() => authenticationRegisterDetail_formData.npwp_sub_district, async (newVal) => {
  //   await store.fetchAuthentication_getZipcode(newVal);
  // });

  watch(
    () => authenticationRegisterDetail_attachment.file_identity,
    () => {
      if (
        !authenticationRegisterDetail_fileValidations.value.file_identity.$invalid &&
        authenticationRegisterDetail_attachment.file_identity
      ) {
        store.uploadData(authenticationRegisterDetail_attachment.file_identity as File, 'identity');
      }
    },
    { deep: true },
  );

  watch(
    () => authenticationRegisterDetail_attachment.file_npwp,
    () => {
      if (
        !authenticationRegisterDetail_fileValidations.value.file_npwp.$invalid &&
        authenticationRegisterDetail_attachment.file_npwp
      ) {
        store.uploadData(authenticationRegisterDetail_attachment.file_npwp as File, 'npwp');
      }
    },
    { deep: true },
  );

  // watch(
  //   () => authenticationRegisterDetail_formData.npwp_number,
  //   val => {
  //     const raw = val?.replace(/\D/g, '').slice(0, 15) || '';

  //     const formatted = raw
  //       .replace(/^(\d{2})(\d)/, '$1.$2')
  //       .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
  //       .replace(/^(\d{2})\.(\d{3})\.(\d{3})(\d)/, '$1.$2.$3.$4')
  //       .replace(/^(\d{2})\.(\d{3})\.(\d{3})\.(\d)(\d{3})(\d{1,3})/, '$1.$2.$3.$4-$5.$6');

  //     // Simpan versi raw
  //     authenticationRegisterDetail_formData.npwp_raw = raw;

  //     // Format kalau berubah (hindari infinite loop)
  //     if (val !== formatted) {
  //       authenticationRegisterDetail_formData.npwp_number = formatted;
  //     }
  //   },
  // );

  // function to toggle checkbox
  // reutrn: none
  const authenticationRegisterDetail_toggleInoviceBill = () => {
    authenticationRegisterDetail_isNeedInvoiceBill.value = !authenticationRegisterDetail_isNeedInvoiceBill.value;
    if (authenticationRegisterDetail_isNeedInvoiceBill.value == true) {
      authenticationRegisterDetail_formData.invoice_number = '';
    }
  };

  // function to fetch province
  // return: none
  const authenticationRegisterDetail_fetchProvinces = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_getProvince();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  // function to fetch taxtype
  // return: none
  const authenticationRegisterDetail_fetchTaxType = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_getTaxType();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  // function to fetch term and conditions
  // return: none
  const authenticationRegisterDetail_fetchTermsAndCondition = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_getTermsAndCondition();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  // function to trigger function in store
  // return : taost information account
  const authenticationRegisterDetail_fetchAuthenticationRegister = async (): Promise<void> => {
    try {
      const result = await store.authenticationRegister_registerData(
        authenticationRegisterDetail_formData,
        authenticationRegisterDetail_attachment,
      );
      if (result) {
        toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Registration Success',
          life: 3000,
        });
        setTimeout(async () => {
          await navigateTo('/authentication/register-success');
          window.location.href = '/authentication/register-success';
        }, 1000);
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Registration Failed',
        life: 3000,
      });
    }
  };

  // function to validate NPWP
  const authenticationRegisterDetail_validateNPWP = async (val: string): Promise<void> => {
    await store.fetchAuthentication_validateNPWP(val);
  };

  // function to validate NPWP
  const authenticationRegisterDetail_validateKTP = async (val: string): Promise<void> => {
    await store.fetchAuthentication_validateKTP(val);
  };

  // function to handle on submit
  // return: none
  const authenticationRegisterDetail_onSubmitForm = () => {
    // eslint-disable-next-line no-negated-condition
    if (!authenticationRegisterDetail_formValidations.value.$error) {
      authenticationRegisterDetail_isShowModalTnc.value = true;
    } else {
      return;
    }
  };

  // function to thandle on submit tnc
  // return: none
  const authenticationRegisterDetail_onSubmitTnc = async (): Promise<void> => {
    authenticationRegisterDetail_isShowModalTnc.value = false;
    if (
      authenticationRegisterDetail_tncBox_1.value === true &&
      authenticationRegisterDetail_tncBox_2.value === true
    ) {
      authenticationRegisterDetail_formValidations.value.$touch();
      if (authenticationRegisterDetail_formValidations.value.$error) {
        return;
      }
      await authenticationRegisterDetail_fetchAuthenticationRegister();
    }
  };

  return {
    authenticationRegisterDetail_toggleInoviceBill,
    authenticationRegisterDetail_isNeedInvoiceBill,
    authenticationRegisterDetail_formData,
    authenticationRegisterDetail_attachment,
    authenticationRegisterDetail_isLoading: authentication_isLoading,
    authenticationRegisterDetail_formValidations,
    authenticationRegisterDetail_onSubmitForm,
    authenticationRegisterDetail_onSubmitTnc,
    authenticationRegisterDetail_fetchProvinces,
    authenticationRegisterDetail_fetchTaxType,
    authenticationRegisterDetail_fetchTermsAndCondition,
    authenticationRegisterDetail_fileValidations,
    authenticationRegisterDetail_isShowModalTnc,
    authenticationRegisterDetail_validateNPWP,
    authenticationRegisterDetail_validateKTP,
    authenticationRegisterDetail_tncBox_1,
    authenticationRegisterDetail_tncBox_2,

    // KTP
    authentication_isExistNPWP,
    authentication_isExistKTP,
    authentication_uploadKtpFileName,
    authentication_isUploadingKtp,
    authentication_isUploadKtpError,
    authentication_uploadKtpProgress,
    authentication_uploadedKtpSize,
    authentication_totalKtpSize,
    // NPWP
    authentication_isUploadingNpwp,
    authentication_isUploadNpwpError,
    authentication_uploadNpwpProgress,
    authentication_uploadedNpwpSize,
    authentication_totalNpwpSize,
    authentication_uploadNpwpFileName,
    // API
    authentication_listProvince,
    authentication_listCities,
    authentication_listSubdistrict,
    authentication_listNpwpProvince,
    authentication_listNpwpCities,
    authentication_listNpwpSubdistrict,
    authentication_listZipcode,
    authentication_taxType,
    authentication_termAndCondition,
    authentication_emailRegister,
    authentication_passwordRegister,
  };
};
