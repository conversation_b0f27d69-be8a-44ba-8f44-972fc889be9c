// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Helpers
import {
  isContainLowerCase,
  isContainNumber,
  isContainSpecialCharacter,
  isContainUpperCase,
} from '~/app/src/core/helpers/regex.helper';

// Interfaces
import type {
  // IAuthenticationRegisterResponse,
  IAuthenticationRegisterValidations,
  IAuthenticationRegisterMobileProvided,
  IAuthenticationRegisterMobileAttachment,
  IAuthenticationRegisterMobileCitiesPayload,
  IAuthenticationRegisterMobileSubdistrictPayload,
  IAuthenticationRegisterMobileData_Section1,
  IAuthenticationRegisterMobileData_Section2,
  IAuthenticationRegisterMobileData_Section3,
  IAuthenticationRegisterMobileData_Section4,
} from '../interfaces/authentication-register-mobile.interface';

// Primevue
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { helpers, minLength, required, sameAs, decimal, maxLength } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationRegisterMobileService = (): IAuthenticationRegisterMobileProvided => {
  /**
   * @description Injected variables
   */
  // Please put your injected variables here
  const store = useAuthenticationStore(); // Instance of the store
  const { $bus } = useNuxtApp();
  const {
    // KTP
    authentication_isUploadingKtp,
    authentication_isUploadKtpError,
    authentication_uploadKtpProgress,
    authentication_uploadedKtpSize,
    authentication_totalKtpSize,
    authentication_uploadKtpFileName,
    // NPWP
    authentication_isUploadingNpwp,
    authentication_isUploadNpwpError,
    authentication_uploadNpwpProgress,
    authentication_uploadedNpwpSize,
    authentication_totalNpwpSize,
    authentication_uploadNpwpFileName,
    //
    authentication_isLoading,
    authentication_emailRegister,
    authentication_passwordRegister,
    // API
    authentication_listProvince,
    authentication_listCities,
    authentication_listSubdistrict,
    authentication_listNpwpProvince,
    authentication_listNpwpCities,
    authentication_listNpwpSubdistrict,
    authentication_listZipcode,
    authentication_taxType,
    authentication_termAndCondition,
    authentication_isExistNPWP,
    authentication_isExistKTP,
  } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */

  const toast = useToast(); // Instance of the toast
  const authenticationRegisterMobile_isNeedInvoiceBill = ref(true);

  const authenticationRegisterMobile_attachment = reactive<IAuthenticationRegisterMobileAttachment>({
    file_identity: null,
    file_npwp: null,
  });

  const authenticationRegisterMobile_tncBox = ref({
    tnc_1: false,
    tnc_2: false,
  });

  const authenticationRegisterMobile_customValidations = reactive<IAuthenticationRegisterValidations>({
    isContainLowerCase: false,
    isContainNumber: false,
    isContainSpecialCharacter: false,
    isContainUpperCase: false,
    isLengthValid: false,
  });


  const noAngleBrackets = helpers.withMessage(
    'input tidak boleh mengandung karakter < atau >',
    (value: string) => {
      if (!value) return true
      return !/[<>]/.test(value)
    }
  )

  const authenticationRegisterMobile_fileRules: ComputedRef = computed(() => ({
    file_identity: {
      // required: helpers.withMessage('File is required', required), // File is required
      formatXLX: helpers.withMessage(
        'Format File tidak valid. Hanya file .pdf or .png .jpg .jpeg yang diperbolehkan',
        (value: File | null) => {
          if (!value) return true;
          const allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
          const fileExtension = value.name.split('.').pop()?.toLowerCase();
          return allowedExtensions.includes(fileExtension || '');
        },
      ),
      fileSizeMax: helpers.withMessage('Ukuran file tidak boleh lebih dari 2MB', (value: File | null) => {
        if (!value) return true;
        const size = value.size;
        const minSizeInBytes = 1 * 1024; // 1KB
        const maxSizeInBytes = 2 * 1024 * 1024; // 2MB
        return size >= minSizeInBytes && size <= maxSizeInBytes;
      }),
    },
    file_npwp: {
      required: helpers.withMessage('File is required', required), // File is required
      formatXLX: helpers.withMessage(
        'Format File tidak valid. Hanya file .pdf or .png .jpg .jpeg yang diperbolehkan',
        (value: File | null) => {
          if (!value) return true;
          const allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
          const fileExtension = value.name.split('.').pop()?.toLowerCase();
          return allowedExtensions.includes(fileExtension || '');
        },
      ),
      fileSizeMax: helpers.withMessage('Ukuran file tidak boleh lebih dari 2MB', (value: File | null) => {
        if (!value) return true;
        const size = value.size;
        const minSizeInBytes = 1 * 1024; // 1KB
        const maxSizeInBytes = 2 * 1024 * 1024; // 2MB
        return size >= minSizeInBytes && size <= maxSizeInBytes;
      }),
    },
  }));

  const authenticationRegisterMobile_fileValidations = useVuelidate(
    authenticationRegisterMobile_fileRules,
    authenticationRegisterMobile_attachment,
    {
      $autoDirty: true,
    },
  );

  // Validation Section 1
  // ===================
  const auhtenticationRegisterMobile_formData_Section_1 = reactive<IAuthenticationRegisterMobileData_Section1>({
    email: '',
    password: '',
    password_confirmation: '',
  });

  const authenticationRegisterMobile_formRules_Section_1: ComputedRef = computed(() => ({
    email: {
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
      required,
    },
    password: {
      required,
      minLength: minLength(8),
      isContainLowerCase,
      isContainNumber,
      isContainSpecialCharacter,
      isContainUpperCase,
    },
    password_confirmation: {
      required,
      minLength: minLength(8),
      sameAsCreatedPassword: sameAs(auhtenticationRegisterMobile_formData_Section_1.password),
      isContainLowerCase,
      isContainNumber,
      isContainSpecialCharacter,
      isContainUpperCase,
    },
  }));

  const authenticationRegisterMobile_formValidations_Section_1 = useVuelidate(
    authenticationRegisterMobile_formRules_Section_1,
    auhtenticationRegisterMobile_formData_Section_1,
    {
      $autoDirty: true,
    },
  );

  // Validation Section 2
  const auhtenticationRegisterMobile_formData_Section_2 = reactive<IAuthenticationRegisterMobileData_Section2>({
    full_name: '',
    email: authentication_emailRegister.value,
    phone_number: '',
    company_name: '',
    shipment_address: '',
    shipment_province: {
      code: '',
      name: '',
      island: '',
    },
    shipment_city: {
      id: '',
      code: '',
      name: '',
      city_name: '',
    },
    shipment_sub_district: {
      code: '',
      name: '',
    },
    shipment_zip_code: '',
  });

  watch(authentication_emailRegister, newVal => {
    auhtenticationRegisterMobile_formData_Section_2.email = newVal;
  });

  const authenticationRegisterMobile_formRules_Section_2: ComputedRef = computed(() => ({
    full_name: {
      required,
      maxLength: maxLength(255),
      invalid_full_name: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[a-zA-Z\s]+$/.test(value);
      }),
    },
    email: {
      required,
      maxLength: maxLength(255),
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
    },
    phone_number: {
      required,
      decimal,
      maxLength: maxLength(16),
      invalid_phone_number: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[1-9][0-9]*$/.test(value);
      }),
    },
    company_name: { required },
    shipment_address: {
      required,
      noAngleBrackets,
      maxLength: maxLength(1000),
    },
    shipment_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    shipment_city: {
      id: { required },
      code: { required },
      name: { required },
    },
    shipment_sub_district: {
      code: { required },
      name: { required },
    },
    shipment_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
    },
  }));

  const authenticationRegisterMobile_formValidations_Section_2 = useVuelidate(
    authenticationRegisterMobile_formRules_Section_2,
    auhtenticationRegisterMobile_formData_Section_2,
    {
      $autoDirty: true,
    },
  );

  // Validation Section 3
  const auhtenticationRegisterMobile_formData_Section_3 = reactive<IAuthenticationRegisterMobileData_Section3>({
    tax_type: '',
    invoice_number: '',
    ktp: '',
    npwp_number: '',
    npwp_raw: '',
    npwp_name: '',
    npwp_address: '',
    npwp_province: {
      code: '',
      name: '',
      island: '',
    },
    npwp_city: {
      id: '',
      code: '',
      name: '',
      city_name: '',
    },
    npwp_sub_district: {
      code: '',
      name: '',
    },
    npwp_zip_code: '',
  });

  const authenticationRegisterMobile_formRules_Section_3: ComputedRef = computed(() => ({
    tax_type: { required },
    invoice_number: {
      required: helpers.withMessage(
        'Invoice number is required',
        (value) => {
          if (authenticationRegisterMobile_isNeedInvoiceBill.value) return true
          return required.$validator(value, {}, null)
        }
      ),
      decimal,
      maxLength: maxLength(255)
    },
    ktp: {
      required,
      invalid_npwp_digits: helpers.withParams(
        { customMessage: 'NPWP harus terdiri dari 15 atau 16 digit' },
        (value: string) => {
          const onlyDigits = value?.replace(/\D/g, '') || '';
          return onlyDigits.length === 16;
        },
      ),
    },
    npwp_number: {
      required,
      invalid_npwp_digits: helpers.withParams(
        { customMessage: 'NPWP harus terdiri dari 15 atau 16 digit' },
        (value: string) => {
          const onlyDigits = value?.replace(/\D/g, '') || '';
          return onlyDigits.length === 16;
        },
      ),
      // valid_format: helpers.withParams(
      //   { customMessage: 'Format NPWP tidak sesuai (contoh: 12.345.678.9-012.345)' },
      //   (value: string) => {
      //     if (!value) return true
      //     return /^\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3}$/.test(value)
      //   }
      // )
    },
    npwp_name: {
      required,
      maxLength: maxLength(255),
      invalid_full_name: helpers.withParams({ customMessage: 'invalid characters' }, function (value: string) {
        return /^[a-zA-Z\s]+$/.test(value);
      }),
    },
    npwp_address: {
      required,
      noAngleBrackets,
      maxLength: maxLength(1000),
    },
    npwp_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    npwp_city: {
      id: { required },
      code: { required },
      name: { required },
    },
    npwp_sub_district: {
      code: { required },
      name: { required },
    },
    npwp_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams(
        { customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return /^[1-9][0-9]*$/.test(value);
        },
      ),
    },
  }));

  const authenticationRegisterMobile_formValidations_Section_3 = useVuelidate(
    authenticationRegisterMobile_formRules_Section_3,
    auhtenticationRegisterMobile_formData_Section_3,
    {
      $autoDirty: true,
    },
  );

  // Validation Section 4
  const auhtenticationRegisterMobile_formData_Section_4 = reactive<IAuthenticationRegisterMobileData_Section4>({
    npwp_province: {
      code: '',
      name: '',
      island: '',
    },
    npwp_city: {
      id: '',
      code: '',
      name: '',
      city_name: '',
    },
    npwp_sub_district: {
      code: '',
      name: '',
    },
    npwp_zip_code: '',
  });

  const authenticationRegisterMobile_formRules_Section_4: ComputedRef = computed(() => ({
    npwp_province: {
      code: { required },
      name: { required },
      island: { required },
    },
    npwp_city: {
      id: { required },
      code: { required },
      name: { required },
      city_name: { required },
    },
    npwp_sub_district: {
      code: { required },
      name: { required },
    },
    npwp_zip_code: {
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams(
        { customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return /^[1-9][0-9]*$/.test(value);
        },
      ),
    },
  }));

  const authenticationRegisterMobile_formValidations_Section_4 = useVuelidate(
    authenticationRegisterMobile_formRules_Section_4,
    auhtenticationRegisterMobile_formData_Section_4,
    {
      $autoDirty: true,
      $scope: false,
    },
  );

  // function to validate NPWP
  const authenticationRegisterMobile_validateNPWP = async (val: string): Promise<void> => {
    await store.fetchAuthentication_validateNPWP(val);
  };

  const authenticationRegisterMobile_validateKTP = async (val: string): Promise<void> => {
    await store.fetchAuthentication_validateKTP(val);
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    () => auhtenticationRegisterMobile_formData_Section_1.password,
    value => {
      if (value) {
        authenticationRegisterMobile_customValidations.isContainLowerCase = isContainLowerCase(value);
        authenticationRegisterMobile_customValidations.isContainNumber = isContainNumber(value);
        authenticationRegisterMobile_customValidations.isContainSpecialCharacter =
          isContainSpecialCharacter(value);
        authenticationRegisterMobile_customValidations.isContainUpperCase = isContainUpperCase(value);
        authenticationRegisterMobile_customValidations.isLengthValid = value.length >= 8;
      }
    },
  );

  watch(
    () => authenticationRegisterMobile_attachment.file_identity,
    () => {
      if (
        !authenticationRegisterMobile_fileValidations.value.file_identity.$invalid &&
        authenticationRegisterMobile_attachment.file_identity
      ) {
        store.uploadData(authenticationRegisterMobile_attachment.file_identity, 'identity');
      }
    },
    { deep: true },
  );

  watch(
    () => authenticationRegisterMobile_attachment.file_npwp,
    () => {
      if (
        !authenticationRegisterMobile_fileValidations.value.file_npwp.$invalid &&
        authenticationRegisterMobile_attachment.file_npwp
      ) {
        store.uploadData(authenticationRegisterMobile_attachment.file_npwp, 'npwp');
      }
    },
    { deep: true },
  );

  const authenticationRegisterMobile_isCustomValidationsValid: ComputedRef<boolean> = computed(() => {
    return Object.values(authenticationRegisterMobile_customValidations).every(value => value);
  });

  watch(
    () => auhtenticationRegisterMobile_formData_Section_3.npwp_province,
    async newVal => {
      await store.fetchAuthentication_getCities(newVal as IAuthenticationRegisterMobileCitiesPayload, 'npwp');
    },
  );

  watch(
    () => auhtenticationRegisterMobile_formData_Section_3.npwp_city,
    async newVal => {
      await store.fetchAuthentication_getSubdisctrict(
        newVal as IAuthenticationRegisterMobileSubdistrictPayload,
        'npwp',
      );
    },
  );

  watch(
    () => auhtenticationRegisterMobile_formData_Section_2.shipment_province,
    async newVal => {
      await store.fetchAuthentication_getCities(newVal as IAuthenticationRegisterMobileCitiesPayload, 'shipment');
    },
  );

  watch(
    () => auhtenticationRegisterMobile_formData_Section_2.shipment_city,
    async newVal => {
      await store.fetchAuthentication_getSubdisctrict(
        newVal as IAuthenticationRegisterMobileSubdistrictPayload,
        'shipment',
      );
    },
  );

  // watch(
  //   () => auhtenticationRegisterMobile_formData_Section_3.npwp_number,
  //   val => {
  //     const raw = val?.replace(/\D/g, '').slice(0, 15) || '';

  //     const formatted = raw
  //       .replace(/^(\d{2})(\d)/, '$1.$2')
  //       .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
  //       .replace(/^(\d{2})\.(\d{3})\.(\d{3})(\d)/, '$1.$2.$3.$4')
  //       .replace(/^(\d{2})\.(\d{3})\.(\d{3})\.(\d)(\d{3})(\d{1,3})/, '$1.$2.$3.$4-$5.$6');

  //     auhtenticationRegisterMobile_formData_Section_3.npwp_raw = raw;

  //     if (val !== formatted) {
  //       auhtenticationRegisterMobile_formData_Section_3.npwp_number = formatted;
  //     }
  //   },
  // );

  // function to toggle checkbox
  // reutrn: none
  const authenticationRegisterMobile_toggleInoviceBill = () => {
    authenticationRegisterMobile_isNeedInvoiceBill.value = !authenticationRegisterMobile_isNeedInvoiceBill.value;
    if (authenticationRegisterMobile_isNeedInvoiceBill.value == true) {
      auhtenticationRegisterMobile_formData_Section_3.invoice_number = '';
    }
  };

  // function to fetch pronvice
  // return:none
  const authenticationRegisterMobile_fetchProvinces = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_getProvince();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  // function to fetch tax type
  // return: none
  const authenticationRegisterMobile_fetchTaxType = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_getTaxType();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  // function to fetch term and condition
  // return: none
  const authenticationRegisterMobile_fetchTermsAndCondition = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_getTermsAndCondition();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };
  // function to check customer
  // return: none
  const auhtenticationRegisterMobile_fetchCheckCustomer = async (): Promise<unknown> => {
    try {
      const response = await store.fetchAuthentication_checkCustomer(
        auhtenticationRegisterMobile_formData_Section_1,
      );
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: 'Akun berhasil diproses',
        type: EAlertType.SUCCESS,
      } as IPropsBaseAlert);
      authentication_isLoading.value = false;
      return response;
    } catch (error) {
      authentication_isLoading.value = false;
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
      throw error;
    }
  };

  // function to handle click section 1
  // return: none
  const authenticationRegisterMobile_onSubmitSection1 = async (): Promise<unknown> => {
    authenticationRegisterMobile_formValidations_Section_1.value.$touch();

    if (authenticationRegisterMobile_formValidations_Section_1.value.$error) {
      return;
    }

    try {
      const response = await auhtenticationRegisterMobile_fetchCheckCustomer();
      return response;
    } catch (error) {
      console.error('Check customer error:', error);
      throw error;
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const authenticationRegisterMobile_onSubmit = async () => {
    if (
      authenticationRegisterMobile_tncBox.value.tnc_1 == true &&
      authenticationRegisterMobile_tncBox.value.tnc_2 == true
    ) {
      authenticationRegisterMobile_formValidations_Section_1.value.$touch();
      authenticationRegisterMobile_formValidations_Section_2.value.$touch();
      authenticationRegisterMobile_formValidations_Section_3.value.$touch();
      // authenticationRegisterMobile_formValidations_Section_4.value.$touch();
      if (
        authenticationRegisterMobile_formValidations_Section_1.value.$error &&
        authenticationRegisterMobile_formValidations_Section_2.value.$error &&
        authenticationRegisterMobile_formValidations_Section_3.value.$error 
        // &&
        // authenticationRegisterMobile_formValidations_Section_4.value.$error
      ) {
        return;
      }
      const data = {
        file_npwp: `${authenticationRegisterMobile_attachment.file_npwp?.name}`,
        file_identity: `${authenticationRegisterMobile_attachment.file_identity?.name}`,
        email: authentication_emailRegister.value,
        password: authentication_passwordRegister.value,
        password_confirmation: authentication_passwordRegister.value,
        
        owner_name: auhtenticationRegisterMobile_formData_Section_2.full_name,
        phone_number: auhtenticationRegisterMobile_formData_Section_2.phone_number,
        instance_name: auhtenticationRegisterMobile_formData_Section_2.company_name,
        shipment_address: auhtenticationRegisterMobile_formData_Section_2.shipment_address,
        shipment_province_code: auhtenticationRegisterMobile_formData_Section_2.shipment_province.code,
        shipment_province: auhtenticationRegisterMobile_formData_Section_2.shipment_province.name,
        shipment_city_code: auhtenticationRegisterMobile_formData_Section_2.shipment_city.code,
        shipment_city: auhtenticationRegisterMobile_formData_Section_2.shipment_city.city_name,
        shipment_district_code: auhtenticationRegisterMobile_formData_Section_2.shipment_sub_district.code,
        shipment_district: auhtenticationRegisterMobile_formData_Section_2.shipment_sub_district.name,
        shipment_zip_code: auhtenticationRegisterMobile_formData_Section_2.shipment_zip_code,

        npwp_province_code: auhtenticationRegisterMobile_formData_Section_3.npwp_province.code,
        npwp_province: auhtenticationRegisterMobile_formData_Section_3.npwp_province.name,
        npwp_city_code: auhtenticationRegisterMobile_formData_Section_3.npwp_city.code,
        npwp_city: auhtenticationRegisterMobile_formData_Section_3.npwp_city.city_name,
        npwp_district_code: auhtenticationRegisterMobile_formData_Section_3.npwp_sub_district.code,
        npwp_district: auhtenticationRegisterMobile_formData_Section_3.npwp_sub_district.name,
        npwp_zip_code: auhtenticationRegisterMobile_formData_Section_3.npwp_zip_code,
        npwp: auhtenticationRegisterMobile_formData_Section_3.npwp_number,
        npwp_name: auhtenticationRegisterMobile_formData_Section_3.npwp_name,
        tax_type: auhtenticationRegisterMobile_formData_Section_3.tax_type,
        npwp_address: auhtenticationRegisterMobile_formData_Section_3.npwp_address,
        tax_invoice: auhtenticationRegisterMobile_formData_Section_3.invoice_number,
      };
      try {
        const result = await store.authenticationRegister_registerMobileData(
          data,
          authenticationRegisterMobile_attachment,
        );
        if (result) {
          $bus.emit('BaseAlert', {
            durationOfAutoClose: 3000,
            isHaveIconClose: true,
            isOpen: true,
            text: 'Pendaftaran Berhasil',
            type: EAlertType.SUCCESS,
          } as IPropsBaseAlert);
          setTimeout(async () => {
            await navigateTo('/authentication/register-success');
            window.location.href = '/authentication/register-success';
          }, 1000);
        }
      } catch {
        toast.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Registration Failed',
          life: 3000,
        });
      }
    }
  };

  /**
   * @description Handle business logic to set dynamic class label validation
   */
  const authenticationRegisterMobile_setDynamicClassLabelValidation = (
    key: keyof typeof authenticationRegisterMobile_customValidations,
  ): string => {
    if (auhtenticationRegisterMobile_formData_Section_1.password) {
      return authenticationRegisterMobile_customValidations[key] ? 'text-success' : 'text-danger';
    }

    return 'text-black';
  };

  /**
   * @description Handle business logic to set dynamic path icon
   */
  const authenticationRegisterMobile_setDynamicPathIcon = (
    key: keyof typeof authenticationRegisterMobile_customValidations,
  ): string => {
    if (authenticationRegisterMobile_customValidations[key]) {
      return '/icons/check-circle.svg';
    } else {
      return '/icons/x-circle-red.svg';
    }
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    // FILE
    authenticationRegisterMobile_fileValidations,
    authenticationRegisterMobile_fileRules,
    // SECTION 1
    authenticationRegisterMobile_formValidations_Section_1,
    auhtenticationRegisterMobile_formData_Section_1,
    // SECTION 2
    authenticationRegisterMobile_formValidations_Section_2,
    auhtenticationRegisterMobile_formData_Section_2,
    // SECTION 3
    authenticationRegisterMobile_formValidations_Section_3,
    auhtenticationRegisterMobile_formData_Section_3,
    // SECTION 3
    authenticationRegisterMobile_formValidations_Section_4,
    auhtenticationRegisterMobile_formData_Section_4,

    // authenticationRegisterMobile_formDat
    authenticationRegisterMobile_attachment,
    authenticationRegisterMobile_customValidations,
    authenticationRegisterMobile_isCustomValidationsValid,
    authenticationRegisterMobile_isLoading: authentication_isLoading,
    authenticationRegisterMobile_onSubmit,
    authenticationRegisterMobile_setDynamicClassLabelValidation,
    authenticationRegisterMobile_setDynamicPathIcon,
    authenticationRegisterMobile_fetchProvinces,
    authenticationRegisterMobile_fetchTaxType,
    authenticationRegisterMobile_fetchTermsAndCondition,
    auhtenticationRegisterMobile_fetchCheckCustomer,
    authenticationRegisterMobile_onSubmitSection1,
    authenticationRegisterMobile_tncBox,
    authenticationRegisterMobile_isNeedInvoiceBill,
    authenticationRegisterMobile_toggleInoviceBill,
    // KTP
    authentication_isUploadingKtp,
    authentication_isUploadKtpError,
    authentication_uploadKtpProgress,
    authentication_uploadedKtpSize,
    authentication_totalKtpSize,
    authentication_uploadKtpFileName,
    // NPWP
    authentication_isUploadingNpwp,
    authentication_isUploadNpwpError,
    authentication_uploadNpwpProgress,
    authentication_uploadedNpwpSize,
    authentication_totalNpwpSize,
    authentication_uploadNpwpFileName,
    // API
    authentication_listProvince,
    authentication_listCities,
    authentication_listSubdistrict,
    authentication_listZipcode,
    authentication_listNpwpProvince,
    authentication_listNpwpCities,
    authentication_listNpwpSubdistrict,
    authentication_taxType,
    authentication_termAndCondition,
    authentication_isExistKTP,
    authentication_isExistNPWP,
    authenticationRegisterMobile_validateNPWP,
    authenticationRegisterMobile_validateKTP,
  };
};
