// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

import type { IAuthenticationProvided } from '../interfaces/authentication.interface';
import type { IAuthenticationLoginPayload } from '../interfaces/authentication-login.interface';
import useVuelidate from '@vuelidate/core';
import { helpers, required } from '@vuelidate/validators';
import { useRememberPassword } from '../stores/remember-password.store';
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationService = (): IAuthenticationProvided => {
  /**
   * @description Injected variables
   */
  const store = useAuthenticationStore(); // Instance of the store
  const {
    authentication_isOpenModalLogin,
    authentication_accessToken,
    authentication_userData,
    authentication_isLoading,
    authentication_checkActivationLoading,
    authentication_resendIsLoading,
  } = storeToRefs(store);

  const toast = useToast();
  const { $bus } = useNuxtApp();
  const rememberStore = useRememberPassword();

  const authentication_isLoggedIn = computed(() => {
    return Boolean(authentication_accessToken.value) && Boolean(authentication_userData.value?.customer_id);
  });

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const authentication_loginFormData = reactive<IAuthenticationLoginPayload>({
    email: '',
    password: '',
    type: 'external',
    reference: 'B2B',
  });

  /**
   * @description Form validations
   */
  const authentication_loginFormRules = computed(() => ({
    email: {
      required,
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
    },
    password: { required },
  }));

  const authentication_loginFormValidations = useVuelidate(
    authentication_loginFormRules,
    authentication_loginFormData,
    {
      $autoDirty: true,
    },
  );

  /**
   * @description Handle fetch api authentication login. We call the fetchAuthenticationLogin function from the store to handle the request.
   */
  const authentication_fetchAuthenticationLogin = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_login(authentication_loginFormData);

      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Login successfully',
        life: 3000,
      });

      authentication_isOpenModalLogin.value = false;

      window.location.reload();
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const authentication_onSubmitLogin = async (checkedRememberMe?: boolean): Promise<void> => {
    authentication_loginFormValidations.value.$touch();
    if (authentication_loginFormValidations.value.$invalid) return;

    await authentication_fetchAuthenticationLogin().finally(() => {
      if (checkedRememberMe) {
        rememberStore.$patch(state => {
          state.email = authentication_loginFormData.email;
          state.password = authentication_loginFormData.password;
        });
      }
    });
  };

  const authentication_checkActivationStatus = async (token: string, email: string): Promise<unknown> => {
    return await store.fetchAuthentication_checkActivation(token, email);
  };

  const authentication_resendActivation = async (email: string): Promise<void> => {
    const result = await store.fetchAuthentication_resendActivation(email);
    if (result) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Email verifikasi telah dikirim ulang ke email Anda.',
        life: 3000,
      });

      setTimeout(() => {
        window.location.href = '/authentication/login';
      }, 500);
    }
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    authentication_isOpenModalLogin,
    authentication_isLoggedIn,
    authentication_loginFormData,
    authentication_loginFormValidations,
    authentication_loginIsLoading: authentication_isLoading,
    authentication_onSubmitLogin,
    authentication_checkActivationLoading,
    authentication_resendIsLoading,
    authentication_checkActivationStatus,
    authentication_resendActivation,
  };
};
