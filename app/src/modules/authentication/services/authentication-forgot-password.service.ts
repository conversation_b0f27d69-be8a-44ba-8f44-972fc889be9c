// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Interfaces
import type { IAuthenticationForgotPasswordPayload } from '../interfaces/authentication-forgot-password.interface';
// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { helpers, required } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationForgotPasswordService = () => {
  /**
   * @description Injected variables
   */
  // Please put your injected variables here
  const store = useAuthenticationStore(); // Instance of the store
  const { $bus } = useNuxtApp();
  const { authentication_isLoading } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const authenticationForgotPassword_formData = reactive<IAuthenticationForgotPasswordPayload>({
    channel: 'B2B',
    type: 'external',
    email: ''
  });
  
  /**
   * @description Form validations
   */
  const authenticationForgotPassword_formRules: ComputedRef = computed(() => ({
    email: { 
      required,
      email: helpers.withParams({ invaidEmail: 'invalid characters' },
        function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
    }) 
  },
  }));
  const authenticationForgotPassword_formValidations = useVuelidate(
    authenticationForgotPassword_formRules,
    authenticationForgotPassword_formData,
    {
      $autoDirty: true,
    },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  // Please put your side effect here

  /**
   * @description Handle fetch api authentication login. We call the fetchAuthenticationLogin function from the store to handle the request.
   */
  const authenticationForgotPassword_fetchAuthenticationForgotPassword = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_forgotPassword(authenticationForgotPassword_formData);

      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: false,
        isOpen: true,
        text: 'Kode verifikasi telah dikirim ke email Anda.',
        type: EAlertType.INFO,
      } as IPropsBaseAlert);
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const authenticationForgotPassword_onSubmit = async (): Promise<void> => {
    authenticationForgotPassword_formValidations.value.$touch();

    if (authenticationForgotPassword_formValidations.value.$invalid) {
      return;
    }

    await authenticationForgotPassword_fetchAuthenticationForgotPassword();
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    authenticationForgotPassword_formData,
    authenticationForgotPassword_isLoading: authentication_isLoading,
    authenticationForgotPassword_formValidations,
    authenticationForgotPassword_onSubmit,
  };
};
