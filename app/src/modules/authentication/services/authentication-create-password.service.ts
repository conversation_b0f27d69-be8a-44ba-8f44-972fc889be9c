// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Helpers
import {
  isContainLowerCase,
  isContainNumber,
  isContainSpecialCharacter,
  isContainUpperCase,
} from '~/app/src/core/helpers/regex.helper';

// Interfaces
import type {
  IAuthenticationCreatePasswordPayload,
  IAuthenticationCreatePasswordProvided,
  IAuthenticationCreatePasswordValidations,
} from '../interfaces/authentication-create-password.interface';

// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { minLength, required, sameAs } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationCreatePasswordService = (): IAuthenticationCreatePasswordProvided => {
  /**
   * @description Injected variables
   */
  // Please put your injected variables here
  const store = useAuthenticationStore(); // Instance of the store
  const { $bus } = useNuxtApp();
  const {
    authentication_isLoading,
    // Password
    authentication_verifyResetPasswordIsLoading,
    authentication_verifyResetPasswordEmail,
    authentication_changePasswordIsLoading,
    authentication_changePasswordIsSuccess,
  } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const authenticationCreatePassword_formData = reactive<IAuthenticationCreatePasswordPayload>({
    token: '',
    password: '',
    password_confirmation: '',
  });
  const authenticationCreatePassword_customValidations = reactive<IAuthenticationCreatePasswordValidations>({
    isContainLowerCase: false,
    isContainNumber: false,
    isContainSpecialCharacter: false,
    isContainUpperCase: false,
    isLengthValid: false,
  });

  /**
   * @description Form validations
   */
  const authenticationCreatePassword_formRules: ComputedRef = computed(() => ({
    password: {
      required,
      minLength: minLength(8),
      isContainLowerCase,
      isContainNumber,
      isContainSpecialCharacter,
      isContainUpperCase,
    },
    password_confirmation: {
      required,
      minLength: minLength(8),
      sameAsCreatedPassword: sameAs(authenticationCreatePassword_formData.password),
      isContainLowerCase,
      isContainNumber,
      isContainSpecialCharacter,
      isContainUpperCase,
    },
  }));
  const authenticationCreatePassword_formValidations = useVuelidate(
    authenticationCreatePassword_formRules,
    authenticationCreatePassword_formData,
    {
      $autoDirty: true,
    },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    () => authenticationCreatePassword_formData.password,
    value => {
      if (value) {
        authenticationCreatePassword_customValidations.isContainLowerCase = isContainLowerCase(value);
        authenticationCreatePassword_customValidations.isContainNumber = isContainNumber(value);
        authenticationCreatePassword_customValidations.isContainSpecialCharacter =
          isContainSpecialCharacter(value);
        authenticationCreatePassword_customValidations.isContainUpperCase = isContainUpperCase(value);
        authenticationCreatePassword_customValidations.isLengthValid = value.length >= 8;
      }
    },
  );

  /**
   * @description Handle business logic to check if all custom validations are valid
   */
  const authenticationCreatePassword_isCustomValidationsValid: ComputedRef<boolean> = computed(() => {
    return Object.values(authenticationCreatePassword_customValidations).every(value => value);
  });

  /**
   * @description Handle fetch api authentication create password. We call the fetchAuthentication_createPassword function from the store to handle the request.
   */
  const authenticationCreatePassword_fetchAuthenticationCreatePassword = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_createPassword(authenticationCreatePassword_formData);

      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: 'Password has been created successfully.',
        type: EAlertType.SUCCESS,
      } as IPropsBaseAlert);

      setTimeout(async () => {
        await navigateTo('/authentication/login');
      }, 3000);
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const authenticationCreatePassword_onSubmit = () => {
    authenticationCreatePassword_formValidations.value.$touch();

    if (authenticationCreatePassword_formValidations.value.$error) {
      return;
    }

    authenticationCreatePassword_fetchAuthenticationCreatePassword();
  };

  /**
   * @description Handle business logic to set dynamic class label validation
   */
  const authenticationCreatePassword_setDynamicClassLabelValidation = (
    key: keyof typeof authenticationCreatePassword_customValidations,
  ): string => {
    if (authenticationCreatePassword_formData.password) {
      return authenticationCreatePassword_customValidations[key] ? 'text-success' : 'text-danger';
    }

    return 'text-black';
  };

  /**
   * @description Handle business logic to set dynamic path icon
   */
  const authenticationCreatePassword_setDynamicPathIcon = (
    key: keyof typeof authenticationCreatePassword_customValidations,
  ): string => {
    if (authenticationCreatePassword_customValidations[key]) {
      return '/icons/check-circle.svg';
    } else {
      return '/icons/x-circle-red.svg';
    }
  };

  /**
   * @description Handle action on verify token reset password.
   */
  const authenticationCreatePassword_verify = async (token: string): Promise<void> => {
    await store.fetchAuthentication_verifyTokenResetPassword(token);
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    authenticationCreatePassword_customValidations,
    authenticationCreatePassword_formData,
    authenticationCreatePassword_formValidations,
    authenticationCreatePassword_isCustomValidationsValid,
    authenticationCreatePassword_isLoading: authentication_isLoading,
    authenticationCreatePassword_onSubmit,
    authenticationCreatePassword_setDynamicClassLabelValidation,
    authenticationCreatePassword_setDynamicPathIcon,
    authenticationCreatePassword_verify,
    authenticationCreatePassword_verifyIsLoading: authentication_verifyResetPasswordIsLoading,
    authenticationCreatePassword_verifyEmail: authentication_verifyResetPasswordEmail,
    authenticationCreatePassword_changePasswordIsLoading: authentication_changePasswordIsLoading,
    authenticationCreatePassword_changePasswordIsSuccess: authentication_changePasswordIsSuccess,
  };
};
