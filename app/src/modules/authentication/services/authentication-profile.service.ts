// Interfaces

// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';
import type { IAuthenticationProfileProvided } from '../interfaces/authentication-profile.interface';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationProfileService = (): IAuthenticationProfileProvided => {
  /**
   * @description Injected variables
   */
  const store = useAuthenticationStore(); // Instance of the store
  const { authentication_profileData, authentication_isLoading: authentication_profileIsLoading } =
    storeToRefs(store);

  /**
   * @description Handle fetch api authentication login. We call the fetchAuthentication_getProfile function from the store to handle the request.
   */
  const authProfile_fetchProfile = async (): Promise<void> => {
    await store.fetchAuthentication_getProfile();
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    authProfile_fetchProfile,
    authProfile_profileData: authentication_profileData,
    authProfile_profileIsLoading: authentication_profileIsLoading,
  };
};
