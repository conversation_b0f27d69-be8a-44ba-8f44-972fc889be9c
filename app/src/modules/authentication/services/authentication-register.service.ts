// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Helpers
import {
  isContainLowerCase,
  isContainNumber,
  isContainSpecialCharacter,
  isContainUpperCase,
} from '~/app/src/core/helpers/regex.helper';

// Interfaces
import type {
  // IAuthenticationRegisterResponse,
  IAuthenticationRegisterPayload,
  IAuthenticationRegisterValidations,
  IAuthenticationRegisterProvided,
} from '../interfaces/authentication-register.interface';

// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { helpers, minLength, required, sameAs } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationRegisterService = (): IAuthenticationRegisterProvided => {
  /**
   * @description Injected variables
   */
  // Please put your injected variables here
  const store = useAuthenticationStore(); // Instance of the store
  const { $bus } = useNuxtApp();
  const {
    authentication_isLoading,
    // KTP
    authentication_isUploadingKtp,
    authentication_isUploadKtpError,
    authentication_uploadKtpProgress,
    authentication_uploadedKtpSize,
    authentication_totalKtpSize,
    authentication_uploadKtpFileName,
    // NPWP
    authentication_isUploadingNpwp,
    authentication_isUploadNpwpError,
    authentication_uploadNpwpProgress,
    authentication_uploadedNpwpSize,
    authentication_totalNpwpSize,
    authentication_uploadNpwpFileName,
  } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */

  const authenticationRegister_formData = reactive<IAuthenticationRegisterPayload>({
    email: '',
    password: '',
    password_confirmation: '',
  });

  const authenticationRegister_customValidations = reactive<IAuthenticationRegisterValidations>({
    isContainLowerCase: false,
    isContainNumber: false,
    isContainSpecialCharacter: false,
    isContainUpperCase: false,
    isLengthValid: false,
  });

  /**
   * @description Form validations
   */
  const authenticationRegister_formRules: ComputedRef = computed(() => ({
    email: {
      required,
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
    },
    password: {
      required,
      minLength: minLength(8),
      isContainLowerCase,
      isContainNumber,
      isContainSpecialCharacter,
      isContainUpperCase,
    },
    password_confirmation: {
      required,
      minLength: minLength(8),
      sameAsCreatedPassword: sameAs(authenticationRegister_formData.password),
      isContainLowerCase,
      isContainNumber,
      isContainSpecialCharacter,
      isContainUpperCase,
    },
  }));
  const authenticationRegister_formValidations = useVuelidate(
    authenticationRegister_formRules,
    authenticationRegister_formData,
    {
      $autoDirty: false,
      $scope: false,
    },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    () => authenticationRegister_formData.password,
    value => {
      if (value) {
        authenticationRegister_customValidations.isContainLowerCase = isContainLowerCase(value);
        authenticationRegister_customValidations.isContainNumber = isContainNumber(value);
        authenticationRegister_customValidations.isContainSpecialCharacter = isContainSpecialCharacter(value);
        authenticationRegister_customValidations.isContainUpperCase = isContainUpperCase(value);
        authenticationRegister_customValidations.isLengthValid = value.length >= 8;
      }
    },
  );

  /**
   * @description Handle business logic to check if all custom validations are valid
   */
  const authenticationRegister_isCustomValidationsValid: ComputedRef<boolean> = computed(() => {
    return Object.values(authenticationRegister_customValidations).every(value => value);
  });

  /**
   * @description Handle fetch api authentication create password. We call the fetchAuthentication_createPassword function from the store to handle the request.
   */
  const authenticationRegister_fetchAuthenticationRegister = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_checkCustomer(authenticationRegister_formData);
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: 'Akun Sukses Terbuat',
        type: EAlertType.SUCCESS,
      } as IPropsBaseAlert);
      authentication_isLoading.value = false;
      navigateTo('/authentication/register-detail');
    } catch (error) {
      authentication_isLoading.value = false;
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const authenticationRegister_onSubmit = () => {
    authenticationRegister_formValidations.value.$touch();

    if (authenticationRegister_formValidations.value.$error) {
      return;
    }

    authenticationRegister_fetchAuthenticationRegister();
  };

  /**
   * @description Handle business logic to set dynamic class label validation
   */
  const authenticationRegister_setDynamicClassLabelValidation = (
    key: keyof typeof authenticationRegister_customValidations,
  ): string => {
    if (authenticationRegister_formData.password) {
      return authenticationRegister_customValidations[key] ? 'text-success' : 'text-danger';
    }

    return 'text-black';
  };

  /**
   * @description Handle business logic to set dynamic path icon
   */
  const authenticationRegister_setDynamicPathIcon = (
    key: keyof typeof authenticationRegister_customValidations,
  ): string => {
    if (authenticationRegister_customValidations[key]) {
      return '/icons/check-circle.svg';
    } else {
      return '/icons/x-circle-red.svg';
    }
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    authenticationRegister_customValidations,
    authenticationRegister_formData,
    authenticationRegister_formValidations,
    authenticationRegister_isCustomValidationsValid,
    authenticationRegister_isLoading: authentication_isLoading,
    authenticationRegister_onSubmit,
    authenticationRegister_setDynamicClassLabelValidation,
    authenticationRegister_setDynamicPathIcon,
    // KTP
    authentication_isUploadingKtp,
    authentication_isUploadKtpError,
    authentication_uploadKtpProgress,
    authentication_uploadedKtpSize,
    authentication_totalKtpSize,
    authentication_uploadKtpFileName,
    // NPWP
    authentication_isUploadingNpwp,
    authentication_isUploadNpwpError,
    authentication_uploadNpwpProgress,
    authentication_uploadedNpwpSize,
    authentication_totalNpwpSize,
    authentication_uploadNpwpFileName,
  };
};
