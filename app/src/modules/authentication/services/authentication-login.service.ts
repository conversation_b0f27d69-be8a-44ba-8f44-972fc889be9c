// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Interfaces
import type { IAuthenticationLoginPayload } from '../interfaces/authentication-login.interface';

// Primevue
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
import { useAuthenticationStore } from '../stores/authentication.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { helpers, required } from '@vuelidate/validators';
import { useRememberPassword } from '../stores/remember-password.store';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAuthenticationLoginService = () => {
  /**
   * @description Injected variables
   */
  const store = useAuthenticationStore(); // Instance of the store
  const toast = useToast(); // Instance of the toast
  const { $bus } = useNuxtApp();
  const { authentication_isLoading } = storeToRefs(store);

  const rememberStore = useRememberPassword();

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const authenticationLogin_formData = reactive<IAuthenticationLoginPayload>({
    email: '',
    password: '',
    type: 'external',
    reference: 'B2B',
  });

  /**
   * @description Form validations
   */
  const authenticationLogin_formRules: ComputedRef = computed(() => ({
    email: {
      required,
      email: helpers.withParams({ invaidEmail: 'invalid characters' }, function (email: string) {
        return /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/.test(email);
      }),
    },
    password: { required },
  }));
  const authenticationLogin_formValidations = useVuelidate(
    authenticationLogin_formRules,
    authenticationLogin_formData,
    {
      $autoDirty: true,
      $scope: false,
    },
  );

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  // Please put your side effect here

  /**
   * @description Handle fetch api authentication login. We call the fetchAuthenticationLogin function from the store to handle the request.
   */
  const authenticationLogin_fetchAuthenticationLogin = async (): Promise<void> => {
    try {
      await store.fetchAuthentication_login(authenticationLogin_formData);

      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Login successfully',
        life: 3000,
      });

      setTimeout(async () => {
        // await navigateTo('/dashboard');
        window.location.href = '/dashboard';
        // store.fetchAuthentication_getProfile();
      }, 1000);
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert);
    }
  };

  /**
   * @description Handle action on submit form.
   */
  const authenticationLogin_onSubmit = async (checkedRememberMe?: boolean): Promise<void> => {
    authenticationLogin_formValidations.value.$touch();

    await authenticationLogin_fetchAuthenticationLogin().finally(() => {
      if (checkedRememberMe) {
        rememberStore.$patch(state => {
          state.email = authenticationLogin_formData.email;
          state.password = authenticationLogin_formData.password;
        });
      }
    });
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    authenticationLogin_formData,
    authenticationLogin_formValidations,
    authenticationLogin_isLoading: authentication_isLoading,
    authenticationLogin_onSubmit,
  };
};
