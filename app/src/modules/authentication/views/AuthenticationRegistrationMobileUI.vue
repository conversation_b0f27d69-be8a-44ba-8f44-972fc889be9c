<script setup lang="ts">
// Components
import AuthenticationRegisterMobileForm from '../components/AuthenticationRegisterMobileForm.vue';

// Interfaces
import type { IAuthenticationRegisterMobileProvided } from '../interfaces/authentication-register-mobile.interface';

// Services
import { useAuthenticationRegisterMobileService } from '../services/authentication-register-mobile.service';

/**
 * @description Destructure all the data and methods what we need
 */

const {
  authenticationRegisterMobile_customValidations,
  authenticationRegisterMobile_fileValidations,
  authenticationRegisterMobile_isCustomValidationsValid,
  authenticationRegisterMobile_isLoading: authentication_isLoading,
  authenticationRegisterMobile_onSubmit,
  authenticationRegisterMobile_setDynamicClassLabelValidation,
  authenticationRegisterMobile_setDynamicPathIcon,
  authenticationRegisterMobile_fetchProvinces,
  authenticationRegisterMobile_fetchTaxType,
  authenticationRegisterMobile_fetchTermsAndCondition,
  authenticationRegisterMobile_attachment,
  authenticationRegisterMobile_tncBox,
  authenticationRegisterMobile_isNeedInvoiceBill,
  authenticationRegisterMobile_toggleInoviceBill,
  authenticationRegisterMobile_fileRules,
  // KTP
  authentication_uploadKtpFileName,
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  // NPWP
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
  // API
  authentication_listProvince,
  authentication_listCities,
  authentication_listSubdistrict,
  authentication_listZipcode,
  authentication_listNpwpProvince,
  authentication_listNpwpCities,
  authentication_listNpwpSubdistrict,
  authentication_taxType,
  authentication_termAndCondition,
  // SECTION 1
  authenticationRegisterMobile_formValidations_Section_1,
  auhtenticationRegisterMobile_formData_Section_1,
  authenticationRegisterMobile_onSubmitSection1,
  auhtenticationRegisterMobile_fetchCheckCustomer,
  // SECTION 2
  authenticationRegisterMobile_formValidations_Section_2,
  auhtenticationRegisterMobile_formData_Section_2,
  // SECTION 3
  authenticationRegisterMobile_formValidations_Section_3,
  auhtenticationRegisterMobile_formData_Section_3,
  // SECTION 4
  authenticationRegisterMobile_formValidations_Section_4,
  auhtenticationRegisterMobile_formData_Section_4,
  authentication_isExistNPWP,
  authenticationRegisterMobile_validateNPWP,
  authentication_isExistKTP,
  authenticationRegisterMobile_validateKTP,
} = useAuthenticationRegisterMobileService();
/**
 * @description Provide all the data and methods what we need
 */
provide<IAuthenticationRegisterMobileProvided>('authenticationRegisterMobile', {
  authenticationRegisterMobile_customValidations,
  authenticationRegisterMobile_fileValidations,
  authenticationRegisterMobile_isCustomValidationsValid,
  authenticationRegisterMobile_isLoading: authentication_isLoading,
  authenticationRegisterMobile_onSubmit,
  authenticationRegisterMobile_setDynamicClassLabelValidation,
  authenticationRegisterMobile_setDynamicPathIcon,
  authenticationRegisterMobile_fetchProvinces,
  authenticationRegisterMobile_fetchTaxType,
  authenticationRegisterMobile_fetchTermsAndCondition,
  authenticationRegisterMobile_attachment,
  authenticationRegisterMobile_tncBox,
  authenticationRegisterMobile_isNeedInvoiceBill,
  authenticationRegisterMobile_toggleInoviceBill,
  authenticationRegisterMobile_fileRules,
  // KTP
  authentication_uploadKtpFileName,
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  // NPWP
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
  // API
  authentication_listProvince,
  authentication_listCities,
  authentication_listSubdistrict,
  authentication_listZipcode,
  authentication_listNpwpProvince,
  authentication_listNpwpCities,
  authentication_listNpwpSubdistrict,
  authentication_taxType,
  authentication_termAndCondition,
  // SECTION 1
  authenticationRegisterMobile_onSubmitSection1,
  auhtenticationRegisterMobile_formData_Section_1,
  auhtenticationRegisterMobile_fetchCheckCustomer,
  authenticationRegisterMobile_formValidations_Section_1,
  // SECTION 2
  authenticationRegisterMobile_formValidations_Section_2,
  auhtenticationRegisterMobile_formData_Section_2,
  // SECTION 3
  authenticationRegisterMobile_formValidations_Section_3,
  auhtenticationRegisterMobile_formData_Section_3,
  // SECTION 4
  authenticationRegisterMobile_formValidations_Section_4,
  auhtenticationRegisterMobile_formData_Section_4,
  authentication_isExistNPWP,
  authenticationRegisterMobile_validateNPWP,
  authentication_isExistKTP,
  authenticationRegisterMobile_validateKTP,
});

onMounted(async () => {
  await Promise.allSettled([authenticationRegisterMobile_fetchProvinces()]);
  await Promise.allSettled([authenticationRegisterMobile_fetchTaxType()]);
  await Promise.allSettled([authenticationRegisterMobile_fetchTermsAndCondition()]);
});
</script>

<template>
  <NuxtLayout name="authentication">
    <section id="authentication-login" class="">
      <AuthenticationRegisterMobileForm />
      <PrimeVueToast />
    </section>
  </NuxtLayout>
</template>
