<script setup lang="ts">
// Components
import AuthenticationCreatePasswordForm from '../components/AuthenticationCreatePasswordForm.vue';

// Interfaces
import type { IAuthenticationCreatePasswordProvided } from '../interfaces/authentication-create-password.interface';

// Services
import { useAuthenticationCreatePasswordService } from '../services/authentication-create-password.service';

// route
const route = useRoute();
/**
 * @description Destructure all the data and methods what we need
 */
const {
  authenticationCreatePassword_customValidations,
  authenticationCreatePassword_formData,
  authenticationCreatePassword_formValidations,
  authenticationCreatePassword_isCustomValidationsValid,
  authenticationCreatePassword_isLoading,
  authenticationCreatePassword_onSubmit,
  authenticationCreatePassword_setDynamicClassLabelValidation,
  authenticationCreatePassword_setDynamicPathIcon,
  authenticationCreatePassword_verify,
  authenticationCreatePassword_verifyIsLoading,
  authenticationCreatePassword_verifyEmail,
  authenticationCreatePassword_changePasswordIsLoading,
  authenticationCreatePassword_changePasswordIsSuccess,
} = useAuthenticationCreatePasswordService();

/**
 * @description Provide all the data and methods what we need
 */
provide<IAuthenticationCreatePasswordProvided>('authenticationCreatePassword', {
  authenticationCreatePassword_customValidations,
  authenticationCreatePassword_formData,
  authenticationCreatePassword_formValidations,
  authenticationCreatePassword_isCustomValidationsValid,
  authenticationCreatePassword_isLoading,
  authenticationCreatePassword_onSubmit,
  authenticationCreatePassword_setDynamicClassLabelValidation,
  authenticationCreatePassword_setDynamicPathIcon,
  authenticationCreatePassword_verify,
  authenticationCreatePassword_verifyIsLoading,
  authenticationCreatePassword_verifyEmail,
  authenticationCreatePassword_changePasswordIsLoading,
  authenticationCreatePassword_changePasswordIsSuccess,
});

onMounted(async () => {
  const queryToken = route.query?.token;
  authenticationCreatePassword_changePasswordIsSuccess.value = false;
  if (queryToken) {
    authenticationCreatePassword_formData.token = queryToken as string;
    authenticationCreatePassword_verify(queryToken as string);
  }
});
</script>

<template>
  <AuthLayout>
    <section id="authentication-forgot-password" class="w-full h-full relative inset-0 z-0">
      <AuthenticationCreatePasswordForm />
    </section>
  </AuthLayout>
</template>
