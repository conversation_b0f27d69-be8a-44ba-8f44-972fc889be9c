<script setup lang="ts">
// Components
import AuthenticationRegisterForm from '../components/AuthenticationRegisterForm.vue';

// Interfaces
import type { IAuthenticationRegisterProvided } from '../interfaces/authentication-register.interface';

// Services
import { useAuthenticationRegisterService } from '../services/authentication-register.service';

/**
 * @description Destructure all the data and methods what we need
 */

const {
  authenticationRegister_customValidations,
  authenticationRegister_formData,
  authenticationRegister_formValidations,
  authenticationRegister_isCustomValidationsValid,
  authenticationRegister_isLoading: authentication_isLoading,
  authenticationRegister_onSubmit,
  authenticationRegister_setDynamicClassLabelValidation,
  authenticationRegister_setDynamicPathIcon,
  // upload KTP
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  authentication_uploadKtpFileName,
  // upload NPWP
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
} = useAuthenticationRegisterService();
/**
 * @description Provide all the data and methods what we need
 */
provide<IAuthenticationRegisterProvided>('authenticationRegister', {
  authenticationRegister_customValidations,
  authenticationRegister_formData,
  authenticationRegister_formValidations,
  authenticationRegister_isCustomValidationsValid,
  authenticationRegister_isLoading: authentication_isLoading,
  authenticationRegister_onSubmit,
  authenticationRegister_setDynamicClassLabelValidation,
  authenticationRegister_setDynamicPathIcon,
  // upload KTP
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  authentication_uploadKtpFileName,
  // upload NPWP
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
});

onMounted(() => {
    //  KTP
    authentication_isUploadingKtp.value= false
    authentication_isUploadKtpError.value = false
    authentication_uploadKtpProgress.value = 0
    authentication_uploadedKtpSize.value = '0.00'
    authentication_totalKtpSize.value = '0.00'
    authentication_uploadKtpFileName.value = null
    // NPWP
    authentication_isUploadingNpwp.value = false
    authentication_isUploadNpwpError.value = false
    authentication_uploadNpwpProgress.value = 0
    authentication_uploadedNpwpSize.value = '0.00'
    authentication_totalNpwpSize.value = '0.00'
    authentication_uploadNpwpFileName.value = null
})
</script>

<template>
  <NuxtLayout name="authentication">
    <section id="authentication-login" class="w-full h-full relative inset-0 z-0">
      <PrimeVueToast />
      <AuthenticationRegisterForm />
    </section>
  </NuxtLayout>
</template>
