<script setup lang="ts">
// Components
import AuthenticationLoginForm from '../components/AuthenticationLoginForm.vue';

// Interfaces
import type { IAuthenticationLoginProvided } from '../interfaces/authentication-login.interface';

// Services
import { useAuthenticationLoginService } from '../services/authentication-login.service';
import { useAuthenticationService } from '../services/authentication.service';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  authenticationLogin_formData,
  authenticationLogin_formValidations,
  authenticationLogin_isLoading,
  authenticationLogin_onSubmit,
} = useAuthenticationLoginService();

const {
  authentication_isLoggedIn,
} = useAuthenticationService();

/**
 * @description Provide all the data and methods what we need
 */
provide<IAuthenticationLoginProvided>('authenticationLogin', {
  authenticationLogin_formData,
  authenticationLogin_formValidations,
  authenticationLogin_isLoading,
  authenticationLogin_onSubmit,
});

onMounted(() => {
  if (authentication_isLoggedIn.value) {
    navigateTo('/dashboard');
  }
})
</script>

<template>
  <NuxtLayout name="authentication">
    <section id="authentication-login" class="w-full h-full relative inset-0 z-0">
      <AuthenticationLoginForm />
      <PrimeVueToast />
    </section>
  </NuxtLayout>
</template>
