<script setup lang="ts">
// Components
import AuthenticationForgotPasswordForm from '../components/AuthenticationForgotPasswordForm.vue';

// Interfaces
import type { IAuthenticationForgotPasswordProvided } from '../interfaces/authentication-forgot-password.interface';

// Services
import { useAuthenticationForgotPasswordService } from '../services/authentication-forgot-password.service';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  authenticationForgotPassword_formData,
  authenticationForgotPassword_formValidations,
  authenticationForgotPassword_isLoading,
  authenticationForgotPassword_onSubmit,
} = useAuthenticationForgotPasswordService();

/**
 * @description Provide all the data and methods what we need
 */
provide<IAuthenticationForgotPasswordProvided>('authenticationForgotPassword', {
  authenticationForgotPassword_formData,
  authenticationForgotPassword_formValidations,
  authenticationForgotPassword_isLoading,
  authenticationForgotPassword_onSubmit,
});
</script>

<template>
  <NuxtLayout name="authentication">
    <section id="authentication-forgot-password" class="w-full h-full relative inset-0 z-0">
      <AuthenticationForgotPasswordForm />
    </section>
  </NuxtLayout>
</template>
