<script setup lang="ts">

// function to handle go to login page
// return: none
const handleBackToLogin = () => {
  window.location.href = '/authentication/login';
}
</script>

<template>
  <AuthLayout>
    <section id="authentication-login" class="w-full h-full relative inset-0 z-0 flex sm:items-center justify-center sm:mt-0 mt-4">
      <div class="border border-[#E5E6E8] rounded-md flex flex-col items-center justify-center sm:w-[441px] sm:h-[410px] w-[358px] h-[431px] p-5 text-center">
        <NuxtImg src="images/email-register.svg" class="w-[100px] h-[100px]"/>
        <div class="text-[16px] font-bold text-[#18191A] my-4">Akun Anda Telah Berhasil Dibuat!</div>
        <div class="text-[14px] text=[#686F72]">
          <PERSON><PERSON> telah men<PERSON> email untuk verifikasi. <span class="text-[#18191A] font-bold"> Periksa folder spam atau promosi </span> jika tidak ada di kotak masuk.
        </div>
        <PrimeVueButton
          label="Masuk dengan Akun"
          type="submit"
          @click="handleBackToLogin"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted !mt-10"
        />
      </div>
    </section>
  </AuthLayout>
</template>
