<script setup lang="ts">
import { useAuthenticationService } from '../services/authentication.service';

const route = useRoute();
const router = useRouter();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  authentication_checkActivationLoading: loading,
  authentication_checkActivationStatus,
  authentication_resendActivation,
  authentication_resendIsLoading,
} = useAuthenticationService();

const isVerified = ref<null | boolean>(null);

const onClickResend = async () => {
  const email = route.query?.email as string;
  if (email) {
    await authentication_resendActivation(email);
  }
};

const onClickLogin = () => {
  router.push({ name: 'login' });
};

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  isVerified.value = null;
  const token = route.query?.token as string;
  const email = route.query?.email as string;
  if (token && email) {
    try {
      const result = await authentication_checkActivationStatus(token, email);
      isVerified.value = !!result;
    } catch {
      isVerified.value = false;
    }
  } else {
    window.location.href = '/';
  }
});
</script>

<template>
  <AuthLayout>
    <PrimeVueToast />
    <section
      id="auth-activation-account"
      class="w-full h-full relative inset-0 z-0 flex items-center justify-center"
    >
      <template v-if="loading">
        <PrimeVueSkeleton class="!h-[410px] !w-[410px]"></PrimeVueSkeleton>
      </template>
      <template v-else-if="isVerified === false">
        <div
          class="border border-[#E5E6E8] rounded-lg flex flex-col items-center justify-center sm:w-[441px] sm:h-[410px] w-[358px] h-[431px] p-5 text-center -mt-[100px]"
        >
          <NuxtImg src="/icons/x-circle-red.svg" class="w-[100px] h-[100px]" />
          <div class="text-[16px] font-bold text-[#18191A] my-4">Link Verifikasi Kadaluwarsa</div>
          <div class="text-[14px] text=[#686F72]">
            Link verifikasi email yang kamu gunakan sudah tidak berlaku <br />
            Silakan minta link verifikasi baru untuk melanjutkan proses pendaftaran.
          </div>
          <PrimeVueButton
            :disabled="authentication_resendIsLoading"
            label="Kirim Ulang Link Verifikasi"
            type="submit"
            class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted !mt-10"
            @click="onClickResend"
          />
        </div>
      </template>

      <template v-else-if="isVerified === true">
        <div
          class="border border-[#E5E6E8] rounded-lg flex flex-col items-center justify-center sm:w-[441px] sm:h-[410px] w-[358px] h-[431px] p-5 text-center -mt-[100px]"
        >
          <NuxtImg src="/icons/check-circle.svg" class="w-[88px] h-[88px] mb-2" />
          <div class="text-[16px] font-bold text-[#18191A] my-4">Akun Anda Berhasil Diverifikasi!</div>
          <div class="text-[14px] text=[#686F72]">Klik tombol dibawah untuk masuk ke akun Anda.</div>
          <PrimeVueButton
            label="Masuk dengan Akun"
            type="submit"
            class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted !mt-10"
            @click="onClickLogin"
          />
        </div>
      </template>
    </section>
  </AuthLayout>
</template>
