<script setup lang="ts">
// Interfaces
import type { IAuthenticationRegisterDetailProvided } from '../interfaces/authentication-register-detail.interface';
// Services
import { useAuthenticationRegisterDetailService } from '../services/authentication-register-detail.service';
// Components
import AuthenticationRegisterDetailForm from '../components/AuthenticationRegisterDetailForm.vue';

/**
 * @description Destructure all the data and methods what we need
 */

const router = useRouter();

const {
  authenticationRegisterDetail_formData,
  authenticationRegisterDetail_isLoading,
  authenticationRegisterDetail_formValidations,
  authenticationRegisterDetail_onSubmitForm,
  authenticationRegisterDetail_onSubmitTnc,
  authenticationRegisterDetail_fetchProvinces,
  authenticationRegisterDetail_fetchTermsAndCondition,
  authenticationRegisterDetail_fetchTaxType,
  authenticationRegisterDetail_attachment,
  authenticationRegisterDetail_fileValidations,
  authenticationRegisterDetail_isShowModalTnc,
  authenticationRegisterDetail_validateNPWP,
  authenticationRegisterDetail_validateKTP,
  authenticationRegisterDetail_tncBox_1,
  authenticationRegisterDetail_tncBox_2,
  authenticationRegisterDetail_toggleInoviceBill,
  authenticationRegisterDetail_isNeedInvoiceBill,
  // upload KTP
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_totalKtpSize,
  authentication_uploadKtpFileName,
  // npwp
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
  //api
  authentication_listProvince,
  authentication_listCities,
  authentication_listSubdistrict,
  authentication_listNpwpProvince,
  authentication_listNpwpCities,
  authentication_listNpwpSubdistrict,
  authentication_listZipcode,
  authentication_taxType,
  authentication_termAndCondition,
  authentication_isExistNPWP,
  authentication_isExistKTP,
  authentication_emailRegister,
} = useAuthenticationRegisterDetailService();

/**
 * @description Provide all the data and methods what we need
 */
provide<IAuthenticationRegisterDetailProvided>('authenticationRegisterDetail', {
  authenticationRegisterDetail_formData,
  authenticationRegisterDetail_isLoading,
  authenticationRegisterDetail_formValidations,
  authenticationRegisterDetail_onSubmitForm,
  authenticationRegisterDetail_onSubmitTnc,
  authenticationRegisterDetail_fetchProvinces,
  authenticationRegisterDetail_fetchTermsAndCondition,
  authenticationRegisterDetail_fetchTaxType,
  authenticationRegisterDetail_fileValidations,
  authenticationRegisterDetail_attachment,
  authenticationRegisterDetail_isShowModalTnc,
  authenticationRegisterDetail_validateNPWP,
  authenticationRegisterDetail_validateKTP,
  authenticationRegisterDetail_tncBox_1,
  authenticationRegisterDetail_tncBox_2,
  authenticationRegisterDetail_toggleInoviceBill,
  authenticationRegisterDetail_isNeedInvoiceBill,
  // ktp,
  authentication_isUploadingKtp,
  authentication_isUploadKtpError,
  authentication_uploadKtpProgress,
  authentication_uploadedKtpSize,
  authentication_uploadKtpFileName,
  authentication_totalKtpSize,
  // npwp
  authentication_isUploadingNpwp,
  authentication_isUploadNpwpError,
  authentication_uploadNpwpProgress,
  authentication_uploadedNpwpSize,
  authentication_totalNpwpSize,
  authentication_uploadNpwpFileName,
  // api
  authentication_listProvince,
  authentication_listCities,
  authentication_listSubdistrict,
  authentication_listZipcode,
  authentication_listNpwpProvince,
  authentication_listNpwpCities,
  authentication_listNpwpSubdistrict,
  authentication_taxType,
  authentication_termAndCondition,
  authentication_isExistNPWP,
  authentication_isExistKTP,
  authentication_emailRegister,
});

onMounted(async () => {
  await Promise.allSettled([authenticationRegisterDetail_fetchProvinces()]);
  await Promise.allSettled([authenticationRegisterDetail_fetchTaxType()]);
  await Promise.allSettled([authenticationRegisterDetail_fetchTermsAndCondition()]);

  setTimeout(() => {
    if (!authentication_emailRegister.value) {
      router.push({ name: 'register-form' });
    } else if (authentication_emailRegister.value) {
      authenticationRegisterDetail_formData.email = authentication_emailRegister.value;
    }
  }, 500);
});
</script>

<template>
  <NuxtLayout name="authentication">
    <section id="authentication" class="relative inset-0 z-0 h-screen">
      <AuthenticationRegisterDetailForm />
      <PrimeVueToast />
    </section>
  </NuxtLayout>
</template>
