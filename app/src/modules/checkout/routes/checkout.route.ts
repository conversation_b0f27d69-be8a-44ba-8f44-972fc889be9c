// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/checkout',
    component: AppBaseWrapper,
    children: [
      {
        path: 'success/:orderNumber',
        name: 'checkout.success',
        component: () => import('../views/CheckoutSuccessUI.vue'),
      },
      {
        path: ':orderNumber',
        name: 'checkout',
        component: () => import('../views/CheckoutUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            {
              label: 'Beranda',
              path: '/dashboard',
            },
            {
              label: 'Keranjang',
              path: '/cart',
            },
            {
              label: 'Checkout',
              path: null,
            },
          ],
        },
      },
      {
        path: '/bulk',
        name: 'bulk-checkout',
        component: () => import('../views/CheckoutUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            {
              label: 'Beranda',
              path: '/dashboard',
            },
            {
              label: '<PERSON><PERSON><PERSON>k',
              path: '/bulk',
            },
            {
              label: 'Keranjang',
              path: '/bulk/cart',
            },
            {
              label: 'Checkout',
              path: null,
            },
          ],
        },
      },
    ],
  },
];

export default routes;
