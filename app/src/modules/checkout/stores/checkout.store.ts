// Constants
import {
  CHECKOUT_CHANGE_ADDRESS,
  CHECKOUT_CONFIRM_ENDPOINT,
  CHECKOUT_ENDPOINT,
  CHECKOUT_LIST_ENDPOINT,
  CHECKOUT_GET_PRONIVCE_ENDPOINT,
  CHECKOUT_GET_CITY_ENDPOINT,
  CHECKOUT_GET_DISTRICT_ENDPOINT,
  CHECKOUT_ADD_ADDRESS_ENDPOINT,
  CHECKOUT_CUSTOMER_SHIPMENT,
  CHECKOUT_CUSTOMER_EDIT_ENDPOINT,
  CHECKOUT_CUSTOMER_DELETE_ENDPOINT,
} from '../constants/checkout.api.constant';

// Helpers
import { setTimestampOfFetchAPI } from '~/app/src/core/helpers/set-timestamp-fetch.helper';

// Interfaces
import type {
  ICheckoutChangeAddressPayload,
  ICheckoutConfirmPayload,
  ICheckoutPayload,
  ICheckoutQueryParams,
  ICheckoutStoreStore,
  ICheckoutCitiesPayload,
  ICheckoutDistrictPayload,
  ICheckoutAddAdressPayload,
} from '../interfaces/checkout.interface';

// Pinia
import { defineStore } from 'pinia';
import type { IOrderDetail } from '../../order/interfaces/order.interface';

export const useCheckoutStore = defineStore('checkout', {
  state: (): ICheckoutStoreStore => ({
    checkout_isLoading: false,
    checkout_loading: false,
    checkout_listLoading: false,
    checkout_listData: null,
    checkout_isOpenModalAddress: false,
    checkout_listShipment: [],
    checkout_listProvince: [],
    checkout_listCities: [],
    checkout_listDistrict: [],
    checkout_pagination: {
      total_data: 0,
      size: 0,
      active_page: 0,
      total_page: 0,
    },
    checkout_isError: false,
    checkout_errorMessage: '',
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get list checkout order.
     * @url /transaction/:orderNumber
     * @method GET
     * @access private
     */
    async fetchCheckout_getList(
      orderNumber: string,
      params: ICheckoutQueryParams,
      isConfirmChecked: boolean = false,
    ): Promise<unknown> {
      try {
        const router = useRouter();
        this.checkout_listLoading = true;
        this.checkout_listData = null;
        const { data, error } = await useApiFetch(`${CHECKOUT_LIST_ENDPOINT}/${orderNumber}`, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            const result: IOrderDetail = response._data.data;
            if (result?.order_detail?.is_confirm && isConfirmChecked) {
              router.push({
                name: 'cart',
              });
            }
            this.checkout_listData = result;
            if (result.total_data) {
              this.checkout_pagination.total_data = result.total_data;
              this.checkout_pagination.size = result.size;
              this.checkout_pagination.active_page = result.active_page;
              this.checkout_pagination.total_page = result.total_page;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        this.checkout_listData = null;
        return Promise.reject(new Error(error as string));
      } finally {
        this.checkout_listLoading = false;
      }
    },

    /**
     * @description Handle fetch api checkout.
     * @url /checkout
     * @method GET
     * @access private
     */
    async fetchCheckout_createOrder(body: ICheckoutPayload): Promise<{ order_no: string }> {
      try {
        const router = useRouter();
        this.checkout_loading = true;
        const { data, error } = await useApiFetch(CHECKOUT_ENDPOINT, {
          method: 'POST',
          body,

          onResponse: async ({ response }) => {
            const orderGroupId = response._data?.data?.order_group_id ?? null;
            if (orderGroupId) {
              router.push({
                name: 'checkout',
                params: {
                  orderNumber: orderGroupId,
                },
              });
            }
          },

          /**
           * @description We also can transform the data before we return it.
           */
          transform: function (input) {
            return setTimestampOfFetchAPI(input);
          },
        });

        if (error.value) {
          const errorData = error.value?.data as { message: string };
          throw new Error(errorData?.message);
        }

        return Promise.resolve(data) as unknown as Promise<{ order_no: string }>;
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        setTimeout(() => {
          this.checkout_loading = false;
        }, 500);
      }
    },

    /**
     * @description Handle fetch confirm checkout.
     * @url /transaction/confirm
     * @method GET
     * @access private
     */
    async fetchCheckout_confirm(body: ICheckoutConfirmPayload): Promise<{ order_no: string }> {
      try {
        // const router = useRouter();
        const route = useRoute();
        this.checkout_loading = true;
        const { data, error } = await useApiFetch(CHECKOUT_CONFIRM_ENDPOINT, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            if (response?.status === 200 || response?.status === 201) {
              const orderGroupId = response._data?.data?.order_group_id ?? route.params?.orderNumber;
              if (orderGroupId) {
                window.location.href = `/checkout/success/${body.order_group_id}`;
              }
            } else {
              this.checkout_isError = true;
              this.checkout_errorMessage = 'Terjadi kesalahan ketika sedang Simulasi Order!';
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data) as unknown as Promise<{ order_no: string }>;
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        setTimeout(() => {
          this.checkout_loading = false;
        }, 500);
      }
    },

    /**
     * @description Handle change api address checkout.
     * @url /change-address
     * @method POST
     * @access private
     */
    async fetchCheckout_changeAddress(customer_shipment_id: string): Promise<unknown> {
      try {
        this.checkout_isLoading = true;
        const { data, error } = await useApiFetch(`${CHECKOUT_CHANGE_ADDRESS}/${customer_shipment_id}`, {
          method: 'PATCH',
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.checkout_isLoading = false;
      }
    },
    /**
     * @description Handle change api address checkout.
     * @url /change-address
     * @method PUT
     * @access private
     */
    async fetchCheckout_editAddress(
      customer_shipment_id: string,
      payload: ICheckoutChangeAddressPayload,
    ): Promise<unknown> {
      try {
        this.checkout_isLoading = true;
        const { data, error } = await useApiFetch(`${CHECKOUT_CUSTOMER_EDIT_ENDPOINT}/${customer_shipment_id}`, {
          method: 'PUT',
          body: {
            shipment_address: payload.address,
            shipment_province_code: payload.province.code,
            shipment_province: payload.province.name,
            shipment_city_code: payload.city.code,
            shipment_city: payload.city.city_name,
            shipment_district_code: payload.district.code,
            shipment_district: payload.district.name,
            shipment_zip_code: payload.zip_code,
          },
          onResponse: async ({ response }) => {
            console.log(response, 'response update date');
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.checkout_isLoading = false;
      }
    },
    /**
     * @description Handle change api address checkout.
     * @url /change-address
     * @method DELETE
     * @access private
     */
    async fetchCheckout_deleteAddress(customer_shipment_id: string): Promise<unknown> {
      try {
        this.checkout_isLoading = true;
        const { data, error } = await useApiFetch(`${CHECKOUT_CUSTOMER_DELETE_ENDPOINT}/${customer_shipment_id}`, {
          method: 'DELETE',
          onResponse: async ({ response }) => {
            console.log(response, 'response update date');
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.checkout_isLoading = false;
      }
    },
    /**
     * @description Handle change api adding address.
     * @url /add-address
     * @method POST
     * @access private
     */
    async fetchCheckout_addAddress(body: ICheckoutAddAdressPayload): Promise<unknown> {
      try {
        this.checkout_isLoading = true;
        const { data, error } = await useApiFetch(CHECKOUT_ADD_ADDRESS_ENDPOINT, {
          method: 'POST',
          body,

          onResponse: async ({ response }) => {
            console.log('response add address', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.checkout_isLoading = false;
      }
    },
    /**
     * @description request to get province.
     * @url /region?per_page=10000
     * @method GET
     * @access private
     */
    async fetchCheckout_getShipmentAddress(): Promise<unknown> {
      try {
        this.checkout_isLoading = true;
        const { data, error } = await useApiFetch(CHECKOUT_CUSTOMER_SHIPMENT, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.checkout_listShipment = response._data.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      } finally {
        this.checkout_isLoading = false;
      }
    },
    /**
     * @description request to get province.
     * @url /region?per_page=10000
     * @method GET
     * @access private
     */
    async fetchCheckout_getProvince(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(CHECKOUT_GET_PRONIVCE_ENDPOINT, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.checkout_listProvince = response._data.data.original.data;
          },
        });
        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      }
    },
    /**
     * @description request to get city.
     * @url /region?per_page=10000
     * @method GET
     * @access private
     */
    async fetchCheckout_getCity(payload: ICheckoutCitiesPayload): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${CHECKOUT_GET_CITY_ENDPOINT}${payload.code}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.checkout_listCities = response._data.data.original.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      }
    },
    /**
     * @description request to get district.
     * @url /region-district?per_page=10000&city_id=
     * @method GET
     * @access private
     */
    async fetchCheckout_getDistrict(payload: ICheckoutDistrictPayload): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${CHECKOUT_GET_DISTRICT_ENDPOINT}${payload.code}`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            this.checkout_listDistrict = response._data.data.original.data;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Terjadi Kesalahan di server, silahkan cobalagi'));
      }
    },
  },
});
