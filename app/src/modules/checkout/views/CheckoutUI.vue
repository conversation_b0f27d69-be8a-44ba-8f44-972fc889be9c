<script lang="ts" setup>
// Components
import CheckoutList from '../components/CheckoutList.vue';
import CheckoutSummary from '../components/CheckoutSummary.vue';

// Services
import { useCheckoutService } from '../services/checkout.service';
import { useOrderService } from '../../order/services/order.service';
import { useCartService } from '../../cart/services/cart.service';
import CartRemarkForm from '../../cart/components/CartRemarkForm.vue';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  checkout_loading,
  checkout_listData,
  checkout_listLoading,
  checkout_pagination,
  checkout_queryParams,
  checkout_addresPayload,
  checkout_formValidations,
  checkout_getList,
  checkout_createOrder,
  checkout_confirm,
  checkout_changeAddress,
  checkout_isCustom,
  checkout_isOpenModalAddress,
  checkout_getProvince,
  checkout_listProvince,
  checkout_listCities,
  checkout_listDistrict,
  checkout_addAddress,
  checkout_getShipmentAddress,
  checkout_listShipment,
  checkout_editAddress,
  checkout_deleteAddress,
  checkout_createAddress,
  checkout_isAddingAddress,
  checkout_isLoading,
  checkout_modifyAddress,
  checkout_isEditAddress,
  checkout_resetAddressPayload,
} = useCheckoutService();
const { cart_isShowModalFormRemark, cart_remarkForm, cart_remarkFormValidations, cart_submitFormRemark } =
  useCartService();

const { order_cancelOrder } = useOrderService();

/**
 * @description Provide all the data and methods what we need
 */
provide('checkout', {
  checkout_loading,
  checkout_listData,
  checkout_listLoading,
  checkout_pagination,
  checkout_queryParams,
  checkout_addresPayload,
  checkout_formValidations,
  checkout_getList,
  checkout_createOrder,
  checkout_confirm,
  checkout_changeAddress,
  checkout_isCustom,
  checkout_isOpenModalAddress,
  checkout_getProvince,
  checkout_listProvince,
  checkout_listCities,
  checkout_listDistrict,
  checkout_addAddress,
  checkout_getShipmentAddress,
  checkout_listShipment,
  checkout_editAddress,
  checkout_deleteAddress,
  checkout_createAddress,
  checkout_isAddingAddress,
  checkout_isLoading,
  checkout_modifyAddress,
  checkout_isEditAddress,
  checkout_resetAddressPayload,
});

provide('order', {
  order_cancelOrder,
});

provide('cart', {
  cart_isShowModalFormRemark,
  cart_remarkForm,
  cart_remarkFormValidations,
  cart_submitFormRemark,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  if (route.params?.orderNumber) {
    await Promise.allSettled([
      checkout_getList(String(route.params?.orderNumber)),
      checkout_getProvince(),
      checkout_getShipmentAddress(),
      // dashboard_getLimitCredit()
    ]);
  }
});
</script>

<template>
  <MainLayout page-title="Checkout">
    <BaseLoader v-show="checkout_loading" />
    <section id="cart-page" class="flex flex-col gap-6 w-full h-full relative inset-0 z-0">
      <!-- <CheckoutCountDown
        v-if="checkout_listData?.order_detail.transaction_date"
        :data="checkout_listData?.order_detail"
      /> -->
      <div class="grid grid-cols-1 sm:grid-cols-[1fr_310px] gap-8 mb-3">
        <CheckoutList class="sm:order-1 order-2" />
        <section id="checkout-summary" class="sm:order-2 order-1">
          <PrimeVueSkeleton v-if="checkout_listLoading" class="sm:!w-[308px] !w-full sm:!h-[970px] !h-[27em]" />
          <CheckoutSummary v-else />
        </section>
      </div>
    </section>
    <CartRemarkForm :is-checkout="true" />
  </MainLayout>
</template>
