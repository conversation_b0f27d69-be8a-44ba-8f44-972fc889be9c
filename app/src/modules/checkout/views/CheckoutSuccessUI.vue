<script lang="ts" setup>
// Components
import CheckoutSuccess from '../components/CheckoutSuccess.vue';

// Services
import { useCheckoutService } from '../services/checkout.service';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const { checkout_listData, checkout_listLoading, checkout_getListSucces, checkout_isCustom } = useCheckoutService();

/**
 * @description Provide all the data and methods what we need
 */
provide('checkout', {
  checkout_listData,
  checkout_listLoading,
  checkout_getListSucces,
  checkout_isCustom,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  if (route.params?.orderNumber) {
    await Promise.allSettled([checkout_getListSucces(String(route.params?.orderNumber))]);
  }
});
</script>

<template>
  <MainLayout :hide-footer="true">
    <section class="flex items-center justify-center w-full md:w-[550px] h-full md:mx-auto">
      <PrimeVueSkeleton v-if="checkout_listLoading" class="!w-[548px] !h-[674px] !rounded-xl !my-10" />
      <CheckoutSuccess v-else />
    </section>
  </MainLayout>
</template>
