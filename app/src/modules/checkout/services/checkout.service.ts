// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Interfaces
import type {
  ICheckoutChangeAddressPayload,
  ICheckoutConfirmPayload,
  ICheckoutPayload,
  ICheckoutProvided,
  ICheckoutQueryParams,
  ICheckoutCitiesPayload,
  ICheckoutDistrictPayload,
  ICheckoutShipmentAddress
  // ICheckoutAddAdressPayload
} from '../interfaces/checkout.interface';

// Store
import { storeToRefs } from 'pinia';
import { useCheckoutStore } from '../stores/checkout.store';

// constants
import { CHECKOUT_PAGINATION_SIZE } from '../constants/checkout.constant';
import { required,decimal,helpers,maxLength } from '@vuelidate/validators';
import useVuelidate from '@vuelidate/core';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useCheckoutService = (): ICheckoutProvided => {
  /**
   * @description Injected variables
   */
  const toast = useToast(); // Instance of the toast
  const store = useCheckoutStore(); // Instance of the store
  const { $bus } = useNuxtApp();
  const checkout_isAddingAddress = ref(false);
  const checkout_isEditAddress = ref(false);
  const edit_customer_shipment_id = ref('')
  const { 
    checkout_loading, 
    checkout_isLoading,
    checkout_listData, 
    checkout_listLoading, 
    checkout_pagination,
    checkout_isOpenModalAddress,
    checkout_listProvince,
    checkout_listCities,
    checkout_listDistrict,
    checkout_listShipment
   } = storeToRefs(store);

  const checkout_queryParams = reactive<ICheckoutQueryParams>({
    page: 1,
    per_page: CHECKOUT_PAGINATION_SIZE[0],
  });

  /**
   * @description Reactive data binding
   */
  const checkout_addresPayload = reactive<ICheckoutChangeAddressPayload>({
    address: checkout_listData.value?.order_detail?.customer_shipment[0].address ?? '',
    province: {
      code: '',
      name: '',
      island: ''
    },
    city: {
      code: '',
      id: '',
      name: '',
      city_name: ''
    },
    district: {
      code: '',
      name: '',
    },
    zip_code: ''
  });

  /**
   * @description Handle fetch api get checkout list product. We call the fetchCheckout_getList function from the store to handle the request.
   */
  const checkout_getList = async (orderNumber: string): Promise<void> => {
    await store.fetchCheckout_getList(orderNumber, checkout_queryParams, true);
  };

  /**
   * @description Handle fetch api get checkout list product. We call the fetchCheckout_getList function from the store to handle the request.
   */
  const checkout_getListSucces = async (orderNumber: string): Promise<void> => {
    await store.fetchCheckout_getList(orderNumber, checkout_queryParams);
  };

  /**
   * @description Handle fetch api get list province. We call the fetchCheckout_getList function from the store to handle the request.
   */
  const checkout_getProvince = async ()  => {
    try {
      await store.fetchCheckout_getProvince()
    } catch (error) {
      console.log(error)      
    }
  }

  /**
   * @description Handle fetch api get list shipment address. We call the fetchCheckout_getShipmentAddress function from the store to handle the request.
   */
  const checkout_getShipmentAddress = async ()  => {
    try {
      await store.fetchCheckout_getShipmentAddress()
    } catch (error) {
      console.log(error)      
    }
  }

  /**
   * @description handle reset address payload
   */
  const checkout_resetAddressPayload = () => {
    checkout_addresPayload.province.code = '';
    checkout_addresPayload.province.island = '';
    checkout_addresPayload.province.name = '';
    checkout_addresPayload.city.city_name = '';
    checkout_addresPayload.city.code=  '';
    checkout_addresPayload.city.id = '';
    checkout_addresPayload.city.name = '';
    checkout_addresPayload.district.code = '';
    checkout_addresPayload.district.name = '';
    checkout_addresPayload.zip_code = '';
    checkout_addresPayload.address = '';
  }

  /**
   * @description Handle fetch edit address address. We call the fetchCheckout_editAddress function from the store to handle the request.
   */
  const checkout_editAddress = async () => {
    try {
      await store.fetchCheckout_editAddress(edit_customer_shipment_id.value,checkout_addresPayload)
      await store.fetchCheckout_getShipmentAddress()
      checkout_isEditAddress.value = !checkout_isEditAddress.value
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Sukses Edit Alamat',
        life: 3000,
      });
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert); 
    } finally {
      checkout_resetAddressPayload();
    }
  }


  /**
   * @description Handle fetch edit address address. We call the fetchCheckout_editAddress function from the store to handle the request.
   */
  const checkout_deleteAddress = async (customer_shipment_id:string) => {
    try {
      await store.fetchCheckout_deleteAddress(customer_shipment_id)
      await store.fetchCheckout_getShipmentAddress()
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Sukses Delete Address',
        life: 3000,
      });
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert); 
    }
  }

  const checkout_isCustom = computed(() =>
    !!checkout_listData?.value?.order_detail?.items?.[0]?.cart_attachments?.length,
  );

  /**
   * @description Form validations
   */
  const checkout_formRules: ComputedRef = computed(() => ({
    province: {
      code: { required },
      name: { required },
    },
    city: {
      code: { required },
      name: { required },
      city_name: { required }
    },
    district: {
      code: { required },
      name: { required }
    },
    zip_code: { 
      required,
      decimal,
      maxLength: maxLength(10),
      invalid_phone_number: helpers.withParams({customMessage: 'Kode pos tidak valid' },
        function (value: string) {
          return  /^[1-9][0-9]*$/.test(value)
      }) 
    },
    address: { required,maxLength:maxLength(255) },
  }));

  const checkout_formValidations = useVuelidate(checkout_formRules, checkout_addresPayload, {
    $autoDirty: false,
    $scope:false
  });

  /**
   * @description Handle fetch api get activities of product. We call the fetchCheckout_createOrder function from the store to handle the request.
   */
  const checkout_createOrder = async (body: ICheckoutPayload): Promise<void> => {
    try {
      await store.fetchCheckout_createOrder(body);
    } catch (error) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: error as string,
        life: 3000,
      });
    }
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCheckout_confirm function from the store to handle the request.
   */
  const checkout_confirm = async (body: ICheckoutConfirmPayload): Promise<void> => {
    await store.fetchCheckout_confirm(body);
  };

  /**
   * @description Handle fetch api get add address. We call the fetchCheckout_confirm function from the store to handle the request.
   */
  const checkout_addAddress = async () : Promise<void> => {
    try {
      const payload = {
        shipment_address: checkout_addresPayload.address,
        shipment_province_code: checkout_addresPayload.province.code,
        shipment_province: checkout_addresPayload.province.name,
        shipment_city_code: checkout_addresPayload.city.code,
        shipment_city: checkout_addresPayload.city.name,
        shipment_district_code: checkout_addresPayload.district.code,
        shipment_district: checkout_addresPayload.district.name,
        shipment_zip_code: checkout_addresPayload.zip_code
      }
      await store.fetchCheckout_addAddress(payload)
      await store.fetchCheckout_getProvince()
      await store.fetchCheckout_getShipmentAddress()
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Success Menambahkan Alamat Baru',
        life: 3000,
      });
      checkout_isAddingAddress.value = !checkout_isAddingAddress.value
    } catch (error) {
      $bus.emit('BaseAlert', {
        durationOfAutoClose: 3000,
        isHaveIconClose: true,
        isOpen: true,
        text: error as string,
        type: EAlertType.DANGER,
      } as IPropsBaseAlert); 
    } finally {
      checkout_resetAddressPayload();
    }
  }

  /**
   * @description Handle fetch api get add address. We call the fetchCheckout_confirm function from the store to handle the request.
   */
  const checkout_createAddress = () => {
    store.$patch(state => {
      state.checkout_listCities = [];
      state.checkout_listDistrict = [];
    });
    checkout_isAddingAddress.value = !checkout_isAddingAddress.value
    checkout_addresPayload.province.code = '';
    checkout_addresPayload.province.island = '';
    checkout_addresPayload.province.name = '';
    checkout_addresPayload.city.city_name = '';
    checkout_addresPayload.city.code=  '';
    checkout_addresPayload.city.id = '';
    checkout_addresPayload.city.name = '';
    checkout_addresPayload.district.code = '';
    checkout_addresPayload.district.name = '';
    checkout_addresPayload.zip_code = '';
    checkout_addresPayload.address = '';
  }

  const checkout_modifyAddress = (customer_id: string) => {
    const selectedAddress = (checkout_listShipment.value as ICheckoutShipmentAddress[]).filter((item) => item.customer_shipment_id === customer_id);
    edit_customer_shipment_id.value = selectedAddress[0].customer_shipment_id
    checkout_addresPayload.province.code = selectedAddress[0].province_code;
    checkout_addresPayload.province.name = selectedAddress[0].province;
    checkout_addresPayload.city.city_name = selectedAddress[0].city;
    checkout_addresPayload.city.id = '';
    checkout_addresPayload.city.name = selectedAddress[0].city; 
    checkout_addresPayload.city.code =  selectedAddress[0].city_code;
    checkout_addresPayload.district.code = selectedAddress[0].district_code;
    checkout_addresPayload.district.name = selectedAddress[0].district;
    checkout_addresPayload.zip_code = selectedAddress[0].zip_code;
    checkout_addresPayload.address = selectedAddress[0].address;
    checkout_isEditAddress.value = !checkout_isEditAddress.value
  }


  /**
   * @description Handle fetch api check item bulk. We call the fetchCart_checkItemBulk function from the store to handle the request.
   */
  const checkout_changeAddress = async (customer_shipment_id: string): Promise<void> => {
      try {
        await store.fetchCheckout_changeAddress(customer_shipment_id)
        await store.fetchCheckout_getShipmentAddress()
        toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Success Update Address',
          life: 3000,
        });
  
      } catch (error) {
        $bus.emit('BaseAlert', {
          durationOfAutoClose: 3000,
          isHaveIconClose: true,
          isOpen: true,
          text: error as string,
          type: EAlertType.DANGER,
        } as IPropsBaseAlert); 
      } finally {
        const orderNumber = checkout_listData.value?.order_detail?.order_group_id;
        if (orderNumber) {
          checkout_getList(orderNumber as string);
        }
      }
    };

  watch(
    checkout_queryParams,
    () => {
      const orderNumber = checkout_listData.value?.order_detail?.order_no;
      if (orderNumber) {
        checkout_getList(orderNumber as string);
      }
    },
    { deep: true },
  );

  watch(
    checkout_listData,
    () => {
      checkout_addresPayload.address =
        checkout_listData.value?.order_detail?.customer_shipment[0].address ?? '';
    },
    { deep: true },
  );

  watch(
    () => checkout_addresPayload.province.code,
    async () => {
      if(checkout_addresPayload.province.code) {
        await store.fetchCheckout_getCity(checkout_addresPayload.province as ICheckoutCitiesPayload)
      }
    }
  );

  watch(
    () => checkout_addresPayload.city.code,
    async () => {
      if(checkout_addresPayload.city.code) {
        await store.fetchCheckout_getDistrict(checkout_addresPayload.city as ICheckoutDistrictPayload);
      }
    }
  );
  
  /**
   * @description Return everything what we need into an object
   */
  return {
    checkout_isOpenModalAddress,
    checkout_loading,
    checkout_isLoading,
    checkout_listData,
    checkout_listLoading,
    checkout_pagination,
    checkout_queryParams,
    checkout_getProvince,
    checkout_addresPayload,
    checkout_formValidations,
    checkout_listShipment,
    checkout_getList,
    checkout_getShipmentAddress,
    checkout_listProvince,
    checkout_listCities,
    checkout_listDistrict,
    checkout_getListSucces,
    checkout_createOrder,
    checkout_confirm,
    checkout_changeAddress,
    checkout_isCustom,
    checkout_addAddress,
    checkout_editAddress,
    checkout_deleteAddress,
    checkout_createAddress,
    checkout_isAddingAddress,
    checkout_modifyAddress,
    checkout_isEditAddress,
    checkout_resetAddressPayload,
  };
};
