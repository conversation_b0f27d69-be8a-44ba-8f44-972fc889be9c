import type { Reactive } from 'vue';
import type { IOrderDetail } from '../../order/interfaces/order.interface';
import type { Validation } from '@vuelidate/core';

export interface ICheckoutPayload {
  items: {
    cart_detail_id: string;
  }[];
  is_custom: number;
}

export interface ICheckoutChangeAddressPayload {
  address: string;
  province  : {
    code : string;
    name : string;
    island: string;
  },
  city : {
        id : string;
        name : string;
        code : string;
        city_name: string;
  };
  district : {
        name : string;
        code : string;
  };
    zip_code : string
  }

export interface ICheckoutConfirmPayload {
  customer_shipment_id: string;
  order_group_id: string;
}

export interface ICheckoutQueryParams {
  page: number;
  per_page: number;
}

export interface ICheckoutCitiesPayload {
  code: string;
  name: string;
  island: string;
}

export interface ICheckoutDistrictPayload {
  name: string;
  code: string;
  id: string;
}

export interface ICheckoutAddAdressPayload {
  shipment_address: string;
  shipment_province_code: string;
  shipment_province: string;
  shipment_city_code: string;
  shipment_city: string;
  shipment_district_code: string;
  shipment_district: string;
  shipment_zip_code: string;
}

export interface ICheckoutShipmentAddress {
  customer_shipment_id: string;
  customer_id: string;
  name : string;
  address_name: string;
  address: string;
  country: string;
  country_code: string;
  province : string;
  province_island: string;
  province_code : string;
  city_id: string;
  city : string;
  city_name: string;
  city_code: string;
  district: string;
  district_code: string;
  subdistrict: string;
  zip_code: string;
  zip_code_id: string;
  zone_code: string;
  shipment_type: string;
  phone_number: string;
  created_date: string;
  created_by: string;
  modified_date: string;
  modified_by: string
  is_selected: number;
  is_primary: number;
}


export interface ICheckoutProvided {
  checkout_loading: Ref<boolean>;
  checkout_isLoading: Ref<boolean>;
  checkout_listData: Ref<IOrderDetail | null>;
  checkout_listLoading: Ref<boolean>;
  checkout_isOpenModalAddress: Ref<boolean>;
  checkout_addresPayload: Reactive<ICheckoutChangeAddressPayload>;
  checkout_getProvince: () => Promise<void>;
  checkout_getShipmentAddress: () => Promise<void>;
  checkout_listProvince: Ref<[]>;
  checkout_listCities: Ref<[]>;
  checkout_listDistrict: Ref<[]>;
  checkout_listShipment: Ref<ICheckoutShipmentAddress[]>;
  checkout_getList: (orderNumber: string) => Promise<void>;
  checkout_getListSucces: (orderNumber: string) => Promise<void>;
  checkout_createOrder: (body: ICheckoutPayload) => Promise<void>;
  checkout_confirm: (body: ICheckoutConfirmPayload) => Promise<void>;
  checkout_changeAddress: (edit_customer_shipment_id: string) => Promise<void>;
  checkout_editAddress: () => Promise<void>;
  checkout_deleteAddress: (customer_shipment_id: string) => Promise<void>;
  checkout_pagination: Ref<IPaginationResponse>;
  checkout_formValidations: globalThis.Ref<Validation>;
  checkout_queryParams: Reactive<ICheckoutQueryParams>;
  checkout_isCustom: ComputedRef<boolean>;
  checkout_addAddress: () => Promise<void>
  checkout_createAddress: () => void;
  checkout_isAddingAddress: Ref<boolean>;
  checkout_modifyAddress: (customer_id: string) => void;
  checkout_isEditAddress: Ref<boolean>;
  checkout_resetAddressPayload: () => void;
}

export interface ICheckoutStoreStore {
  checkout_loading: boolean;
  checkout_isLoading: boolean;
  checkout_listProvince: [];
  checkout_listCities: [];
  checkout_listDistrict: [];
  checkout_listShipment: [];
  checkout_listLoading: boolean;
  checkout_isOpenModalAddress: boolean;
  checkout_listData: IOrderDetail | null;
  checkout_pagination: IPaginationResponse;
  checkout_isError: boolean;
  checkout_errorMessage: string;
}
