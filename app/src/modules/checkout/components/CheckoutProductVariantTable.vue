<script setup lang="ts">
// intefaces
import type { IOrderProductItem } from '../../order/interfaces/order.interface';

interface Props {
  data: IOrderProductItem[];
  basePrice: string;
}

defineProps<Props>();
</script>

<template>
  <div class="w-full">
    <div class="mt-1">
      <table class="min-w-full table-auto border-[#ACB1B4]">
        <thead>
          <tr class="border-t-1 border-b-1 border-[#ACB1B4]">
            <th class="py-2.5 px-4 text-left text-sm w-[100px]">SKU</th>
            <th class="py-2.5 px-4 text-left text-sm w-[100px]">Size</th>
            <th class="py-2.5 px-4 text-left text-sm w-[100px]">Jumlah Order</th>
            <th class="py-2.5 px-4 text-left text-sm w-[100px]">Harga</th>
          </tr>
        </thead>
        <tbody class="border-none">
          <tr v-for="(item, index) in data" :key="String(index)">
            <td class="py-2.5 px-4 text-left text-sm">{{ item.article }}</td>
            <td class="py-2.5 px-4 text-left text-sm">{{ item.product_size }}</td>
            <td class="py-2.5 px-4 text-left text-sm">{{ useNumberFormat(item.qty) }}</td>
            <td class="py-2.5 px-4 text-left text-sm">
              {{ basePrice ? useCurrencyFormat(parseInt(basePrice) * Number(item.qty)) : '-' }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
