<script setup lang="ts">
// import type { IDashboardProvided } from '../../dashboard/interfaces/dashboard.interface';
import type { IOrderCustomerShipment, IOrderDetail } from '../../order/interfaces/order.interface';
import type { ICheckoutConfirmPayload, ICheckoutProvided } from '../interfaces/checkout.interface';

const isOpenModalConfirmFinish = ref(false);
const displayAddress = ref<IOrderCustomerShipment | null>(null);

/**
 * @description Injecting dependencies
 */
const { checkout_listData, checkout_confirm, checkout_isCustom, checkout_isOpenModalAddress, checkout_isLoading } =
  inject<ICheckoutProvided>('checkout')!;

const checkoutValue = reactive<ICheckoutConfirmPayload>({
  order_group_id: checkout_listData.value?.order_detail?.order_group_id as string,
  customer_shipment_id: checkout_listData.value?.order_detail?.customer_shipment_id as string,
});

const orderDetail = computed<IOrderDetail['order_detail']>(
  () => checkout_listData.value?.order_detail as IOrderDetail['order_detail'],
);

/**
 * Sum all quantities
 */
const orderQuantities = computed<number>(() => {
  if (orderDetail.value?.items) {
    return orderDetail.value?.items.reduce((total, product) => {
      const productQtySum = product.product_items.reduce((itemTotal, item) => {
        return itemTotal + item.qty;
      }, 0);

      return total + productQtySum;
    }, 0);
  }
  return 0;
});

const handleConfirmCheckout = async () => {
  if (checkoutValue.order_group_id && checkoutValue.customer_shipment_id) {
    checkout_confirm(checkoutValue as ICheckoutConfirmPayload);
  }
};

const onClickConfirm = () => {
  isOpenModalConfirmFinish.value = true;
};

const onCancelCheckout = () => {
  isOpenModalConfirmFinish.value = false;
};

const onClickChangeAddress = () => {
  checkout_isOpenModalAddress.value = true;
};

const onConfirmCheckout = () => {
  isOpenModalConfirmFinish.value = false;
  handleConfirmCheckout();
};

watch(
  orderDetail.value as IOrderDetail['order_detail'],
  value => {
    if (value?.order_group_id) {
      checkoutValue.order_group_id = value.order_group_id;
    } else if (value?.customer_shipment_id) {
      checkoutValue.customer_shipment_id = value.customer_shipment_id;
    }

    if (value?.customer_shipment?.[0]?.address) {
      displayAddress.value = value?.customer_shipment?.[0];
    }
  },
  { deep: true, immediate: true },
);
</script>

<template>
  <section v-if="orderDetail" id="order-summary" class="bg-white border border-[#E5E6E8] rounded-lg h-fit">
    <div class="gap-3 px-5 py-6">
      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Informasi Pengiriman</p>
        </div>
        <div class="flex flex-col gap-2">
          <div class="flex flex-col gap-2">
            <div class="flex flex-col">
              <p class="text-[#686F72] text-[14px]">Nama Penerima</p>
              <p class="text-black text-[16px] font-medium">{{ checkout_listData?.order_detail?.ship_to }}</p>
            </div>
            <div class="flex flex-col">
              <p class="text-[#686F72] text-[14px]">Nama Perusahaan</p>
              <p class="text-black text-[16px] font-medium">{{ checkout_listData?.order_detail?.company_name }}</p>
            </div>
            <div class="flex flex-col">
              <p class="text-[#686F72] text-[14px]">Email Perusahaan</p>
              <p class="text-black text-[16px] font-medium">
                {{ checkout_listData?.order_detail?.company_email }}
              </p>
            </div>
            <div class="flex flex-col">
              <p class="text-[#686F72] text-[14px]">No. Handphone</p>
              <p class="text-black text-[16px] font-medium">
                {{ checkout_listData?.order_detail?.ship_to_phone_number }}
              </p>
            </div>
            <div class="flex flex-col">
              <p class="text-[#686F72] text-[14px]">Detail Alamat Pengiriman</p>
              <p class="text-black text-[16px]">
                {{ checkout_listData?.order_detail?.ship_to_address }}
              </p>
            </div>
          </div>
          <PrimeVueButton
            class="!border-gray-300 !bg-white !text-black"
            :disabled="orderDetail.is_confirm"
            @click="onClickChangeAddress"
          >
            Ubah Alamat Pengirim
          </PrimeVueButton>
          <div class="border-b border-b-[#ACB1B4] my-4 sm:block hidden"></div>
        </div>
      </div>

      <div class="sm:block hidden">
        <div class="flex w-full items-center justify-between mb-3">
          <p class="text-black font-bold">Ringkasan Pesanan</p>
        </div>
        <p class="mb-2">Produk</p>
        <div class="flex flex-col gap-2 ml-3">
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Jumlah Produk</p>
              </div>
              <p class="text-sm text-black text-right">
                {{ orderQuantities ? useNumberFormat(orderQuantities) : '-' }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Subtotal</p>
              </div>
              <p class="text-sm text-black text-right">
                {{ useCurrencyFormat(orderDetail?.total) }}
              </p>
            </div>
          </div>
          <div v-if="checkout_isCustom">
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Harga Kustom</p>
              </div>
              <p class="text-sm text-black text-right">
                {{ orderDetail.total_kustomisasi ? useCurrencyFormat(orderDetail.total_kustomisasi) : '-' }}
              </p>
            </div>
          </div>
        </div>
        <div class="mt-1.5">
          <div class="grid grid-cols-[1fr_128px]">
            <p class="text-[#05964C] text-base font-medium mb-2">Diskon</p>
            <p class="text-[#05964C] text-base font-medium text-right">
              -{{ useCurrencyFormat(orderDetail?.total_discount ?? 0) }}
            </p>
          </div>
        </div>
        <div class="border-b border-b-[#ACB1B4] my-5"></div>
        <div class="mb-2">
          <div class="grid grid-cols-[100px_1fr]">
            <p v-if="checkout_isCustom" class="text-[10px] uppercase font-bold">ESTIMASI HARGA</p>
            <p v-else class="text-[12px] uppercase font-bold">TOTAL HARGA</p>
            <p class="text-[20px] font-semibold text-black text-right">
              {{ useCurrencyFormat(orderDetail?.sub_total) }}
            </p>
          </div>
        </div>
      </div>

      <div class="sm:block hidden">
        <div class="flex flex-col gap-2 mb-5">
          <PrimeVueButton
            label="Pesan Barang"
            class="!bg-black !border-black !rounded-xl !text-white"
            :class="
              orderDetail.is_confirm || checkout_isLoading
                ? '!bg-[#a6a7a7] !border-[#a6a7a7] !text-black'
                : '!bg-black !border-black'
            "
            :disabled="orderDetail.is_confirm || checkout_isLoading"
            @click="onClickConfirm"
          />
        </div>
        <BaseCustomerSupportCard />
      </div>

      <!-- Dialog confirm checkout -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmFinish"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>

        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[19px] text-black">Konfirmasi Pesanan</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-base text-black">
              Pastikan barang yang ingin kamu pesan telah sesuai sebelum memproses pesanan
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Batalkan"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-base !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelCheckout"
            />
            <PrimeVueButton
              type="button"
              label="Lanjutkan Pesanan"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="onConfirmCheckout"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <!-- Dialog change address -->
      <PrimeVueDialog
        v-model:visible="checkout_isOpenModalAddress"
        modal
        class="h-fit w-full sm:w-[880px]"
        :closable="true"
        :close-on-escape="true"
        :dismissable-mask="true"
        :pt="{
          header: '!hidden',
          content: '!pt-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
          mask: 'sm:!flex !hidden',
        }"
        :draggable="false"
      >
        <template #header> </template>
        <template #default>
          <BaseChangeAddress />
        </template>
      </PrimeVueDialog>

      <PrimeVueDrawer
        v-model:visible="checkout_isOpenModalAddress"
        class="rounded-t-xl"
        position="bottom"
        style="height: auto"
        :show-close-icon="false"
        :pt="{
          header: '!hidden',
          content: '!px-6 !pt-4',
          mask: '!flex sm:!hidden',
        }"
      >
        <template #default>
          <BaseChangeAddress />
        </template>
      </PrimeVueDrawer>
    </div>
  </section>
  <div class="fixed bottom-0 left-0 w-full p-5 sm:hidden block bg-white border border-[#E5E6E8]">
    <div class="flex items-center justify-between">
      <div class="font-bold text-[12px]">TOTAL HARGA</div>
      <div class="font-bold text-[20px]">{{ useCurrencyFormat(orderDetail?.total) }}</div>
    </div>
    <div class="grid grid-cols-[15fr_85fr] mt-2 gap-2">
      <div class="p-2 border border-[#E5E6E8] flex items-center justify-center rounded-xl cursor-pointer">
        <img src="/icons/chat.svg" alt="" class="h-[20px] w-[20px]" />
      </div>
      <PrimeVueButton
        label="Pesan Barang"
        class="!bg-black !border-black !rounded-xl !text-white !w-full"
        @click="onClickConfirm"
      />
    </div>
  </div>
</template>
