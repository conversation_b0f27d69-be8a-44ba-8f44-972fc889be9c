<script lang="ts" setup>
// interfaces
import { OrderStatus } from '../../order/constants';
import type { IOrderDetail, IOrderProvided } from '../../order/interfaces/order.interface';

interface Props {
  data: IOrderDetail['order_detail'] | null;
}

const props = defineProps<Props>();

/**
 * @description Injecting dependencies
 */
const { order_cancelOrder } = inject<IOrderProvided>('order')!;

const targetTimer = computed(() => {
  const deadline =
    props.data?.transaction_date === null || props.data?.transaction_date === ''
      ? new Date()
      : new Date(`${props.data?.transaction_date}+0700`);
  deadline.setHours(deadline.getHours() + 1);
  return deadline.getTime();
});

const currentTime = ref(new Date().getTime());

const timeRemaining = ref(targetTimer.value - currentTime.value);

const formattedTime = computed(() => {
  const minutes = Math.floor((timeRemaining.value % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeRemaining.value % (1000 * 60)) / 1000);
  return `${minutes}:${seconds}`;
});

function updateTimer() {
  currentTime.value = new Date().getTime();
  timeRemaining.value = Math.max(0, targetTimer.value - currentTime.value);
}

let intervalId = 0;

onMounted(() => {
  intervalId = setInterval(updateTimer, 1000) as unknown as number;
});

onBeforeUnmount(() => {
  clearInterval(intervalId);
});

watch(
  () => timeRemaining.value,
  value => {
    if (value <= 0 && props.data?.status !== OrderStatus.CANCELLED) {
      order_cancelOrder({
        order_no: props.data?.order_no as string,
        customer_shipment_id: props.data?.customer_shipment[0].customer_shipment_id as string,
      });
    }
  },
);
</script>

<template>
  <div class="w-full mt-6">
    <div
      v-if="data?.status !== OrderStatus.CANCELLED"
      class="flex bg-[#FFEDD3] items-center text-center rounded-md w-full justify-center h-[56px]"
    >
      <div class="text-lg text-black w-max flex items-center text-center gap-2">
        <NuxtImg src="/icons/timer-orange-icon.svg" alt="timer icon" class="h-[20px] w-[20px]" />
        <span class="text-[16px]"> Selesaikan pesanan Anda segera. Pesanan akan dibatalkan otomatis dalam </span>
        <strong class="text-[#FF5A00] text-[16px] font-semibold">{{ formattedTime }}</strong>
      </div>
    </div>
  </div>
</template>
