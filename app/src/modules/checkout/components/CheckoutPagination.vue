<script lang="ts" setup>
import { ref } from 'vue';
import { CHECKOUT_PAGINATION_SIZE } from '../constants/checkout.constant';

// const cartStore = useCartStore();

const selectedColor = ref<string>('24');
</script>

<template>
  <section
    id="cart-pagination"
    class="w-full h-full relative inset-0 z-0 border-t border-b border-t-[#CED1D3] border-b-[#CED1D3] py-4"
  >
    <div class="flex justify-between">
      <div class="flex items-center">
        Menampilkan
        <span class="mx-2">
          <select
            id="color"
            v-model="selectedColor"
            class="block w-full px-1 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option v-for="item in CHECKOUT_PAGINATION_SIZE" :key="item" :value="item" selected>
              {{ item }}
            </option>
          </select>
        </span>
        <span>per halaman</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="text-md">1-24 dari 2.500</div>
        <div class="flex gap-2">
          <div>
            <button class="bg-white text-black h-7 w-7 cursor-pointer border text-lg">&lt;</button>
          </div>
          <div>
            <button class="bg-white text-black h-7 w-7 cursor-pointer border text-lg">></button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
