<script lang="ts" setup>
// Components
import CheckoutProductItem from './CheckoutProductItem.vue';
import OrderItemSkeleton from '../../order/components/OrderItemSkeleton.vue';

// interfaces
import type { ICheckoutProvided } from '../interfaces/checkout.interface';
import type { IOrderDetail } from '../../order/interfaces/order.interface';
// constants
import { CHECKOUT_PAGINATION_SIZE } from '../constants/checkout.constant';

/**
 * @description Injecting dependencies
 */
const { checkout_listData, checkout_listLoading, checkout_pagination, checkout_queryParams, checkout_isCustom } =
  inject<ICheckoutProvided>('checkout')!;

const onChangePerPage = (value: number) => {
  checkout_queryParams.per_page = value;
};
const onClickPrev = () => {
  checkout_queryParams.page = checkout_queryParams.page - 1;
};
const onClickNext = () => {
  checkout_queryParams.page = checkout_queryParams.page + 1;
};
const orderDetail = computed<IOrderDetail['order_detail']>(
  () => checkout_listData.value?.order_detail as IOrderDetail['order_detail'],
);
const cartQuantities = computed<number>(() => {
  if (orderDetail.value?.items) {
    return orderDetail.value?.items.reduce((total, product) => {
      const productQtySum = product.product_items.reduce((itemTotal, item) => {
        return itemTotal + item.qty;
      }, 0);

      return total + productQtySum;
    }, 0);
  }
  return 0;
});
</script>

<template>
  <section id="checkout-list-section" class="w-full h-full relative inset-0 z-0">
    <section v-if="!checkout_listLoading && checkout_listData" id="checkout-list-header">
      <h4 v-if="checkout_isCustom" class="!text-[#686F72] text-base sm:text-[20px] font-medium mb-6">
        Custom Order
      </h4>
      <h4 v-else class="!text-[#686F72] text-base sm:text-[20px] font-medium mb-6">Reguler Order</h4>
    </section>
    <div class="w-full">
      <section id="product-list-cart">
        <div class="flex flex-col gap-4">
          <template v-if="checkout_listLoading">
            <div v-for="(_, index) in 3" :key="String(index)" class="w-full">
              <OrderItemSkeleton />
            </div>
          </template>
          <template v-if="checkout_listData && checkout_listData?.order_detail?.items?.length > 0">
            <div
              v-for="(item, index) in checkout_listData?.order_detail?.items"
              :key="String(index)"
              class="w-full"
            >
              <CheckoutProductItem :item="item" :index="index" />
            </div>
          </template>
          <div v-else>No items</div>
        </div>
      </section>
      <div class="border border-[#E5E6E8] rounded-lg px-5 py-6 mb-4 sm:hidden block">
        <div class="flex w-full items-center justify-between mb-3">
          <p class="text-black font-bold">Ringkasan Pesanan</p>
        </div>
        <p class="mb-2">Produk</p>
        <div class="flex flex-col gap-2 ml-3">
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Jumlah Produk</p>
              </div>
              <p class="text-sm text-black text-right">
                {{ cartQuantities ? Number(cartQuantities) : '-' }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Subtotal</p>
              </div>
              <p class="text-sm text-black text-right">
                {{ useCurrencyFormat(orderDetail?.total) }}
              </p>
            </div>
          </div>
        </div>
        <div class="border-b border-b-[#ACB1B4] my-5"></div>
        <div class="mb-2">
          <div class="grid grid-cols-[100px_1fr]">
            <p class="text-[12px] uppercase font-bold">Total Harga</p>
            <p class="text-[20px] font-semibold text-black text-right">
              {{ useCurrencyFormat(orderDetail?.total) }}
            </p>
          </div>
        </div>
      </div>
      <BasePagination
        :data="checkout_pagination"
        :click-action-prev="onClickPrev"
        :click-action-next="onClickNext"
        :click-action-per-page="onChangePerPage"
        :sizes="CHECKOUT_PAGINATION_SIZE"
      />
    </div>
  </section>
</template>
