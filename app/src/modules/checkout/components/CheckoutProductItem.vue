<script setup lang="ts">
// components
import CheckoutProductVariantTable from './CheckoutProductVariantTable.vue';
import type { IOrderDetailItem } from '../../order/interfaces/order.interface';
import type { ICheckoutProvided } from '../interfaces/checkout.interface';
import { capitalizeFirstLetter } from '~/app/src/core/helpers/text.helper';
import type { ICartProvided } from '../../cart/interfaces/cart.interface';

interface Props {
  item: IOrderDetailItem;
  index: number;
}

const props = defineProps<Props>();
const config = useRuntimeConfig();

const FALLBACK_PRODUCT_IMAGE = 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';

/**
 * @description Injecting dependencies
 */
const { checkout_isCustom } = inject<ICheckoutProvided>('checkout')!;
const { cart_isShowModalFormRemark, cart_remarkForm } = inject<ICartProvided>('cart')!;

const filteredUniqueVariant = computed(() => {
  return props.item.product_items?.filter(
    (item, index, self) => index === self.findIndex(t => t.product_variant === item.product_variant),
  );
});

const selectedTab = ref(props.item?.product_items?.[0]?.product_variant ?? null);

const filteredVariants = computed(() => {
  return props.item.product_items?.filter(i => i.product_variant === selectedTab.value);
});

const customProductName = computed<string[]>(() => {
  const _customName = [];
  const imageAttachments = props?.item.cart_attachments?.filter(t => t.file_path);
  const textAttachments = props?.item?.cart_attachments?.filter(t => t.text);

  if (imageAttachments?.length > 0) {
    _customName.push('Logo');
  }

  if (textAttachments?.length > 0) {
    _customName.push('Text');
  }

  return _customName;
});

const estimatePriceLogo = computed<number>(() => {
  let customPrice = 0;
  const imageAttachments = props?.item.cart_attachments?.filter(t => t.file_path);
  if (imageAttachments?.length > 0) {
    const imagePrice = imageAttachments.reduce((total, item) => {
      return total + item.estimate_price;
    }, 0);
    const quantities = props?.item?.product_items.reduce((total, item) => {
      return total + item.qty;
    }, 0);
    customPrice += imagePrice * quantities;
  }
  return customPrice;
});

const estimateTextPrice = computed<number>(() => {
  let customPrice = 0;
  const textAttachments = props?.item.cart_attachments?.filter(t => t.text);
  if (textAttachments?.length > 0) {
    const textPrice = textAttachments.reduce((total, item) => {
      return total + item.estimate_price;
    }, 0);
    const quantities = props?.item?.product_items.reduce((total, item) => {
      return total + item.qty;
    }, 0);
    customPrice += textPrice * quantities;
  }
  return customPrice;
});

const onClickAddRemark = (attachmentGroupId: string, prevRemark?: string) => {
  if (attachmentGroupId) {
    cart_isShowModalFormRemark.value = true;
    cart_remarkForm.attachment_group_id = attachmentGroupId;
    cart_remarkForm.remark = prevRemark as string;
  }
};

const onClickViewAttachments = (url: string) => {
  if (url) {
    const html = `
      <!DOCTYPE html>
      <html lang="en"> 
        <head>
          <meta charset="UTF-8" />
          <title>Preview</title>
          <style>
            body {
              margin: 0;
              background-color: #000;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
            }
            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          </style>
        </head>
        <body> 
          <img src="${url}" alt="Preview Image" />
        </body>
      </html>
    `;

    const blob = new Blob([html], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);
    window.open(blobUrl, '_blank');
  }
};

// filteredUniqueVariant.value[0].product_variant
watch([() => props.item.product_items[0]], value => {
  console.log('props.item.product_items[0', value);
});
</script>

<template>
  <div class="w-full border-b border-[#E5E6E8] mb-4">
    <div class="w-full grid sm:grid-cols-[168px_1fr] grid-cols-[10fr_80fr] gap-4 mb-3">
      <div class="flex items-start">
        <div class="sm:w-[159px] sm:h-[159px] w-[64px] h-[64px] bg-grey-200">
          <NuxtLink>
            <NuxtImg
              :src="item.image_url ?? FALLBACK_PRODUCT_IMAGE"
              alt="product"
              class="w-full h-full object-cover"
            />
          </NuxtLink>
        </div>
      </div>
      <div class="w-full">
        <div class="flex">
          <NuxtLink>
            <h4 class="font-bold font-druk text-lg md:text-[28px] leading-none mb-1">
              {{ item?.product_items?.[0]?.product_name ?? '-' }}
            </h4>
          </NuxtLink>
          <PrimeVueButton
            v-if="checkout_isCustom"
            variant="outlined"
            class="!border-[#ACB1B4] border !h-[37px] ml-auto"
            @click="() => onClickAddRemark(item.attachment_group_id, item.remark as string)"
          >
            <img src="/icons/edit-icon.svg" alt="" />
            <span class="!text-[#18191A] font-medium">Buat Catatan</span>
          </PrimeVueButton>
        </div>
        <div class="flex items-center gap-3 mb-1">
          <template v-for="(flag, index) in item?.flag" :key="`progress-${index}`">
            <div v-if="index !== 0" class="h-[4px] w-[4px] rounded-full bg-muted"></div>
            <p class="text-[14px] text-muted">{{ capitalizeFirstLetter(flag) }}</p>
          </template>
        </div>
        <div class="mb-2">
          <div v-if="checkout_isCustom" class="text-[14px]">
            <!-- <span class="text-[#686F72]"> Custom : </span>
            <span class="font-medium"> {{ customProductName?.toString() }} </span> -->
          </div>
          <div v-else class="text-[14px]">
            <span class="text-[#686F72]">Warna : </span> <span class="font-medium"> {{ selectedTab }} </span>
          </div>
        </div>

        <template v-if="checkout_isCustom">
          <div class="text-[14px]">
            <div class="grid grid-cols-[110px_1fr] mt-2">
              <p class="text-[#18191A] font-medium">Logo Gambar</p>
              <div v-if="item.cart_attachments?.filter(t => t.file_path)?.length > 0" class="flex flex-wrap">
                <div
                  v-for="(image, index) in item.cart_attachments?.filter(t => t.file_path)"
                  :key="index"
                  class="flex gap-1 -mt-[4px]"
                >
                  <NuxtImg
                    :src="config.public.middlewareBaseUrl + '/stream/' + image.file_path"
                    class="h-[22px] md:h-[30px] w-auto"
                  />
                  <PrimeVueButton
                    variant="text"
                    class="!underline !bg-[transparent] !px-0 !py-0 !text-[#147FFF] mr-2"
                    @click="
                      () => onClickViewAttachments(config.public.middlewareBaseUrl + '/stream/' + image.file_path)
                    "
                    >View</PrimeVueButton
                  >
                </div>
              </div>
              <div v-else>-</div>
            </div>
            <div class="grid grid-cols-[110px_1fr] mt-2">
              <p class="text-[#18191A] font-medium">Logo Text</p>
              <div v-if="item.cart_attachments?.filter(t => t.text)?.length > 0" class="flex flex-wrap gap-2">
                <div
                  v-for="(textValue, index) in item.cart_attachments?.filter(t => t.text)"
                  :key="index"
                  class="flex gap-1 -mt-[4px]"
                >
                  <NuxtImg class="h-[20px] w-[20px]" src="/icons/text-icon.svg" />
                  <div class="flex items-center flex-wrap gap-1 mr-2">
                    <p class="text-[14px] text-muted">{{ textValue.text }}</p>
                    <div class="h-[4px] w-[4px] rounded-full bg-muted"></div>
                    <p class="text-[14px] text-muted">{{ textValue.color }}</p>
                  </div>
                </div>
              </div>
              <div v-else>-</div>
            </div>
          </div>
        </template>

        <template v-if="checkout_isCustom">
          <div class="sm:flex w-full items-center justify-between mb-1 mt-2 hidden">
            <p class="text-[14px] text-[#686F72]">Harga Logo (Gambar)</p>
            <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold">
              {{ estimatePriceLogo ? useCurrencyFormat(estimatePriceLogo) : '-' }}
            </p>
          </div>
          <div class="sm:flex w-full items-center justify-between mb-1 hidden">
            <p class="text-[14px] text-[#686F72]">Harga Logo (Text)</p>
            <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold">
              {{ estimateTextPrice ? useCurrencyFormat(estimateTextPrice) : '-' }}
            </p>
          </div>
        </template>

        <div class="flex w-full items-center justify-between mb-2">
          <p class="font-medium">Total Harga</p>
          <p class="font-bold text-lg" :data-testid="`cart-product-item-total-${index}`">
            {{ item.sub_total ? useCurrencyFormat(item.sub_total) : '-' }}
          </p>
        </div>

        <div class="w-full">
          <div class="sm:block hidden">
            <PrimeVueTabs v-model:value="selectedTab" class="pt-3">
              <PrimeVueTabList
                class="w-fit"
                :pt="{
                  activeBar: '!bg-header-orange',
                }"
              >
                <PrimeVueTab
                  v-for="(variant, variantIndex) in filteredUniqueVariant"
                  :key="`variant-${variantIndex}`"
                  :value="variant.product_variant ?? null"
                  :pt="{
                    root: `text-sm ${useActiveTab(selectedTab, variant.article)}`,
                  }"
                >
                  {{ useCapitalize(variant?.product_variant ?? '-') }}
                </PrimeVueTab>
              </PrimeVueTabList>
              <PrimeVueTabPanels class="!px-0 !pt-4">
                <PrimeVueTabPanel
                  v-for="(tab, tabIndex) in filteredUniqueVariant"
                  :key="`tab-panel-${tabIndex}`"
                  :value="tab.product_variant ?? null"
                >
                  <CheckoutProductVariantTable :data="filteredVariants" :base-price="item.base_price" />
                </PrimeVueTabPanel>
              </PrimeVueTabPanels>
            </PrimeVueTabs>
          </div>
          <div
            v-for="(items, variantIndex) in filteredVariants"
            :key="String(variantIndex)"
            class="sm:hidden block"
          >
            <div class="w-full flex flex-col gap-4 mt-4">
              <div class="flex gap-2 text-[13px]">
                <div class="">Size :</div>
                <div class="">{{ items.product_size }}</div>
                <div class="">·</div>
                <div class="">Stock :</div>
                <div class="">
                  <span :data-testid="`checkout-product-item-stock-${index}-${variantIndex}`">
                    {{ items.stock }}
                  </span>
                </div>
              </div>
              <div class="flex justify-between">
                <div class="text-[14px]">
                  <span :data-testid="`checkout-product-item-qty-${index}-${variantIndex}`">
                    {{ items.qty }}
                  </span>
                </div>
                <div class="text-[16px] font-medium">
                  <span :data-testid="`checkout-product-item-sub-total-${index}-${variantIndex}`">
                    Rp. {{ useCurrencyFormat(items.sub_total) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
:deep(.p-panel-header) {
  position: relative;
  padding: 0 !important;
}
:deep(.p-panel-header) .p-panel-header-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
:deep(.p-panel-header) .p-panel-header-actions .p-button {
  width: 100%;
  height: 100%;
  border-radius: 0 !important;
  background-color: transparent !important;
}
:deep(.p-panel-content) {
  padding: 0 !important;
}
</style>
