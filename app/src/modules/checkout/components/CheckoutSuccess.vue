<script lang="ts" setup>
import type { IOrderDetail } from '../../order/interfaces/order.interface';
import type { ICheckoutProvided } from '../interfaces/checkout.interface';

/**
 * @description Injecting dependencies
 */
const { checkout_listData, checkout_isCustom } = inject<ICheckoutProvided>('checkout')!;

const toast = useToast();
const router = useRouter();

const orderDetail = computed<IOrderDetail['order_detail']>(
  () => checkout_listData.value?.order_detail as IOrderDetail['order_detail'],
);

const onClickBack = () => {
  router.push({
    name: 'catalog',
  });
};

const onClickCheckStatusOrder = () => {
  let ORDER_ID: string = '';
  if (orderDetail.value?.order_no?.length === 1) {
    ORDER_ID = orderDetail.value?.order_no[0];
  } else if (typeof orderDetail.value?.order_no === 'string') {
    ORDER_ID = String(orderDetail.value?.order_no);
  }
  router.push({
    name: 'order.detail',
    params: {
      orderNumber: ORDER_ID,
    },
  });
};

const onClickSeeOrderList = () => {
  router.push({
    name: 'order',
  });
};

const onClickCopy = (waybill: string) => {
  if (waybill) {
    navigator.clipboard.writeText(waybill);
    toast.add({
      severity: 'success',
      summary: 'Sukses',
      detail: 'Kode Pesanan di salin ke clipboard',
      life: 3000,
    });
  }
};

const orderQuantities = computed<number>(() => {
  if (orderDetail.value?.items) {
    return orderDetail.value?.items.reduce((total, product) => {
      const productQtySum = product.product_items.reduce((itemTotal, item) => {
        return itemTotal + item.qty;
      }, 0);

      return total + productQtySum;
    }, 0);
  }
  return 0;
});

const totalCustomPrice = computed(() => {
  return checkout_listData.value?.order_detail?.items?.[0].customs?.reduce(
    (sum, item) => sum + item.harga_satuan,
    0,
  );
});
</script>

<template>
  <div v-if="checkout_listData" class="flex items-center justify-center h-full md:mx-auto my-10">
    <div class="md:shadow-xl rounded-xl bg-white w-full sm:w-[500px] border border-[#f7f7f7]">
      <!-- Card Header -->
      <div class="py-4 text-center px-5 justify-items-center mb-3">
        <div class="h-[100px] w-[100px] my-5">
          <NuxtImg src="/icons/icon-success.svg" alt="warning-icon" class="h-full w-full" />
        </div>
        <h1 class="font-bold text-[20px] text-black">
          Pesanan
          {{ Array.isArray(orderDetail?.order_no) ? orderDetail?.order_no?.join(', ') : orderDetail?.order_no }}
          berhasil dibuat
        </h1>
        <!-- <p class="text-[14px] text-[#686F72] mt-2">
          Terima kasih! Tim kami akan segera memeriksa pesanan Anda. Jika ada yang ingin ditanyakan hubungi kami
          <a
            href="https://api.whatsapp.com/send/?phone=085721909233&text&type=phone_number&app_absent=0"
            target="_blank"
            class="underline text-[#147FFF] cursor-pointer"
          >
            di sini
          </a>
        </p> -->
      </div>

      <div class="py-4 px-5">
        <div>
          <div class="grid grid-cols-1 sm:grid-cols-2 mb-3 gap-3">
            <div class="">
              <p class="text-[14px] text-[#686F72]">Nama Pemesan</p>
              <p class="text-14px text-black font-semibold">
                {{ checkout_listData?.order_detail?.bill_to }}
              </p>
            </div>
            <div class="">
              <p class="text-[14px] text-[#686F72]">Nama Perusahaan</p>
              <p class="text-14px text-black font-semibold">
                {{ checkout_listData?.order_detail?.company_name }}
              </p>
            </div>
          </div>
          <div>
            <div class="">
              <p class="text-[14px] text-[#686F72]">Alamat Penerima</p>
              <p class="text-14px text-black font-semibold">
                {{ checkout_listData?.order_detail?.ship_to_address }}
              </p>
            </div>
          </div>
        </div>
        <div class="border-b mt-6 border-b-[#CDD3DA]" />
      </div>

      <div class="py-4 px-5">
        <div class="grid grid-cols-1 md:grid-cols-1 gap-x-4 gap-y-3 mb-5">
          <p class="text-[16px] font-semibold text-[#15130F]">Pesanan</p>
          <div class="grid grid-cols-[1fr_150px] sm:grid-cols-[1fr_200px]">
            <p class="text-[14px]">
              Jumlah Order <span class="text-[14px] text-[#686F72]">({{ orderQuantities }} Barang)</span>
            </p>
            <p class="text-14px text-black text-right font-semibold">
              {{ orderDetail?.total ? useCurrencyFormat(orderDetail?.total) : '-' }}
            </p>
          </div>
          <div v-if="checkout_isCustom" class="grid grid-cols-[1fr_150px] sm:grid-cols-[1fr_200px]">
            <div class="">
              <p class="text-[14px]">Estimasi Biaya Kustom</p>
            </div>
            <p class="text-14px text-black text-right font-semibold">
              {{ orderDetail?.total_kustomisasi ? useCurrencyFormat(orderDetail?.total_kustomisasi) : '-' }}
            </p>
          </div>
          <div class="grid grid-cols-[1fr_150px] sm:grid-cols-[1fr_200px]">
            <div class="">
              <p class="text-[14px]">Diskon</p>
            </div>
            <p class="text-14px text-black text-right font-semibold">
              -{{ useCurrencyFormat(orderDetail?.total_discount ?? 0) }}
            </p>
          </div>
          <div class="grid grid-cols-[1fr_150px] sm:grid-cols-[1fr_272px]">
            <div class="">
              <p class="text-[12px] uppercase font-bold">
                {{ checkout_isCustom ? 'ESTIMASI HARGA' : 'TOTAL HARGA' }}
              </p>
            </div>
            <p class="text-[20px] sm:text-[28px] text-[#18191A] text-right font-semibold font-druk">
              {{ orderDetail?.sub_total ? useCurrencyFormat(orderDetail?.sub_total) : '-' }}
            </p>
          </div>
        </div>
      </div>

      <div class="flex flex-col gap-4 px-6 w-full mb-6">
        <PrimeVueButton
          v-if="Array.isArray(orderDetail?.order_no) && orderDetail.order_no?.length > 1"
          class="!bg-[#18191A] !border-[#18191A] !text-white !py-2 !px-5 !rounded-md w-full"
          @click="onClickSeeOrderList"
        >
          Lihat Status Pesanan
        </PrimeVueButton>
        <PrimeVueButton
          v-else
          class="!bg-[#18191A] !border-[#18191A] !text-white !py-2 !px-5 !rounded-md w-full"
          @click="onClickCheckStatusOrder"
        >
          Lihat Status Pesanan
        </PrimeVueButton>
        <PrimeVueButton
          class="!bg-white !border !border-[#ACB1B4] !text-gray-800 !py-2 !px-5 !rounded-md w-full"
          @click="onClickBack"
        >
          Kembali ke katalog
        </PrimeVueButton>
      </div>
    </div>
  </div>
</template>
