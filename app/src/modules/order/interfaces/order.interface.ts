import type { Validation } from '@vuelidate/core';
import type { Reactive, Ref } from 'vue';

export interface IOrderProgress {
  label: string;
  value: string | null;
  isDone: boolean;
  isNextStep: boolean;
  isCancel: boolean;
}

export interface IQueryParamsGetOrderList {
  page: number;
  per_page: number;
  status: string;
  type: string;
  text: string | null;
  date_from: string | null;
  date_to: string | null;
}

export interface IQueryParamsGetDetailOrder {
  is_available: boolean;
  page: number;
  per_page: number;
}

export interface IOrderUploadFilePayload {
  payment_file: File | null;
}

export interface IOrderProductItem {
  article: string;
  product_name: string;
  product_variant: string;
  product_size: string;
  issued_qty: number;
  stock: number;
  qty: number;
  sub_total: number;
}

export interface IOrderCartAttachment {
  id: string;
  attachments_group_id: string;
  file_path: string | null;
  text: string | null;
  color: string;
  estimate_price: number;
}

export interface IOrderDetailItem {
  sku_code_c: string;
  product_name_c: string;
  image_url: string;
  flag: string[];
  base_price: string;
  sub_total: number;
  is_custom: boolean;
  stock: number;
  custom_type: string;
  product_items: IOrderProductItem[];
  cart_attachments: IOrderCartAttachment[];
  attachment_group_id: string;
  remark: string | null;
  customs: {
    harga_satuan: number;
  }[];
}

export interface IOrderRatingPayload {
  order_no: string;
  rating_value: number;
  comment: string;
}

export interface IOrderCustomerShipment {
  customer_shipment_id: string;
  customer_id: string;
  name: string;
  address: string;
  city: string;
  province: string;
  district: string;
  zip_code: string;
  shipment_type: string;
  phone_number: string;
}

export interface IOrderDetail {
  total_data: number;
  size: number;
  active_page: number;
  total_page: number;
  order_detail: {
    is_confirm: boolean;
    order_group_id: string;
    payment_date: string;
    sap_response: string | null;
    order_no: string[] | string;
    customer_shipment_id: string;
    sub_total: number;
    total_discount: number;
    total_discount_pesanan_percentage: number;
    total: number;
    total_nett: number;
    total_dp: number;
    dp_percentage: number;
    company_name: string;
    customer_id: number;
    sales_order_no: string;
    delivery_number: string;
    transaction_date: string;
    pembayaran_date: string;
    pesanan_diproses_date: string;
    gi_date: string;
    shipping_date: string;
    received_date: string;
    pesanan_diterima_date: string;
    invoice_no: string;
    order_summary: string;
    no_resi: string;
    bill_to: string;
    bill_to_address: string;
    bill_to_phone_number: string;
    bill_to_email: string;
    ship_to: string;
    ship_to_address: string;
    ship_to_phone_number: string;
    distribution_channel: string;
    verifikasi_date: string;
    transportation_zone: string;
    pesanan_dibatalkan_date: null;
    data_limit: [];
    items: IOrderDetailItem[];
    customer_shipment: IOrderCustomerShipment[];
    instance_name: string;
    company_email: string;
    available: number;
    unavailable: number;
    ongkos_kirim: number;
    pajak: number;
    is_custom: boolean;
    total_available: number;
    total_unavailable: number;
    total_qty: number;
    total_kustomisasi: number;
    payment_file: string;
    order_status: string;
    payment_status: string;
  };
}

export interface IOrderListItem {
  nama_produk: string;
  image: string;
  order_no: string;
  order_group_id: string;
  sales_order_no: number;
  customer_id: number;
  sub_total: number;
  total_discount: number;
  total_pesanan: number;
  customer_shipment_id: number;
  delivery_number: string;
  gi_date: string;
  total: number;
  transaction_date: string;
  order_status: string;
  inovice_no: string;
  order_date: string;
  total_produk: number;
  custom_type: string;
  cart_attachments: ICartAttachment[];
}

export interface IListOrder extends IPaginationResponse {
  data: IOrderListItem[];
}

export interface IPayloadCancelOrder {
  order_no: string[];
  customer_shipment_id: string;
}

export interface IOrderFormInquiryPayload {
  type: string;
  full_name: string;
  no_telp: string;
  instituion_name: string;
  email: string;
  npwp_address: string;
  topic: string;
  question: string;
}

export interface IOrderProvided {
  order_fetchOrderList: (params?: Partial<IQueryParamsGetOrderList>) => Promise<void>;
  order_getOrderDetail: (orderNumber: string) => Promise<void>;
  order_cancelOrder: (body: IPayloadCancelOrder) => Promise<void>;
  order_receiptOrder: (orderNumber: string) => Promise<void>;
  order_submitRating: (payload: IOrderRatingPayload) => Promise<unknown>;
  order_uploadPaymentFile: (orderNumber: string) => Promise<void>;
  order_listOrderActiveTab: Ref<unknown>;
  order_listOrderLoading: Ref<boolean>;
  order_listOrderData: Ref<IOrderListItem[]>;
  order_listOrderPagination: Ref<IPaginationResponse>;
  order_listOrderQueryParams: Reactive<IQueryParamsGetOrderList>;
  order_detailOrderQueryParams: Reactive<IQueryParamsGetDetailOrder>;
  order_detailData: Ref<IOrderDetail | null>;
  order_detailIsLoading: Ref<boolean>;
  order_formInquiry: IOrderFormInquiryPayload;
  order_formValidations: globalThis.Ref<Validation>;
  order_isCustom: ComputedRef<boolean>;
  order_uploadFile: Reactive<IOrderUploadFilePayload>;
  order_paymentformValidations: globalThis.Ref<Validation>;
  order_deletePaymentFile: (orderNumber: string) => Promise<void>;
}

export interface IStateOrderStore {
  order_listOrderLoading: boolean;
  order_listOrderData: IOrderListItem[];
  order_listOrderPagination: IPaginationResponse;
  order_detailData: IOrderDetail | null;
  order_detailIsLoading: boolean;
}
