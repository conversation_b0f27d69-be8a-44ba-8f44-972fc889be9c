import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useOrderStore } from '../order.store';

describe('Order store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useOrderStore();

      expect(store.order_listOrderLoading).toBe(false);
      expect(store.order_listOrderData).toMatchObject([]);
      expect(store.order_listOrderPagination).toMatchObject({
        total_data: 0,
        size: 12,
        active_page: 0,
        total_page: 0,
      });
      expect(store.order_detailData).toBeNull();
      expect(store.order_detailIsLoading).toBe(false);
    });

    it('has correct initial computed state', () => {
      const store = useOrderStore();
      expect(store.order_listOrderLoading).toBe(false);
      expect(store.order_detailIsLoading).toBe(false);
    });
  });

  it('Order list data should be empty', () => {
    const store = useOrderStore();
    expect(store.order_listOrderData).toMatchObject([]);
  });

  it('Order detail data should be null', () => {
    const store = useOrderStore();
    expect(store.order_detailData).toBeNull();
  });
});
