// Constants
import { ORDER_STATUS_TABS } from '../constants';
import {
  ORDER_ENDPOINT_GET_CANCEL,
  ORDER_ENDPOINT_GET_LIST_B2B,
  ORDER_ENDPOINT_RATING,
  ORDER_ENDPOINT_RECEIPT,
  UPLOAD_FILE,
} from '../constants/order.api.constant';

// Interfaces
import type {
  IOrderDetail,
  IOrderRatingPayload,
  IOrderUploadFilePayload,
  IPayloadCancelOrder,
  IQueryParamsGetDetailOrder,
  IQueryParamsGetOrderList,
  IStateOrderStore,
} from '../interfaces/order.interface';
// import JSON_DATA from './data.json';

// Axios
import axios from 'axios';

// Pinia
import { defineStore } from 'pinia';

export const useOrderStore = defineStore('order', {
  state: (): IStateOrderStore => ({
    order_listOrderLoading: false,
    order_listOrderData: [],
    order_listOrderPagination: {
      total_data: 0,
      size: 12,
      active_page: 0,
      total_page: 0,
    },
    order_detailData: null,
    order_detailIsLoading: false,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get order list.
     * @url /transaction
     * @method GET
     * @access private
     */
    async fetchOrder_getOrderList(requestParams: IQueryParamsGetOrderList): Promise<unknown> {
      try {
        this.order_listOrderLoading = true;
        this.order_listOrderData = [];
        this.order_listOrderPagination = {
          total_data: 0,
          size: 0,
          active_page: 0,
          total_page: 0,
        };
        const params = {
          ...requestParams,
          status: requestParams.status === ORDER_STATUS_TABS[0].value ? '' : requestParams.status,
        };
        const { data, error } = await useApiFetch(ORDER_ENDPOINT_GET_LIST_B2B, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            this.order_listOrderData = response._data.data?.data ?? [];
            this.order_listOrderPagination = {
              total_data: response._data.data?.total_data ?? 0,
              size: response._data.data?.size ?? 0,
              active_page: response._data.data?.active_page ?? 0,
              total_page: response._data.data?.total_page ?? 0,
            };
          },
          onRequestError: () => {
            this.order_listOrderData = [];
            this.order_listOrderPagination = {
              total_data: 0,
              size: 0,
              active_page: 0,
              total_page: 0,
            };
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.order_listOrderLoading = false;
      }
    },

    /**
     * @description Handle fetch api get order list.
     * @url /transaction
     * @method GET
     * @access private
     */
    async fetchOrder_getOrderDetail(
      orderNumber: string,
      requestParams: IQueryParamsGetDetailOrder,
      loading: boolean = true,
    ): Promise<unknown> {
      try {
        if (loading) {
          this.order_detailIsLoading = true;
        }
        const params = {
          ...requestParams,
          limit: requestParams.per_page,
        };
        const { data, error } = await useApiFetch(`${ORDER_ENDPOINT_GET_LIST_B2B}/${orderNumber}`, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            const result: IOrderDetail = response._data.data;
            this.order_detailData = result;
          },
        });

        if (error.value) {
          this.order_detailData = null;
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.order_detailIsLoading = false;
      }
    },

    /**
     * @description Handle fetch cancel order.
     * @url /transaction
     * @method GET
     * @access private
     */
    async fetchOrder_cancelOrder(body: IPayloadCancelOrder): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${ORDER_ENDPOINT_GET_CANCEL}`, {
          method: 'POST',
          body,
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch receipt order.
     * @url /transaction/receive
     * @method POST
     * @access private
     */
    async fetchOrder_receiptOrder(orderNumber: string): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${ORDER_ENDPOINT_RECEIPT}`, {
          method: 'POST',
          body: {
            order_no: orderNumber,
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle upload s3
     * @method PUT
     * @access public
     */
    async fetchOrder_uploadS3(file: File, s3Url: string): Promise<unknown> {
      try {
        const response = await axios.put(s3Url, file, {
          headers: {
            'Content-Type': 'application/octet-stream',
          },
        });
        return response;
      } catch (error) {
        // Handle any errors that occur during the fetch
        console.error('Error upload file:', error);
        throw error;
      }
    },

    /**
     * @description Handle upload payment file.
     * @url /b2b/transaction/{order-no}/upload-payment
     * @method POST
     * @access public
     */
    async fetchOrder_uploadPaymentFile(order_no: string, filePath: string): Promise<unknown> {
      try {
        this.order_detailIsLoading = true;

        const body = {
          payment_file: filePath,
        };

        const { data, error } = await useApiFetch(`${ORDER_ENDPOINT_GET_LIST_B2B}/${order_no}/upload-payment`, {
          method: 'POST',
          body: body,
          onResponse: async ({ response }) => {
            console.log('response of test->', response);
          },
        });

        if (error.value) {
          throw new Error(error.value.message);
        }
        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(error as string);
      } finally {
        this.order_detailIsLoading = false;
      }
    },

    /**
     * @description Handle upload payment file.
     * @url /b2b/transaction/{order-no}/upload-payment
     * @method POST
     * @access public
     */
    async fetchOrder_uploadFile(
      order_no: string,
      customer_id: string,
      payload: IOrderUploadFilePayload,
    ): Promise<unknown> {
      try {
        this.order_detailIsLoading = true;

        const body = {
          name: `payment-dev/${customer_id}/${(payload.payment_file as File).name}`,
        };

        const { data, error } = await useApiFetch(`${UPLOAD_FILE}`, {
          method: 'POST',
          body: body,
          onResponse: async ({ response }) => {
            if (response?._data?.data?.s3_url) {
              await this.fetchOrder_uploadS3(payload.payment_file as File, response?._data?.data?.s3_url);
              await this.fetchOrder_uploadPaymentFile(order_no, response?._data?.data?.filepath);
            }
          },
        });

        if (error.value) {
          throw new Error(error.value.message);
        }
        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(error as string);
      } finally {
        this.order_detailIsLoading = false;
      }
    },

    /**
     * @description Handle upload payment file.
     * @url /b2b/transaction/{order-no}/upload-payment
     * @method DELETE
     * @access public
     */
    async fetchOrder_deletePaymentFile(order_no: string): Promise<unknown> {
      try {
        this.order_detailIsLoading = true;

        const { data, error } = await useApiFetch(`${ORDER_ENDPOINT_GET_LIST_B2B}/${order_no}/delete-payment`, {
          method: 'DELETE',
        });

        if (error.value) {
          throw new Error(error.value.message);
        }
        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(error as string);
      } finally {
        this.order_detailIsLoading = false;
      }
    },

    /**
     * @description Handle fetch rating
     * @url /rating
     * @method POST
     * @access private
     */
    async fetchOrder_submitRating(body: IOrderRatingPayload): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(`${ORDER_ENDPOINT_RATING}`, {
          method: 'POST',
          body,
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },
  },
});
