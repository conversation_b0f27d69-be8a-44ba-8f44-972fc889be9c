<script setup lang="ts">
// components
import CheckoutProductVariantTable from './OrderProductVariantTable.vue';
import type { IOrderDetailItem, IOrderProvided } from '../interfaces/order.interface';
import { capitalizeFirstLetter } from '~/app/src/core/helpers/text.helper';

interface Props {
  item: IOrderDetailItem;
  isCustom: boolean;
}

const props = defineProps<Props>();
const config = useRuntimeConfig();

/**
 * @description Injecting dependencies
 */
const { order_isCustom } = inject<IOrderProvided>('order')!;

const FALLBACK_PRODUCT_IMAGE = 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';

const customProductName = computed<string[]>(() => {
  const _customName = [];
  const imageAttachments = props?.item?.cart_attachments?.filter(t => t.file_path);
  const textAttachments = props?.item?.cart_attachments?.filter(t => t.text);

  if (imageAttachments?.length > 0) {
    _customName.push('Logo');
  }

  if (textAttachments?.length > 0) {
    _customName.push('Text');
  }

  return _customName;
});

const selectedTab = ref(props.item?.product_items?.[0]?.product_variant ?? null);

const filteredUniqueVariant = computed(() => {
  return props.item.product_items?.filter(
    (item, index, self) => index === self.findIndex(t => t.product_variant === item.product_variant),
  );
});

const subtotalOrder = computed<number>(() => {
  if (props.item.product_items?.length > 0) {
    return props.item.product_items.reduce((itemTotal, item) => {
      return parseInt(itemTotal.toString()) + parseInt(item.sub_total.toString());
    }, 0);
  }
  return 0;
});

const filteredVariants = computed(() => {
  return props.item.product_items?.filter(i => i.product_variant === selectedTab.value);
});

const estimatePriceLogo = computed<number>(() => {
  let customPrice = 0;
  const imageAttachments = props?.item.cart_attachments?.filter(t => t.file_path);
  if (imageAttachments?.length > 0) {
    const imagePrice = imageAttachments.reduce((total, item) => {
      return total + item.estimate_price;
    }, 0);
    const quantities = props?.item?.product_items.reduce((total, item) => {
      return total + item.qty;
    }, 0);
    customPrice += imagePrice * quantities;
  }
  return customPrice;
});

const estimateTextPrice = computed<number>(() => {
  let customPrice = 0;
  const textAttachments = props?.item.cart_attachments?.filter(t => t.text);
  if (textAttachments?.length > 0) {
    const textPrice = textAttachments.reduce((total, item) => {
      return total + item.estimate_price;
    }, 0);
    const quantities = props?.item?.product_items.reduce((total, item) => {
      return total + item.qty;
    }, 0);
    customPrice += textPrice * quantities;
  }
  return customPrice;
});

const onClickViewAttachments = (url: string) => {
  if (url) {
    const html = `
      <!DOCTYPE html>
      <html lang="en"> 
        <head>
          <meta charset="UTF-8" />
          <title>Preview</title>
          <style>
            body {
              margin: 0;
              background-color: #000;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
            }
            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          </style>
        </head>
        <body> 
          <img src="${url}" alt="Preview Image" />
        </body>
      </html>
    `;

    const blob = new Blob([html], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);
    window.open(blobUrl, '_blank');
  }
};
</script>

<template>
  <div class="w-full">
    <div class="w-full grid grid-cols-[65px_1fr] sm:grid-cols-[168px_1fr] gap-4 mb-3">
      <div class="flex items-start">
        <div class="w-[64px] h-[64px] sm:w-[159px] sm:h-[159px] bg-grey-200">
          <NuxtLink>
            <NuxtImg
              :src="item.image_url ?? FALLBACK_PRODUCT_IMAGE"
              alt="product"
              class="w-full h-full object-cover"
            />
          </NuxtLink>
        </div>
      </div>

      <div class="w-full">
        <NuxtLink>
          <h4 class="font-bold font-druk text-lg md:text-[28px] leading-none mb-1">{{ item.product_name_c }}</h4>
        </NuxtLink>
        <div class="flex items-center gap-3 mb-3">
          <template v-for="(flag, index) in item?.flag" :key="`progress-${index}`">
            <div v-if="index !== 0" class="h-[4px] w-[4px] rounded-full bg-muted"></div>
            <p class="text-[14px] text-muted">{{ capitalizeFirstLetter(flag) }}</p>
          </template>
        </div>
        <div class="mb-3 flex sm:flex-col justify-between">
          <div v-if="!order_isCustom" class="flex gap-1 text-[14px]">
            <p class="text-[#686F72]">Warna:</p>
            <p class="capitalize">{{ selectedTab.toLowerCase() }}</p>
          </div>
          <!-- <div v-if="order_isCustom" class="flex gap-1 text-[14px]">
            <p class="text-[#686F72]">Custom:</p>
            <p class="capitalize">{{ customProductName?.toString() }}</p>
          </div> -->
          <template v-if="order_isCustom">
            <div class="text-[14px]">
              <div class="grid grid-cols-[110px_1fr] mt-2">
                <p class="text-[#18191A] font-medium">Logo Gambar</p>
                <div v-if="item.cart_attachments?.filter(t => t.file_path)?.length > 0" class="flex flex-wrap">
                  <div
                    v-for="(image, index) in item.cart_attachments?.filter(t => t.file_path)"
                    :key="index"
                    class="flex gap-1 -mt-[4px]"
                  >
                    <NuxtImg
                      :src="config.public.middlewareBaseUrl + '/stream/' + image.file_path"
                      class="h-[22px] md:h-[30px] w-auto"
                    />
                    <PrimeVueButton
                      variant="text"
                      class="!underline !bg-[transparent] !px-0 !py-0 !text-[#147FFF] mr-2"
                      @click="
                        () =>
                          onClickViewAttachments(config.public.middlewareBaseUrl + '/stream/' + image.file_path)
                      "
                      >View</PrimeVueButton
                    >
                  </div>
                </div>
                <div v-else>-</div>
              </div>
              <div class="grid grid-cols-[110px_1fr] mt-2">
                <p class="text-[#18191A] font-medium">Logo Text</p>
                <div v-if="item.cart_attachments?.filter(t => t.text)?.length > 0" class="flex flex-wrap gap-2">
                  <div
                    v-for="(textValue, index) in item.cart_attachments?.filter(t => t.text)"
                    :key="index"
                    class="flex gap-1 -mt-[4px]"
                  >
                    <NuxtImg class="h-[20px] w-[20px]" src="/icons/text-icon.svg" />
                    <div class="flex items-center flex-wrap gap-1 mr-2">
                      <p class="text-[14px] text-muted">{{ textValue.text }}</p>
                      <div class="h-[4px] w-[4px] rounded-full bg-muted"></div>
                      <p class="text-[14px] text-muted">{{ textValue.color }}</p>
                    </div>
                  </div>
                </div>
                <div v-else>-</div>
              </div>
            </div>
          </template>

          <template v-if="order_isCustom">
            <div class="sm:flex w-full items-center justify-between mb-1 mt-2 hidden">
              <p class="text-[14px] text-[#686F72]">Harga Logo (Gambar)</p>
              <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold">
                {{ estimatePriceLogo ? useCurrencyFormat(estimatePriceLogo) : '-' }}
              </p>
            </div>
            <div class="sm:flex w-full items-center justify-between mb-1 hidden">
              <p class="text-[14px] text-[#686F72]">Harga Logo (Text)</p>
              <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold">
                {{ estimateTextPrice ? useCurrencyFormat(estimateTextPrice) : '-' }}
              </p>
            </div>
          </template>
        </div>
        <div class="sm:flex w-full items-center justify-between mb-2">
          <p class="font-medium">{{ order_isCustom ? 'Estimasi Total Harga' : 'Total Harga' }}</p>
          <p class="font-bold text-lg">
            {{ subtotalOrder ? useCurrencyFormat(subtotalOrder) : '-' }}
          </p>
        </div>
        <div class="w-full">
          <PrimeVueTabs v-model:value="selectedTab">
            <PrimeVueTabList
              class="w-fit"
              :class="order_isCustom ? '!hidden' : '!flex'"
              :pt="{
                activeBar: '!bg-header-orange',
              }"
            >
              <PrimeVueTab
                v-for="(variant, variantIndex) in filteredUniqueVariant"
                :key="`variant-${variantIndex}`"
                :value="variant.product_variant ?? null"
                :pt="{
                  root: `text-sm ${useActiveTab(selectedTab, variant.article)}`,
                }"
              >
                {{ useCapitalize(variant?.product_variant ?? '-') }}
              </PrimeVueTab>
            </PrimeVueTabList>
            <PrimeVueTabPanels class="!px-0 !pt-4">
              <PrimeVueTabPanel
                v-for="(tab, tabIndex) in filteredUniqueVariant"
                :key="`tab-panel-${tabIndex}`"
                :value="tab.product_variant ?? null"
              >
                <CheckoutProductVariantTable :data="filteredVariants" :base-price="item.base_price" />
              </PrimeVueTabPanel>
            </PrimeVueTabPanels>
          </PrimeVueTabs>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
:deep(.p-panel-header) {
  position: relative;
  padding: 0 !important;
}
:deep(.p-panel-header) .p-panel-header-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
:deep(.p-panel-header) .p-panel-header-actions .p-button {
  width: 100%;
  height: 100%;
  border-radius: 0 !important;
  background-color: transparent !important;
}
:deep(.p-panel-content) {
  padding: 0 !important;
}
</style>
