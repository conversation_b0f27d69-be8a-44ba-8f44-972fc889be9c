<script setup lang="ts">
import type { IOrderDetail, IOrderProgress, IOrderProvided } from '../interfaces/order.interface';

/**
 * @description Injecting dependencies
 */
const { order_detailData } = inject<IOrderProvided>('order')!;

const ORDER_LABEL = {
  VERIFICATION: 'Verifikasi',
  CREATED: 'Pesanan Dibuat',
  PROCESSED: 'Pesanan Diproses',
  SHIPMENT: 'Pesanan Dikirim',
  RECEIVED: 'Pesanan Diterima',
  PAYMENT: 'Pembayaran',
  CANCEL: 'Batal',
};

const getIsDone = (label: string, order: IOrderDetail['order_detail']): boolean => {
  // add complexity logic to check order detail here.
  if (label === ORDER_LABEL.VERIFICATION) {
    return Boolean(order?.verifikasi_date);
  } else if (label === ORDER_LABEL.PROCESSED) {
    return Boolean(order?.pesanan_diproses_date);
  } else if (label === ORDER_LABEL.PAYMENT) {
    return Boolean(order?.pembayaran_date);
  } else if (label === ORDER_LABEL.SHIPMENT) {
    return Boolean(order?.gi_date);
  } else if (label === ORDER_LABEL.RECEIVED) {
    return Boolean(order?.pesanan_diterima_date);
  }
  return false;
};

const getIsNextStep = (label: string, order: IOrderDetail['order_detail']): boolean => {
  // add complexity logic to check order detail here.
  if (label === ORDER_LABEL.VERIFICATION) {
    // always set false for this status
    return true;
  } else if (label === ORDER_LABEL.PAYMENT) {
    return !!order?.verifikasi_date && !order?.pembayaran_date;
  } else if (label === ORDER_LABEL.PROCESSED) {
    return !!order?.pembayaran_date && !order?.pesanan_diproses_date;
  } else if (label === ORDER_LABEL.SHIPMENT) {
    return (
      Boolean(order?.verifikasi_date) && Boolean(order?.pesanan_diproses_date) && Boolean(order?.pembayaran_date)
    );
  } else if (label === ORDER_LABEL.RECEIVED) {
    return (
      Boolean(order?.verifikasi_date) && Boolean(order?.pesanan_diproses_date) && Boolean(order?.pembayaran_date)
    );
  }
  return false;
};

/**
 * References date:
 dibuat: summaryData.order_detail?.transaction_date,
 diproses: summaryData.order_detail?.pesanan_diproses_date,
 siapDikirim: summaryData.order_detail?.gi_date,
 pembayaran: summaryData.order_detail?.pembayaran_date,
 dikirim: summaryData.order_detail?.gi_date,
 diterima: summaryData.order_detail?.pesanan_diterima_date,
 */
const orderProgress = computed<IOrderProgress[]>(() => {
  // Common order progress steps for both store types
  const commonSteps = [
    {
      label: ORDER_LABEL.VERIFICATION,
      value: order_detailData.value?.order_detail.verifikasi_date as string,
      isDone: getIsDone(
        ORDER_LABEL.VERIFICATION,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isNextStep: getIsNextStep(
        ORDER_LABEL.VERIFICATION,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isCancel: order_detailData.value?.order_detail.order_status == 'Batal' ? true : false,
    },
    {
      label: ORDER_LABEL.PAYMENT,
      value: order_detailData.value?.order_detail.pembayaran_date as string,
      isDone: getIsDone(ORDER_LABEL.PAYMENT, order_detailData.value?.order_detail as IOrderDetail['order_detail']),
      isNextStep: getIsNextStep(
        ORDER_LABEL.PAYMENT,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isCancel: order_detailData.value?.order_detail.order_status == 'Batal' ? true : false,
    },
    {
      label: ORDER_LABEL.PROCESSED,
      value: order_detailData.value?.order_detail.pesanan_diproses_date as string,
      isDone: getIsDone(
        ORDER_LABEL.PROCESSED,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isNextStep: getIsNextStep(
        ORDER_LABEL.PROCESSED,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isCancel: order_detailData.value?.order_detail.order_status == 'Batal' ? true : false,
    },
    {
      label: ORDER_LABEL.SHIPMENT,
      value: order_detailData.value?.order_detail.gi_date as string,
      isDone: getIsDone(
        ORDER_LABEL.SHIPMENT,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isNextStep: getIsNextStep(
        ORDER_LABEL.SHIPMENT,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isCancel: order_detailData.value?.order_detail.order_status == 'Batal' ? true : false,
    },
    {
      label: ORDER_LABEL.RECEIVED,
      value: order_detailData.value?.order_detail.pesanan_diterima_date as string,
      isDone: getIsDone(
        ORDER_LABEL.RECEIVED,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isNextStep: getIsNextStep(
        ORDER_LABEL.RECEIVED,
        order_detailData.value?.order_detail as IOrderDetail['order_detail'],
      ),
      isCancel: order_detailData.value?.order_detail.order_status == 'Batal' ? true : false,
    },
  ];

  return commonSteps;
});

const getIcon = (status: IOrderProgress['label'], whiteIcon: boolean, isCancel: boolean) => {
  const baseIconPath = '/icons/orders/';
  if (status == 'Verifikasi' && isCancel) {
    return `${baseIconPath}/order-cancel-white.svg`;
  }
  if (status == 'Verifikasi') {
    return `${baseIconPath}/order-created-icon-white.svg`;
  }
  if (status == 'Pesanan Diproses' && isCancel) {
    return `${baseIconPath}/order-cancel-white.svg`;
  }
  if (status == 'Pesanan Diproses') {
    return whiteIcon
      ? `${baseIconPath}/order-processed-icon-white.svg`
      : `${baseIconPath}/order-processed-icon.svg`;
  }
  if (status == 'Pembayaran' && isCancel) {
    return `${baseIconPath}/order-cancel-white.svg`;
  }
  if (status == 'Pembayaran') {
    return whiteIcon ? `${baseIconPath}/order-payment-icon-white.svg` : `${baseIconPath}/order-payment-icon.svg`;
  }
  if (status == 'Pesanan Dikirim' && isCancel) {
    return `${baseIconPath}/order-cancel-white.svg`;
  }
  if (status == 'Pesanan Dikirim') {
    return whiteIcon ? `${baseIconPath}/order-shipped-icon-white.svg` : `${baseIconPath}/order-shipped-icon.svg`;
  }
  if (status == 'Pesanan Diterima' && isCancel) {
    return `${baseIconPath}/order-cancel-white.svg`;
  }
  if (status == 'Pesanan Diterima') {
    return whiteIcon ? `${baseIconPath}/order-received-icon-white.svg` : `${baseIconPath}/order-received-icon.svg`;
  }
  // switch (status) {
  //   case 'Pesanan Dibuat':
  //     return whiteIcon
  //       ? `${baseIconPath}/order-created-icon-white.svg`
  //       : `${baseIconPath}/order-created-icon.svg`;
  //   case `Pesanan Diproses`:
  //     return whiteIcon
  //       ? `${baseIconPath}/order-processed-icon-white.svg`
  //       : `${baseIconPath}/order-processed-icon.svg`;
  //   case `Pembayaran`:
  //     return whiteIcon
  //       ? `${baseIconPath}/order-payment-icon-white.svg`
  //       : `${baseIconPath}/order-payment-icon.svg`;
  //   case `Pesanan Dikirim`:
  //     return whiteIcon
  //       ? `${baseIconPath}/order-shipped-icon-white.svg`
  //       : `${baseIconPath}/order-shipped-icon.svg`;
  //   case `Pesanan Diterima`:
  //     return whiteIcon
  //       ? `${baseIconPath}/order-received-icon-white.svg`
  //       : `${baseIconPath}/order-received-icon.svg`;
  //   case `Batal`:
  //     return whiteIcon
  //       ? `${baseIconPath}/order-received-icon-white.svg`
  //       : `${baseIconPath}/order-received-icon.svg`;
  //   default:
  //     break;
  // }
};

const lastOrderValue = computed(() => {
  return orderProgress.value.filter(item => item.value).pop()?.value || undefined;
});
</script>

<template>
  <header v-if="order_detailData" id="order-progress" class="flex flex-col gap-4 bg-[#F5F6F6] mb-3">
    <div class="flex items-center justify-between gap-1 sm:gap-3 px-2 sm:px-4 md:px-8 py-3 sm:py-4 md:py-10">
      <div
        v-for="(item, index) in orderProgress"
        :key="String(index)"
        class="flex items-center"
        :class="index !== orderProgress?.length - 1 ? 'w-full' : 'w-auto'"
      >
        <div class="md:min-w-[140px]">
          <div
            class="w-[32px] h-[32px] flex items-center justify-center rounded-full border sm:mb-4 justify-self-center"
            :class="[
              item.isNextStep && !item.isDone && !item.isCancel && 'bg-[#FF5A00] text-white !border-[#FF5A00]',
              item.isNextStep && !item.isDone && item.isCancel && 'bg-[#E9151D] text-white !border-[#E9151D]',
              item.isDone && 'bg-[#05964C] text-white !border-[#05964C]',
              !item.isDone && !item.isNextStep && !item.isCancel && '!bg-transparent !border-[#ACB1B4]',
              !item.isDone && !item.isNextStep && item.isCancel && '!bg-[#E9151D] !border-[#E9151D]',
            ]"
          >
            <div class="progress-icon">
              <NuxtImg
                :src="
                  item.isDone
                    ? '/icons/check-icon-white.svg'
                    : getIcon(item.label, item.isDone || item.isNextStep, item.isCancel)
                "
                alt="cube"
                class="w-4 h-auto"
              />
            </div>
          </div>
          <div class="hidden sm:block text-center">
            <h4 class="text-[#18191A] font-semibold text-[12px] mb-1 leading-none">
              {{ item.label !== ORDER_LABEL.VERIFICATION ? item.label : 'Baru' }}
            </h4>
            <div class="min-w-full">
              <p v-if="item.label !== ORDER_LABEL.VERIFICATION" class="text-muted text-[10px]">
                {{ item.isCancel ? 'Dibatalkan' : item.value ? useFormatDateTime(item.value) : 'Menunggu...' }}
              </p>
              <p v-else class="text-muted text-[10px]">
                {{
                  item.isCancel ? 'Dibatalkan' : item.value ? useFormatDateTime(item.value) : 'Menunggu Verifikasi'
                }}
              </p>
            </div>
          </div>
        </div>
        <div
          v-if="index !== orderProgress?.length - 1"
          class="h-[4px] rounded-full mx-2 sm:mx-6 w-full"
          :class="[item.isDone && 'bg-[#05964C] text-white !border-[#05964C]', !item.isDone && 'bg-[#ACB1B4]']"
        ></div>
      </div>
    </div>
  </header>
  <div class="sm:hidden">
    <div class="text-[16px] font-bold">{{ order_detailData?.order_detail.order_status }}</div>
    <div class="text-[14px] text-muted">{{ useFormatDateTime(lastOrderValue) }}</div>
  </div>
</template>
