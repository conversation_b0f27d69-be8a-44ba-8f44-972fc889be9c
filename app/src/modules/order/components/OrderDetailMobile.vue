<script setup lang="ts">
import OrderDetailMobileProductList from '../components/OrderDetailMobileProductList.vue';
// Interfaces
import type { IOrderDetail, IOrderProvided } from '../interfaces/order.interface';

const isOpenModalConfirmCancel = ref(false);
const isOpenModalConfirmOrderCompletion = ref(false);
const isOpenModalInquiryForm = ref(false);

/**
 * @description Injecting dependencies
 */
const {
  order_detailData,
  order_formInquiry,
  order_formValidations,
} = inject<IOrderProvided>('order')!;

const orderDetail = computed<IOrderDetail['order_detail']>(
  () => order_detailData.value?.order_detail as IOrderDetail['order_detail'],
);

// const onClickConfirmOrderCompletion = () => {
//   isOpenModalConfirmOrderCompletion.value = true;
// };

const onCancelFinishOrder = () => {
  isOpenModalConfirmOrderCompletion.value = false;
  isOpenModalInquiryForm.value = true;
};

const onConfirmFinishOrder = () => {
  isOpenModalConfirmOrderCompletion.value = false;
};

// const onClickCancel = () => {
//   isOpenModalConfirmCancel.value = true;
// };

const onCancelFinish = () => {
  isOpenModalConfirmCancel.value = false;
};

const onConfirmFinish = () => {
  isOpenModalConfirmCancel.value = false;
};

const onInquryFormSubmit = () => {
  isOpenModalInquiryForm.value = false;
};


const options = ref([
    { name: 'New York', code: 'NY' },
    { name: 'Rome', code: 'RM' },
    { name: 'London', code: 'LDN' },
    { name: 'Istanbul', code: 'IST' },
    { name: 'Paris', code: 'PRS' }
]);

</script>

<template>
  <section v-if="orderDetail" id="order-summary" class="border border-[#E5E6E8] rounded-md h-fit">
    <div class="gap-3 px-5 py-6">
      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Detail</p>
        </div>
        <div class="flex flex-col gap-2">
          <div>
            <div class="grid grid-cols-[100px_1fr]">
              <p class="text-[#686F72] text-sm">Order</p>
              <p class="text-sm text-black text-right">{{ orderDetail.order_no }}</p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[100px_1fr]">
              <p class="text-[#686F72] text-sm">Tanggal Pesan</p>
              <p class="text-sm text-black text-right">
                {{ useFormatDateTime(orderDetail.transaction_date ?? '') }}
              </p>
            </div>
          </div>
          <div class="border-b border-b-[#ACB1B4] my-4"></div>
        </div>
      </div>

      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Pengiriman</p>
        </div>
        <div class="flex flex-col gap-2">
          <div class="flex flex-col">
            <p class="text-[#686F72] text-sm">Nama Perusahaan</p>
            <p class="text-black text-base font-medium">{{ orderDetail.ship_to }}</p>
          </div>
          <div class="flex flex-col">
            <p class="text-[#686F72] text-sm">Detail Alamat</p>
            <p class="text-black text-base font-medium">{{ orderDetail.ship_to }}</p>
          </div>
        </div>
      </div>
      

      <!-- Dialog confirm finish -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmCancel"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>

        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[19px] text-black">Konfirmasi Pembatalan Pesanan</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-base text-black">
              Apakah Anda yakin ingin membatalkan pesanan? Setelah dibatalkan, pesanan ini tidak dapat dipulihkan
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelFinish"
            />
            <PrimeVueButton
              type="button"
              label="Ya, Batalkan"
              size="large"
              class="!bg-[#E9151D] !border-none text-white !text-sm !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="onConfirmFinish"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <!-- Dialog confirm re-order -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmOrderCompletion"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>
        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[18px] text-black">Pesan Diselesaikan</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-base text-black">
              Pesanan Anda telah diproses dan diselesaikan dengan sukses. Apakah semuanya sudah sesuai?
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelFinishOrder"
            />

            <PrimeVueButton
              type="button"
              label="Ya, sudah"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="onConfirmFinishOrder"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <PrimeVueDialog
        v-model:visible="isOpenModalInquiryForm"
        modal
        header="Header"
        :style="{ width: '50vw' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>
        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-druk font-bold text-[42px] text-black">B2B Inquiry Form</h4>
          </header>
        </template>
        <template #default>
          <BaseModalInquiryForm/>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end w-full">
            <PrimeVueButton
              type="button"
              label="Submit"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !px-5 !w-full"
              @click="onInquryFormSubmit"
            />
          </section>
        </template>
      </PrimeVueDialog>
    </div>
  </section>
  <OrderDetailMobileProductList/>
</template>

<style lang="css" scoped>
:deep(.p-dialog .p-dialog-header) {
  border-bottom: 1px solid #e5e6e8 !important;
}
</style>
