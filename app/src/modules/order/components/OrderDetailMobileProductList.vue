<script setup lang="ts">
import type { IOrderProvided } from '../interfaces/order.interface';

// components
import OrderMobileProductItem from './OrderMobileProductItem.vue';

// constants
import { ORDER_PAGINATION_SIZE } from '../constants';

/**
 * @description Injecting dependencies
 */
const { order_detailData, order_detailOrderQueryParams } = inject<IOrderProvided>('order')!;

const selectedTab = ref('tersedia');

const pagination = ref<IPaginationResponse>({
  total_data: Number(order_detailData.value?.total_data),
  size: Number(order_detailData.value?.size),
  active_page: Number(order_detailData.value?.active_page),
  total_page: Number(order_detailData.value?.total_page),
});

const onChangePerPage = (value: number) => {
  order_detailOrderQueryParams.per_page = value;
};
const onClickPrev = () => {
  order_detailOrderQueryParams.page = order_detailOrderQueryParams.page - 1;
};
const onClickNext = () => {
  order_detailOrderQueryParams.page = order_detailOrderQueryParams.page + 1;
};
</script>

<template>
  <section id="order-product-list" class="w-full h-full relative inset-0 z-0 sm:order-0 order-1">
    <div class="w-full">
      <section id="filter-available" class="mb-10 sm:block hidden">
        <div class="flex gap-4 my-5 items-center">
          <div class="flex gap-3">
            <div class="flex">
              <NuxtImg src="/icons/calendar-checklist.svg" alt="upload-icon" class="w-6 h-6" />
            </div>
            <div class="text-md font-semibold">Ketersediaan Barang</div>
          </div>
          <div>
            <Tabs v-model:value="selectedTab">
              <TabList class="flex items-center gap-1 p-1 rounded-md bg-muted-tabs">
                <Tab
                  value="tersedia"
                  class="p-2 rounded-sm cursor-pointer duration-300 ease-in-out font-medium"
                  :class="[selectedTab === 'tersedia' ? 'bg-white' : 'bg-transparent']"
                  @click="selectedTab = 'tersedia'"
                >
                  Tersedia
                </Tab>
                <Tab
                  value="tidak tersedia"
                  class="p-2 rounded-sm cursor-pointer duration-300 ease-in-out font-medium"
                  :class="[selectedTab === 'tidak tersedia' ? 'bg-white' : 'bg-transparent']"
                  @click="selectedTab = 'tidak tersedia'"
                >
                  Tidak Tersedia
                </Tab>
              </TabList>
            </Tabs>
          </div>
        </div>
      </section>
      <section id="product-list-order">
        <div class="flex flex-col gap-4">
          <template v-if="order_detailData && order_detailData?.order_detail?.items?.length > 0">
            <div
              v-for="(item, index) in order_detailData?.order_detail?.items"
              :key="String(index)"
              class="w-full sm:order-1 order-0"
            >
              <OrderMobileProductItem :item="item" />
            </div>
          </template>
          <div v-else>
            <BaseEmptyState title="Produk kosong" subtitle="" />
          </div>
        </div>
      </section>
      <BasePagination
        :data="pagination"
        :click-action-prev="onClickPrev"
        :click-action-next="onClickNext"
        :click-action-per-page="onChangePerPage"
        :sizes="ORDER_PAGINATION_SIZE"
      />
    </div>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-panel-header) {
  position: relative;
  padding: 0 !important;
}
:deep(.p-panel-header) .p-panel-header-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
:deep(.p-panel-header) .p-panel-header-actions .p-button {
  width: 100%;
  height: 100%;
}
:deep(.p-panel-content) {
  padding: 0 !important;
}
</style>
