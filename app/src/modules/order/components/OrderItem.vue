<script setup lang="ts">
// Interfaces
import type { IOrderListItem } from '../../order/interfaces/order.interface';

// Components
import OrderStatusLabel from './OrderStatusLabel.vue';

const props = defineProps<{ order: IOrderListItem }>();
const router = useRouter();
const FALLBACK_PRODUCT_IMAGE = 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';

/**
 * @description Injecting dependencies
 */
// const { order_listOrderQueryParams } = inject<IOrderProvided>('order')!;

const windowSize = ref(window.innerWidth);
/**
 * Navigate to order detail / checkout detail
 */
const onClickDetailOrder = () => {
  if (props.order.order_no || props.order.order_group_id) {
    if (props.order.order_status?.trim()?.toLowerCase() === 'menunggu konfirmasi') {
      router.push({
        name: 'checkout',
        params: {
          orderNumber: props.order.order_group_id,
        },
      });
    } else {
      router.push({
        name: 'order.detail',
        params: {
          orderNumber: props.order.order_no,
        },
      });
    }
  }
};

const handleResizeWidth = () => {
  windowSize.value = window.innerWidth;
};

const onClickDetailMobileOrder = () => {
  if (windowSize.value < 640) {
    router.push({
      name: 'order.mobile.detail',
      params: {
        orderNumber: props.order.order_no,
      },
    });
  }
};

const isCustom = computed((): boolean => {
  return props.order?.cart_attachments?.length > 0;
});

const customProductName = computed<string[]>(() => {
  const _customName = [];
  const imageAttachments = props?.order.cart_attachments?.filter(t => t.file_path);
  const textAttachments = props?.order?.cart_attachments?.filter(t => t.text);

  if (imageAttachments?.length > 0) {
    _customName.push('Logo');
  }

  if (textAttachments?.length > 0) {
    _customName.push('Text');
  }

  return _customName;
});

onMounted(() => {
  window.addEventListener('resize', handleResizeWidth);
});
</script>

<template>
  <section
    :id="'order-item' + order.order_no"
    class="grid grid-cols-[124px_1fr] sm:grid-cols-[159px_1fr] gap-2 md:gap-3 sm:gap-6 sm:my-0 bg-white border-white text-left text-black"
  >
    <div class="w-[124px] sm:w-[159px] h-[124px] sm:h-[159px] bg-grey-200" @click="onClickDetailMobileOrder">
      <NuxtLink>
        <NuxtImg
          :src="order.image ? order.image : FALLBACK_PRODUCT_IMAGE"
          alt="product"
          class="w-full h-full object-cover"
        />
      </NuxtLink>
    </div>
    <div class="w-full">
      <div class="flex gap-2 items-center">
        <OrderStatusLabel :status="order.order_status" :text="order.order_status" />
        <div class="border border-[#ACB1B4] rounded-full px-2.5 py-1 leading- none inline-flex md:hidden">
          <p class="text-xs font-medium">{{ useNumberFormat(order.total_pesanan) }} Produk</p>
        </div>
      </div>

      <div class="sm:flex justify-between items-center my2">
        <h4 class="font-bold font-druk text-lg md:text-[28px] leading-none mb-1 mt-2">{{ order.nama_produk }}</h4>
      </div>

      <div class="flex items-center gap-3 mb-2">
        <p class="text-muted text-sm">Nomor Pesanan: {{ order.order_no }}</p>
      </div>

      <div class="border border-[#ACB1B4] rounded-full px-2.5 py-1 leading-none mb-2 hidden md:block w-fit my-2">
        <p class="text-[11px] font-medium">{{ order.total_produk }} Barang</p>
      </div>

      <div v-if="isCustom">
        <p class="text-[14px] text-[#686F72] mb-2">
          Custom: <span class="capitalize text-[#18191A] font-semibold">{{ customProductName?.toString() }}</span>
        </p>
      </div>

      <div class="flex flex-col sm:flex-row w-full sm:items-center justify-between mb-2">
        <div class="flex flex-col sm:flex-row w-full sm:items-center justify-between mb-2">
          <p class="text-[14px] text-black font-normal sm:font-semibold sm:">
            {{ isCustom ? 'Estimasi Total Harga' : 'Total Harga' }}
          </p>
          <p class="font-bold text-[20px]">
            {{ order.total_pesanan ? useCurrencyFormat(order.total_pesanan) : '-' }}
          </p>
        </div>
      </div>
      <div class="w-full hidden sm:block">
        <PrimeVueButton
          type="button"
          label="Detail Pesanan"
          variant="outlined"
          severity="secondary"
          class="w-full !text-black !text-sm font-medium !border-input-gray !px-7"
          @click="onClickDetailOrder"
        />
      </div>
    </div>
  </section>
</template>
