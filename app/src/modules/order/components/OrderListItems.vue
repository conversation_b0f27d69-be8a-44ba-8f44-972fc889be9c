<script setup lang="ts">
// interfaces
import type { IOrderProvided } from '../../order/interfaces/order.interface';

// constants
import { ORDER_LIST_PAGINATION_SIZE } from '../constants';

// components
import OrderItem from './OrderItem.vue';
import OrderItemSkeleton from './OrderItemSkeleton.vue';

/**
 * @description Injecting dependencies
 */
const { order_listOrderData, order_listOrderPagination, order_listOrderQueryParams, order_listOrderLoading } =
  inject<IOrderProvided>('order')!;

const onChangePerPage = (value: number) => {
  order_listOrderQueryParams.per_page = value;
  order_listOrderQueryParams.page = 1;
};
const onClickPrev = () => {
  order_listOrderQueryParams.page = order_listOrderQueryParams.page - 1;
};
const onClickNext = () => {
  order_listOrderQueryParams.page = order_listOrderQueryParams.page + 1;
};
</script>

<template>
  <section id="order-product-list" class="flex flex-col gap-4">
    <div v-if="order_listOrderLoading">
      <OrderItemSkeleton v-for="index in 3" :key="index" />
    </div>
    <div v-else-if="order_listOrderData.length > 0">
      <div class="flex flex-col w-full sm:gap-3 gap-y-5">
        <OrderItem v-for="order in order_listOrderData" :key="order.order_no" :order="order" />
      </div>
    </div>
    <div v-else>
      <BaseEmptyState title="Pesanan tidak ditemukan" subtitle="" />
    </div>
    <div class="border-y border-y-[#CED1D3] py-2">
      <BasePagination
        :data="order_listOrderPagination"
        :click-action-prev="onClickPrev"
        :click-action-next="onClickNext"
        :click-action-per-page="onChangePerPage"
        :sizes="ORDER_LIST_PAGINATION_SIZE"
      />
    </div>
  </section>
</template>
