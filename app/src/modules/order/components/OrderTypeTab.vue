<script setup lang="ts">
// interfaces
import type { IOrderProvided } from '../interfaces/order.interface';

// constants
import { ORDER_TYPE_TABS } from '../constants/order.constant';

/**
 * @description Injecting dependencies
 */
const { order_listOrderQueryParams } = inject<IOrderProvided>('order')!;

const onChangeTab = (val: string | number) => {
  order_listOrderQueryParams.type = String(val);
  order_listOrderQueryParams.page = 1;
};
</script>

<template>
  <section id="order-product-list" class="flex flex-col gap-4 mb-6">
    <div class="flex flex-col w-full items-center justify-between gap-3">
      <div class="w-full">
        <div class="mt-2">
          <PrimeVueTabs
            :value="order_listOrderQueryParams.type || ORDER_TYPE_TABS[0].value"
            @update:value="onChangeTab"
          >
            <PrimeVueTabList
              class="w-fit"
              :pt="{
                activeBar: '!bg-header-orange',
              }"
            >
              <PrimeVueTab
                v-for="(tab, tabIndex) in ORDER_TYPE_TABS"
                :key="`tab-${tabIndex}`"
                :value="tab.value"
                :pt="{
                  root: `text-sm !px-3 !py-2 ${useActiveTab(order_listOrderQueryParams.type, tab.value)}`,
                }"
              >
                {{ tab.label }}
              </PrimeVueTab>
            </PrimeVueTabList>
          </PrimeVueTabs>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped></style>
