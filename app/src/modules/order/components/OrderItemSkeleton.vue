<script setup lang="ts"></script>

<template>
  <section>
    <div class="!w-full !grid !grid-cols-[159px_1fr] !gap-4 !mb-3 !bg-white !border-white !text-left !text-black">
      <PrimeVueSkeleton class="!w-[124px] !h-[124px] md:!w-[159px] md:!h-[159px] !rounded" />

      <div class="!w-full">
        <div class="!flex !gap-2">
          <PrimeVueSkeleton class="!w-[80px] !h-[20px] !rounded" />
          <PrimeVueSkeleton class="!w-[60px] !h-[20px] !rounded md:!hidden" />
        </div>

        <PrimeVueSkeleton class="!w-[200px] !h-[28px] md:!h-[36px] !my-2 !rounded" />

        <div class="!flex !items-center !gap-3 !mb-2">
          <PrimeVueSkeleton class="!w-[150px] !h-[16px] !rounded" />
        </div>

        <PrimeVueSkeleton class="!w-[80px] !h-[20px] !rounded-xl md:!block !mb-2" />

        <!-- <PrimeVueSkeleton class="!w-[120px] !h-[20px] !rounded !mb-2" /> -->

        <div class="!flex !flex-col md:!flex-row !w-full md:!items-center !justify-between !mb-2 gap-2">
          <PrimeVueSkeleton class="!w-[120px] !h-[20px] !rounded" />
          <PrimeVueSkeleton class="!w-[100px] !h-[20px] !rounded" />
        </div>

        <PrimeVueSkeleton class="!w-full !h-[40px] !rounded md:!block" />
      </div>
    </div>
  </section>
</template>
