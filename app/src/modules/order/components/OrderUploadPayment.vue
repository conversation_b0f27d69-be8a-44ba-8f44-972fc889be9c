<script setup lang="ts">
// Interfaces
import type { IOrderProvided } from '../interfaces/order.interface';

// Vuelidate
import type { BaseValidation, ErrorObject } from '@vuelidate/core';

/**
 * @description Injecting dependencies
 */
const {
  order_uploadFile,
  order_detailData,
  order_uploadPaymentFile,
  order_deletePaymentFile,
  order_paymentformValidations,
} = inject<IOrderProvided>('order')!;

const paymentFile = ref<string>();
const isOpenPaymentFile = ref(false);
const dragging = ref<boolean>(false);
const selectedFile = ref<File | null>(null);

/**
 * @description Handle get order number
 */
const orderNumber: ComputedRef<string> = computed(() => {
  return order_detailData.value?.order_detail?.order_no as string;
});

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragging.value = false;
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0];
    selectedFile.value = event.dataTransfer.files[0];
    order_uploadFile.payment_file = file;
  }
};

const openFilePicker = () => {
  if (order_detailData.value?.order_detail.order_status?.toLocaleLowerCase() === 'baru') {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    fileInput.click();
  }
};

const handleFileSelected = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    selectedFile.value = file;
    order_uploadFile.payment_file = file;
  }
};

const shouldUseIframe = (fileUrl: string) => {
  const isPdfExtension = fileUrl.toLowerCase().endsWith('.pdf');
  const isBlob = fileUrl.startsWith('blob:');

  return isPdfExtension || isBlob;
};

const openDocument = async (url: File | string) => {
  const isFile = url instanceof File;
  const fileUrl = isFile ? URL.createObjectURL(url) : url;
  const isPdf = isFile
    ? url.type === 'application/pdf'
    : fileUrl.toLowerCase().endsWith('.pdf');

  if (isPdf && !isFile) {
    const response = await fetch(url);
    const blob = await response.blob();
    const blobUrl = URL.createObjectURL(new Blob([blob], { type: 'application/pdf' }));
    paymentFile.value = blobUrl;
  } else {
    paymentFile.value = fileUrl;
  }

  isOpenPaymentFile.value = true;
};

/**
 * @description Check the error message and retrieve the first error
 */
const error: ComputedRef<ErrorObject | null> = computed(() => {
  if (
    !order_paymentformValidations.value.payment_file &&
    (order_paymentformValidations.value.payment_file as BaseValidation)?.$errors.length === 0
  )
    return null;

  return order_paymentformValidations.value.payment_file.$errors[0];
});
</script>

<template>
  <div>
    <div
      v-if="order_uploadFile.payment_file || order_detailData?.order_detail?.payment_file"
      class="border-1 border-dashed border-[#05964C] bg-green-100 p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-lg "
      :class="order_detailData?.order_detail?.order_status?.toLocaleLowerCase() === 'baru' ? 'cursor-pointer' : ''"
      @dragover.prevent="dragging = true"
      @dragleave="dragging = false"
      @drop="handleDrop($event)"
      @click="() => openFilePicker()"
    >
      <div class="w-full justify-items-center my-5">
        <NuxtImg src="/icons/checklist-rounded-green.svg" alt="upload-icon" class="w-[32] h-[32]" />
        <div v-if="order_uploadFile.payment_file || order_detailData?.order_detail?.payment_file">
          <p class="mt-1 text-[14px] font-medium text-black text-center mb-1">
            {{order_uploadFile.payment_file?.name ?? order_detailData?.order_detail?.payment_file.split('/').pop() }}
          </p>
          <div class="flex flex-row items-center justify-center gap-2 align-middle">
            <p v-if="order_uploadFile.payment_file?.size" class="text-[12px] font-normal text-[#686F72] text-center">
              {{ typeof order_uploadFile.payment_file !== 'string' ? (order_uploadFile.payment_file?.size / (1024 * 1024)).toFixed(2) : '' }} MB
            </p>
            <button
              class="text-[12px] underline font-normal text-black cursor-pointer"
              @click.stop="openDocument(order_uploadFile.payment_file || order_detailData?.order_detail?.payment_file || '')"
              >
              Lihat Dokumen
            </button>
          </div>
        </div>
      </div>
      <input
        id="fileInput"
        accept=".pdf, .jpg, .jpeg, .png"
        type="file"
        class="hidden"
        @change="e => handleFileSelected(e)"
      />
    </div>
    <div
      v-else
      :class="[
        'border-1 border-dashed p-6 flex flex-col items-center justify-center transition-all duration-300 rounded-lg',
        dragging ? 'border-[#FF5A00] bg-orange-100' : 'border-[#ACB1B4]',
        order_detailData?.order_detail?.order_status?.toLocaleLowerCase() === 'baru' ? 'cursor-pointer' : ''
      ]"
      data-testid="bulk-file-input-draggable"
      @dragover.prevent="dragging = true"
      @dragleave="dragging = false"
      @drop="handleDrop($event)"
      @click="() => openFilePicker()"
    >
      <div class="w-full justify-items-center">
        <NuxtImg src="/icons/upload_doc.svg" alt="upload-icon" class="w-10 h-10" />
      </div>
      <div class="flex justify-center gap-5 font-medium">Tambah Dokumen</div>
      <div class="text-sm text-gray-500 mx-auto text-center">
        Format .jpg, pdf dan png (maksimal 2 Mb)
      </div>
      <input
        id="fileInput"
        accept=".pdf, .jpg, .jpeg, .png"
        type="file"
        class="hidden"
        @change="e => handleFileSelected(e)"
      />
    </div>
  </div>

  <PrimeVueMessage v-if="error" severity="error" variant="simple" size="small">
    {{ error.$message }}
  </PrimeVueMessage>

  <section v-if="order_detailData?.order_detail?.order_status?.toLocaleLowerCase() === 'baru'" id="payment-file-action">
    <PrimeVuebutton
      v-if="!order_detailData?.order_detail?.payment_file"
      :disabled="!order_uploadFile.payment_file || !!error"
      class="!text-[#18191A] !font-medium !bg-white !border-[#ACB1B4] w-full my-2"
      @click="order_uploadPaymentFile(orderNumber)"
    >
      Unggah File
    </PrimeVuebutton>
    <div v-else>
      <PrimeVuebutton
        class="!text-white !font-medium !bg-[#18191A] !border-[#18191A] w-full my-2"
        :disabled="!order_uploadFile.payment_file || !!error"
        @click="order_uploadPaymentFile(orderNumber)"
      >
        Ubah Dokumen
      </PrimeVuebutton>
      <PrimeVuebutton
        class="!text-[#E9151D] !font-medium !bg-white !border-[#ACB1B4] w-full my-2"
        @click="order_deletePaymentFile(orderNumber)"
      >
        Hapus
      </PrimeVuebutton>
    </div>
  </section>

  <!-- Dialog rating -->
  <PrimeVueDialog
    v-model:visible="isOpenPaymentFile"
    modal
    class="h-fit w-full md:w-[600px]"
    :pt="{
      header: '!py-2 border-b border-b-[#E5E6E8]',
      content: '!py-4',
      footer: '!py-3 border-t border-t-[#E5E6E8]',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[18px] text-black">Bukti Pembayaran</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col h-screen overflow-hidden">
        <iframe
          v-if="shouldUseIframe(paymentFile as string)"
          :src="paymentFile"
          type="application/pdf"
          class="flex-1 w-full h-[500px] border-none rounded"
        ></iframe>

        <img
          v-else
          :src="paymentFile"
          alt="payment file"
          class="max-w-full max-h-[500px] object-contain rounded"
        />
      </section>
    </template>
  </PrimeVueDialog>
</template>