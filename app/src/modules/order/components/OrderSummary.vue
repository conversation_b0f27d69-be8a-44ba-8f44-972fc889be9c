<script setup lang="ts">

// Components
import Rating from 'primevue/rating';
// import OrderStatusLabel from './OrderStatusLabel.vue';
import OrderUploadPayment from './OrderUploadPayment.vue';

// Interfaces
import type { IOrderDetail, IOrderProvided } from '../../order/interfaces/order.interface';

// constants
import { OrderStatus } from '../constants';

// Cancel ref
const isOpenModalConfirmCancel = ref(false);

// Reguler ref
const isOpenModalConfirmFinish = ref(false);
const isOpenDialogReview = ref(false);
const ratingValue = ref(0);
const ratingText = ref('');

// Custom ref
const isOpenModalConfirmOrderCompletion = ref(false);
const isOpenModalInquiryForm = ref(false);

/**
 * @description Injecting dependencies
 */
const {
  order_detailData,
  order_formInquiry,
  order_formValidations,
  order_cancelOrder,
  order_receiptOrder,
  order_isCustom,
  order_submitRating,
} = inject<IOrderProvided>('order')!;

const orderDetail = computed<IOrderDetail['order_detail']>(
  () => order_detailData.value?.order_detail as IOrderDetail['order_detail'],
);

const paymentStatus = computed(() => orderDetail.value?.payment_status?.toLocaleLowerCase()?.trim());

const onClickConfirmOrderCompletion = () => {
  if (orderDetail.value.is_custom) {
    isOpenModalConfirmOrderCompletion.value = true;
  } else {
    isOpenModalConfirmFinish.value = true;
  }
};

// Cancel function
const onClickCancel = () => {
  isOpenModalConfirmCancel.value = true;
};

const onCancelFinishOrder = () => {
  isOpenModalConfirmOrderCompletion.value = false;
  // isOpenModalInquiryForm.value = true;
};

// Reguler order function
const onConfirmFinishRegulerOrder = () => {
  isOpenModalConfirmFinish.value = false;
  order_receiptOrder(orderDetail.value.order_no as string).then(() => {
    isOpenDialogReview.value = true;
  });
};

const onCancelFinishRegulerOrder = () => {
  isOpenModalConfirmFinish.value = false;
};

const onCancelReview = () => {
  isOpenDialogReview.value = false;
};

const onConfirmReview = async () => {
  const result = await order_submitRating({
    order_no: Array.isArray(orderDetail?.value?.order_no)
      ? orderDetail?.value?.order_no[0]
      : String(orderDetail?.value?.order_no),
    rating_value: ratingValue.value,
    comment: ratingText.value,
  });
  if (result) {
    isOpenDialogReview.value = false;
  }
};

const disabledSubmitRating = computed(() => !ratingText.value?.trim());

// Custom order function
const onConfirmFinishCustomOrder = () => {
  isOpenModalConfirmOrderCompletion.value = false;
  order_receiptOrder(orderDetail.value.order_no as string).then(() => {
    isOpenDialogReview.value = true;
  });
};

const onCancelFinishCustomOrder = () => {
  isOpenModalConfirmCancel.value = false;
};

const onInquryFormSubmit = () => {
  isOpenModalInquiryForm.value = false;
};

const handleCancelOrder = () => {
  isOpenModalConfirmCancel.value = false;
  order_cancelOrder({
    customer_shipment_id: orderDetail.value.customer_shipment_id,
    order_no: orderDetail.value?.order_no as string[],
  });
};

const listProduct = computed(() => {
  if (!order_detailData.value) return [];

  return order_detailData.value.order_detail.items.map(product => ({
    id: product?.sku_code_c,
    name: product?.product_name_c,
    sub_total: product?.sub_total,
    qty: product?.sub_total,
  }));
});

const options = ref([
  { name: 'Informasi Umum', code: 'Informasi Umum' },
  { name: 'Produk & Kustomisasi', code: 'Produk & Kustomisasi' },
  { name: 'Proses Pemesanan', code: 'Proses Pemesanan' },
  { name: 'Pengiriman & Pengantaran', code: 'Pengiriman & Pengantaran' },
  { name: 'Garansi & Pengembalian', code: 'Garansi & Pengembalian' },
]);

const showCancelButton = computed(
  () =>
    orderDetail?.value?.order_status === OrderStatus.WAITING_VERIFICATION ||
    orderDetail?.value?.order_status === OrderStatus.ON_HOLD,
);
</script>

<template>
  <section id="order-summary" class="border border-[#E5E6E8] rounded-md h-fit">
    <div class="gap-3 px-5 py-6">
      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Detail</p>
          <!-- <OrderStatusLabel v-if="orderDetail?.order_status" :status="orderDetail?.order_status" :text="orderDetail?.order_status" /> -->
        </div>
        <div class="flex flex-col gap-2">
          <div>
            <div class="grid grid-cols-[130px_1fr]">
              <p class="text-[#686F72] text-[15px]">Order</p>
              <p class="text-[15px] text-black text-right font-medium">{{ orderDetail?.order_no ?? '-' }}</p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[130px_1fr]">
              <p class="text-[#686F72] text-[15px]">Ref</p>
              <p class="text-[15px] text-black text-right font-medium">
                {{ orderDetail?.sales_order_no ? orderDetail?.sales_order_no : '-' }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[130px_1fr]">
              <p class="text-[#686F72] text-[15px]">DN</p>
              <p class="text-[15px] text-black text-right font-medium">
                {{ orderDetail?.delivery_number ?? '-' }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[130px_1fr]">
              <p class="text-[#686F72] text-[15px]">Billing</p>
              <p class="text-[15px] text-black text-right font-medium">{{ orderDetail?.invoice_no ?? '-' }}</p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[130px_1fr]">
              <p class="text-[#686F72] text-[15px]">Tanggal GI</p>
              <p class="text-[15px] text-black text-right">
                {{ orderDetail?.gi_date ? useFormatDateTime(orderDetail?.gi_date) : '-' }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[130px_1fr]">
              <p class="text-[#686F72] text-[15px]">Tanggal Pesan</p>
              <p class="text-[15px] text-black text-right font-medium">
                {{ orderDetail?.transaction_date ? useFormatDateTime(orderDetail?.transaction_date) : '-' }}
              </p>
            </div>
          </div>

          <div class="border-b border-b-[#ACB1B4] my-4"></div>
        </div>
      </div>

      <div
        v-if="orderDetail?.verifikasi_date && orderDetail?.order_status !== OrderStatus.PENDING && orderDetail?.order_status !== OrderStatus.ON_HOLD"
        class="mb-4"
      >
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Bukti Pembayaran <span class="text-[#E9151D]">*</span></p>
        </div>
        <OrderUploadPayment />
      </div>

      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Pengiriman</p>
        </div>
        <div class="flex flex-col gap-2">
          <div class="flex flex-col">
            <p class="text-[#686F72] text-sm">Nama Pengirim</p>
            <p class="text-black text-base font-medium">{{ orderDetail?.ship_to ?? '-' }}</p>
          </div>
          <div class="flex flex-col">
            <p class="text-[#686F72] text-sm">Nama Perusahaan</p>
            <p class="text-black text-base font-medium">{{ orderDetail?.instance_name ?? '-' }}</p>
          </div>
          <div class="flex flex-col">
            <p class="text-[#686F72] text-sm">Email Perusahaan</p>
            <p class="text-black text-base font-medium">{{ orderDetail?.bill_to_email ?? '-' }}</p>
          </div>
          <div class="flex flex-col">
            <p class="text-[#686F72] text-sm">Detail Alamat</p>
            <p class="text-black text-base font-medium">{{ orderDetail?.ship_to_address ?? '-' }}</p>
          </div>
          <div class="border-b border-b-[#ACB1B4] my-4"></div>
        </div>
      </div>

      <div class="mb-6">
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold sm:block hidden">Konfirmasi Produk</p>
          <p class="text-black font-bold sm:hidden block">Ringkasan Barang</p>
        </div>
        <div class="sm:flex flex-col gap-2 hidden">
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Tersedia</p>
              </div>
              <p class="text-sm text-black text-right">{{ useNumberFormat(orderDetail?.total_available) ?? 0 }}</p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <div class="">
                <p class="text-black text-sm">Tidak Tersedia</p>
              </div>
              <p class="text-sm text-black text-right">
                {{ useNumberFormat(orderDetail?.total_unavailable) ?? 0 }}
              </p>
            </div>
          </div>
        </div>
        <div class="sm:hidden flex-col gap-2 flex">
          <div v-for="(item, key) in listProduct" :key="key" class="flex justify-between">
            <div>
              {{ item.name }}
            </div>
            <div>
              {{ useCurrencyFormat(item.sub_total) }}
            </div>
          </div>
        </div>
      </div>

      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Ringkasan Pesanan</p>
          <div
            v-if="!!paymentStatus"
            class="bg-[#05964C] rounded-sm text-white leading-none px-3 py-2 text-xs"
            :class="[
              !paymentStatus && 'text-[#1966F7] !border-[#147FFF] bg-[#D1ECF1]',
              (paymentStatus === 'belum lunas' || paymentStatus === 'belum dibayar') && 'bg-[#E9151D]',
              paymentStatus === 'lunas' && 'text-white !border-[#05964C] bg-[#05964C]',
            ]"
          >
            <span class="text-white">{{ orderDetail?.payment_status }}</span>
          </div>
        </div>
        <div class="bg-[#F5F6F6] rounded-md p-4 mb-4">
          <p class="text-[14px]">Semua harga yang tertera sudah termasuk pajak.</p>
        </div>
        <div class="flex flex-col">
          <div class="flex flex-col gap-2 mb-2">
            <p class="text-black text-base font-medium">Produk</p>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-sm text-black ml-2">Jumlah Produk</p>
              <p class="text-sm text-black text-right">{{ useNumberFormat(orderDetail?.total_qty) ?? '-' }}</p>
            </div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-sm text-black ml-2">Subtotal</p>
              <p class="text-sm text-black text-right">{{ useCurrencyFormat(orderDetail?.sub_total ?? 0) }}</p>
            </div>
            <div v-if="order_isCustom" class="grid grid-cols-[1fr_128px]">
              <p class="text-sm text-black ml-2">Total Biaya Kustom</p>
              <p class="text-sm text-black text-right">
                {{ useCurrencyFormat(orderDetail?.total_kustomisasi ?? 0) }}
              </p>
            </div>
          </div>
          <!-- <div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-black text-base font-medium mb-2">Biaya Pengantaran</p>
              <p class="text-black text-base font-medium text-right">
                {{ useCurrencyFormat(orderDetail?.ongkos_kirim ?? 0) }}
              </p>
            </div>
          </div> -->
          <!-- <div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-black text-base font-medium mb-2">Pajak</p>
              <p class="text-black text-base font-medium text-right">
                {{ useCurrencyFormat(orderDetail?.pajak ?? 0) }}
              </p>
            </div>
          </div> -->
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-[#05964C] text-base font-medium mb-2">Diskon</p>
              <p class="text-[#05964C] text-base font-medium text-right">
                -{{ useCurrencyFormat(orderDetail?.total_discount ?? 0) }}
              </p>
            </div>
          </div>
          <div class="border-b border-b-[#ACB1B4] my-2"></div>
          <div>
            <div class="grid grid-cols-[100px_1fr]">
              <p class="text-[#18191A] text-[12px] uppercase font-semibold">Total Harga</p>
              <p class="text-[17px] text-[#18191A] text-right font-bold text-lg md:text[20px]">
                {{ orderDetail?.total_nett >= 0 ? useCurrencyFormat(orderDetail?.total_nett ?? 0) : 0 }}
              </p>
            </div>
          </div>

          <div class="flex flex-col items-center justify-center w-full mt-7">
            <PrimeVueButton
              v-if="showCancelButton"
              :fluid="true"
              label="Batalkan Pesanan"
              class="!bg-white !border-[#ACB1B4] !rounded-lg !text-[#E9151D] mb-4 -mt-2"
              @click="onClickCancel"
            />
            <PrimeVueButton
              v-if="orderDetail?.order_status === OrderStatus.SHIPPING"
              :fluid="true"
              label="Selesaikan Pesanan"
              class="!bg-black !border-black !rounded-lg !text-white !font-base mb-4 -mt-2"
              @click="onClickConfirmOrderCompletion"
            />
          </div>

          <div>
            <BaseCustomerSupportCard />
          </div>
        </div>
      </div>

      <!-- Dialog confirm cancel order -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmCancel"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>

        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[19px] text-black">Konfirmasi Pembatalan Pesanan</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-base text-black">
              Apakah Anda yakin ingin membatalkan pesanan? Setelah dibatalkan, pesanan ini tidak dapat dipulihkan
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelFinishCustomOrder"
            />
            <PrimeVueButton
              type="button"
              label="Ya, Batalkan"
              size="large"
              class="!bg-[#E9151D] !border-none text-white !text-sm !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="handleCancelOrder"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <!-- Dialog confirm finish reguler order -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmFinish"
        modal
        class="h-fit w-full md:w-[537px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>

        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[19px] text-black">Pesan Diterima</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-[16px] text-black">
              Pesanan Anda telah diproses dan diselesaikan dengan sukses. Apakah semuanya sudah sesuai?
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelFinishRegulerOrder"
            />
            <PrimeVueButton
              type="button"
              label="Ya, sudah"
              size="large"
              class="!bg-black !border-none text-white !text-sm !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="onConfirmFinishRegulerOrder"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <!-- Dialog rating -->
      <PrimeVueDialog
        v-model:visible="isOpenDialogReview"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>
        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[18px] text-black">Rating Form</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col">
            <p class="font-bold text-[16px] text-black text-center mb-1">
              Terima kasih telah berbelanja di EIGER!
            </p>
            <p class="text-[14px] text-[#686F72] text-center">
              Beri rating dan ulasan tentang produk yang kamu terima.
            </p>
            <div class="card flex justify-center py-4">
              <Rating v-model="ratingValue" class="flex !gap-10">
                <template #onicon>
                  <NuxtImg src="/icons/feedback-active.svg" alt="close-circle" class="w-[37px] h-[37px]" />
                </template>
                <template #officon>
                  <NuxtImg src="/icons/feedback-inactive.svg" alt="close-circle" class="w-[37px] h-[37px]" />
                </template>
              </Rating>
            </div>
            <textarea
              id="review"
              v-model="ratingText"
              rows="4"
              v-bind="{ ...useBindStateForm('Tambahkan komentar') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              type="text"
            />
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Batalkan"
              type="button"
              class="!bg-transparent !border-none !text-black !text-base !text-center font-medium !w-fit !px-8"
              @click="onCancelReview"
            />
            <PrimeVueButton
              type="button"
              label="Kirim"
              :disabled="disabledSubmitRating"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !w-fit !px-8"
              @click="onConfirmReview"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <!-- Dialog confirm custom order -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmOrderCompletion"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>
        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[18px] text-black">Pesan Diselesaikan</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-base text-black">
              Pesanan Anda telah diproses dan diselesaikan dengan sukses. Apakah semuanya sudah sesuai?
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelFinishOrder"
            />

            <PrimeVueButton
              type="button"
              label="Ya, sudah"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="onConfirmFinishCustomOrder"
            />
          </section>
        </template>
      </PrimeVueDialog>

      <PrimeVueDialog
        v-model:visible="isOpenModalInquiryForm"
        modal
        header="Header"
        :style="{ width: '50vw' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>
        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-druk font-bold text-[42px] text-black">B2B Inquiry Form</h4>
          </header>
        </template>
        <template #default>
          <section id="content" class="flex flex-col">
            <div>
              <BaseFormGroup
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Tipe Kustomer"
                :validators="order_formValidations.type"
              >
                <div class="flex flex-wrap gap-10">
                  <div class="flex items-center gap-2">
                    <PrimeVueRadioButton
                      v-model="order_formInquiry.type"
                      input-id="Perusahaan"
                      name="type"
                      value="Perusahaan"
                      class="!border-black"
                      :pt="{
                        root: {
                          style: `
                            --p-radiobutton-checked-background: #FFF;
                            --p-radiobutton-checked-border-color: #FF5A00;
                            --p-radiobutton-icon-checked-color: #FF5A00;`,
                        },
                      }"
                    />
                    <label for="Perusahaan">Perusahaan</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <PrimeVueRadioButton
                      v-model="order_formInquiry.type"
                      input-id="Individu"
                      name="type"
                      value="Individu"
                      :pt="{
                        root: {
                          style: `
                            --p-radiobutton-checked-background: #FFF;
                            --p-radiobutton-checked-border-color: #FF5A00;
                            --p-radiobutton-icon-checked-color: #FF5A00;`,
                        },
                      }"
                    />
                    <label for="Individu">Individu</label>
                  </div>
                </div>
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Full Name"
                :validators="order_formValidations.full_name"
              >
                <PrimeVueInputText
                  id="full_name"
                  v-model="order_formInquiry.full_name"
                  v-bind="{ ...useBindStateForm('Masukkan nama lengkap') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(order_formValidations, 'full_name')"
                />
              </BaseFormGroup>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Nomor HP"
                :validators="order_formValidations.no_telp"
              >
                <PrimeVueInputGroup>
                  <PrimeVueInputGroupAddon class="!bg-[#F9FAFB]">
                    <p class="text-[#18191A] text-base font-normal">+62</p>
                  </PrimeVueInputGroupAddon>
                  <PrimeVueInputText
                    id="no_telp"
                    v-model="order_formInquiry.no_telp"
                    v-bind="{ ...useBindStateForm('Masukkan nomor handphone') }"
                    class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    inputmode="numeric"
                    v-on="useListenerForm(order_formValidations, 'no_telp')"
                  />
                </PrimeVueInputGroup>
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Nama Perusahaan"
                :validators="order_formValidations.instituion_name"
              >
                <PrimeVueInputText
                  id="instituion_name"
                  v-model="order_formInquiry.instituion_name"
                  v-bind="{ ...useBindStateForm('Masukkan nama perusahaan') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(order_formValidations, 'instituion_name')"
                />
              </BaseFormGroup>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Alamat Email"
                :validators="order_formValidations.email"
              >
                <PrimeVueInputText
                  id="email"
                  v-model="order_formInquiry.email"
                  v-bind="{ ...useBindStateForm('Masukkan alamat email') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(order_formValidations, 'email')"
                />
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Alamat NPWP"
                :validators="order_formValidations.npwp_address"
              >
                <PrimeVueTextarea
                  id="npwp_address"
                  v-model="order_formInquiry.npwp_address"
                  v-bind="{ ...useBindStateForm('Masukkan alamat perusahaan sesuai NPWP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  rows="3"
                  v-on="useListenerForm(order_formValidations, 'npwp_address')"
                />
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Topik Pembahasan"
                :validators="order_formValidations.topic"
              >
                <PrimeVueSelect
                  v-model="order_formInquiry.topic"
                  :options="options"
                  option-label="name"
                  placeholder="Pilih Informasi Umum"
                  class="!w-full !rounded-lg"
                  :class="{ ...classes }"
                  v-on="useListenerForm(order_formValidations, 'topic')"
                />
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <PrimeVueTextarea
                id="email"
                v-model="order_formInquiry.question"
                v-bind="{ ...useBindStateForm('Berikan rincian tentang pertanyaan Anda di sini') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                type="text"
                rows="3"
              />
            </div>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end w-full">
            <PrimeVueButton
              type="button"
              label="Submit"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !px-5 !w-full"
              @click="onInquryFormSubmit"
            />
          </section>
        </template>
      </PrimeVueDialog>
    </div>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-dialog .p-dialog-header) {
  border-bottom: 1px solid #e5e6e8 !important;
}
</style>
