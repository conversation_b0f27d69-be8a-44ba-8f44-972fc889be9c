<script lang="ts" setup>
/**
 * interfaces
 */
interface IBaseLabel {
  text: string;
  status: string;
  onClick?: () => void;
}

defineProps<IBaseLabel>();

const emit = defineEmits<{
  (e: 'click'): void;
}>();

const handleClick = () => {
  emit('click');
};
</script>

<template>
  <div
    class="border inline-flex rounded-sm px-2 md:px-3 py-1.5 leading-none text-[13px] md:text-sm font-medium w-fit"
    :class="[
      status === 'Menunggu Konfirmasi' && 'text-white !border-[#18191A] bg-[#18191A]',
      status === 'Pending' && 'text-white !border-[#18191A] bg-[#18191A]',
      status === 'On Hold' && 'text-white !border-[#ACB1B4] bg-[#ACB1B4]',
      status === 'Baru' && 'text-white !border-[#FF5A00] bg-[#FF5A00]',
      status === 'Selesai' && 'text-white !border-[#05964C] bg-[#05964C]',
      status === 'Batal' && 'text-white !border-[#E9151D] bg-[#E9151D]',
      status === 'Diproses' && 'text-white !border-[#147FFF] bg-[#147FFF]',
      status === 'Siap Dikirim' && 'text-white !border-[#147FFF] bg-[#147FFF]',
      status === 'Dikirim' && 'text-white !border-[#147FFF] bg-[#147FFF]',
      status === 'Pembayaran' && 'text-white !border-[#147FFF] bg-[#147FFF]',
      status === 'Diterima' && 'text-white !border-[#147FFF] bg-[#147FFF]',
    ]"
    @click="handleClick"
  >
    {{ text }}
  </div>
</template>

<style scoped></style>
