<script setup lang="ts">
// Interfaces
import type { IOrderDetail, IOrderProvided } from '../interfaces/order.interface';

const isOpenModalConfirmCancel = ref(false);
const isOpenModalConfirmOrderCompletion = ref(false);
const isOpenModalInquiryForm = ref(false);

/**
 * @description Injecting dependencies
 */
const { order_detailData } = inject<IOrderProvided>('order')!;

const orderDetail = computed<IOrderDetail['order_detail']>(
  () => order_detailData.value?.order_detail as IOrderDetail['order_detail'],
);

const onClickConfirmOrderCompletion = () => {
  isOpenModalConfirmOrderCompletion.value = true;
};

const onCancelFinishOrder = () => {
  isOpenModalConfirmOrderCompletion.value = false;
  isOpenModalInquiryForm.value = true;
};

const onConfirmFinishOrder = () => {
  isOpenModalConfirmOrderCompletion.value = false;
};

const onClickCancel = () => {
  isOpenModalConfirmCancel.value = true;
};

const onCancelFinish = () => {
  isOpenModalConfirmCancel.value = false;
};

const onConfirmFinish = () => {
  isOpenModalConfirmCancel.value = false;
};

const onClickDrag = () => {
  isOpenModalConfirmCancel.value = false;
  isOpenModalConfirmOrderCompletion.value = false;
};

</script>

<template>
  <section v-if="orderDetail" id="order-summary" class="border border-[#E5E6E8] rounded-md h-fit mt-5">
    <div class="gap-3 px-5 py-6">
      <div>
        <div class="flex w-full items-center justify-between mb-4">
          <p class="text-black font-bold">Ringkasan Pesanan</p>
        </div>
        <div class="flex flex-col">
          <div class="flex flex-col gap-2 mb-2">
            <p class="text-black text-base font-medium">Produk</p>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-sm text-black ml-2">Jumlah Produk</p>
              <p class="text-sm text-black text-right">{{ useCurrencyFormat(orderDetail.sub_total) }}</p>
            </div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-sm text-black ml-2">Sub-Total</p>
              <p class="text-sm text-black text-right">{{ useCurrencyFormat(orderDetail.sub_total) }}</p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-black text-base font-medium mb-2">Biaya Pengantaran</p>
              <p class="text-black text-base font-medium text-right">
                -{{ useCurrencyFormat(orderDetail.total_discount) }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-black text-base font-medium mb-2">Pajak</p>
              <p class="text-black text-base font-medium text-right">
                -{{ useCurrencyFormat(orderDetail.total_discount) }}
              </p>
            </div>
          </div>
          <div>
            <div class="grid grid-cols-[1fr_128px]">
              <p class="text-[#05964C] text-base font-medium mb-2">Diskon</p>
              <p class="text-[#05964C] text-base font-medium text-right">
                -{{ useCurrencyFormat(orderDetail.total_discount) }}
              </p>
            </div>
          </div>
          <div class="border-b border-b-[#ACB1B4] my-2"></div>
          <div>
            <div class="grid grid-cols-[100px_1fr]">
              <p class="text-[#18191A] text-sm uppercase font-semibold">Total Harga</p>
              <p class="text-[17px] text-[#18191A] text-right font-bold text-lg md:text[20px]">
                {{ useCurrencyFormat(orderDetail.total) }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Dialog confirm finish -->
      <PrimeVueDrawer
        v-model:visible="isOpenModalConfirmCancel"
        modal
        class="!h-[19em] rounded-t-lg"
        :pt="{
          header: 'border-b border-b-[#E5E6E8] !py-5',
          content: '!py-0',
        }"
        :draggable="false"
        position="bottom"
        :show-close-icon="false"
      >
        <template #header>
          <header class="w-full px-24" @click="onClickDrag">
            <div class="w-full bg-gray-300 rounded-full h-1"></div>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4 p-4">
            <h4 class="font-bold text-[19px] text-black">Konfirmasi Pembatalan Pesanan</h4>
            <span class="text-base text-black">
              Apakah Anda yakin ingin membatalkan pesanan? Setelah dibatalkan, pesanan ini tidak dapat dipulihkan
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center w-full gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-full !px-4"
              @click="onCancelFinish"
            />
            <PrimeVueButton
              type="button"
              label="Ya, Batalkan"
              size="large"
              class="!bg-[#E9151D] !border-none text-white !text-sm !text-center font-medium !rounded-lg !w-full !px-5"
              @click="onConfirmFinish"
            />
          </section>
        </template>
      </PrimeVueDrawer>

      <!-- Dialog confirm re-order -->
      <PrimeVueDrawer
        v-model:visible="isOpenModalConfirmOrderCompletion"
        position="bottom"
        :show-close-icon="false"
        class="!h-[18em] w-full rounded-t-lg"
        :pt="{
          header: '!hidden',
          content: '!px-6 !pt-4',
          mask: '!flex sm:!hidden',
        }"
      >
        <template #header>
          <header class="w-full px-24" @click="onClickDrag">
            <div class="w-full bg-gray-300 rounded-full h-1"></div>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4 p-4">
            <h4 class="font-bold text-[18px] text-black">Pesan Diselesaikan</h4>
            <span class="text-base text-black">
              Pesanan Anda telah diproses dan diselesaikan dengan sukses. Apakah semuanya sudah sesuai?
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center w-full gap-4">
            <PrimeVueButton
              label="Tidak"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-full !px-4"
              @click="onCancelFinishOrder"
            />

            <PrimeVueButton
              type="button"
              label="Ya, sudah"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !w-full !px-5"
              @click="onConfirmFinishOrder"
            />
          </section>
        </template>
      </PrimeVueDrawer>
    </div>
  </section>
  <div class="my-4">
    <div>
      <BaseCustomerSupportCard />
    </div>
  </div>
  <div
    v-if="orderDetail?.status !== 'Dibuat' || 'Baru' "
    class="flex flex-col gap-2 items-center justify-center w-full mt-7 fixed bottom-0 left-0 right-0 bg-white p-5 border-t border-gray-300"
  >
  <PrimeVueButton
      :fluid="true"
      label="Batalkan Pesanan"
      class="!bg-white !border-[#ACB1B4] !text-[16px] !rounded-lg !text-[#E9151D] !py-3"
      @click="onClickCancel"
      />
      <!--  -->
      <PrimeVueButton
      :fluid="true"
      v-if="orderDetail?.status === 'Diterima'" 
      label="Selesaikan Pesanan"
      class="!bg-black !border-black !text-[16px] !rounded-lg !text-white !font-base !py-3"
      @click="onClickConfirmOrderCompletion"
    />
  </div>
</template>

<style lang="css" scoped>
:deep(.p-dialog .p-dialog-header) {
  border-bottom: 1px solid #e5e6e8 !important;
}
</style>
