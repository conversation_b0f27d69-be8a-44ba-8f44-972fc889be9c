<script setup lang="ts">
// components
// import CheckoutProductVariantTable from './OrderProductVariantTable.vue';
import type { IOrderDetailItem } from '../interfaces/order.interface';

interface Props {
  item: IOrderDetailItem;
}

const props = defineProps<Props>();

const FALLBACK_PRODUCT_IMAGE =
  'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';

const filteredUniqueVariant = computed(() => {
  return props.item.product_items?.filter(
    (item, index, self) => index === self.findIndex(t => t.product_variant === item.product_variant),
  );
});

// const selectedTab = ref(props.item?.product_items?.[0]?.product_variant ?? null);

const subtotalOrder = computed<number>(() => {
  if (props.item.product_items?.length > 0) {
    return props.item.product_items.reduce((itemTotal, item) => {
      return itemTotal + item.sub_total;
    }, 0);
  }
  return 0;
});

// const filteredVariants = computed(() => {
//   return props.item.product_items?.filter(i => i.product_variant === selectedTab.value);
// });

// filteredUniqueVariant.value[0].product_variant
watch([() => props.item.product_items[0]], value => {
  console.log('props.item.product_items[0]', value);
});
</script>

<template>
  <div class="w-full">
    <div class="w-full grid grid-cols-[10fr_90fr] gap-4 mb-3">
      <div class="flex items-start">
        <div class="w-[5em] h-[5em] bg-grey-200">
          <NuxtLink>
            <NuxtImg :src="item.image_url ?? FALLBACK_PRODUCT_IMAGE" alt="product" class="w-full h-full object-cover" />
          </NuxtLink>
        </div>
      </div>

      <div class="w-full">
        <NuxtLink>
          <h4 class="font-bold font-druk text-lg md:text-[28px] leading-none">{{ item.product_name_c }}</h4>
        </NuxtLink>
        <div class="flex items-center gap-3 my-1">
          <template v-for="(flag, index) in item?.flag" :key="`progress-${index}`">
            <div v-if="index !== 0" class="h-[4px] w-[4px] rounded-full bg-muted"></div>
            <p class="text-sm text-muted">{{ flag }}</p>
          </template>
        </div>
        <div class="flex items-center gap-2">
          <div class="text-muted text-md">Warna: </div>
          <div v-for="(uniqVariant,key) in filteredUniqueVariant" :key="key">
            <span>{{ uniqVariant.product_variant }}</span>
          </div>
        </div>
        <div class="pl-2">
          <div class="grid grid-cols-2 overflow-auto mt-5">
            <div class="font-medium text-sm text-muted">Estimasi Harga</div>
            <div class="font-bold ml-auto">{{ item.base_price ? useCurrencyFormat(subtotalOrder) : '-' }}</div>
          </div>
          <div v-for="(product,key) in filteredUniqueVariant" :key="key" class="w-full">
            <div class="flex items-center">
              <div class="mr-2">
                Size : {{ product.product_size }}
              </div>
              <div class="flex items-center gap-2">
                <div class="h-[2px] w-[2px] rounded-full bg-muted ml-2"></div>
                Stock : 100
              </div>
            </div>
            <div class="flex items-center my-2 justify-between">
              <div class="">
                {{ product.issued_qty }}
              </div>
              <div class="font-medium">{{ useCurrencyFormat(item.sub_total)  }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
:deep(.p-panel-header) {
  position: relative;
  padding: 0 !important;
}
:deep(.p-panel-header) .p-panel-header-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
:deep(.p-panel-header) .p-panel-header-actions .p-button {
  width: 100%;
  height: 100%;
  border-radius: 0 !important;
  background-color: transparent !important;
}
:deep(.p-panel-content) {
  padding: 0 !important;
}
</style>
