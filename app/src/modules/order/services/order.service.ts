// Constants
import {
  ORDER_LIST_PAGINATION_SIZE,
  ORDER_PAGINATION_SIZE,
  ORDER_STATUS_TABS,
  ORDER_TYPE_TABS,
} from '../constants';

// Interfaces
import type {
  IOrderProvided,
  IOrderRatingPayload,
  IOrderUploadFilePayload,
  IPayloadCancelOrder,
  IQueryParamsGetDetailOrder,
  IQueryParamsGetOrderList,
} from '../interfaces/order.interface';

// Primevue
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
import { useOrderStore } from '../stores/order.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import {helpers, email, numeric, required } from '@vuelidate/validators';


/**
 * @description Closure function that returns everything what we need into an object
 */
export const useOrderService = (): IOrderProvided => {
  /**
   * @description Injected variables
   */
  const toast = useToast();
  const store = useOrderStore(); // Instance of the store
  const {
    order_listOrderLoading,
    order_listOrderData,
    order_listOrderPagination,
    order_detailData,
    order_detailIsLoading,
  } = storeToRefs(store);

  /**
   * @description Reactive data binding
   */
  const order_listOrderActiveTab = ref<string>(ORDER_STATUS_TABS[0].value);

  const order_listOrderQueryParams = reactive<IQueryParamsGetOrderList>({
    page: 1,
    per_page: ORDER_LIST_PAGINATION_SIZE[0],
    status: ORDER_STATUS_TABS[0].value,
    type: ORDER_TYPE_TABS[0].value,
    text: null,
    date_from: null,
    date_to: null,
  });

  const order_detailOrderQueryParams = reactive<IQueryParamsGetDetailOrder>({
    is_available: true,
    page: 1,
    per_page: ORDER_PAGINATION_SIZE[0],
  });

  /**
   * @description Reactive data binding for the upload form, including file and change status
   */
  const order_uploadFile = reactive<IOrderUploadFilePayload>({
    payment_file: null,
  });

  /**
   * @description Form validation rules for file upload
   */
  const order_paymentFormRules: ComputedRef = computed(() => ({
    payment_file: {
      formatFile: helpers.withMessage(
        'File must be of type .jpg, .jpeg, .pdf, or .png',
        (value: File | null) => {
          if (!value) return true;
          const allowedExtensions = ['jpg', 'jpeg', 'pdf', 'png'];
          const fileExtension = value.name.split('.').pop()?.toLowerCase();
          return allowedExtensions.includes(fileExtension || '');
        }
      ),
      fileSizeMax: helpers.withMessage('File size must not exceed 2 MB', (value: File | null) => {
        if (!value) return true;
        const maxSizeInBytes = 2 * 1024 * 1024; // Max size 2 MB
        return value.size <= maxSizeInBytes;
      }),
    },
  }));

  /**
   * @description Handle form validations using Vuelidate
   */
  const order_paymentformValidations = useVuelidate(
    order_paymentFormRules,
    order_uploadFile, {
      $autoDirty: true,
    }
  );

  /**
   * @description Handle fetch api get order list. We call the fetchOrder_getOrderList function from the store to handle the request.
   */
  const order_fetchOrderList = async () => {
    await store.fetchOrder_getOrderList(order_listOrderQueryParams);
  };

  /**
   * @description Handle fetch api get order list. We call the fetchOrder_getOrderList function from the store to handle the request.
   */
  const order_getOrderDetail = async (orderNumber: string, loading: boolean = true) => {
    await store.fetchOrder_getOrderDetail(orderNumber, order_detailOrderQueryParams, loading);
  };

  /**
   * @description Handle fetch api get order list. We call the fetchOrder_getOrderList function from the store to handle the request.
   */
  const order_cancelOrder = async (body: IPayloadCancelOrder) => {
    await store.fetchOrder_cancelOrder(body).finally(() => {
      window.location.href = `/order/${body.order_no}`;
    });
  };

  /**
   * @description Handle fetch api get order list. We call the fetchOrder_receiptOrder function from the store to handle the request.
   */
  const order_receiptOrder = async (orderNumber: string) => {
    await store.fetchOrder_receiptOrder(orderNumber).finally(() => {
      order_getOrderDetail(orderNumber, false);
    });
  };

  const order_isCustom = computed<boolean>(
    () => !!(order_detailData && order_detailData.value?.order_detail?.is_custom),
  );

  /**
   * @description Handle uploading the payment file
   */
  const order_uploadPaymentFile = async (orderNumber: string): Promise<void> => {
    try {
      await store.fetchOrder_uploadFile(orderNumber, order_detailData?.value?.order_detail?.customer_shipment[0].customer_id as string, order_uploadFile);
    } catch (error) {
      console.log(error);
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Gagal upload bukti pembayaran',
        life: 3000,
      });
    } finally {
      order_uploadFile.payment_file = null
      order_getOrderDetail(orderNumber)
    }
  };

  /**
   * @description Handle uploading the payment file
   */
  const order_deletePaymentFile = async (orderNumber: string): Promise<void> => {
    try {
      await store.fetchOrder_deletePaymentFile(orderNumber);
    } catch (error) {
      console.log(error);
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Gagal hapus bukti pembayaran',
        life: 3000,
      });
    } finally {
      order_getOrderDetail(orderNumber)
    }
  };

  /**
   * @description Handle fetch api get order list. We call the fetchOrder_receiptOrder function from the store to handle the request.
   */
  const order_submitRating = async (body: IOrderRatingPayload): Promise<unknown> => {
    try {
      return await store.fetchOrder_submitRating(body);
    } catch (error) {
      console.log(error);
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to submit rating',
        life: 3000,
      });
    }
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    order_listOrderQueryParams,
    () => {
      order_fetchOrderList();
    },
    { deep: true },
  );

  watch(
    order_detailOrderQueryParams,
    () => {
      const orderNumber = order_detailData.value?.order_detail.order_no;
      if (orderNumber) {
        order_getOrderDetail(orderNumber as string);
      }
    },
    { deep: true },
  );

  /**
   * @description Reactive data binding
   */
  const order_formInquiry = reactive({
    type: 'Perusahaan',
    full_name: '',
    no_telp: '',
    instituion_name: '',
    email: '',
    npwp_address: '',
    topic: '',
    question: '',
  });

  /**
   * @description Form validations
   */
  const order_formRules: ComputedRef = computed(() => ({
    type: { required },
    full_name: { required },
    no_telp: { required, numeric },
    instituion_name: { required },
    email: { email, required },
    npwp_address: { required },
    topic: { required },
  }));

  const order_formValidations = useVuelidate(order_formRules, order_formInquiry, {
    $autoDirty: true,
  });

  return {
    order_fetchOrderList,
    order_getOrderDetail,
    order_cancelOrder,
    order_receiptOrder,
    order_uploadPaymentFile,
    order_deletePaymentFile,
    order_submitRating,
    order_listOrderLoading,
    order_listOrderData,
    order_listOrderPagination,
    order_listOrderQueryParams,
    order_detailOrderQueryParams,
    order_listOrderActiveTab,
    order_detailData,
    order_detailIsLoading,
    order_formInquiry,
    order_formValidations,
    order_isCustom,
    order_uploadFile,
    order_paymentformValidations,
  };
};
