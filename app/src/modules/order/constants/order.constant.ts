// Components
// import OrderProductList from '../components/OrderListItems.vue';

export enum ORDER_TYPE {
  CUSTOM = 'Custom Order',
  REGULER = 'Regular Order',
}

export enum OrderStatus {
  ALL = 'Semua',
  WAITING_CONFIRM = 'Menung<PERSON>',
  WAITING_VERIFICATION='Menunggu Verifikasi',
  PENDING = 'Pending',
  ON_HOLD = 'On Hold',
  NEW = 'Baru',
  PROCESSING = 'Diproses',
  PAYMENT = 'Pembayaran',
  READY_TO_SHIP = 'Siap dikirim',
  SHIPPING = 'Dikirim',
  COMPLETED = 'Selesai',
  CANCELLED = 'Batal',
}

export enum OrderPaymentStatus {
  UNPAID = 'Belum Dibayar',
  NOT_PAID_OFF = 'Belum Lunas',
  PAID = 'Lunas',
}

export const ORDER_TYPE_TABS = [
  {
    label: ORDER_TYPE.CUSTOM,
    value: 'custom',
  },
  {
    label: ORDER_TYPE.REGULER,
    value: 'regular',
  },
];

export const ORDER_STATUS_TABS = [
  {
    label: OrderStatus.ALL,
    value: OrderStatus.ALL,
  },
  {
    label: OrderStatus.WAITING_CONFIRM,
    value: OrderStatus.WAITING_CONFIRM,
  },
  {
    label: OrderStatus.WAITING_VERIFICATION,
    value: OrderStatus.WAITING_VERIFICATION,
  },
  {
    label: OrderStatus.PENDING,
    value: OrderStatus.PENDING,
  },
  {
    label: OrderStatus.ON_HOLD,
    value: OrderStatus.ON_HOLD,
  },
  {
    label: OrderStatus.NEW,
    value: OrderStatus.NEW,
  },
  {
    label: OrderStatus.PROCESSING,
    value: OrderStatus.PROCESSING,
  },
  {
    label: OrderStatus.PAYMENT,
    value: OrderStatus.PAYMENT,
  },
  {
    label: OrderStatus.READY_TO_SHIP,
    value: OrderStatus.READY_TO_SHIP,
  },
  {
    label: OrderStatus.SHIPPING,
    value: OrderStatus.SHIPPING,
  },
  {
    label: OrderStatus.COMPLETED,
    value: OrderStatus.COMPLETED,
  },
  {
    label: OrderStatus.CANCELLED,
    value: OrderStatus.CANCELLED,
  },
];

export const ORDER_PAMENT_STATUS = [{}];

export const ORDER_PAGINATION_SIZE = Array.from(BASE_PAGINATION_SIZE);
export const ORDER_LIST_PAGINATION_SIZE = Array.from(BASE_PAGINATION_SIZE);
