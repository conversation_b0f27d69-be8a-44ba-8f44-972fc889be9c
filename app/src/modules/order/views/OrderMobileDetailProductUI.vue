<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import OrderProgress from '../components/OrderProgress.vue';
import OrderDetailMobile from '../components/OrderDetailMobile.vue';
import OrderOverviewMobileDetail from '../components/OrderOverviewMobileDetail.vue';
import { useOrderService } from '../services/order.service';

const route = useRoute();
/**
 * @description Destructure all the data and methods what we need
 */
const {
  order_getOrderDetail,
  order_detailData,
  order_cancelOrder,
  order_detailIsLoading,
  order_detailOrderQueryParams,
  order_formInquiry,
  order_formValidations,
} = useOrderService();

/**
 * @description Provide all the data and methods what we need
 */
provide('order', {
  order_getOrderDetail,
  order_cancelOrder,
  order_detailData,
  order_detailIsLoading,
  order_detailOrderQueryParams,
  order_formInquiry,
  order_formValidations,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  if (route.params?.orderNumber) {
    await Promise.allSettled([order_getOrderDetail(String(route.params?.orderNumber))]);
  }
});

</script>

<template>
  <MainLayout page-title="Rincian Pesanan">
    <section id="order-list" class="flex flex-col w-full h- full relative inset-0 z-0 pt-6">
      <BaseLoaderBoxed v-if="order_detailIsLoading" :height="400" />
      <template v-else>
        <div class="flex flex-col gap-4">
            <OrderProgress />
            <OrderDetailMobile/>
        </div>
        <div class="">
          <OrderOverviewMobileDetail/>
        </div>
      </template>
    </section>
  </MainLayout>
</template>
