<script setup lang="ts">
// Components
import OrderTypeTab from '../components/OrderTypeTab.vue';
import OrderListFilter from '../components/OrderListFilter.vue';
import OrderListTab from '../components/OrderListTab.vue';
import OrderListItems from '../components/OrderListItems.vue';

// Services
import { useOrderService } from '../services/order.service';
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  order_fetchOrderList,
  order_listOrderLoading,
  order_listOrderData,
  order_listOrderPagination,
  order_listOrderQueryParams,
  order_listOrderActiveTab,
} = useOrderService();

/**
 * @description Provide all the data and methods what we need
 */
provide('order', {
  order_fetchOrderList,
  order_listOrderLoading,
  order_listOrderData,
  order_listOrderPagination,
  order_listOrderQueryParams,
  order_listOrderActiveTab,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  await Promise.allSettled([order_fetchOrderList()]);
});
</script>

<template>
  <MainLayout page-title="Pesanan Saya">
    <section id="order-list" class="flex flex-col w-full h-full relative inset-0 z-0 pt-6">
      <OrderTypeTab />
      <OrderListFilter />
      <OrderListTab />
      <OrderListItems />
    </section>
  </MainLayout>
</template>
