<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import OrderSummary from '../components/OrderSummary.vue';
import { useOrderService } from '../services/order.service';
import { OrderPaymentStatus, OrderStatus } from '../constants';

const route = useRoute();
const router = useRouter();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  order_getOrderDetail,
  order_detailData,
  order_cancelOrder,
  order_detailIsLoading,
  order_detailOrderQueryParams,
  order_formInquiry,
  order_formValidations,
} = useOrderService();

/**
 * @description Provide all the data and methods what we need
 */
provide('order', {
  order_getOrderDetail,
  order_cancelOrder,
  order_detailData,
  order_detailIsLoading,
  order_detailOrderQueryParams,
  order_formInquiry,
  order_formValidations,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  if (route.params?.orderNumber) {
    await Promise.allSettled([order_getOrderDetail(String(route.params?.orderNumber))]);
  }
});

const onClickDetail = () => {
  router.push({
    name: 'order.mobile.item.detail',
    params: {
      orderNumber: order_detailData.value?.order_detail.order_no,
    },
  });
};
</script>

<template>
  <MainLayout page-title="Rincian Pesanan">
    <section id="order-list" class="flex flex-col w-full h- full relative inset-0 z-0 pt-6">
      <BaseLoaderBoxed v-if="order_detailIsLoading" :height="400" />
      <template v-else>
        <div
          v-if=" 
            [OrderPaymentStatus.NOT_PAID_OFF, OrderPaymentStatus.UNPAID]?.includes(
              order_detailData?.order_detail.order_summary as OrderPaymentStatus,
            ) &&order_detailData?.order_detail.payment_status !== OrderStatus.CANCELLED 
          "
          class="bg-[#FFF1F2] text-[#E9151D] p-4 text-sm text-center mb-4"
        >
          Terdapat tagihan yang belum diselesaikan, harap lakukan <strong>pembayaran</strong> dahulu sebelum
          melakukan pemesanan.
        </div>
        <div class="flex flex-col gap-4">
          <div class="grid grid-cols-1 md:grid-cols-[1fr_310px] gap-8 mb-3">
            <OrderSummary />
          </div>
        </div>
      </template>
    </section>
    <div class="bg-white fixed w-screen bottom-0 right-0 left-0 py-4 border-t border-gray-200 px-5">
      <PrimeVueButton
        type="button"
        label="Detail Pesanan"
        variant="outlined"
        severity="secondary"
        class="w-full !text-white !text-[16px] !py-3 font-medium !px-7 !bg-black"
        @click="onClickDetail"
      />
    </div>
  </MainLayout>
</template>
