<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import OrderDetailProductList from '../components/OrderDetailProductList.vue';
import OrderProgress from '../components/OrderProgress.vue';
import OrderSummary from '../components/OrderSummary.vue';
import OrderItemSkeleton from '../../order/components/OrderItemSkeleton.vue';
import { useOrderService } from '../services/order.service';
import { OrderPaymentStatus, OrderStatus } from '../constants';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  order_cancelOrder,
  order_receiptOrder,
  order_getOrderDetail,
  order_uploadPaymentFile,
  order_deletePaymentFile,
  order_submitRating,
  order_detailData,
  order_detailIsLoading,
  order_detailOrderQueryParams,
  order_formInquiry,
  order_formValidations,
  order_isCustom,
  order_uploadFile,
  order_paymentformValidations,
} = useOrderService();

/**
 * @description Provide all the data and methods what we need
 */
provide('order', {
  order_getOrderDetail,
  order_cancelOrder,
  order_receiptOrder,
  order_uploadPaymentFile,
  order_deletePaymentFile,
  order_submitRating,
  order_detailData,
  order_detailIsLoading,
  order_detailOrderQueryParams,
  order_formInquiry,
  order_formValidations,
  order_isCustom,
  order_uploadFile,
  order_paymentformValidations,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  if (route.params?.orderNumber) {
    await Promise.allSettled([order_getOrderDetail(String(route.params?.orderNumber))]);
  }
});
</script>

<template>
  <MainLayout page-title="Detail Pesanan">
    <section id="order-list" class="flex flex-col w-full h-full relative inset-0 z-0 pt-6">
      <!-- <BaseLoaderBoxed v-if="order_detailIsLoading" :height="400" /> -->
      <div
        v-if=" 
          [OrderPaymentStatus.NOT_PAID_OFF, OrderPaymentStatus.UNPAID]?.includes(
            order_detailData?.order_detail.order_summary as OrderPaymentStatus,
          ) && order_detailData?.order_detail.order_status !== OrderStatus.CANCELLED 
        "
        class="bg-[#FFF1F2] text-[#E9151D] p-4 text-sm text-center mb-4"
      >
        Terdapat tagihan yang belum diselesaikan, harap lakukan <strong>pembayaran</strong> dahulu sebelum
        melakukan pemesanan.
      </div>
      <div class="flex flex-col gap-4">
        <section id="order-detail-header">
          <PrimeVueSkeleton v-if="order_detailIsLoading" class="!h-[160px] !w-full" />
          <OrderProgress v-else />
        </section>
        <div class="grid grid-cols-1 md:grid-cols-[1fr_372px] gap-8 mb-3">
          <seaction id="order-detail-product">
            <template v-if="order_detailIsLoading">
              <div v-for="(_, index) in 3" :key="String(index)" class="w-full">
                <OrderItemSkeleton />
              </div>
            </template>
            <template v-else>
              <OrderDetailProductList />
            </template>
          </seaction>
          <section id="order-detail-summary">
            <PrimeVueSkeleton v-if="order_detailIsLoading" class="!h-[1200px] !w-[328px]" />
            <OrderSummary v-else />
          </section>
        </div>
      </div>
    </section>
  </MainLayout>
</template>
