// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/order',
    component: AppBaseWrapper,
    children: [
      {
        path: ':orderNumber',
        name: 'order.detail',
        component: () => import('../views/OrderDetailUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            { label: 'Beranda', path: '/dashboard' },
            { label: '<PERSON><PERSON><PERSON>', path: '/order' },
            { label: 'Detail <PERSON>esanan', path: null },
          ],
        },
      },
      {
        path: '',
        name: 'order',
        component: () => import('../views/OrderListUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            { label: 'Beranda', path: '/dashboard' },
            { label: '<PERSON><PERSON><PERSON>', path: null },
          ],
        },
      },
    ],
  },
  {
    path: '/order-mobile',
    component: AppBaseWrapper,
    children: [
      {
        path: ':orderNumber',
        name: 'order.mobile.detail',
        component: () => import('../views/OrderMobileDetailUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            { label: 'Beranda', path: '/dashboard' },
            { label: 'Pesanan Saya', path: null },
          ],
        },
      },
      {
        path: '/order-mobile-detail/:orderNumber',
        name: 'order.mobile.item.detail',
        component: () => import('../views/OrderMobileDetailProductUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            { label: 'Beranda', path: '/dashboard' },
            { label: 'Pesanan Saya', path: null },
          ],
        },
      },
    ],
  },
];

export default routes;
