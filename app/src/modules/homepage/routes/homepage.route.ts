// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: AppBaseWrapper,
    children: [
      {
        path: '',
        name: 'homepage',
        component: () => import('../views/HomepageView.vue'),
      },
    ],
  },
];

export default routes;
