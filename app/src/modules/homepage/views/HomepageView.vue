<script setup lang="ts">
// Components
import HomepageHero from '../components/HomepageHero.vue';
import HomepageAdvantages from '../components/HomepageAdvantages.vue';
import HomepageProduct from '../components/HomepageProduct.vue';
import HomepageFAQ from '../components/HomepageFAQ.vue';
import HomepageContactForm from '../components/HomepageContactForm.vue';
import HomepageSupportBy from '../components/HomepageSupportBy.vue';
import { useProductService } from '../../product/services/product.service';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  product_homepageCustomData,
  product_homepageCustomIsLoading,
  product_homepagePopularData,
  product_homepagePopularIsLoading,
  product_fetchProductHomepageCustom,
  product_fetchProductHomepagePopular,
} = useProductService();

provide('product', {
  product_homepageCustomData,
  product_homepageCustomIsLoading,
  product_homepagePopularData,
  product_homepagePopularIsLoading,
  product_fetchProductHomepageCustom,
  product_fetchProductHomepagePopular,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  await Promise.allSettled([product_fetchProductHomepageCustom()]);
});
</script>

<template>
  <LandingPageLayout>
    <section id="homepage" class="relative inset-0 z-0">
      <HomepageHero />
      <HomepageSupportBy />
      <HomepageAdvantages />
      <HomepageProduct />
      <HomepageFAQ />
      <HomepageContactForm />
    </section>
  </LandingPageLayout>
</template>
