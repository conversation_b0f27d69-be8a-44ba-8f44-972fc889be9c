<script setup lang="ts">
import TextUnderline from '/icons/text-underline.svg';

interface AdvantageItem {
  title: string;
  subtitle: string;
  img: string;
}
const advantageItems = ref<AdvantageItem[]>([
  {
    title: 'Kualitas Terpercaya untuk <PERSON>',
    subtitle: `<p>Produk yang dirancang untuk mendukung <strong class="text-[#18191A]">aktivitas harian dengan daya tahan tinggi.</strong></p>`,
    img: '/images/advantages/1.png',
  },
  {
    title: 'Garansi 1 Tahun',
    subtitle: '<p><strong class="text-[#18191A]"><PERSON><PERSON><PERSON> perlindungan</strong> untuk setiap pembelian.</p>',
    img: '/images/advantages/2.png',
  },
  {
    title: 'Kustomisasi Produk',
    subtitle:
      '<p><strong class="text-[#18191A]">Solusi</strong>yang dirancang khusus sesuai <strong class="text-[#18191A]">keb<PERSON><PERSON><PERSON> bisnis</strong> Anda.</p>',
    img: '/images/advantages/3.png',
  },
  {
    title: 'Diskon & Gratis Ongkos Kirim',
    subtitle:
      '<p>Dapatkan <strong class="text-[#18191A]">diskon mulai dari 5%</strong> dan <strong class="text-[#18191A]">gratis ongkir</strong> untuk setiap pembelian B2B Eigerindo.</p>',
    img: '/images/advantages/4.png',
  },
]);

const activeIndex = ref<number>(0);
const activeItem = ref<AdvantageItem>(advantageItems.value[0]);

const onMouseEnter = (item: AdvantageItem, index: number) => {
  activeIndex.value = index;
  activeItem.value = item;
};
</script>

<template>
  <section
    id="homepage-advantages-section"
    class="px-4 md:px-0 flex flex-col items-center justify-center h-full bg-[#ffffff] sm:bg-[#F5F6F6] py-[70px] md:py-[120px]"
  >
    <div class="mb-5 w-full lg:w-[813px] mx-auto text-left md:text-center">
      <h1 class="text-[42px] md:text-[64px] text-[#18191A] mb-4 md:mb-6 leading-normal md:leading-18 font-druk">
        Perusahaan Anda Layak Mendapatkan yang
        <span class="text-[#FF5A00] relative">
          Terbaik <img :src="TextUnderline" alt="carousel img" class="absolute left-[-3px] bottom-[-8px]"
        /></span>
      </h1>
      <p class="text-base text-muted mb-6 sm:mb-20">
        Produk unggulan yang dirancang dengan <strong class="text-[#18191A]">kualitas terbaik</strong> untuk
        mendukung aktivitas kantor Anda.
        <strong class="text-[#18191A]">Tahan lama, nyaman, dan siap menunjang produktivitas</strong>
        di setiap momen kerja.<strong class="text-[#18191A]"></strong>
      </p>
    </div>
    <div class="px-3 mb-4 flex md:hidden">
      <NuxtImg :src="activeItem.img" alt="app-logo" class="w-[590px]" />
    </div>
    <div class="lg:w-[1192px] mx-auto flex w-full justify-between md:mt-0 mt-6">
      <ul class="ml-4">
        <li
          v-for="(item, index) in advantageItems"
          :key="String(index)"
          class="advantage-item w-full flex"
          :class="{
            active: activeIndex === index,
          }"
          @mouseenter="() => onMouseEnter(item, index)"
        >
          <div class="grid grid-cols-[40px_1fr] md:grid-cols-[68px_1fr] mb-3">
            <div class="index-item text-[#686F72] text-lg md:text-[20px] font-bold">0{{ index + 1 }} /</div>
            <div class="text-left w-[240px] md:w-[330px]">
              <h4 class="title text-[20px] text-[#686F72] leading-none mb-2">
                {{ item.title }}
              </h4>
              <div class="mb-3 subtitle h-0 overflow-hidden" v-html="item.subtitle" />
              <div class="divider h-[1px] w-full bg-[#F5F6F6] mb-3" />
            </div>
          </div>
        </li>
      </ul>
      <div class="hidden md:flex">
        <NuxtImg :src="activeItem.img" alt="app-logo" class="w-[590px]" />
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped>
.advantage-item,
.subtitle,
.title {
  transition: all 0.3s;
}
.advantage-item.active .title,
.advantage-item:hover .title {
  font-family: 'Druk Text', sans-serif;
  color: #18191a;
  font-size: 28px;
}
.advantage-item.active .divider,
.advantage-item:hover .divider {
  background-color: #18191a;
}
.advantage-item.active .index-item,
.advantage-item:hover .index-item {
  color: #18191a;
}
.advantage-item.active .subtitle,
.advantage-item:hover .subtitle {
  height: auto !important;
}
</style>
