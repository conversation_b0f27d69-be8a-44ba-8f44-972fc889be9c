<script setup lang="ts">
import SlideImage1 from '/images/gallery/img1.png';
import SlideImage2 from '/images/gallery/img2.png';
import SlideImage3 from '/images/gallery/img3.png';
import SlideImage4 from '/images/gallery/img4.png';
import SlideImage5 from '/images/gallery/img5.png';
import SlideImage6 from '/images/gallery/img6.png';
import TextUnderline from '/icons/text-underline.svg';

const onClickGetProduct = () => {
  navigateTo({
    name: 'catalog',
  });
};
</script>

<template>
  <section
    id="homepage-hero"
    class="flex flex-col items-center justify-center h-full bg-[#18191A] text-white py-[70px] md:py-[120px]"
  >
    <div class="w-[90%] md:w-[1192px] mx-auto grid grid-cols-1 md:grid-cols-[1fr_700px]">
      <div class="mb-5 w-full text-left leading-normal">
        <h1
          class="text-[40px] md:text-[42px] lg:text-[64px] text-white mb-2"
          style="font-family: 'Druk Text', sans-serif"
        >
          Tingkatkan
          <span class="text-[#FF5A00] relative">
            Produktivitas <img :src="TextUnderline" alt="text line" class="absolute left-[-3px] bottom-[-8px]"
          /></span>
          dan Gaya Hidup dengan Produk Eiger
        </h1>
        <p class="text-base text-[#CED1D3] mb-8">
          Eiger menghadirkan produk berkualitas untuk mendukung produktivitas dan gaya hidup modern. Cocok untuk
          berbagai <strong> aktivitas, dari profesional hingga personal.</strong>
        </p>

        <div class="hidden md:flex w-[351px] gap-6 items-center mb-16">
          <button
            class="cursor-pointer w-full !bg-white border border-[#E5E6E8] !text-black font-sans text-sm font-semibold select-none h-[44px] font-bebas py-2 rounded-lg"
            type="button"
          >
            Hubungi Kami
          </button>
          <button
            class="cursor-pointer w-full !bg-[#FF5A00] font-sans text-sm font-semibold text-white select-none h-[44px] font-bebas py-2 rounded-lg"
            type="button"
            @click="onClickGetProduct"
          >
            Dapatkan Produk
          </button>
        </div>
      </div>
      <div id="home-image-auto-slide" class="grid grid-cols-2 gap-4 md:gap-6">
        <div class="hero-slider" style="--quantity: 6">
          <div class="list">
            <div class="item" style="--position: 1"><img :src="SlideImage6" alt="" /></div>
            <div class="item" style="--position: 2"><img :src="SlideImage5" alt="" /></div>
            <div class="item" style="--position: 3"><img :src="SlideImage4" alt="" /></div>
            <div class="item" style="--position: 4"><img :src="SlideImage3" alt="" /></div>
            <div class="item" style="--position: 5"><img :src="SlideImage2" alt="" /></div>
            <div class="item" style="--position: 6"><img :src="SlideImage1" alt="" /></div>
          </div>
        </div>
        <div class="hero-slider" reverse="true" style="--quantity: 6">
          <div class="list">
            <div class="item" style="--position: 1"><img :src="SlideImage1" alt="" /></div>
            <div class="item" style="--position: 2"><img :src="SlideImage2" alt="" /></div>
            <div class="item" style="--position: 3"><img :src="SlideImage3" alt="" /></div>
            <div class="item" style="--position: 4"><img :src="SlideImage4" alt="" /></div>
            <div class="item" style="--position: 5"><img :src="SlideImage5" alt="" /></div>
            <div class="item" style="--position: 6"><img :src="SlideImage6" alt="" /></div>
          </div>
        </div>
      </div>

      <!-- mobile button -->
      <div class="md:hidden flex flex-col w-full mt-8 gap-4 items-center mb-4">
        <button
          class="cursor-pointer w-full !bg-white border border-[#E5E6E8] !text-black font-sans text-sm font-semibold select-none h-[44px] font-bebas py-2 rounded-lg"
          type="button"
        >
          Hubungi Kami
        </button>
        <button
          class="cursor-pointer w-full !bg-[#FF5A00] font-sans text-sm font-semibold text-white select-none h-[44px] font-bebas py-2 rounded-lg"
          type="button"
          @click="onClickGetProduct"
        >
          Dapatkan Produk
        </button>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped></style>
