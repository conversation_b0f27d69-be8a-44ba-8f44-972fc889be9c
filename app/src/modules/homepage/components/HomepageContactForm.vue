<script setup lang="ts">
import Button from 'primevue/button';
import { email, numeric, required } from '@vuelidate/validators';
import useVuelidate from '@vuelidate/core';
import { formatSizeUnits } from '~/app/src/core/helpers/text.helper';
import type { IConfigurationProvided } from '../../configurations/interfaces/configurations.interface';

/**
 * @description Injecting dependencies
 */
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

const SELECT_OPTION = [
  {
    name: 'Informasi Umum',
    code: 'Informasi Umum',
  },
  {
    name: 'Produk & Kustomisasi',
    code: 'Produk & Kustomisasi',
  },
  {
    name: '<PERSON><PERSON>em<PERSON>an',
    code: '<PERSON><PERSON>',
  },
  {
    name: 'Faktur Pajak',
    code: 'Faktur Pajak',
  },
  {
    name: 'Pengiriman & Pengantaran',
    code: 'Pengiriman & Pengantaran',
  },
  {
    name: '<PERSON><PERSON><PERSON> & Pengembalian',
    code: '<PERSON><PERSON><PERSON> & Penge<PERSON>lian',
  },
];

const inputRef = ref(null);
const isDragging = ref(false);
const selectedFile = ref<File | null>(null);
const previewFile = ref<string | ArrayBuffer | null>(null);
const maxFileSize = ref<number>(2e7);
const acceptFile = ref<string>('.jpg, .png, .jpeg, .pdf');
const isOpenDialogPreviewFile = ref(false);

const toast = useToast();

/**
 * @description Form validations
 */
const form_formRules: ComputedRef = computed(() => ({
  type: { required },
  customerType: {},
  question: { required },
  fullName: { required },
  phoneNumber: { required, numeric },
  companyName: { required },
  email: { required, email },
  companyAddress: { required },
  checked: { required },
}));

const form_values = reactive({
  type: SELECT_OPTION[0].code,
  customerType: 'Perusahaan',
  question: '',
  fullName: '',
  phoneNumber: '',
  companyName: '',
  email: '',
  companyAddress: '',
  checked: false,
});

const form_formValidations = useVuelidate(form_formRules, form_values, {
  $autoDirty: true,
});

const isSelectedTaxField = computed(() => form_values.type === 'Faktur Pajak');

const onClickRegisterNow = () => {
  navigateTo({
    name: 'register-form',
  });
};

const onClickLogin = () => {
  navigateTo({
    name: 'login',
  });
};

const onSubmitForm = () => {
  form_formValidations.value.$touch();
  if (form_formValidations.value.$invalid) return;
};

const onChangeType = (value: string) => {
  if (value === 'Faktur Pajak') {
    form_formValidations.value.$reset();
  }
};

const isValidFile = async (file: File, showToast = true) => {
  if (file?.size && file.size > maxFileSize.value) {
    if (showToast) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'File lebih dari 2MB',
        life: 3000,
      });
    }
    return false;
  }

  // File type checking
  if (!acceptFile.value.split(',').some(ext => file.name.includes(ext?.trim()))) {
    if (showToast) {
      toast.add({
        severity: 'error',
        summary: 'Success',
        detail: 'File tidak sesuai.',
        life: 3000,
      });
    }
    return false;
  }

  return true;
};

const onClickUpload = () => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  inputRef?.value?.click();
};

const onDragEnter = () => {
  isDragging.value = true;
};

const onDragLeave = () => {
  isDragging.value = false;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onDragover = e => {
  e.preventDefault();
  isDragging.value = true;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onDrop = async e => {
  const file = e.dataTransfer?.files?.[0];
  console.log('onDrop', file);

  if (file) {
    if (!(await isValidFile(file))) return;

    const reader = new FileReader();
    reader.onload = e => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      previewFile.value = e.target.result;
    };
    reader.readAsDataURL(file);
  }
  selectedFile.value = file;
  isDragging.value = false;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const onFileChange = async e => {
  const file = e.target.files[0];
  console.log('onFileChange', file);

  if (file) {
    if (!(await isValidFile(file))) return;

    const reader = new FileReader();
    reader.onload = e => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      previewFile.value = e.target.result;
    };
    reader.readAsDataURL(file);
    selectedFile.value = file;
  }
};

const onClickSeeFile = () => {
  const base64Preview = String(previewFile.value);
  console.log('base64Preview', encodeURI(base64Preview));
  if (selectedFile.value && selectedFile?.value.type?.includes('image')) {
    isOpenDialogPreviewFile.value = true;
  } else {
    window.open(encodeURI(encodeURI(base64Preview)), '_blank');
  }
};
</script>

<template>
  <section
    v-if="!config_hideInquiryForm"
    id="homepage-contact"
    class="flex flex-col items-center justify-center h-full bg-[#F5F6F6] border-t-[4px] border-[#FF5A00] py-[80px] md:py-[120px]"
  >
    config_hideInquiryForm {{ config_hideInquiryForm }}
    <div class="w-full mb-8">
      <div class="mb-5 w-full mx-auto text-left md:text-center px-5 md:px-0">
        <h1
          class="text-[42px] md:text-[64px] text-[#18191A] mb-4 leading-12 md:leading-18"
          style="font-family: 'Druk Text', sans-serif"
        >
          Ada pertanyaan mengenai Eiger Business?
        </h1>
        <p class="text-base text-muted mb-10">
          Kami siap membantu <strong class="text-[#18191A]">menjawab segala pertanyaan</strong> tentang Eiger
          Bisnis.
        </p>
      </div>
    </div>

    <div class="w-full lg:w-[872px] gap-x-8 gap-y-4 px-5 md:px-0">
      <div class="w-full mb-4">
        <BaseFormGroup
          class-label="w-full font-medium text-[14px] text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Topic Pembahasan"
          :is-mandatory="true"
          :validators="form_formValidations.type"
        >
          <PrimeVueSelect
            v-model="form_values.type"
            :options="SELECT_OPTION"
            option-label="name"
            option-value="code"
            placeholder="Topic Pembahasan"
            size="large"
            class="!w-full"
            @update:model-value="onChangeType"
          />
        </BaseFormGroup>
      </div>

      <template v-if="isSelectedTaxField">
        <div class="w-full overflow-hidden">
          <div
            class="rounded-border bg-primary w-full h-auto mx-auto animate-slideup animate-once animate-duration-1000"
          >
            <div id="tax-info-content" class="bg-[#ECF6FF] px-6 py-4 rounded mb-5">
              <p class="mb-3">
                Untuk mendapatkan faktur pajak, harap melakukan Pendaftaran terlebih dahulu untuk membuat akun.
                Setelah akun terbuat, anda akan dapat mengakses dan mengunduh faktur pajak yang dibutuhkan.
              </p>
              <p class="mb-3">Harap siapkan dokumen yang dibutuhkan untuk keperluan permintaan faktur pajak.</p>
              <p class="mb-3">
                Jika anda sudah mempunyai akun, harap
                <span class="text-[#147FFF] font-bold cursor-pointer underline" @click="onClickLogin"> login</span>
                ke akun anda untuk mengakses dan mengunduh faktur pajak yang dibutuhkan.
              </p>
              <ol>
                <p class="mb-1"><strong></strong>Langkah-langkah Mengakses Faktur Pajak:</p>
                <li>
                  <strong>1.</strong><strong>Login atau Daftar</strong> terlebih dahulu jika belum memiliki akun.
                </li>
                <li><strong>2.</strong>Masuk ke <strong>Menu Profil</strong> di akun Anda.</li>
                <li>
                  <strong>3.</strong>Pilih <strong>Tab Faktur Pajak</strong>, lalu masukkan nomor invoice yang
                  telah diterima dan klik kirim.
                </li>
                <li>
                  <strong>4.</strong>Anda akan <strong>mendapatkan Faktur Pajak</strong> melalui email yang sudah
                  terdaftar.
                </li>
              </ol>
            </div>
            <div class="card flex justify-center">
              <Button
                size="large"
                label="Daftar Sekarang"
                class="!bg-[#18191A] !ring-0 !border-[#18191A] text-white w-full"
                @click="onClickRegisterNow"
              />
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <div class="w-full overflow-hidden">
          <div
            :class="`rounded-border bg-primary w-full h-auto mx-auto animate-slideup animate-once animate-duration-1000`"
          >
            <div class="w-full mb-2">
              <BaseFormGroup
                class-label="font-medium text-[14px] text-black block mb-2 w-full"
                is-name-as-label
                name="Pertanyaan"
                :hide-label="true"
                :validators="form_formValidations.question"
                :is-mandatory="true"
              >
                <PrimeVueTextarea
                  id="question"
                  v-model="form_values.question"
                  v-bind="{ ...useBindStateForm('Berikan rincian tentang pertanyaan Anda di sini') }"
                  :auto-resize="true"
                  rows="4"
                  size="large"
                  placeholder="Berikan rincian tentang pertanyaan Anda di sini"
                  class="w-full"
                  v-on="useListenerForm(form_formValidations, 'question')"
                />
              </BaseFormGroup>
            </div>

            <div class="w-full mb-6">
              <p class="mb-3">Tipe Kustomer</p>
              <div class="flex flex-wrap gap-10">
                <div class="flex items-center gap-2">
                  <PrimeVueRadioButton
                    v-model="form_values.customerType"
                    input-id="Perusahaan"
                    name="type"
                    value="Perusahaan"
                    class="!border-black"
                    size="large"
                    :pt="{
                      root: {
                        style: `
                            --p-radiobutton-checked-background: #FFF;
                            --p-radiobutton-checked-border-color: #FF5A00;
                            --p-radiobutton-icon-checked-color: #FF5A00;`,
                      },
                    }"
                  />
                  <label for="Perusahaan">Perusahaan</label>
                </div>
                <div class="flex items-center gap-2">
                  <PrimeVueRadioButton
                    v-model="form_values.customerType"
                    input-id="Individu"
                    name="type"
                    value="Individu"
                    size="large"
                    :pt="{
                      root: {
                        style: `
                            --p-radiobutton-checked-background: #FFF;
                            --p-radiobutton-checked-border-color: #FF5A00;
                            --p-radiobutton-icon-checked-color: #FF5A00;`,
                      },
                    }"
                  />
                  <label for="Individu">Individu</label>
                </div>
              </div>
            </div>

            <div class="w-full mb-2">
              <div class="mx-auto grid grid-cols-1 md:grid-cols-2 gap-x-2 md:gap-x-8 gap-y-4">
                <div class="w-full">
                  <BaseFormGroup
                    v-slot="{ classes }"
                    class-label="font-medium text-[14px] text-black block mb-2"
                    is-name-as-label
                    name="Nama Lengkap"
                    :validators="form_formValidations.fullName"
                    :is-mandatory="true"
                  >
                    <PrimeVueInputText
                      v-model="form_values.fullName"
                      v-bind="{ ...useBindStateForm('Masukan nama lengkap') }"
                      class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                      :class="{ ...classes }"
                      type="text"
                      size="large"
                      v-on="useListenerForm(form_formValidations, 'fullName')"
                    />
                  </BaseFormGroup>
                </div>

                <div class="w-full">
                  <BaseFormGroup
                    v-slot="{ classes }"
                    class-label="font-medium text-[14px] text-black block mb-2"
                    is-name-as-label
                    name="Nomor HP"
                    :validators="form_formValidations.phoneNumber"
                    :is-mandatory="true"
                  >
                    <PrimeVueInputText
                      v-model="form_values.phoneNumber"
                      v-bind="{ ...useBindStateForm('Masukkan nomor handphone') }"
                      class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                      :class="{ ...classes }"
                      type="text"
                      size="large"
                      v-on="useListenerForm(form_formValidations, 'phoneNumber')"
                    />
                  </BaseFormGroup>
                </div>
              </div>
              <div class="mx-auto grid grid-cols-1 md:grid-cols-2 gap-x-2 md:gap-x-8 gap-y-4">
                <div class="w-full">
                  <BaseFormGroup
                    v-slot="{ classes }"
                    class-label="font-medium text-[14px] text-black block mb-2"
                    is-name-as-label
                    name="Nama Perusahaan"
                    :validators="form_formValidations.companyName"
                    :is-mandatory="true"
                  >
                    <PrimeVueInputText
                      v-model="form_values.companyName"
                      v-bind="{ ...useBindStateForm('Masukkan nama perusahaan') }"
                      class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                      :class="{ ...classes }"
                      type="text"
                      size="large"
                      v-on="useListenerForm(form_formValidations, 'companyName')"
                    />
                  </BaseFormGroup>
                </div>
                <div class="w-full">
                  <BaseFormGroup
                    v-slot="{ classes }"
                    class-label="font-medium text-[14px] text-black block mb-2"
                    is-name-as-label
                    name="Alamat Email"
                    :validators="form_formValidations.email"
                    :is-mandatory="true"
                  >
                    <PrimeVueInputText
                      v-model="form_values.email"
                      v-bind="{ ...useBindStateForm('Masukkan alamat email') }"
                      class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                      :class="{ ...classes }"
                      type="text"
                      size="large"
                      v-on="useListenerForm(form_formValidations, 'email')"
                    />
                  </BaseFormGroup>
                </div>
              </div>
            </div>

            <div class="w-full mb-2">
              <BaseFormGroup
                class-label="font-medium text-[14px] text-black block mb-2 w-full"
                is-name-as-label
                name="Alamat Perusahaan"
                :validators="form_formValidations.companyAddress"
                :is-mandatory="true"
              >
                <PrimeVueTextarea
                  id="companyAddress"
                  v-model="form_values.companyAddress"
                  v-bind="{ ...useBindStateForm('Masukkan alamat perusahaan') }"
                  :auto-resize="true"
                  rows="4"
                  size="large"
                  class="w-full"
                  v-on="useListenerForm(form_formValidations, 'companyAddress')"
                />
              </BaseFormGroup>
            </div>

            <div class="w-full mb-8">
              <div
                v-if="selectedFile?.name"
                class="flex items-center justify-center flex-col h-[200px] border border-dashed border-[#05964C] rounded-[10px] bg-[#EEFFF5] cursor-pointer"
                @dragenter.prevent="onDragEnter"
                @dragover.prevent="onDragover"
                @dragleave.prevent="onDragLeave"
                @drop.prevent="onDrop"
                @click="onClickUpload"
              >
                <NuxtImg src="/icons/checklist-rounded-green.svg" class="mx-auto h-[32px] w-[32px] mb-3" />
                <div class="text-center">
                  <p class="text-[14px] font-semibold mb-1">{{ selectedFile?.name }}</p>
                  <div class="flex items-center justify-center gap-3 mb-2">
                    <p class="text-[12px] text-[#686F72]">
                      {{ formatSizeUnits(selectedFile.size) }}
                    </p>
                    <p class="h-[4px] w-[4px] rounded-full bg-[#ACB1B4]"></p>
                    <p
                      class="text-[12px] font-medium underline text-[#18191A] cursor-pointer"
                      @click.stop="onClickSeeFile"
                    >
                      Lihat Foto
                    </p>
                  </div>
                </div>
              </div>
              <div
                v-else
                class="flex items-center justify-center flex-col h-[200px] border border-dashed border-[#ACB1B4] rounded-[10px] bg-white cursor-pointer"
                @dragenter.prevent="onDragEnter"
                @dragover.prevent="onDragover"
                @dragleave.prevent="onDragLeave"
                @drop.prevent="onDrop"
                @click="onClickUpload"
              >
                <NuxtImg src="/icons/upload_doc.svg" class="mx-auto h-[32px] w-[32px] mb-3" />
                <div class="text-center">
                  <p class="text-[14px] font-semibold mb-1">Tambah Dokumen</p>
                  <p class="text-[12px] text-[#686F72]">
                    Dapat menambahkan dokumen dengan format .jpg .jpeg .pdf dan .png (maksimal 2 Mb)
                  </p>
                </div>
              </div>
              <input
                id="imageUpload"
                ref="inputRef"
                class="hidden"
                type="file"
                :accept="acceptFile"
                @change="onFileChange"
              />
            </div>
            <div class="w-full mb-8">
              <div class="flex items-start gap-1">
                <PrimeVueCheckbox
                  v-model="form_values.checked"
                  input-id="aggre-tnc-1"
                  name="agree-tnc-1"
                  class="mr-2"
                  binary
                />
                <label for="aggree-tnc-1" class="text-[#18191A] text-[16px] -mt-1"
                  >Saya memahami dan menyetujui bahwa data pribadi saya akan diproses sesuai dengan
                  <span class="text-[#147FFF] font-bold cursor-pointer underline" @click="onClickLogin">
                    kebijakan privasi</span
                  >
                  yang berlaku.</label
                >
              </div>
            </div>

            <div class="card flex justify-center">
              <Button
                type="submit"
                label="Submit"
                size="large"
                class="!bg-[#18191A] !ring-0 !border-[#18191A] text-white w-full !rounded-[12px]"
                @click="onSubmitForm"
              />
            </div>
          </div>
        </div>
      </template>
    </div>

    <PrimeVueDialog
      v-model:visible="isOpenDialogPreviewFile"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <p class="text-medium">{{ selectedFile?.name }}</p>
      </template>
      <template #default>
        <img
          v-if="selectedFile && selectedFile?.type?.includes('image')"
          :src="String(previewFile)"
          class="!w-full !h-auto"
        />
      </template>
      <template #footer> </template>
    </PrimeVueDialog>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-accordioncontent-content) {
  background-color: transparent !important;
}

#tax-info-content {
  * {
    font-size: 16px;
  }

  ol {
    li {
      list-style: none;
    }
  }
}

:deep(.p-inputtext),
:deep(.p-textarea),
:deep(.p-select-label) {
  font-size: 16px !important;
}
</style>
