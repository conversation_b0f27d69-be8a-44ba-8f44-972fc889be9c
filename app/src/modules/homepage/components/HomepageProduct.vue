<script setup lang="ts">
import TextUnderline from '/icons/text-underline.svg';

// swiper
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay, Navigation } from 'swiper/modules';

import type { IProductProvided } from '../../product/interfaces/product.interface';
import ProductCardHome from '../../product/components/ProductCardHome.vue';
import ProductCardHomeSkeleton from '../../product/components/ProductCardHomeSkeleton.vue';

const modules = ref([Autoplay, Navigation]);

const router = useRouter();

/**
 * @description Injecting dependencies
 */
const {
  product_homepageCustomData,
  product_homepageCustomIsLoading,
  product_fetchProductHomepageCustom,
  product_fetchProductHomepagePopular,
  product_homepagePopularData,
  product_homepagePopularIsLoading,
} = inject<IProductProvided>('product')!;

const tabs = computed(() => [
  {
    value: 'Produk Kustom',
  },
  {
    value: 'Produk Populer',
  },
]);

const activeTab = ref(tabs.value[0].value);

const isLoading = computed(() =>
  activeTab.value === tabs.value?.[0]?.value
    ? product_homepageCustomIsLoading.value
    : product_homepagePopularIsLoading.value,
);

const data = computed(() =>
  activeTab.value === tabs.value?.[0]?.value
    ? product_homepageCustomData.value
    : product_homepagePopularData.value,
);

const onChangeTab = (val: string | number | null) => {
  activeTab.value = String(val);
  if (val === tabs.value?.[0]?.value) {
    product_fetchProductHomepageCustom();
  } else {
    product_fetchProductHomepagePopular();
  }
};

const onClickSeeProduct = () => {
  router.push({
    name: 'catalog',
  });
};
const arraySkeleton = ref(new Array(4));
</script>

<template>
  <section
    id="homepage-product-section"
    class="px-4 md:px-0 flex flex-col items-center justify-center h-full py-[60px] md:py-[120px]"
  >
    <BaseContainer>
      <div class="mb-5 w-full lg:w-[813px] mx-auto text-left md:text-center">
        <h1 class="text-[42px] md:text-[64px] text-[#18191A] mb-4 md:mb-6 leading-normal md:leading-18 font-druk">
          Produk yang
          <span class="text-[#FF5A00] relative">
            Tepat <img :src="TextUnderline" alt="carousel img" class="absolute left-[-3px] bottom-[-8px]"
          /></span>
          untuk Kebutuhan Anda
        </h1>
        <p class="text-base text-muted mb-10 md:mb-16">
          Temukan produk <strong class="text-[#18191A]">berkualitas</strong> untuk kebutuhan bisnis Anda.
        </p>
      </div>
      <div class="w-full md:w-[1000px] mx-auto">
        <PrimeVueTabs id="home-product-tab" :value="activeTab" class="!mb-4" @update:value="onChangeTab">
          <PrimeVueTabList
            class="w-fit"
            :pt="{
              activeBar: '!bg-header-orange',
            }"
          >
            <PrimeVueTab
              v-for="(tab, tabIndex) in tabs"
              :key="`tab-${tabIndex}`"
              :value="tab.value"
              class="!w-[50%]"
              :pt="{
                root: `text-sm !px-3 !py-2 ${useActiveTab(activeTab, tab.value)}`,
              }"
            >
              {{ tab.value }}
            </PrimeVueTab>
          </PrimeVueTabList>
        </PrimeVueTabs>
      </div>
      <section id="dashboard-product-content" class="w-full">
        <swiper
          :modules="modules"
          class="mySwiper w-full"
          :navigation="true"
          :breakpoints="{
            '300': { slidesPerView: 1.6, spaceBetween: 12 },
            '640': { slidesPerView: 2, spaceBetween: 20 },
            '768': { slidesPerView: 3, spaceBetween: 32 },
            '1024': { slidesPerView: 4, spaceBetween: 32 },
          }"
        >
          <template v-if="isLoading">
            <swiper-slide v-for="(_, index) in arraySkeleton" :key="String(index)">
              <ProductCardHomeSkeleton />
            </swiper-slide>
          </template>
          <template v-else>
            <swiper-slide v-for="(product, index) in data" :key="String(index)">
              <ProductCardHome :product="product" />
            </swiper-slide>
          </template>
        </swiper>
      </section>
      <div class="w-full mt-6">
        <PrimeVueButton
          variant="outlined"
          class="!w-full !border-[#18191A] !text-sm !text-black !bg-transparent !h-[44px]"
          s
          @click="onClickSeeProduct"
        >
          Cari Produk lain
        </PrimeVueButton>
      </div>
      <div class="lg:w-[1192px] mx-auto flex w-full justify-between"></div>
    </BaseContainer>
  </section>
</template>

<style lang="css" scoped>
#home-product-tab .p-tablist.w-fit {
  width: 100% !important;
}
</style>
