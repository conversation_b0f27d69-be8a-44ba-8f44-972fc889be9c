<script setup lang="ts">
import Accordion from 'primevue/accordion';
import AccordionPanel from 'primevue/accordionpanel';
import AccordionHeader from 'primevue/accordionheader';
import AccordionContent from 'primevue/accordioncontent';

import TextUnderline from '/icons/text-underline.svg';

interface Faq {
  title: string;
  content: string;
}

const faqItems = ref<Faq[]>([
  {
    title: 'Berapa lama proses pengerjaan sampai barang siap untuk dikirim?',
    content: `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>`,
  },
  {
    title: 'Berapa lama waktu proses pemesanan?',
    content:
      '<p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
  },
  {
    title: 'Apakah ada batas minimum untuk pemesanan?',
    content: `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>`,
  },
  {
    title: 'Berapa diskon yang saya dapatkan melalui pemesanan jumlah besar?',
    content:
      '<p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
  },
  {
    title: 'Bagaimana cara memesan produk custom?',
    content: `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>`,
  },
  {
    title: 'Apa saja metode pembayaran yang disediakan?',
    content:
      '<p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
  },
  {
    title: 'Apa saja metode pengiriman yang disediakan?',
    content: `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>`,
  },
  {
    title: 'Bagaimana cara mendapatkan faktur pajak?',
    content:
      '<p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
  },
  {
    title: 'Apa saja persyaratan untuk memesan produk?',
    content: `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>`,
  },
  {
    title: 'Apa syarat untuk melakukan transaksi atau pemesanan produk kustomisasi?',
    content:
      '<p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
  },
  {
    title: 'Apakah kita bisa kustomisasi logo atau produk? Bagaimana caranya?',
    content: `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>`,
  },
]);
</script>

<template>
  <section id="homepage-faq-section" class="flex flex-col items-center justify-center h-full mb-32">
    <div class="w-full bg-[#18191A] mb-8 px-6 md:px-0">
      <div class="mb-5 w-full lg:w-[813px] mx-auto text-left md:text-center py-12">
        <h1
          class="text-[42px] md:text-[64px] text-[#FFFFFF] mb-4 leading-12 md:leading-18"
          style="font-family: 'Druk Text', sans-serif"
        >
          Frequently
          <span class="text-[#FF5A00] relative"> Asked </span>
          Question
        </h1>
        <p class="text-base text-[#fbfbfb]">
          Dapatkan <strong class="text-[#FFFFFF]">informasi lengkap</strong> mengenai produk, layanan, dan
          kebutuhan bisnis Anda di halaman FAQ kami.
        </p>
      </div>
    </div>

    <div class="w-full px-5 md:px-0 lg:w-[800px] mx-auto flex justify-between">
      <Accordion value="0" class="!w-full">
        <AccordionPanel
          v-for="tab in faqItems"
          :key="tab.title"
          :value="tab.title"
          class="!border !border-[#ACB1B4] !rounded-xl !mb-4"
        >
          <AccordionHeader class="!bg-[transparent] rounded-xl">
            <span class="text-[18px] font-bold text-[#18191A]">{{ tab.title }}</span>
          </AccordionHeader>
          <AccordionContent class="!bg-[transparent] !border-t !border-[#ACB1B4] !pt-4">
            <div class="mb-0" v-html="tab.content"></div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
  </section>
</template>

<style lang="css" scoped>
:deep(.p-accordioncontent-content) {
  background-color: transparent !important;
}
</style>
