<script setup lang="ts">
import 'swiper/css';
import 'swiper/css/pagination';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay } from 'swiper/modules';

const modules = ref([Autoplay]);

const partnerLogos = [
  '/images/partners/bca.png',
  '/images/partners/mandiri.png',
  '/images/partners/pln.png',
  '/images/partners/pertamina.png',
  '/images/partners/bni.png',
  '/images/partners/bca.png',
  '/images/partners/mandiri.png',
  '/images/partners/pln.png',
  '/images/partners/pertamina.png',
  '/images/partners/bni.png',
];
</script>

<template>
  <section id="homepage-partner" class="flex items-center w-full lg:w-[920px] mx-auto justify-center mb-18">
    <swiper
      :slides-per-view="5"
      :space-between="20"
      :autoplay="{
        delay: 1000,
        disableOnInteraction: false,
      }"
      :modules="modules"
      class="mySwiper w-full"
      :loop="true"
    >
      <swiper-slide v-for="(image, index) in partnerLogos" :key="String(index)">
        <div class="rounded-xl w-[120px] h-[120px]">
          <NuxtImg :src="image" alt="parner image" class="w-full h-full" />
        </div>
      </swiper-slide>
    </swiper>
  </section>
</template>

<style lang="css" scoped></style>
