<script setup lang="ts">
const partnerLogos = [
  '/images/support/bca.png',
  '/images/support/mandiri.png',
  '/images/support/pln.png',
  '/images/support/bni.png',
  '/images/support/sinarmas.png',
  '/images/support/adora.png',
  '/images/support/kehutanan.png',
  '/images/support/ipbi.png',
];
</script>

<template>
  <section id="homepage-support-section" class="relative flex items-center justify-center py-[70px] md:py-[120px]">
    <div class="bg-text">
      <span class="font-druk select-none">SUPPORT BY</span>
    </div>
    <div class="relative">
      <div class="px-4 md:px-0 w-full lg:w-[650px] mx-auto">
        <div class="font-medium text-lg md:text-[20px] text-center mb-12">
          <p class="text-black">Dipercaya perusahaan dan institusi ternama, bukti komitmen kami <br /></p>
          <p class="text-[#767B8080]">menghadirkan solusi terbaik</p>
        </div>
      </div>
      <div class="logo-grid w-full md:w-[800px]">
        <div v-for="(image, index) in partnerLogos" :key="String(index)" class="logo-item">
          <img :src="image" :alt="'support-logo-' + index" />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped>
.bg-text {
  position: absolute;
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  top: 80px;
  left: 0;
}

.bg-text span {
  font-size: 72px;
  font-weight: bold;
  color: #fbfbfb;
}

@media (min-width: 800px) {
  .bg-text {
    top: 0;
  }
  .bg-text span {
    font-size: 180px;
  }
}

.logo-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  max-width: 1200px;
  margin: 0 auto;
}

.logo-item {
  display: flex;
  justify-content: center;
  align-items: center;
  border-right: 1px solid #e9e9e9; /* Very light gray border */
  border-bottom: 1px solid #e9e9e9;
  background-color: #ffffff;
  width: 200px;
  height: 100px;
}

/* Remove right border from items in the last column */
.logo-item:nth-child(4n) {
  border-right: none;
}

/* Remove bottom border from items in the last row */
.logo-item:nth-child(n + 5):nth-child(-n + 8) {
  border-bottom: none;
}

.logo-item img {
  max-width: 100%;
  max-height: 70px;
  object-fit: contain;
}

/* For mobile responsiveness */
@media (max-width: 600px) {
  .logo-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Adjust borders for 2-column layout */
  .logo-item:nth-child(2n) {
    border-right: none;
  }

  .logo-item:nth-child(n + 5):nth-child(-n + 8) {
    border-bottom: 1px solid #e9e9e9;
  }

  .logo-item:nth-child(n + 7):nth-child(-n + 8) {
    border-bottom: none;
  }
}
</style>
