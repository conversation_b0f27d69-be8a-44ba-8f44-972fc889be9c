import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useDashboardStore } from '../dashboard.store';
import { DASHBOARD_STATE_LIST_OF_PRODUCTS } from '../../../dashboard/constants/dashboard.constant';

describe('Dashboard store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Reset date mocking
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useDashboardStore();

      expect(store.dashboard_listProductLoading).toBe(false);
      expect(store.dashboard_listOfProductActivities).toMatchObject([]);
      expect(store.dashboard_listOfProductCategories).toMatchObject([]);
      expect(store.dashboard_listOfProductColors).toMatchObject([]);
      expect(store.dashboard_listOfProductSizes).toMatchObject([]);
      expect(store.dashboard_listOfProducts).toMatchObject(DASHBOARD_STATE_LIST_OF_PRODUCTS);
      expect(store.dashboard_productDetail).toBeNull();
      expect(store.dashboard_detailIsLoading).toBe(false);
      expect(store.dashboard_productDetailVariants).toMatchObject({});
      expect(store.dashboard_listOfNewProductsLoading).toBe(false);
      expect(store.dashboard_listOfNewProducts).toMatchObject(DASHBOARD_STATE_LIST_OF_PRODUCTS);
      expect(store.dashboard_limitCreditData).toBeNull();
      expect(store.dashboard_limitCreditIsLoading).toBe(false);
      expect(store.dashboard_summaryTotalProductData).toBeNull();
      expect(store.dashboard_summaryTotalProductIsLoading).toBe(false);
      expect(store.dashboard_summaryTotalTransactionData).toBeNull();
      expect(store.dashboard_summaryTotalTransactionIsLoading).toBe(false);
      expect(store.dashboard_ongoingOrdersData).toMatchObject([]);
      expect(store.dashboard_ongoingOrdersIsLoading).toBe(false);
    });

    it('has correct initial computed state', () => {
      const store = useDashboardStore();
      expect(store.dashboard_listProductLoading).toBe(false);
      expect(store.dashboard_detailIsLoading).toBe(false);
      expect(store.dashboard_limitCreditIsLoading).toBe(false);
    });
  });

  it('Product list data should be empty', () => {
    const store = useDashboardStore();
    expect(store.dashboard_listOfProducts).toMatchObject(DASHBOARD_STATE_LIST_OF_PRODUCTS);
  });

  it('Product detail data should be null', () => {
    const store = useDashboardStore();
    expect(store.dashboard_productDetail).toBeNull();
  });

  it('Limit credit should be null', () => {
    const store = useDashboardStore();
    expect(store.dashboard_limitCreditData).toBeNull();
  });

  it('Summary total product data should be null', () => {
    const store = useDashboardStore();
    expect(store.dashboard_summaryTotalProductData).toBeNull();
  });
  it('Ongoing order should be null', () => {
    const store = useDashboardStore();
    expect(store.dashboard_ongoingOrdersData).toMatchObject([]);
  });
});
