import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia';
import { useCartStore } from '../cart.store';
import { INITIAL_STATE_DATA_LIMIT } from '../../constants/cart.constant';

describe('Cart store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useCartStore();

      expect(store.cart_listLoading).toBe(false);
      expect(store.cart_dataListCart).toBeNull();
      expect(store.cart_checkLoading).toMatchObject({});
      expect(store.cart_checkAllLoading).toBe(false);
      expect(store.cart_dataLimit).toMatchObject(INITIAL_STATE_DATA_LIMIT);
      expect(store.cart_addToCartLoading).toBe(false);
      expect(store.cart_selectedItems).toMatchObject([]);
    });

    it('has correct initial computed state', () => {
      const store = useCartStore();
      expect(store.cart_listLoading).toBe(false);
      expect(store.cart_addToCartLoading).toBe(false);
      expect(store.cart_checkAllLoading).toBe(false);
    });
  });

  it('Is correct default value cart data limit', () => {
    const store = useCartStore();
    expect(store.cart_dataLimit).toMatchObject(INITIAL_STATE_DATA_LIMIT);
  });
});
