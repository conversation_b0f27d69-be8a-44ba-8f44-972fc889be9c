// Constants
import {
  CART_CUSTOM_ENDPOINT_ADD_CART_BULK,
  CART_CUSTOM_ENDPOINT_CHECK_BULK,
  CART_CUSTOM_ENDPOINT_DELETE_BULK,
  CART_CUSTOM_ENDPOINT_UPDATE_QTY,
  CART_CUSTOM_POST_REMARK,
  CART_CUSTOM_POST_TEXT_ATTACHMENTS,
  CART_ENDPOINT_ADD_CART,
  CART_ENDPOINT_ADD_CART_BULK,
  CART_ENDPOINT_CHECK_BULK,
  CART_ENDPOINT_CHECK_CART,
  CART_ENDPOINT_DELETE,
  CART_ENDPOINT_DELETE_ALL,
  CART_ENDPOINT_DELETE_BULK,
  CART_ENDPOINT_GET_LIST,
  CART_ENDPOINT_UNCHECK_CART,
  CART_GET_CONFIGURATION,
} from '../constants/cart.api.constant';
import { INITIAL_STATE_DATA_LIMIT } from '../constants/cart.constant';

// Interfaces
import type {
  IAddToCartPayload,
  ICartConfiguration,
  ICartStoreState,
  IPayloadAddToCartBulk,
  IPayloadCheckItemBulk,
  IPayloadCustomAddToCartBulk,
  IPayloadCustomCheckCartItemBulk,
  IPayloadCustomDeleteCartItemBulk,
  IPayloadDeleteCartItemBulk,
  IPayloadPostCartAttachments,
  IPayloadPostCartRemark,
  IPayloadToggleCartItem,
  IPayloadUpdateCustomCartQty,
  IQueryParamsGetListCart,
  IReturnCartPostTextAttachments,
} from '../interfaces/cart.interface';

// Pinia
import { defineStore } from 'pinia';

export const useCartStore = defineStore('cart', {
  state: (): ICartStoreState => ({
    cart_listLoading: false,
    cart_dataListCart: null,
    cart_checkLoading: {},
    cart_checkAllLoading: false,
    cart_dataLimit: { ...INITIAL_STATE_DATA_LIMIT },
    cart_showToast: false,
    cart_toastData: {
      count: 0,
      variants: [],
    },
    cart_addToCartLoading: false,
    cart_deleteLoading: false,
    cart_selectedItems: [],
    cart_isShowModalFormRemark: false,
    cart_configuration: null,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get cart list.
     * @url /cart/list
     * @method GET
     * @access private
     */
    async fetchCart_getList(params: IQueryParamsGetListCart, loading?: boolean): Promise<unknown> {
      try {
        this.cart_dataLimit = { ...INITIAL_STATE_DATA_LIMIT };

        // set selected items
        this.cart_selectedItems = [];

        if (loading) {
          this.cart_listLoading = true;
        }
        const { data, error } = await useApiFetch(CART_ENDPOINT_GET_LIST, {
          method: 'GET',
          params,

          onResponse: async ({ response }) => {
            const result = response._data.data as ICart;
            if (result?.data_limit?.[0]) {
              this.cart_dataLimit = response?._data.data.data_limit[0];
            }

            if (result?.data_list_cart) {
              this.cart_dataListCart = result.data_list_cart;

              // set selected items
              this.cart_selectedItems = result?.data_list_cart?.cart_detail.map(item => item.sku);
            } else {
              this.cart_dataListCart = null;
            }
          },
          onRequestError: () => {
            this.cart_dataLimit = { ...INITIAL_STATE_DATA_LIMIT };

            this.cart_dataListCart = null;

            // set selected items
            this.cart_selectedItems = [];
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_listLoading = false;
      }
    },

    /**
     * @description Handle fetch api add to cart.
     * @url /cart/add
     * @method GET
     * @access private
     */
    async fetchCart_addToCart(body: IAddToCartPayload): Promise<unknown> {
      try {
        // this.cart_listLoading = true;
        const { data, error } = await useApiFetch(CART_ENDPOINT_ADD_CART, {
          method: 'POST',
          body,

          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_addToCart', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        // this.cart_listLoading = false;
      }
    },

    /**
     * @description Handle fetch api add to cart bulk.
     * @url /cart/add-bulk
     * @method GET
     * @access private
     */
    async fetchCart_addToCartBulk(body: IPayloadAddToCartBulk): Promise<boolean | string> {
      try {
        this.cart_addToCartLoading = true;
        const { error } = await useApiFetch(CART_ENDPOINT_ADD_CART_BULK, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_addToCartBulk', response._data);
          },
        });

        if (error.value) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          return Promise.resolve(error.value.data?.message);
        }
        return Promise.resolve(true);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_addToCartLoading = false;
      }
    },

    /**
     * @description Handle fetch api add to cart bulk.
     * @url /cart/add-bulk
     * @method GET
     * @access private
     */
    async fetchCart_customAddToCartBulk(body: IPayloadCustomAddToCartBulk): Promise<unknown> {
      try {
        this.cart_addToCartLoading = true;
        const { data, error } = await useApiFetch(CART_CUSTOM_ENDPOINT_ADD_CART_BULK, {
          method: 'POST',
          body,
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_addToCartLoading = false;
      }
    },

    /**
     * @description Handle fetch api check cart item.
     * @url /cart/check
     * @method POST
     * @access private
     */
    async fetchCart_checkItem(body: IPayloadToggleCartItem): Promise<unknown> {
      try {
        this.cart_checkLoading[body.article] = true;
        const { data, error } = await useApiFetch(CART_ENDPOINT_CHECK_CART, {
          method: 'POST',
          body,

          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_checkItem', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_checkLoading[body.article] = false;
      }
    },

    /**
     * @description Handle fetch api check cart item bulk.
     * @url /cart/check-bulk
     * @method POST
     * @access private
     */
    async fetchCart_checkItemBulk(body: IPayloadCheckItemBulk, allChecked?: boolean): Promise<unknown> {
      try {
        if (allChecked) {
          this.cart_checkAllLoading = true;
        } else {
          this.cart_checkLoading[body.sku_code_c as string] = true;
        }
        const { data, error } = await useApiFetch(CART_ENDPOINT_CHECK_BULK, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_checkItemBulk', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_checkLoading[body.sku_code_c as string] = false;
        if (allChecked) {
          this.cart_checkAllLoading = false;
        }
      }
    },

    /**
     * @description Handle fetch api check bulk cart custom.
     * @url /cart/custom/check-bulk
     * @method POST
     * @access private
     */
    async fetchCart_customCheckItemBulk(
      body: IPayloadCustomCheckCartItemBulk,
      allChecked?: boolean,
    ): Promise<unknown> {
      try {
        if (allChecked) {
          this.cart_checkAllLoading = true;
        } else {
          this.cart_checkLoading[body.attachments_group_id] = true;
        }
        const { data, error } = await useApiFetch(CART_CUSTOM_ENDPOINT_CHECK_BULK, {
          method: 'POST',
          body,
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_checkLoading[body.attachments_group_id] = false;
        if (allChecked) {
          this.cart_checkAllLoading = false;
        }
      }
    },

    /**
     * @description Handle fetch api uncheck cart item bulk.
     * @url /cart/check-bulk
     * @method POST
     * @access private
     */
    async fetchCart_unCheckItemBulk(body: IPayloadCheckItemBulk, allChecked?: boolean): Promise<unknown> {
      try {
        if (allChecked) {
          this.cart_checkAllLoading = true;
        } else {
          this.cart_checkLoading[body.sku_code_c as string] = true;
        }
        const { data, error } = await useApiFetch(CART_ENDPOINT_CHECK_BULK, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_checkItemBulk', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_checkLoading[body.sku_code_c as string] = false;
        if (allChecked) {
          this.cart_checkAllLoading = false;
        }
      }
    },

    /**
     * @description Handle fetch api uncheck cart item bulk.
     * @url /cart/check-bulk
     * @method POST
     * @access private
     */
    async fetchCart_customUncheckItemBulk(
      body: IPayloadCustomCheckCartItemBulk,
      allChecked?: boolean,
    ): Promise<unknown> {
      try {
        if (allChecked) {
          this.cart_checkAllLoading = true;
        } else {
          this.cart_checkLoading[body.attachments_group_id] = true;
        }
        const { data, error } = await useApiFetch(CART_ENDPOINT_CHECK_BULK, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_checkItemBulk', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_checkLoading[body.attachments_group_id] = false;
        if (allChecked) {
          this.cart_checkAllLoading = false;
        }
      }
    },

    /**
     * @description Handle fetch api check uncheck item.
     * @url /cart/uncheck
     * @method GET
     * @access private
     */
    async fetchCart_unCheckItem(body: IPayloadToggleCartItem): Promise<unknown> {
      try {
        this.cart_checkLoading[body.article] = true;
        const { data, error } = await useApiFetch(CART_ENDPOINT_UNCHECK_CART, {
          method: 'POST',
          body,

          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_unCheckItem', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_checkLoading[body.article] = false;
      }
    },

    /**
     * @description Handle fetch api delete item.
     * @url /cart/delete
     * @method GET
     * @access private
     */
    async fetchCart_deleteItem(cartDetailId: string): Promise<unknown> {
      try {
        // this.cart_listLoading = true;
        const { data, error } = await useApiFetch(`${CART_ENDPOINT_DELETE}/${cartDetailId}}`, {
          method: 'DELETE',
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_deleteItem', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        // this.cart_listLoading = false;
      }
    },

    /**
     * @description Handle fetch api delete item bulk.
     * @url /cart/delete-bulk
     * @method POST
     * @access private
     */
    async fetchCart_deleteItemBulk(body: IPayloadDeleteCartItemBulk): Promise<unknown> {
      try {
        this.cart_deleteLoading = true;
        const { data, error } = await useApiFetch(`${CART_ENDPOINT_DELETE_BULK}`, {
          method: 'POST',
          body,
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_deleteItemBulk', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_deleteLoading = false;
      }
    },

    /**
     * @description Handle fetch api delete custom item bulk.
     * @url /cart/custom/delete-bulk
     * @method POST
     * @access private
     */
    async fetchCart_customDeleteItemBulk(body: IPayloadCustomDeleteCartItemBulk): Promise<unknown> {
      try {
        this.cart_deleteLoading = true;
        const { data, error } = await useApiFetch(`${CART_CUSTOM_ENDPOINT_DELETE_BULK}`, {
          method: 'POST',
          body,
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_deleteLoading = false;
      }
    },

    /**
     * @description Handle fetch api delete all cart item.
     * @url /cart-delete-all
     * @method GET
     * @access private
     */
    async fetchCart_deleteAll(cartId: string): Promise<unknown> {
      try {
        // this.cart_listLoading = true;
        const { data, error } = await useApiFetch(CART_ENDPOINT_DELETE_ALL, {
          method: 'POST',
          body: {
            cartId,
          },
          onResponse: async ({ response }) => {
            console.log('RESPONSE fetchCart_deleteAll', response._data);
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        // this.cart_listLoading = false;
      }
    },

    /**
     * @description Handle fetch api update custom cart qty.
     * @url /cart/add
     * @method GET
     * @access private
     */
    async fetchCart_updateCustomQty(body: IPayloadUpdateCustomCartQty): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch<IBaseApiResponse<unknown>>(CART_CUSTOM_ENDPOINT_UPDATE_QTY, {
          method: 'POST',
          body,
        });

        if (error.value) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          return Promise.reject(error.value.data?.message as string);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(error as string);
      } finally {
        // this.cart_listLoading = false;
      }
    },

    /**
     * @description Handle post cart attachments.
     * @url /cart/attachments
     * @method POST
     * @access private
     */
    async fetchCart_postTextAttachments(
      body: IPayloadPostCartAttachments,
    ): Promise<IReturnCartPostTextAttachments> {
      try {
        const { data, error } = await useApiFetch<IBaseApiResponse<IReturnCartPostTextAttachments>>(
          CART_CUSTOM_POST_TEXT_ATTACHMENTS,
          {
            method: 'POST',
            body: {
              sku_code_c: body.sku_code_c,
              text: body.text.map(item => ({
                value: btoa(item.value),
                color: item.color,
              })),
            },
          },
        );

        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data.value?.data as IReturnCartPostTextAttachments);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle post cart remak
     * @url /cart/custom/remark
     * @method POST
     * @access private
     */
    async fetchCart_postRemark(body: IPayloadPostCartRemark): Promise<unknown> {
      console.log('fetchCart_postRemark', body);
      try {
        const { data, error } = await useApiFetch<IBaseApiResponse<unknown>>(CART_CUSTOM_POST_REMARK, {
          method: 'POST',
          body,
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data.value?.data as unknown);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      }
    },

    /**
     * @description Handle fetch api get cart list.
     * @url /get-configuration-b2b
     * @method GET
     * @access private
     */
    async fetchCart_getConfiguration(): Promise<unknown> {
      try {
        const { data, error } = await useApiFetch(CART_GET_CONFIGURATION, {
          method: 'GET',

          onResponse: async ({ response }) => {
            const result = response._data.data as ICartConfiguration;
            if (result) {
              this.cart_configuration = result;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.cart_listLoading = false;
      }
    },
  },
});
