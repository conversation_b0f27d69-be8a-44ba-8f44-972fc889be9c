<script lang="ts" setup>
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import { useCheckoutService } from '../../checkout/services/checkout.service';
import CartList from '../components/CartList.vue';
import CartSummary from '../components/CartSummary.vue';

// Services
import { useCartService } from '../services/cart.service';
import CartRemarkForm from '../components/CartRemarkForm.vue';

const route = useRoute();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  cart_queryParamsGetListCart,
  cart_dataListCart,
  cart_dataLimit,
  cart_listLoading,
  cart_showToast,
  cart_toastData,
  cart_getList,
  cart_addToCart,
  cart_addToCartLoading,
  cart_addToCartBulk,
  cart_customAddToCartBulk,
  cart_updateQty,
  cart_deleteLoading,
  cart_checkLoading,
  cart_checkAllLoading,
  cart_deleteItemBulk,
  cart_selectedItems,
  cart_deleteAll,
  cart_checkItemBulk,
  cart_customCheckItemBulk,
  cart_unCheckItemBulk,
  cart_customUncheckItemBulk,
  cart_onCheckAll,
  cart_onDeleteBulk,
  cart_onCustomDeleteBulk,
  cart_deleteItem,
  cart_activeTab,
  cart_isShowModalFormRemark,
  cart_remarkForm,
  cart_remarkFormValidations,
  cart_submitFormRemark,
  cart_updateCustomQty,
  cart_getConfiguration,
  cart_configuration,
} = useCartService();
const { checkout_createOrder, checkout_loading, checkout_listData } = useCheckoutService();

/**
 * @description Provide all the data and methods what we need
 */
provide('cart', {
  cart_queryParamsGetListCart,
  cart_dataListCart,
  cart_dataLimit,
  cart_listLoading,
  cart_showToast,
  cart_toastData,
  cart_getList,
  cart_addToCart,
  cart_addToCartLoading,
  cart_updateQty,
  cart_addToCartBulk,
  cart_customAddToCartBulk,
  cart_deleteLoading,
  cart_checkLoading,
  cart_checkAllLoading,
  cart_deleteItemBulk,
  cart_selectedItems,
  cart_deleteAll,
  cart_checkItemBulk,
  cart_customCheckItemBulk,
  cart_unCheckItemBulk,
  cart_customUncheckItemBulk,
  cart_onCheckAll,
  cart_onDeleteBulk,
  cart_onCustomDeleteBulk,
  cart_deleteItem,
  cart_activeTab,
  cart_isShowModalFormRemark,
  cart_remarkForm,
  cart_remarkFormValidations,
  cart_updateCustomQty,
  cart_submitFormRemark,
  cart_configuration,
});
provide('checkout', {
  checkout_createOrder,
  checkout_loading,
  checkout_listData,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onMounted(async () => {
  const isRegular = route.query?.type === 'regular';
  cart_activeTab.value = isRegular ? '0' : '1';
  cart_queryParamsGetListCart.is_custom = isRegular ? '0' : '1';
  setTimeout(async () => {
    await Promise.allSettled([
      cart_getList({
        is_custom: isRegular ? '0' : '1',
      }),
      cart_getConfiguration(),
    ]);
  }, 350);
});
</script>

<template>
  <MainLayout page-title="Keranjang" :required-auth="true">
    <section id="cart-page" class="flex flex-col gap-6 w-full h-full relative z-0">
      <div class="grid sm:grid-cols-[1fr_310px] grid-cols-1 gap-8 mb-3">
        <CartList />
        <div>
          <div class="sticky top-5">
            <CartSummary />
          </div>
        </div>
      </div>
      <CartRemarkForm :is-checkout="false" />
    </section>
  </MainLayout>
</template>
