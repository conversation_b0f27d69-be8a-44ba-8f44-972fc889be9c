// Components
import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/cart',
    component: AppBaseWrapper,
    children: [
      {
        path: '',
        name: 'cart',
        component: () => import('../views/CartUI.vue'),
        meta: {
          requireAuth: true,
          breadcrumbs: [
            {
              label: 'Beranda',
              path: '/dashboard',
            },
            {
              label: 'Katalog',
              path: '/catalog',
            },
            {
              label: 'Keranjang',
              path: null,
            },
          ],
        },
      },
    ],
  },
];

export default routes;
