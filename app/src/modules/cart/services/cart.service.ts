// Interfaces
import type {
  IAddToCartPayload,
  ICartProvided,
  IPayloadAddToCartBulk,
  IPayloadCheckItemBulk,
  IPayloadDeleteCartItemBulk,
  IPayloadToggleCartItem,
  IQueryParamsGetListCart,
  IPayloadUpdateCartItem,
  IPayloadPostCartAttachments,
  IReturnCartPostTextAttachments,
  IPayloadCustomAddToCartBulk,
  IPayloadCustomCheckCartItemBulk,
  IPayloadCustomDeleteCartItemBulk,
  IPayloadUpdateCustomCartQty,
} from '../interfaces/cart.interface';

// Store
import { storeToRefs } from 'pinia';
import { useCartStore } from '../stores/cart.store';
import { CART_PAGINATION_SIZE, CART_TYPE_TABS } from '../constants/cart.constant';
import { required } from '@vuelidate/validators';
import useVuelidate from '@vuelidate/core';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useCartService = (): ICartProvided => {
  /**
   * @description Injected variables
   */
  const store = useCartStore(); // Instance of the store
  const {
    cart_dataListCart,
    cart_dataLimit,
    cart_listLoading,
    cart_showToast,
    cart_toastData,
    cart_addToCartLoading,
    cart_checkLoading,
    cart_checkAllLoading,
    cart_deleteLoading,
    cart_selectedItems,
    cart_isShowModalFormRemark,
    cart_configuration,
  } = storeToRefs(store);

  const toast = useToast();

  /**
   * @description Reactive data binding
   */
  const cart_queryParamsGetListCart = reactive<IQueryParamsGetListCart>({
    is_custom: CART_TYPE_TABS[0].value,
    page: 1,
    per_page: CART_PAGINATION_SIZE[0],
    is_available: 'tersedia',
  });

  const cart_activeTab = ref('1');

  /**
   * @description Reactive data binding
   */
  const cart_remarkForm = reactive({
    remark: '',
    attachment_group_id: '',
    is_checkout: false,
  });

  /**
   * @description Form validations
   */
  const cart_remarkFormRules = reactive(() => ({
    remark: { required },
    // attachment_group_id: { required },
  }));

  const cart_remarkFormValidations = useVuelidate(cart_remarkFormRules, cart_remarkForm, {
    $autoDirty: true,
  });

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_getList function from the store to handle the request.
   */
  const cart_getConfiguration = async (): Promise<void> => {
    await store.fetchCart_getConfiguration();
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_getList function from the store to handle the request.
   */
  const cart_getList = async (params?: Partial<IQueryParamsGetListCart>): Promise<void> => {
    const requestParams = {
      ...{ ...cart_queryParamsGetListCart, is_custom: cart_activeTab.value },
      ...params,
    };
    await store.fetchCart_getList(requestParams, true);
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_checkItem function from the store to handle the request.
   */
  const cart_addToCart = async (body: IAddToCartPayload): Promise<void> => {
    await store.fetchCart_addToCart(body);
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_addToCartBulk function from the store to handle the request.
   */
  const cart_addToCartBulk = async (body: IPayloadAddToCartBulk): Promise<boolean> => {
    try {
      const result = await store.fetchCart_addToCartBulk(body);
      if (typeof result === 'string') {
        if (result?.includes('Your account has not been verified')) {
          toast.add({
            severity: 'error',
            summary: 'Akun belum diverifikasi. Tidak dapat menambahkan produk ke keranjang.',
            detail: null,
            life: 3000,
          });
        }
        return false;
      }
      return result;
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      toast.add({
        severity: 'error',
        summary: 'Failed to add to cart',
        detail: null,
        life: 3000,
      });
      return false;
    }
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_customAddToCartBulk function from the store to handle the request.
   */
  const cart_customAddToCartBulk = async (body: IPayloadCustomAddToCartBulk): Promise<void> => {
    await store.fetchCart_customAddToCartBulk(body);
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_checkItem function from the store to handle the request.
   */
  const cart_checkItem = async (body: IPayloadToggleCartItem): Promise<void> => {
    await store.fetchCart_checkItem(body);
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_unCheckItem function from the store to handle the request.
   */
  const cart_unCheckItem = async (body: IPayloadToggleCartItem): Promise<void> => {
    await store.fetchCart_unCheckItem(body);
  };

  /**
   * @description Handle fetch api check item bulk. We call the fetchCart_checkItemBulk function from the store to handle the request.
   */
  const cart_checkItemBulk = async (body: IPayloadCheckItemBulk): Promise<void> => {
    await store
      .fetchCart_checkItemBulk(body)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
  };

  /**
   * @description Handle fetch api check item bulk. We call the fetchCart_checkItemBulk function from the store to handle the request.
   */
  const cart_customCheckItemBulk = async (body: IPayloadCustomCheckCartItemBulk): Promise<void> => {
    await store
      .fetchCart_customCheckItemBulk(body)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
  };

  /**
   * @description Handle fetch api uncheck item bulk. We call the fetchCart_checkItemBulk function from the store to handle the request.
   */
  const cart_unCheckItemBulk = async (body: IPayloadCheckItemBulk): Promise<void> => {
    await store
      .fetchCart_unCheckItemBulk(body)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
  };

  /**
   * @description Handle fetch api uncheck item bulk. We call the fetchCart_checkItemBulk function from the store to handle the request.
   */
  const cart_customUncheckItemBulk = async (body: IPayloadCustomCheckCartItemBulk): Promise<void> => {
    await store
      .fetchCart_customUncheckItemBulk(body)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
  };

  /**
   * @description Handle fetch api check all item. We call the fetchCart_checkItemBulk | fetchCart_unCheckItemBulk  function from the store to handle the request.
   */
  const cart_onCheckAll = async (
    body: IPayloadCheckItemBulk[],
    checked: boolean,
    isCartCustom: boolean,
  ): Promise<void> => {
    cart_checkAllLoading.value = true;

    if (isCartCustom) {
      await Promise.all(
        checked
          ? body.map(
              async (req: IPayloadCheckItemBulk) =>
                await store.fetchCart_customCheckItemBulk(req as IPayloadCustomCheckCartItemBulk),
            )
          : body.map(
              async (req: IPayloadCheckItemBulk) =>
                await store.fetchCart_customUncheckItemBulk(req as IPayloadCustomCheckCartItemBulk),
            ),
      )
        .then(() => {})
        .catch(error => {
          throw error;
        })
        .finally(async () => {
          await store.fetchCart_getList(cart_queryParamsGetListCart, false);
          cart_checkAllLoading.value = false;
        });
    } else {
      // regular cart
      await Promise.all(
        checked
          ? body.map(async (req: IPayloadCheckItemBulk) => await store.fetchCart_checkItemBulk(req))
          : body.map(async (req: IPayloadCheckItemBulk) => await store.fetchCart_unCheckItemBulk(req)),
      )
        .then(() => {})
        .catch(error => {
          throw error;
        })
        .finally(async () => {
          await store.fetchCart_getList(cart_queryParamsGetListCart, false);
          cart_checkAllLoading.value = false;
        });
    }
  };

  /**
   * @description Handle fetch api check all item. We call the fetchCart_deleteItemBulk  function from the store to handle the request.
   */
  const cart_onDeleteBulk = async (body: IPayloadDeleteCartItemBulk[]): Promise<void> => {
    cart_deleteLoading.value = true;
    // Using Promise.all to wait for all requests to complete
    await Promise.all(
      body.map(async (req: IPayloadDeleteCartItemBulk) => await store.fetchCart_deleteItemBulk(req)),
    )
      .then(() => {})
      .catch(error => {
        // If any promise rejects, this will execute
        console.error('One of the requests failed:', error);
        throw error;
      })
      .finally(async () => {
        await store.fetchCart_getList(cart_queryParamsGetListCart, true);
        cart_deleteLoading.value = false;
      });
  };

  /**
   * @description Handle fetch api check all item. We call the fetchCart_deleteItemBulk  function from the store to handle the request.
   */
  const cart_onCustomDeleteBulk = async (body: IPayloadCustomDeleteCartItemBulk[]): Promise<void> => {
    cart_deleteLoading.value = true;
    // Using Promise.all to wait for all requests to complete
    await Promise.all(
      body.map(async (req: IPayloadCustomDeleteCartItemBulk) => await store.fetchCart_customDeleteItemBulk(req)),
    )
      .then(() => {})
      .catch(error => {
        throw error;
      })
      .finally(async () => {
        await store.fetchCart_getList(cart_queryParamsGetListCart, true);
        cart_deleteLoading.value = false;
      });
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_deleteItem function from the store to handle the request.
   */
  const cart_deleteItem = async (cartDetailId: string): Promise<void> => {
    await store.fetchCart_deleteItem(cartDetailId);
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_deleteItemBulk function from the store to handle the request.
   */
  const cart_deleteItemBulk = async (body: IPayloadDeleteCartItemBulk): Promise<void> => {
    await store
      .fetchCart_deleteItemBulk(body)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
  };

  /**
   * @description Handle fetch api get activities of product. We call the fetchCart_deleteAll function from the store to handle the request.
   */
  const cart_deleteAll = async (cartId: string): Promise<void> => {
    await store
      .fetchCart_deleteAll(cartId)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, true));
  };

  /**
   * @description Handle fetch api to update qty in cart item
   */
  const cart_updateQty = async (body: IPayloadUpdateCartItem): Promise<void> => {
    await store
      .fetchCart_addToCart(body)
      .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
  };

  /**
   * @description Handle fetch api to update qty in cart item
   */
  const cart_updateCustomQty = async (body: IPayloadUpdateCustomCartQty): Promise<void> => {
    try {
      await store
        .fetchCart_updateCustomQty(body)
        .finally(async () => await store.fetchCart_getList(cart_queryParamsGetListCart, false));
    } catch (e) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: String(e),
        life: 3000,
      });
    }
  };

  /**
   * @description Handle fetch api to post cart attachments
   */
  const cart_postTextAttachments = async (
    body: IPayloadPostCartAttachments,
  ): Promise<IReturnCartPostTextAttachments> => {
    const result = await store.fetchCart_postTextAttachments(body);
    return result;
  };

  /**
   * @description Handle fetch api to post cart attachments
   */
  const cart_submitFormRemark = async (isCheckout?: boolean): Promise<unknown> => {
    const attachmentGroupId = cart_remarkForm.attachment_group_id;
    const remark = cart_remarkForm.remark;

    if (attachmentGroupId && remark) {
      const result = await store.fetchCart_postRemark({
        attachment_group_id: cart_remarkForm.attachment_group_id,
        remark: cart_remarkForm.remark,
        is_checkout: isCheckout ?? false,
      });
      if (result) {
        cart_isShowModalFormRemark.value = false;
        cart_remarkForm.attachment_group_id = '';
        cart_remarkForm.remark = '';
        cart_getList();
        toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Catatan berhasil ditambahkan',
          life: 3000,
        });
        return result;
      }
    }
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(cart_queryParamsGetListCart, async () => {
    await cart_getList();
  });

  /**
   * @description Return everything what we need into an object
   */
  return {
    cart_queryParamsGetListCart,
    cart_activeTab,
    cart_listLoading,
    cart_dataListCart,
    cart_dataLimit,
    cart_showToast,
    cart_toastData,
    cart_checkLoading,
    cart_checkAllLoading,
    cart_deleteLoading,
    cart_selectedItems,
    cart_addToCartLoading,
    cart_getList,
    cart_addToCart,
    cart_addToCartBulk,
    cart_customAddToCartBulk,
    cart_updateQty,
    cart_updateCustomQty,
    cart_postTextAttachments,
    cart_checkItem,
    cart_unCheckItem,
    cart_checkItemBulk,
    cart_customCheckItemBulk,
    cart_unCheckItemBulk,
    cart_customUncheckItemBulk,
    cart_onCheckAll,
    cart_onDeleteBulk,
    cart_onCustomDeleteBulk,
    cart_deleteItem,
    cart_deleteItemBulk,
    cart_deleteAll,
    cart_isShowModalFormRemark,
    cart_remarkForm,
    cart_remarkFormValidations,
    cart_submitFormRemark,
    cart_getConfiguration,
    cart_configuration,
  };
};
