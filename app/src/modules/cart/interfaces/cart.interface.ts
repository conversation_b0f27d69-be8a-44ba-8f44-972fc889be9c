import type { Validation } from '@vuelidate/core';
import type { Reactive } from 'vue';

export interface IAddToCartPayload {
  article: string;
  qty: number;
}

export interface IPayloadToggleCartItem {
  article: string;
  selected: boolean;
}

export interface IPayloadAddToCartBulk {
  sku_code_c: string;
  items: {
    article: string;
    qty: number;
  }[];
}

export interface IPayloadCustomAddToCartBulk extends Omit<IPayloadAddToCartBulk, 'sku_code_c'> {
  attachments_group_id: string;
}

export interface IPayloadCheckItemBulk {
  sku_code_c: string | null;
  attachments_group_id: string | null;
  items: {
    article: string;
    selected: boolean;
  }[];
}

export interface IPayloadCustomCheckCartItemBulk extends Omit<IPayloadCheckItemBulk, 'sku_code_c'> {
  attachments_group_id: string;
}

export interface IPayloadDeleteCartItemBulk {
  sku_code_c: string;
  items: {
    article: string;
  }[];
}

export interface IPayloadCustomDeleteCartItemBulk extends Omit<IPayloadDeleteCartItemBulk, 'sku_code_c'> {
  attachments_group_id: string;
}

export interface IPayloadUpdateCartItem {
  article: string;
  qty: number;
}

export interface IPayloadUpdateCustomCartQty {
  cart_detail_id: string;
  qty: number;
}

export interface IQueryParamsGetListCart {
  is_custom: string;
  page: number;
  per_page: number;
  is_available: 'tersedia' | 'tidak tersedia';
}

export interface ICartDetailDataLimit {
  terpakai: string;
  customer: string;
  sisa: string;
  currency: string;
  plafond: string;
  persentasi: string;
  total_harga: number;
  total_selected_barang: number;
  total_barang: number;
  sub_total_sku: number;
  over_limit: boolean;
  sub_total: number;
  total_custom: number;
  harga_custom: string;
  cart_count: number;
}

export interface IPayloadPostCartAttachments {
  text: { value: string; color: string }[];
  sku_code_c: string;
}

export interface IReturnCartPostTextAttachments {
  attachments_group_id: string;
}

export interface IPayloadPostCartRemark {
  attachment_group_id: string;
  remark: string;
  is_checkout: boolean;
}

export interface ICartConfiguration {
  CUSTOM_ORDER_LOGO_PRICE: number;
  CUSTOM_ORDER_TEXT_PRICE: number;
  MIN_CUSTOM_ORDER_AMOUNT: number;
  MIN_CUSTOM_ORDER_QTY: number;
  MIN_REGULAR_ORDER_AMOUNT: number;
  MIN_REGULAR_ORDER_QTY: number;
}

export interface ICartProvided {
  cart_queryParamsGetListCart: Reactive<IQueryParamsGetListCart>;
  cart_listLoading: Ref<boolean>;
  cart_dataListCart: Ref<ICart['data_list_cart'] | null>;
  cart_checkLoading: Ref<{
    [key: string]: boolean;
  }>;
  cart_checkAllLoading: Ref<boolean>;
  cart_dataLimit: Ref<ICartDetailDataLimit>;
  cart_getList: (params?: Partial<IQueryParamsGetListCart>) => Promise<void>;
  cart_addToCart: (body: IAddToCartPayload) => Promise<void>;
  cart_updateQty: (body: IPayloadUpdateCartItem) => Promise<void>;
  cart_updateCustomQty: (body: IPayloadUpdateCustomCartQty) => Promise<void>;
  cart_addToCartBulk: (body: IPayloadAddToCartBulk) => Promise<boolean>;
  cart_customAddToCartBulk: (body: IPayloadCustomAddToCartBulk) => Promise<void>;
  cart_checkItem: (body: IPayloadToggleCartItem) => Promise<void>;
  cart_unCheckItem: (body: IPayloadToggleCartItem) => Promise<void>;
  cart_checkItemBulk: (body: IPayloadCheckItemBulk) => Promise<void>;
  cart_customCheckItemBulk: (body: IPayloadCustomCheckCartItemBulk) => Promise<void>;
  cart_unCheckItemBulk: (body: IPayloadCheckItemBulk) => Promise<void>;
  cart_customUncheckItemBulk: (body: IPayloadCustomCheckCartItemBulk) => Promise<void>;
  cart_onCheckAll: (body: IPayloadCheckItemBulk[], checked: boolean, isCartCustom: boolean) => Promise<void>;
  cart_onDeleteBulk: (body: IPayloadDeleteCartItemBulk[]) => Promise<void>;
  cart_onCustomDeleteBulk: (body: IPayloadCustomDeleteCartItemBulk[]) => Promise<void>;
  cart_deleteItem: (cartDetailId: string) => Promise<void>;
  cart_deleteItemBulk: (body: IPayloadDeleteCartItemBulk) => Promise<void>;
  cart_deleteAll: (cartId: string) => Promise<void>;
  cart_postTextAttachments: (payload: IPayloadPostCartAttachments) => Promise<IReturnCartPostTextAttachments>;
  cart_showToast: Ref<boolean>;
  cart_toastData: Ref<{
    count: number;
    variants: string[];
  }>;
  cart_addToCartLoading: Ref<boolean>;
  cart_deleteLoading: Ref<boolean>;
  cart_selectedItems: Ref<string[]>;
  cart_activeTab: Ref<string>;
  cart_isShowModalFormRemark: Ref<boolean>;
  cart_remarkForm: Reactive<IPayloadPostCartRemark>;
  cart_remarkFormValidations: globalThis.Ref<Validation>;
  cart_submitFormRemark: (isCheckout?: boolean) => Promise<unknown>;
  cart_getConfiguration: () => Promise<unknown>;
  cart_configuration: Ref<ICartConfiguration | null>;
}

export interface ICartStoreState {
  cart_listLoading: boolean;
  cart_dataLimit: ICartDetailDataLimit;
  cart_dataListCart: ICart['data_list_cart'] | null;
  cart_checkLoading: {
    [key: string]: boolean;
  };
  cart_checkAllLoading: boolean;
  cart_showToast: boolean;
  cart_toastData: {
    count: number;
    variants: string[];
  };
  cart_addToCartLoading: boolean;
  cart_deleteLoading: boolean;
  cart_selectedItems: string[];
  cart_isShowModalFormRemark: boolean;
  cart_configuration: ICartConfiguration | null;
}
