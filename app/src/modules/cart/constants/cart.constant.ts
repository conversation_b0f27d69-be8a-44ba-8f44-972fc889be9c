import type { ICartDetailDataLimit } from '../interfaces/cart.interface';

export const CART_PAGINATION_SIZE = Array.from(BASE_PAGINATION_SIZE);

export const INITIAL_STATE_DATA_LIMIT: ICartDetailDataLimit = {
  terpakai: '',
  customer: '',
  sisa: '',
  currency: 'IDR',
  plafond: '',
  persentasi: '',
  total_harga: 0,
  total_custom: 0,
  harga_custom: '0',
  total_barang: 0,
  sub_total_sku: 0,
  over_limit: false,
  total_selected_barang: 0,
  sub_total: 0,
  cart_count: 0,
};

export enum CART_TYPE {
  CUSTOM = 'Custom Order',
  REGULER = 'Regular Order',
}

export const CART_TYPE_TABS = [
  {
    label: CART_TYPE.CUSTOM,
    value: '1',
  },
  {
    label: CART_TYPE.REGULER,
    value: '0',
  },
];
