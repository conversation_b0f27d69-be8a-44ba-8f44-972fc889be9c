<script setup lang="ts">
// components
import type {
  ICartProvided,
  IPayloadCustomDeleteCartItemBulk,
  IPayloadDeleteCartItemBulk,
} from '../interfaces/cart.interface';
import { capitalizeFirstLetter } from '~/app/src/core/helpers/text.helper';
import CartProductVariantTable from './CartProductVariantTable.vue';

interface Props {
  item: ICartItem;
}

const props = defineProps<Props>();
const config = useRuntimeConfig();

/**
 * @description Injecting dependencies
 */
const {
  cart_checkLoading,
  cart_checkAllLoading,
  cart_queryParamsGetListCart,
  cart_checkItemBulk,
  cart_customCheckItemBulk,
  cart_onDeleteBulk,
  cart_onCustomDeleteBulk,
  cart_activeTab,
  cart_isShowModalFormRemark,
  cart_remarkForm,
} = inject<ICartProvided>('cart')!;

const filteredUniqueVariant = computed(() => {
  return props.item.items?.filter((item, index, self) => index === self.findIndex(t => t.varian === item.varian));
});

const selectedTab = ref(filteredUniqueVariant.value[0].varian);

const filteredVariants = computed(() => {
  return props.item.items?.filter(i => i.varian === selectedTab.value);
});

const isCustom = computed(() => {
  return cart_queryParamsGetListCart.is_custom === '1' && cart_activeTab.value === '1';
});

/**
 * Sub total per product
 */
const subtotalPerProduct = computed(() => {
  const price = parseFloat(props.item.price);

  const itemsWithTotals = props.item.items.map(item => {
    return price * item.qty;
  });
  return itemsWithTotals.reduce((sum, item) => sum + item, 0);
});

const handleDeleteOne = (item: ICartItem, articles: { article: string }[]) => {
  if (cart_activeTab.value === '1') {
    const params: IPayloadCustomDeleteCartItemBulk[] = [
      {
        attachments_group_id: item.attachments_group_id,
        items: articles.map(item => ({ article: item.article })),
      },
    ];
    cart_onCustomDeleteBulk(params);
  } else {
    const params: IPayloadDeleteCartItemBulk[] = [
      {
        sku_code_c: item.sku,
        items: articles.map(item => ({ article: item.article })),
      },
    ];
    cart_onDeleteBulk(params);
  }
};

const onChangeCheckbox = (value: boolean) => {
  if (cart_activeTab.value === '1') {
    cart_customCheckItemBulk({
      attachments_group_id: props.item.attachments_group_id,
      items: props.item.items.map(i => ({
        article: i.article,
        selected: value,
      })),
    });
  } else {
    cart_checkItemBulk({
      sku_code_c: props.item.sku,
      attachments_group_id: null,
      items: props.item.items.map(i => ({
        article: i.article,
        selected: value,
      })),
    });
  }
};

const customProductName = computed<string[]>(() => {
  const _customName = [];
  const imageAttachments = props?.item.cart_attachments?.filter(t => t.file_path);
  const textAttachments = props?.item?.cart_attachments?.filter(t => t.text);

  if (imageAttachments?.length > 0) {
    _customName.push('Logo');
  }

  if (textAttachments?.length > 0) {
    _customName.push('Text');
  }

  return _customName;
});

const estimatePriceLogo = computed<number>(() => {
  let customPrice = 0;
  const imageAttachments = props?.item.cart_attachments?.filter(t => t.file_path);
  if (imageAttachments?.length > 0) {
    const imagePrice = imageAttachments.reduce((total, item) => {
      return total + item.estimate_price;
    }, 0);
    const quantities = props?.item?.items.reduce((total, item) => {
      return total + item.qty;
    }, 0);
    customPrice += imagePrice * quantities;
  }
  return customPrice;
});

const estimateTextPrice = computed<number>(() => {
  let customPrice = 0;
  const textAttachments = props?.item.cart_attachments?.filter(t => t.text);
  if (textAttachments?.length > 0) {
    const textPrice = textAttachments.reduce((total, item) => {
      return total + item.estimate_price;
    }, 0);
    const quantities = props?.item?.items.reduce((total, item) => {
      return total + item.qty;
    }, 0);
    customPrice += textPrice * quantities;
  }
  return customPrice;
});

const onClickAddRemark = (attachmentGroupId: string, prevRemark?: string) => {
  if (attachmentGroupId) {
    cart_isShowModalFormRemark.value = true;
    cart_remarkForm.attachment_group_id = attachmentGroupId;
    cart_remarkForm.remark = prevRemark as string;
  }
};

const onClickViewAttachments = (url: string) => {
  if (url) {
    const html = `
      <!DOCTYPE html>
      <html lang="en"> 
        <head>
          <meta charset="UTF-8" />
          <title>Preview</title>
          <style>
            body {
              margin: 0;
              background-color: #000;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
            }
            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          </style>
        </head>
        <body> 
          <img src="${url}" alt="Preview Image" />
        </body>
      </html>
    `;

    const blob = new Blob([html], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);
    window.open(blobUrl, '_blank');
  }
};
</script>

<template>
  <div class="w-full">
    <div class="w-full grid sm:grid-cols-[20fr_70fr] grid-cols-[10fr_90fr] gap-2 md:gap-4 mb-3">
      <div class="flex items-start">
        <div class="w-[24px]">
          <PrimeVueCheckbox
            v-model="item.selected"
            binary
            input-id="cart-check-all"
            name="cart-check-all"
            class="cart-checkbox sm:mr-3"
            :disabled="cart_checkAllLoading || cart_checkLoading?.[item.sku]"
            @update:model-value="onChangeCheckbox"
          />
        </div>
        <div class="sm:w-[159px] sm:h-[159px] w-24 h-24 bg-grey-200 relative flex items-center justify-center">
          <NuxtLink :to="`/product/detail/${item.sku}`">
            <NuxtImg
              :src="item.image_url"
              alt="product"
              class="sm:w-full sm:h-full h-24 w-24 object-cover"
              :class="[!item.is_available && 'opacity-70']"
            />
          </NuxtLink>
          <div
            v-if="!item.is_available"
            class="absolute bg-[#18191A] text-[#fbfbfb] rounded-lg px-3 py-2.5 opacity-70"
          >
            <span class="text-[14px]">Stok Tidak Ada</span>
          </div>
        </div>
      </div>

      <div class="sm:w-full">
        <div class="flex justify-between items-center">
          <div class="">
            <h4 class="font-bold font-druk md:text-[32px] leading-none mb-2">
              {{ item.product_name }}
            </h4>
          </div>
          <div class="flex gap-4 items-center">
            <div v-if="item.is_available && isCustom" class="sm:block cursor-pointer hidden">
              <PrimeVueButton
                variant="outlined"
                class="!border-[#ACB1B4] border !h-[37px]"
                @click="() => onClickAddRemark(item.attachments_group_id, item.remark as string)"
              >
                <img src="/icons/edit-icon.svg" alt="" />
                <span class="!text-[#18191A] font-medium">Catatan</span>
              </PrimeVueButton>
            </div>
            <div
              v-if="item.is_available"
              class="sm:block cursor-pointer hidden"
              @click="() => handleDeleteOne(item, item.items)"
            >
              <img src="/icons/trash-red.svg" alt="" />
            </div>
          </div>
        </div>
        <div class="flex items-center gap-3 mb-4">
          <template v-for="(flag, index) in item?.flag" :key="`progress-${index}`">
            <div v-if="index !== 0" class="h-[4px] w-[4px] rounded-full bg-muted"></div>
            <p class="text-[14px] text-muted">{{ capitalizeFirstLetter(flag) }}</p>
          </template>
        </div>
        <div v-if="isCustom" class="text-[14px]">
          <div class="grid grid-cols-[110px_1fr] mt-2">
            <p class="text-[#18191A] font-medium">Logo Gambar</p>
            <div v-if="item.cart_attachments?.filter(t => t.file_path)?.length > 0" class="flex flex-wrap">
              <div
                v-for="(image, index) in item.cart_attachments?.filter(t => t.file_path)"
                :key="index"
                class="flex gap-1 -mt-[4px]"
              >
                <NuxtImg
                  :src="config.public.middlewareBaseUrl + '/stream/' + image.file_path"
                  class="h-[22px] md:h-[30px] w-auto"
                />
                <PrimeVueButton
                  variant="text"
                  class="!underline !bg-[transparent] !px-0 !py-0 !text-[#147FFF] mr-2"
                  @click="
                    () => onClickViewAttachments(config.public.middlewareBaseUrl + '/stream/' + image.file_path)
                  "
                  >View</PrimeVueButton
                >
              </div>
            </div>
            <div v-else>-</div>
          </div>
          <div class="grid grid-cols-[110px_1fr] mt-2">
            <p class="text-[#18191A] font-medium">Logo Text</p>
            <div v-if="item.cart_attachments?.filter(t => t.text)?.length > 0" class="flex flex-wrap gap-2">
              <div
                v-for="(textValue, index) in item.cart_attachments?.filter(t => t.text)"
                :key="index"
                class="flex gap-1 -mt-[4px]"
              >
                <NuxtImg class="h-[20px] w-[20px]" src="/icons/text-icon.svg" />
                <div class="flex items-center flex-wrap gap-1 mr-2">
                  <p class="text-[14px] text-muted">{{ textValue.text }}</p>
                  <div class="h-[4px] w-[4px] rounded-full bg-muted"></div>
                  <p class="text-[14px] text-muted">{{ textValue.color }}</p>
                </div>
              </div>
            </div>
            <div v-else>-</div>
          </div>
        </div>
        <div v-else class="text-[14px]">
          <span class="text-[#686F72]"> Warna : </span> <span class="font-medium"> {{ selectedTab }} </span>
        </div>
        <template v-if="isCustom">
          <div class="sm:flex w-full items-center justify-between mb-1 mt-2 hidden">
            <p class="text-[14px] text-[#686F72]">Harga Logo (Gambar)</p>
            <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold mr-4">
              {{ estimatePriceLogo ? useCurrencyFormat(estimatePriceLogo) : '-' }}
            </p>
          </div>
          <div class="sm:flex w-full items-center justify-between mb-1 hidden">
            <p class="text-[14px] text-[#686F72]">Harga Logo (Text)</p>
            <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold mr-4">
              {{ estimateTextPrice ? useCurrencyFormat(estimateTextPrice) : '-' }}
            </p>
          </div>
        </template>
        <div class="sm:flex w-full items-center justify-between mb-2 hidden">
          <p class="text-[14px] font-bold">Total Harga</p>
          <p class="font-bold sm:text-[20px] text-16px text-base mr-4">
            {{
              subtotalPerProduct
                ? useCurrencyFormat(subtotalPerProduct + estimatePriceLogo + estimateTextPrice)
                : '-'
            }}
          </p>
        </div>
        <div class="w-full">
          <PrimeVueTabs v-model:value="selectedTab" class="pt-3">
            <PrimeVueTabList
              v-if="isCustom"
              class="w-fit"
              :pt="{
                activeBar: '!bg-header-orange',
              }"
            >
              <PrimeVueTab
                v-for="(variant, variantIndex) in filteredUniqueVariant"
                :key="`variant-${variantIndex}`"
                :value="variant.varian"
                :pt="{
                  root: `text-sm ${useActiveTab(selectedTab, variant.varian)}`,
                }"
              >
                {{ useCapitalize(variant.varian) }}
              </PrimeVueTab>
            </PrimeVueTabList>
            <PrimeVueTabList
              v-else
              class="w-fit"
              :pt="{
                activeBar: '!border-white border',
                root: '!border-white border',
              }"
            >
              <PrimeVueTab
                v-for="(variant, variantIndex) in filteredUniqueVariant"
                :key="`variant-${variantIndex}`"
                :value="variant.varian"
                :pt="{
                  activeBar: '!border-white border ',
                  root: `!border-white border !py-2 !px-0 !mr-2`,
                }"
              >
                <NuxtImg
                  :src="variant.image_variant"
                  alt="Product Image"
                  class="h-15 w-15  border-2 rounded {{product_selectedTabProductVariant == variant.product_variant_c ? 'border-black' : '#ECECEC'  }}"
                />
              </PrimeVueTab>
            </PrimeVueTabList>
            <div
              class="flex justify-end mr-6 mb-2 cursor-pointer sm:hidden"
              @click="() => handleDeleteOne(item, item.items)"
            >
              <img src="/icons/trash-red.svg" alt="" />
            </div>

            <template v-if="isCustom">
              <div class="flex w-full items-center justify-between mb-2 sm:hidden">
                <p class="text-[14px] text-[#686F72]">Biaya Logo (Sablon)</p>
                <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold mr-4">
                  {{ estimatePriceLogo ? useCurrencyFormat(estimatePriceLogo) : '-' }}
                </p>
              </div>
              <div class="flex w-full items-center justify-between mb-2 sm:hidden">
                <p class="text-[14px] text-[#686F72]">Biaya Logo (Text)</p>
                <p class="text-[#686F72] sm:text-[20px] text-16px font-semibold mr-4">
                  {{ estimateTextPrice ? useCurrencyFormat(estimateTextPrice) : '-' }}
                </p>
              </div>
            </template>

            <div class="flex w-full items-center justify-between mb-2 sm:hidden">
              <p class="text-[14px] font-bold">Total Harga</p>
              <p class="font-bold sm:text-[20px] text-16px text-base mr-4">
                {{
                  subtotalPerProduct
                    ? useCurrencyFormat(subtotalPerProduct + estimatePriceLogo + estimateTextPrice)
                    : '-'
                }}
              </p>
            </div>
            <PrimeVueTabPanels class="!px-0 !pt-4">
              <PrimeVueTabPanel
                v-for="(tab, tabIndex) in filteredUniqueVariant"
                :key="`tab-panel-${tabIndex}`"
                :value="tab.varian"
              >
                <CartProductVariantTable
                  :data="filteredVariants"
                  :price="item.price"
                  :variant-index="tabIndex"
                  :generic-sku="item.sku"
                />
              </PrimeVueTabPanel>
            </PrimeVueTabPanels>
          </PrimeVueTabs>
        </div>
      </div>
    </div>
  </div>
  <div class="border-t border-[#E5E6E8] mt-1 mb-6"></div>
</template>

<style lang="css" scoped>
/* *{
  outline: 1px solid limegreen;
} */
:deep(.p-panel-header) {
  position: relative;
  padding: 0 !important;
}
:deep(.p-panel-header) .p-panel-header-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
:deep(.p-panel-header) .p-panel-header-actions .p-button {
  width: 100%;
  height: 100%;
  border-radius: 0 !important;
  background-color: transparent !important;
}
:deep(.p-panel-content) {
  padding: 0 !important;
}

:deep(.cart-checkbox.p-checkbox-checked) {
  .p-checkbox-box {
    border-color: #ff5a00 !important;
    background-color: #ff5a00 !important;
  }
}
</style>
