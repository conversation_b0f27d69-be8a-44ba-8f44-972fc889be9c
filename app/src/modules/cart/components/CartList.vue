<script lang="ts" setup>
// Components
import CartItem from './CartItem.vue';
import CartTypeTab from './CartTypeTab.vue';
import CartItemSkeleton from './CartItemSkeleton.vue';

// Interfaces
import type {
  ICartProvided,
  IPayloadCheckItemBulk,
  IPayloadCustomDeleteCartItemBulk,
  IPayloadDeleteCartItemBulk,
  IQueryParamsGetListCart,
} from '../interfaces/cart.interface';

// Constants
import { CART_PAGINATION_SIZE } from '../constants/cart.constant';

const selectedTab = ref('tersedia');
const isOpenModalConfirmDeleteAll = ref(false);

/**
 * @description Injecting dependencies
 */
const {
  cart_dataListCart,
  cart_listLoading,
  cart_queryParamsGetListCart,
  cart_checkAllLoading,
  cart_onCheckAll,
  cart_onDeleteBulk,
  cart_onCustomDeleteBulk,
  cart_activeTab,
} = inject<ICartProvided>('cart')!;

const checkAll = ref(false);

const handleDeleteAll = () => {
  if (cart_activeTab.value === '1') {
    const params = cart_dataListCart.value?.cart_detail
      .filter(i => i.selected)
      .map(i => ({
        attachments_group_id: i.attachments_group_id,
        items: i.items.map(item => ({
          article: item.article,
        })),
      }));
    cart_onCustomDeleteBulk(params as IPayloadCustomDeleteCartItemBulk[]);
  } else {
    const params = cart_dataListCart.value?.cart_detail
      .filter(i => i.selected)
      .map(i => ({
        sku_code_c: i.sku,
        items: i.items.map(item => ({
          article: item.article,
        })),
      }));
    cart_onDeleteBulk(params as IPayloadDeleteCartItemBulk[]);
  }
};

const onClickDelete = () => {
  isOpenModalConfirmDeleteAll.value = true;
};

const onChangeCheckAll = (value: boolean) => {
  const params = cart_dataListCart.value?.cart_detail.map(i => ({
    sku_code_c: i.sku,
    attachments_group_id: i.attachments_group_id,
    items: i.items.map(item => ({
      article: item.article,
      selected: value,
    })),
  }));
  cart_onCheckAll(params as IPayloadCheckItemBulk[], value, cart_activeTab.value === '1');
};

const cartPagination = computed<IPaginationResponse>(() => {
  return {
    total_data: Number(cart_dataListCart.value?.total_data ?? 0),
    size: Number(cart_dataListCart.value?.size ?? 0),
    active_page: Number(cart_dataListCart.value?.active_page ?? 0),
    total_page: Number(cart_dataListCart.value?.total_page ?? 0),
  };
});

const onChangePerPage = (value: number) => {
  cart_queryParamsGetListCart.per_page = value;
};

const onClickPrev = () => {
  cart_queryParamsGetListCart.page = cart_queryParamsGetListCart.page - 1;
};

const onClickNext = () => {
  cart_queryParamsGetListCart.page = cart_queryParamsGetListCart.page + 1;
};

const onCancelDeleteAll = () => {
  isOpenModalConfirmDeleteAll.value = false;
};

const onConfirmDeleteAll = () => {
  isOpenModalConfirmDeleteAll.value = false;
  handleDeleteAll();
};

/**
 * @description Handle side effect when reactive data binding is changed
 */
watch(selectedTab, value => {
  cart_queryParamsGetListCart.is_available = value as IQueryParamsGetListCart['is_available'];
  cart_queryParamsGetListCart.page = 1;
  cart_queryParamsGetListCart.per_page = CART_PAGINATION_SIZE[0];
});

watch(
  [() => cart_dataListCart.value?.cart_detail as ICartItem[]],
  value => {
    if (value[0]?.length) {
      checkAll.value = value[0].every(x => x.selected);
    }
  },
  { deep: true, immediate: true },
);
</script>

<template>
  <section id="cart-list-container" class="w-full h-full relative inset-0 z-0">
    <div class="w-full">
      <section id="product-list-cart">
        <CartTypeTab />
        <div class="sm:flex gap-4 my-5 items-center hidden">
          <div class="flex gap-3">
            <div class="flex">
              <NuxtImg src="/icons/calendar-checklist.svg" alt="upload-icon" class="w-[20px] h-[20px]" />
            </div>
            <div class="text-[14px] font-medium">Ketersediaan Barang</div>
          </div>
          <div>
            <Tabs v-model:value="selectedTab">
              <TabList class="flex items-center gap-1 p-1 rounded-md bg-muted-tabs">
                <Tab
                  value="tersedia"
                  class="p-2 rounded-sm cursor-pointer duration-300 ease-in-out font-medium text-[14px]"
                  :class="[selectedTab === 'tersedia' ? 'bg-white font-medium' : 'bg-transparent font-normal']"
                  @click="selectedTab = 'tersedia'"
                >
                  Tersedia
                </Tab>
                <Tab
                  value="tidak tersedia"
                  class="p-2 rounded-sm cursor-pointer duration-300 ease-in-out text-[14px]"
                  :class="[
                    selectedTab === 'tidak tersedia' ? 'bg-white font-medium' : 'bg-transparent font-normal',
                  ]"
                  @click="selectedTab = 'tidak tersedia'"
                >
                  Tidak Tersedia
                </Tab>
              </TabList>
            </Tabs>
          </div>
        </div>

        <PrimeVueMessage
          v-if="cart_dataListCart && cart_dataListCart?.cart_detail.length > 0 && cart_activeTab === '1'"
          closable
          severity="info"
          class="!text-black !rounded-lg !border-none !outline-none !mb-2 !py-2"
          :pt="{
            closeButton: '!text-black',
            text: '!text-center !w-full',
          }"
        >
          <div class="flex items-center gap-3 ml-2">
            <NuxtImg src="/icons/custom-info-blue-icon.svg" class="!h-[20px] !w-[20px]" alt="info icon" />
            <span>
              Harga barang kustom yang ditampilkan merupakan estimasi berdasarkan komponen yang telah dipilih.
            </span>
          </div>
        </PrimeVueMessage>

        <div
          v-if="cart_dataListCart && cart_dataListCart?.cart_detail.length > 0 || cart_listLoading"
          class="mt-10 flex flex-col gap-4"
        >
          <!-- toolbar header -->
          <div class="flex w-full justify-between pb-4 border-b border-b-[#E5E6E8]">
            <div class="flex items-center gap-2">
              <PrimeVueSkeleton v-if="cart_checkAllLoading || cart_listLoading" class="!w-6 !h-6 !rounded" />
              <PrimeVueCheckbox
                v-else
                v-model="checkAll"
                input-id="cart-check-all"
                name="cart-check-all"
                class="mr-2 cart-checkbox"
                binary
                :disabled="cart_checkAllLoading || cart_listLoading"
                @update:model-value="onChangeCheckAll"
              />
              <label for="cart-check-all" class="font-medium">Pilih Semua</label>
            </div>
            <div>
              <PrimeVueButton
                label="Hapus"
                variant="outlined"
                size="small"
                class="!text-[#E9151D] !border-none !px-6"
                :disabled="
                  cart_checkAllLoading ||
                  cart_listLoading ||
                  !cart_dataListCart?.cart_detail.some(item => item.selected === true)
                "
                :class="{
                  'opacity-50': cart_checkAllLoading,
                }"
                @click="onClickDelete"
              />
            </div>
          </div>
          <div v-if="cart_listLoading">
            <CartItemSkeleton v-for="i in 3" :key="i" />
          </div>
          <div v-else class="w-full">
            <CartItem v-for="(item, index) in cart_dataListCart?.cart_detail" :key="String(index)" :item="item" />
          </div>
        </div>
        <div v-else class="pt-8 pb-6">
          <BaseEmptyState title="Keranjang Masih Kosong" subtitle="" />
        </div>
      </section>
      <BasePagination
        :data="cartPagination"
        :click-action-prev="onClickPrev"
        :click-action-next="onClickNext"
        :click-action-per-page="onChangePerPage"
        :sizes="CART_PAGINATION_SIZE"
      />
      <!-- Dialog confirm delete all -->
      <PrimeVueDialog
        v-model:visible="isOpenModalConfirmDeleteAll"
        modal
        class="h-fit w-full md:w-[532px]"
        :pt="{
          header: '!py-2 border-b border-b-[#E5E6E8]',
          content: '!py-4',
          footer: '!py-3 border-t border-t-[#E5E6E8]',
        }"
        :draggable="false"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>

        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-bold text-[19px] text-black">Konfirmasi Hapus Semua</h4>
          </header>
        </template>

        <template #default>
          <section id="content" class="flex flex-col gap-4">
            <span class="text-base text-black">
              Apakah Anda yakin ingin menghapus semua produk? Pastikan Anda sudah memeriksanya sebelum melanjutkan
            </span>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end gap-4">
            <PrimeVueButton
              label="Batalkan"
              type="button"
              class="!bg-transparent !border-[#ACB1B4] !text-base !text-black !text-center font-medium !w-fit !px-4"
              @click="onCancelDeleteAll"
            />
            <PrimeVueButton
              type="button"
              label="Hapus Semua"
              size="large"
              class="!bg-[#E9151D] !border-none text-white !text-base !text-center font-medium !rounded-lg !w-fit !px-5"
              @click="onConfirmDeleteAll"
            />
          </section>
        </template>
      </PrimeVueDialog>
    </div>
  </section>
</template>

<style lang="css" scoped>
:deep(.cart-checkbox.p-checkbox-checked) {
  .p-checkbox-box {
    border-color: #ff5a00 !important;
    background-color: #ff5a00 !important;
  }
}
</style>
