<script setup lang="ts">
// interfaces
import type { ICartProvided } from '../interfaces/cart.interface';

// constants
import { CART_TYPE_TABS } from '../constants/cart.constant';

/**
 * @description Injecting dependencies
 */
const { cart_queryParamsGetListCart, cart_activeTab, cart_getList } = inject<ICartProvided>('cart')!;

const router = useRouter();

const onChangeTab = (val: string | number) => {
  cart_activeTab.value = String(val);
  cart_queryParamsGetListCart.is_custom = String(val);
  cart_queryParamsGetListCart.page = 1;

  if (val === '1') {
    router.push({ name: 'cart', query: {} });
    cart_getList();
  } else {
    router.push({
      name: 'cart',
      query: {
        type: 'regular',
      },
    });
    // cart_getList();
  }
};
</script>

<template>
  <section id="order-product-list" class="flex flex-col gap-4 mb-6">
    <div class="flex flex-col w-full items-center justify-between gap-3">
      <div class="w-full">
        <div class="mt-2">
          <PrimeVueTabs v-model:value="cart_activeTab" @update:value="onChangeTab">
            <PrimeVueTabList
              class="w-fit"
              :pt="{
                activeBar: '!bg-header-orange',
              }"
            >
              <PrimeVueTab
                v-for="(tab, tabIndex) in CART_TYPE_TABS"
                :key="`tab-${tabIndex}`"
                :value="tab.value"
                :pt="{
                  root: `text-sm !px-3 !py-2 ${useActiveTab(cart_activeTab, tab.value)}`,
                }"
              >
                {{ tab.label }}
              </PrimeVueTab>
            </PrimeVueTabList>
          </PrimeVueTabs>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped></style>
