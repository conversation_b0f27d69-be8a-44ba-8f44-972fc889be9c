<script setup lang="ts">
import type { ICartProvided } from '../interfaces/cart.interface';

const router = useRouter();

/**
 * @description Injecting dependencies
 */
const { cart_showToast, cart_toastData } = inject<ICartProvided>('cart')!;

const onClickCheckCart = () => {
  router.push({
    name: 'cart',
    query: {
      type: 'regular',
    },
  });
};
</script>

<template>
  <div
    id="add-to-car-toast"
    class="w-full flex items-center justify-center md:w-[432px] fixed top-[300px] h-[76px] right-0 bg=[#05964C] rounded-l-[16px] bg-[#05964C] pl-6"
    :class="cart_showToast ? 'visible' : ''"
  >
    <div class="w-full grid grid-cols-[1fr_148px] gap-4">
      <div class="flex w-full items-center">
        <div class="flex items-start mr-4">
          <NuxtImg src="/icons/cart-white.svg" alt="product" class="w-[24px] h-[24px]" />
        </div>
        <div>
          <h4 class="text-white text-[16px] font-bold">{{ cart_toastData.count }} variasi produk</h4>
          <h4 class="text-[#CED1D3] text-[12px]">Warna: {{ cart_toastData.variants.join(', ') }}</h4>
        </div>
      </div>
      <div>
        <PrimeVueButton
          variant="text"
          class="cursor-pointer text-end font-medium hover:underline !text-white !px-2 !py-2 !bg-transparent"
          type="button"
          @click="onClickCheckCart"
        >
          Cek Keranjang
        </PrimeVueButton>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
#add-to-car-toast {
  transition: right cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.3s;
  right: -1000px;
  z-index: 1199 !important;
}
#add-to-car-toast.visible {
  right: 0 !important;
}
</style>
