<script lang="ts" setup>
import type { ICartProvided } from '../interfaces/cart.interface';

interface Props {
  isCheckout: boolean;
}

const props = defineProps<Props>();

/**
 * @description Injecting dependencies
 */
const { cart_isShowModalFormRemark, cart_remarkForm, cart_remarkFormValidations, cart_submitFormRemark } =
  inject<ICartProvided>('cart')!;

const onCloseDialog = () => {
  cart_isShowModalFormRemark.value = false;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  cart_remarkFormValidations?.$reset();
};

const onClickBack = () => {
  onCloseDialog();
};

const onUpdateVisibleModal = (val: boolean) => {
  cart_isShowModalFormRemark.value = val;
};

const onSubmitForm = () => {
  cart_submitFormRemark(props.isCheckout);
};
</script>

<template>
  <!-- Modal inquiry form -->
  <PrimeVueDialog
    v-model:visible="cart_isShowModalFormRemark"
    modal
    header="Header"
    class="!w-[90%] md:!w-[700px]"
    :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
    :draggable="false"
    :pt="{
      header: '!hidden !flex !items-center !justify-center !px-2 sm:!px-4',
      content: '!px-2 sm:!px-4',
    }"
    @update:visible="onUpdateVisibleModal"
  >
    <template #default>
      <section id="remark-form">
        <header class="flex pt-4 pb-3 items-center justify-center">
          <h4 class="text-lg sm:text-[20px] text-[#18191A] font-bold text-center">Tambah Catatan</h4>
        </header>
        <section id="content" class="flex flex-col">
          <div class="flex justify-between gap-5">
            <BaseFormGroup
              v-slot="{ classes }"
              is-mandatory
              class-label="font-medium text-sm text-black block m b-2"
              is-name-as-label
              label-for="remark"
              name="Catatan"
              :validators="cart_remarkFormValidations.remark"
            >
              <PrimeVueTextarea
                id="remark"
                v-model="cart_remarkForm.remark"
                v-bind="{ ...useBindStateForm('Masukkan catatan yang ingin kamu sampaikan') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                :class="{ ...classes }"
                type="text"
                rows="3"
                :maxlength="250"
                v-on="useListenerForm(cart_remarkFormValidations, 'remark')"
              />
            </BaseFormGroup>
          </div>
          <div v-if="!cart_remarkFormValidations?.remark?.$invalid" class="flex items-center">
            <NuxtImg src="/icons/info-grey-icon.svg" class="h-[10px] !w-[20px]" alt="info icon" />
            <span class="text-[12px] font-medium text-[#686F72]">Maksimal 250 Karakter</span>
          </div>
        </section>
        <section id="btn-actions" class="mt-2 flex items-center gap-4 justify-between w-full">
          <PrimeVueButton
            variant="outlined"
            type="button"
            label="Kembali"
            size="large"
            class="!border !border-[#ACB1B4] !text-[#18191A] !text-[16px] font-medium !rounded-lg !px-5 !py-3 !w-full"
            @click="onClickBack"
          />
          <PrimeVueButton
            :disabled="Boolean(cart_remarkFormValidations?.remark?.$invalid)"
            type="button"
            size="large"
            label="Simpan"
            class="!bg-black !border-none text-white !text-[16px] !text-center font-medium !rounded-lg !px-5 !py-3 !w-full"
            @click="onSubmitForm"
          />
        </section>
      </section>
    </template>
  </PrimeVueDialog>
</template>
