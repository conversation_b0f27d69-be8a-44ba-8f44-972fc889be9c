<script setup lang="ts">
</script>

<template>
  <div class="w-full">
    <div class="w-full grid grid-cols-[192px_1fr] gap-4 mb-3">
      <div class="flex items-start">
        <div class="w-[32px]">
          <PrimeVueSkeleton class="!w-6 !h-6 !rounded" />
        </div>
        <div class="w-[159px] h-[159px] bg-grey-200 relative flex items-center justify-center">
          <PrimeVueSkeleton class="!w-full !h-full" />
        </div>
      </div>

      <div class="w-full">
        <PrimeVueSkeleton class="!w-3/4 !h-6 !mb-2" />
        <div class="flex items-center gap-3 mb-2">
          <PrimeVueSkeleton class="!w-20 !h-5" />
        </div>
        <div class="flex w-full items-center justify-between mb-5">
          <PrimeVueSkeleton class="!w-24 !h-5" />
          <PrimeVueSkeleton class="!w-20 !h-5" />
        </div>
        <div class="w-full mb-2">
          <PrimeVueSkeleton class="!w-full !h-8" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
</style>
