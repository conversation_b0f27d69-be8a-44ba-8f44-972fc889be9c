<script lang="ts" setup>
import { ref } from 'vue';
import { CART_PAGINATION_SIZE } from '../constants/cart.constant';

// Interfaces
import type { ICartProvided } from '../interfaces/cart.interface';

const selectedPerPage = ref<number>(CART_PAGINATION_SIZE[0]);

/**
 * @description Injecting dependencies
 */
const { cart_dataListCart, cart_queryParamsGetListCart } = inject<ICartProvided>('cart')!;

const onClickNext = () => {
  if (cart_dataListCart.value) {
    if (cart_dataListCart.value.total_page > cart_dataListCart.value?.active_page) {
      cart_queryParamsGetListCart.page = cart_queryParamsGetListCart.page + 1;
    }
  }
};

const onClickPrev = () => {
  if (cart_dataListCart.value) {
    if (cart_dataListCart.value.active_page > 1) {
      cart_queryParamsGetListCart.page = cart_queryParamsGetListCart.page - 1;
    }
  }
};

const onChangePerPage = (value: number) => {
  cart_queryParamsGetListCart.page = 1;
  cart_queryParamsGetListCart.per_page = value;
};
</script>

<template>
  <section
    id="cart-pagination"
    class="w-full h-full relative inset-0 z-0 border-t border-b border-t-[#CED1D3] border-b-[#CED1D3] py-4"
  >
    <div class="flex justify-between">
      <div class="flex items-center">
        Menampilkan
        <span class="mx-2">
          <select
            id="color"
            v-model="selectedPerPage"
            class="block w-full px-1 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            @update:model-value="onChangePerPage"
          >
            <option v-for="item in CART_PAGINATION_SIZE" :key="item" :value="item" selected>
              {{ item }}
            </option>
          </select>
        </span>
        <span>per halaman</span>
      </div>
      <div v-if="cart_dataListCart" class="flex items-center gap-2">
        <div class="text-md">
          {{ cart_dataListCart.active_page === 1 ? 1 : cart_dataListCart.size }}-{{
            cart_dataListCart.active_page * cart_dataListCart.size
          }}
          dari
          {{ cart_dataListCart?.total_data }}
        </div>
        <div class="flex gap-2">
          <div>
            <button
              class="bg-white text-black h-7 w-7 cursor-pointer border text-lg"
              :class="{ 'opacity-50': cart_dataListCart.active_page > 1 }"
              :disabled="cart_dataListCart.active_page > 1"
              @click="onClickPrev"
            >
              &lt;
            </button>
          </div>
          <div>
            <button
              class="bg-white text-black h-7 w-7 cursor-pointer border text-lg"
              :class="{ 'opacity-50': cart_dataListCart.total_page <= cart_dataListCart.active_page }"
              :disabled="cart_dataListCart.total_page <= cart_dataListCart.active_page"
              @click="onClickNext"
            >
              &gt;
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
