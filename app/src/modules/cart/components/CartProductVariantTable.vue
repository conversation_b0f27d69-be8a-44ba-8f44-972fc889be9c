<script setup lang="ts">
// components
import type { InputNumberInputEvent } from 'primevue/inputnumber';
import type { ICartProvided, IPayloadUpdateCartItem } from '../interfaces/cart.interface';

interface Props {
  data: ICartVariantItem[];
  genericSku: string;
  variantIndex: number;
  price: string;
}

const props = defineProps<Props>();

const cartValues = computed<ICartVariantItem[]>(() => [...props.data]);

/**
 * @description Injecting dependencies
 */
const { cart_queryParamsGetListCart, cart_updateQty, cart_activeTab, cart_updateCustomQty } =
  inject<ICartProvided>('cart')!;

const isCustom = computed(() => {
  return cart_queryParamsGetListCart.is_custom === '1' || cart_activeTab.value === '1';
});

const isAvailable = computed(() => {
  return cart_queryParamsGetListCart.is_available === 'tersedia';
});

//function to trigger update qty in cart
// return: none
const onUpdateValue = useDebounce((event: InputNumberInputEvent, item: ICartVariantItem) => {
  if (event.value) {
    const maxValue: number =
      parseInt(event.value as string) > item.stock ? item.stock : parseInt(event.value as string);

    if (isCustom.value) {
      cart_updateCustomQty({
        cart_detail_id: item.cart_detail_id,
        qty: maxValue < 1 ? 1 : maxValue,
      });
    } else {
      const payload: IPayloadUpdateCartItem = {
        article: item.article,
        qty: maxValue < 1 ? 1 : maxValue,
      };
      cart_updateQty(payload);
    }
  } else {
    cart_updateCustomQty({
      cart_detail_id: item.cart_detail_id,
      qty: 1,
    });
  }
}, 750);
</script>

<template>
  <div class="grid grid-cols-1">
    <div
      class="sm:grid grid-cols-[30fr_20fr_25fr_25fr] border-y-[1px] py-1 border-[#ACB1B4] font-bold text-black text-[14px] hidden"
    >
      <div class="">SKU</div>
      <div class="text-center">Size</div>
      <div class="text-center">Jumlah Order</div>
      <div class="text-center">{{ isCustom ? 'Estimasi Harga' : 'Harga' }}</div>
    </div>

    <div
      v-for="(item, index) in cartValues"
      :key="index"
      class="sm:grid sm:grid-cols-[30fr_20fr_25fr_25fr] py-1 items-center text-black text-[14px] hidden"
    >
      <div class="sm:block hidden">
        <span :data-testid="`cart-product-item-article-${index}`">
          {{ item.article }}
        </span>
      </div>
      <div class="text-center">
        <span :data-testid="`cart-product-item-size-${index}`">
          {{ item.size }}
        </span>
      </div>
      <!-- <div v-if="isCustom || !item.is_available" class="text-center">
                      {{ useNumberFormat(item.qty) }}
                    </div> -->
      <div class="text-center">
        <PrimeVueInputNumber
          v-model="item.qty"
          input-id="horizontal-buttons"
          show-buttons
          button-layout="horizontal"
          :step="1"
          :min="1"
          :max="item.stock"
          fluid
          class="!w-[90px] md:!w-[110px] !p-0 !border-none"
          :pt="{
            pcInputText: {
              root: '!text-center !text-[14px] !px-[1px] !py-[2px] !w-10 !font-bold',
            },
            root: {
              class: '!border-2  !rounded-lg !shadow-none',
            },
            decrementButton: {
              class: 'h-7 !w-6',
            },
            incrementButton: {
              class: 'h-7 !w-6',
            },
          }"
          @input="e => onUpdateValue(e, item)"
        >
          <template #decrementicon>
            <NuxtImg src="/icons/minus-flat-black.svg" alt="plus" class="w-4 h-4" />
          </template>
          <template #incrementicon>
            <NuxtImg src="/icons/plus-flat-black.svg" alt="plus" class="w-4 h-4" />
          </template>
        </PrimeVueInputNumber>
      </div>
      <div class="text-center">
        <span :data-testid="`cart-product-item-amount-${index}`">
          {{ price ? useCurrencyFormat(parseInt(price) * item.qty) : '-' }}
        </span>
      </div>
    </div>
    <div
      v-for="(item, index) in cartValues"
      :key="index"
      class="sm:hidden grid grid-cols-1 text-black text-[14px] mt-4 gap-y-2"
    >
      <div class="flex items-center gap-2 w-full">
        <div class="">Size : {{ item.size }}</div>
        <div class="!h-[2px] w-[2px] rounded-full bg-muted"></div>
        <div class="">Stock : {{ item.stock }}</div>
      </div>
      <div class="flex justify-between mr-4 items-center">
        <div v-if="isCustom || (!isCustom && !isAvailable)">{{ item.qty }}</div>
        <div v-else class="py-2">
          <PrimeVueInputNumber
            v-model="item.qty"
            input-id="horizontal-buttons"
            show-buttons
            button-layout="horizontal"
            :step="1"
            :min="0"
            :max="item.stock"
            fluid
            class="!w-[120px] !p-0 !border-none"
            :pt="{
              pcInputText: {
                root: '!text-center !text-[14px] !px-[1px] !py-[2px] !w-10 !font-bold',
              },
              root: {
                class: '!border-2  !rounded-lg !shadow-none',
              },
              decrementButton: {
                class: 'h-7 !w-6',
              },
              incrementButton: {
                class: 'h-7 !w-6',
              },
            }"
            @input="e => onUpdateValue(e, item)"
          >
            <template #decrementicon>
              <NuxtImg src="/icons/minus-flat-black.svg" alt="plus" class="w-4 h-4" />
            </template>
            <template #incrementicon>
              <NuxtImg src="/icons/plus-flat-black.svg" alt="plus" class="w-4 h-4" />
            </template>
          </PrimeVueInputNumber>
        </div>
        <div class="text-[14px] md:text-[16px] font-medium">
          <span :data-testid="`cart-product-item-amount-${index}`">
            {{ price ? useCurrencyFormat(parseInt(price) * item.qty) : '-' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
/* *{
  outline: 1px solid limegreen;
} */
:deep(.p-panel-header) {
  position: relative;
  padding: 0 !important;
}
:deep(.p-panel-header) .p-panel-header-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
:deep(.p-panel-header) .p-panel-header-actions .p-button {
  width: 100%;
  height: 100%;
  border-radius: 0 !important;
  background-color: transparent !important;
}
:deep(.p-panel-content) {
  padding: 0 !important;
}

:deep(.cart-checkbox.p-checkbox-checked) {
  .p-checkbox-box {
    border-color: #ff5a00 !important;
    background-color: #ff5a00 !important;
  }
}
</style>
