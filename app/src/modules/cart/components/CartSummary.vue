<script setup lang="ts">
import type { ICheckoutPayload, ICheckoutProvided } from '../../checkout/interfaces/checkout.interface';
import type { IConfigurationProvided } from '../../configurations/interfaces/configurations.interface';
import type { ICartProvided } from '../interfaces/cart.interface';

/**
 * @description Injecting dependencies
 */
const { cart_activeTab, cart_dataLimit, cart_dataListCart } = inject<ICartProvided>('cart')!;
const { checkout_createOrder, checkout_loading } = inject<ICheckoutProvided>('checkout')!;
const { MIN_CUSTOM_ORDER_AMOUNT, MIN_REGULAR_ORDER_AMOUNT } = inject<IConfigurationProvided>('configurations')!;

// const showButtonInvoiceTerms = ref(true);
// const acceptsTaxInvoiceProcessing = ref(false);

// const onClicRejectTerms = () => {
//   showButtonInvoiceTerms.value = false;
//   acceptsTaxInvoiceProcessing.value = false;
// };

// const onClicAcceptTerms = () => {
//   showButtonInvoiceTerms.value = false;
//   acceptsTaxInvoiceProcessing.value = true;
// };

const isCustom = computed((): boolean => {
  return cart_activeTab.value === '1';
});

const getAllCartDetailIds = (data: ICartItem[]) => {
  const cartDetailIds: string[] = [];
  data.forEach(product => {
    product.items.forEach(item => {
      cartDetailIds.push(item.cart_detail_id);
    });
  });

  return cartDetailIds;
};

// function to get total price
// return: none
const cartTotalPrice = computed<number>(() => {
  if (cart_dataListCart.value?.cart_detail) {
    return cart_dataListCart.value.cart_detail.reduce((total, product) => {
      // Calculate total price for each product
      const productTotalPrice = product.items.reduce((itemTotal, item) => {
        return itemTotal + item.qty * parseFloat(product.price);
      }, 0);

      // Add this product's total price to the overall total
      return total + productTotalPrice;
    }, 0);
  }
  return 0;
});

const minimunOrderAmount = computed<number>(() => {
  if (cart_activeTab.value === '1') {
    return MIN_CUSTOM_ORDER_AMOUNT.value || 0;
  } else {
    return MIN_REGULAR_ORDER_AMOUNT.value || 0;
  }
});

const onClickOrderNow = () => {
  if (cart_dataListCart?.value) {
    const itemIds = getAllCartDetailIds(cart_dataListCart.value?.cart_detail);
    const checkoutPayload: ICheckoutPayload['items'] = itemIds.map(i => ({
      cart_detail_id: i,
    }));

    checkout_createOrder({
      items: checkoutPayload,
      is_custom: cart_activeTab.value === '1' ? 1 : 0,
    });
  }
};

const disabledCheckoutButton = computed(() => {
  if (cart_activeTab.value === '1') {
    return (
      !cart_dataListCart?.value?.isAbleCheckout || minimunOrderAmount.value > cart_dataLimit.value?.total_harga
    );
  } else {
    return (
      !cart_dataListCart?.value?.isAbleCheckout || minimunOrderAmount.value > cart_dataLimit.value?.total_harga
    );
  }
});
</script>

<template>
  <section id="cart-summary" class="sm:block hidden">
    <div class="bg-white border border-[#E5E6E8] rounded-lg">
      <div class="gap-3 px-5 py-6">
        <div class="mb-5">
          <div class="flex w-full items-center justify-between mb-4">
            <h4 class="text-lg font-bold text-gray-800">Ringkasan Pesanan</h4>
          </div>
          <div class="flex flex-col gap-2">
            <div>
              <div class="grid grid-cols-[150px_1fr]">
                <p class="text-black text-sm">Produk</p>
              </div>
            </div>
            <div>
              <div class="grid grid-cols-[150px_1fr]">
                <p class="text-black text-sm ml-2">Jumlah Produk</p>
                <p class="text-sm text-black text-right" data-testid="cart-summary-selected-item">
                  {{
                    cart_dataLimit?.total_selected_barang
                      ? useNumberFormat(cart_dataLimit?.total_selected_barang)
                      : '-'
                  }}
                </p>
              </div>
            </div>
            <div v-show="isCustom">
              <div class="grid grid-cols-[150px_1fr]">
                <p class="text-black text-sm ml-2">Subtotal</p>
                <p class="text-sm text-black text-right" data-testid="cart-summary-sub-total">
                  {{ cart_dataLimit?.sub_total ? useCurrencyFormat(cart_dataLimit?.sub_total) : '-' }}
                </p>
              </div>
            </div>
            <div v-show="isCustom">
              <div class="grid grid-cols-[150px_1fr]">
                <p class="text-black text-sm ml-2">Jumlah Kustom</p>
                <p class="text-sm text-black text-right">
                  {{ cart_dataLimit?.total_custom ? useNumberFormat(cart_dataLimit.total_custom) : '-' }}
                </p>
              </div>
            </div>
            <div v-show="isCustom">
              <div class="grid grid-cols-[150px_1fr]">
                <p class="text-black text-sm ml-2">Estimasi Harga Kustom</p>
                <p class="text-sm text-black text-right" data-testid="cart-summary-custom-amount">
                  {{
                    cart_dataLimit?.harga_custom ? useCurrencyFormat(Number(cart_dataLimit?.harga_custom)) : '-'
                  }}
                </p>
              </div>
            </div>
            <div class="border-b border-b-[#ACB1B4] my-3"></div>
            <div>
              <div class="grid grid-cols-[110px_1fr]">
                <p class="text-[#686F72] text-[12px] font-bold">
                  {{ isCustom ? 'ESTIMASI HARGA' : 'TOTAL HARGA' }}
                </p>
                <p class="text-[20px] font-bold text-black text-right" data-testid="cart-summary-total-amount">
                  {{
                    isCustom
                      ? cart_dataLimit?.total_harga
                        ? useCurrencyFormat(Number(cart_dataLimit?.total_harga))
                        : '-'
                      : cart_dataLimit?.total_harga
                      ? useCurrencyFormat(Number(cart_dataLimit?.total_harga))
                      : '-'
                  }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="mb-5">
          <section v-if="showButtonInvoiceTerms" id="tax-invoice-terms">
            <div class="flex flex-col w-full mb-5">
              <p class="text-[14px] mb-2">Apakah Anda bersedia memproses faktur pajak sendiri?</p>
              <div class="flex w-full gap-2 justify-between">
                <PrimeVueButton
                  label="Tidak"
                  class="!bg-white !border-[#ACB1B4] !rounded-lg !text-black w-full"
                  @click="onClicRejectTerms"
                />
                <PrimeVueButton
                  label="Ya, Bersedia"
                  class="!bg-black !border-black !rounded-lg !text-white w-full"
                  @click="onClicAcceptTerms"
                />
              </div>
            </div>
          </section>

          <section v-else id="tax-invoice-terms">
            <div v-if="acceptsTaxInvoiceProcessing" class="text-center">
              <p class="text-[#05964C] my-2 text-[14px] font-normal">
                Fakur Pajak akan dikirim via email sesuai email yang terdaftar
              </p>
            </div>
            <div v-else class="text-center">
              <p class="text-[#147FFF] my-2 text-[14px] font-normal">Anda tidak bersedia memproses pajak</p>
            </div>
          </section>
        </div> -->
        <div class="flex w-full mb-5">
          <PrimeVueButton
            :disabled="disabledCheckoutButton || checkout_loading"
            label="Pesan Barang"
            class="!rounded-lg text-white w-full"
            :class="
              disabledCheckoutButton || checkout_loading
                ? '!bg-[#a6a7a7] !border-[#a6a7a7] !text-black'
                : '!bg-black !border-black'
            "
            @click="onClickOrderNow"
          />
        </div>
        <template v-if="cart_activeTab === '1'">
          <section
            v-if="Number(cart_dataLimit?.total_harga) < Number(MIN_CUSTOM_ORDER_AMOUNT)"
            class="bg-[#FFE0E1] px-3 py-3 flex gap-2 items-center mb-3 rounded"
          >
            <NuxtImg src="/icons/info-red.svg" class="h-[15px] w-[15px]" />
            <span class="text-[14px] text-[#18191A]">
              Minimum Pesanan {{ useCurrencyFormat(Number(minimunOrderAmount)) }}
            </span>
          </section>
        </template>
        <template v-else>
          <section
            v-if="Number(cart_dataLimit?.total_harga) < Number(MIN_REGULAR_ORDER_AMOUNT)"
            class="bg-[#FFE0E1] px-3 py-3 flex gap-2 items-center mb-3 rounded"
          >
            <span class="text-[14px] text-[#18191A]">
              Minimum Pesanan {{ useCurrencyFormat(Number(MIN_REGULAR_ORDER_AMOUNT)) }}
            </span>
          </section>
        </template>
        <div class="border-b border-[#E5E6E8] mb-2"></div>
        <BaseCustomerSupportCard :border="false" :background-image="false" :padding="0" />
      </div>
    </div>
  </section>
  <!-- content bottom -->
  <div class="fixed bottom-0 left-0 right-0 bg-white py-3 border-t border-[#E5E6E8] sm:hidden block p-4">
    <div class="flex justify-between items-center">
      <div class="text-black text-[12px] font-bold">TOTAL HARGA</div>
      <div class="text-black font-bold text-[20px]">{{ useCurrencyFormat(cartTotalPrice) }}</div>
    </div>
    <div class="grid grid-cols-[10fr_90fr] gap-2 mt-2">
      <div class="border flex items-center justify-center border-[#E5E6E8] rounded-md p-1">
        <img src="/icons/icon-chat.svg" alt="" class="w-6 h-6" />
      </div>
      <div class="">
        <PrimeVueButton
          label="Checkout"
          type="button"
          class="!bg-black !border-none !text-white !text-center font-medium !w-full"
          @click="onClickOrderNow"
        />
      </div>
    </div>
  </div>
</template>
