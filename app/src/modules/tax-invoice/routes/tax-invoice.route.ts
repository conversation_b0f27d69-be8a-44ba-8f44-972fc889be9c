// Components
// import AppBaseWrapper from '../../../core/components/base/BaseWrapper.vue';

// Interfaces
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/tax-invoice',
    name: 'tax-invoice',
    component: () => import('../views/InvoiceTaxUI.vue'),
    meta: {
      breadcrumbs: [
        {
          label: 'Beranda',
          path: '/dashboard',
        },
        {
          label: 'Faktur <PERSON>jak',
          path: null,
        },
      ],
    },
  },
];

export default routes;
