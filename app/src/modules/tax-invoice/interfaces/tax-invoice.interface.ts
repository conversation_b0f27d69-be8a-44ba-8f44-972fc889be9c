import type { Validation } from '@vuelidate/core';
import type { Reactive, Ref } from 'vue';

export interface ITaxInvoiceForm {
    type: string;
    invoice_no: string;
}

export interface ITaxInvoiceSearchData {
  search: string;
}
  
export interface ITaxInvoiceProvided {
    taxInvoice_formData : ITaxInvoiceForm,
    taxInvoice_formValidations: globalThis.Ref<Validation> ,
    taxInvoice_onSubmit : () => void,
    taxInvoice_isShowModal: boolean;
    taxInvoice_isShowConfirmationModal: boolean;
    taxInvoice_onConfirm : () => void;
}


export interface IOrderProgress {
  label: string;
  value: string;
}

export interface IQueryParamsGetListInvoice {
  page: number;
  per_page: number;
  status: string;
  text: string | null;
  date_from: string | null;
  date_to: string | null;
}

export interface IQueryParamsGetDetailInvoiceProducts {
  page: number;
  per_page: number;
  text: string | null;
}

export interface IOrderProductItem {
  article: string;
  product_name: string;
  product_variant: string;
  product_size: string;
  issued_qty: number;
  qty: number;
  sub_total: number;
}

export interface IInvoiceDetailItem {
  sku_code_c: string;
  product_name_c: string;
  image_url: string;
  flag: string[];
  base_price: string;
  sub_total: number;
  customs: [];
  product_items: IOrderProductItem[];
}

export interface IOrderCustomerShipment {
  customer_shipment_id: string;
  customer_id: string;
  name: string;
  address: string;
  city: string;
  province: string;
  district: string;
  zip_code: string;
  shipment_type: string;
  phone_number: string;
}

export interface IInvoiceProduct {
  product_name: string;
  sku: string;
  product_size: string;
  qty: number;
  price: number;
  sub_total: number;
  discount: number;
  net_price: number;
}

export interface IResponseInvoiceProductList {
  total_data: number;
  size: number;
  active_page: number;
  total_page: number;
  data: IInvoiceProduct[];
}

export interface IInvoiceDetailHeader {
  order: {
    order_no: string;
    ref: string;
    dn: string;
    delivery_status: string;
    billing: number;
    order_date: string;
    gi_date: string | null;
    due_date: string;
    qty_items: string | null;
    sub_total: number;
    discount: number;
    total: number;
    down_payment: string;
    status: string;
  };
  shipment: {
    nomor_resi: {
      noresi: string;
      namaresi: string;
    }[];
    address: {
      name: string;
      company_name: string;
      phone: string;
      address: string;
    };
  };
  attachment: {
    tax_url: string;
    invoice_url: string;
  };
}

export interface IInvoiceListItem {
  invoice_no: number;
  customer_id: string;
  total_tagihan: string;
  invoice_status: string;
  billing_date: string;
  total_produk: string;
  image: string;
  product_name: string;
  product_category_name: string;
  jatuh_tempo: string;
}

export interface ITaxInvoice {
  type: string;
  invoice_no: string;
}

export interface IListOrder extends IPaginationResponse {
  data: IInvoiceListItem[];
}

export interface IInvoiceProvided {
  invoice_getList: (params?: Partial<IQueryParamsGetListInvoice>) => Promise<void>;
  invoice_getDetailHeader: (invoiceNumber: number) => Promise<void>;
  invoice_getDetailProductList: (invoiceNumber: number) => Promise<void>;
  invoice_download: (invoiceNumber: number) => Promise<void>;
  invoice_listOrderActiveTab: Ref<unknown>;
  invoice_listIsLoading: Ref<boolean>;
  invoice_listData: Ref<IInvoiceListItem[]>;
  invoice_detailHeaderData: Ref<IInvoiceDetailHeader | null>;
  invoice_detailHeaderLoading: Ref<boolean>;
  invoice_listPagination: Ref<IPaginationResponse>;
  invoice_listQueryParams: Reactive<IQueryParamsGetListInvoice>;
  invoice_detailProductsListQueryParams: Reactive<IQueryParamsGetDetailInvoiceProducts>;
  invoice_detailProductListData: Ref<IInvoiceProduct[] | null>;
  invoice_detailProductIsLoading: Ref<boolean>;
  invoice_detailProductListPagination: Ref<IPaginationResponse>;
}

export interface IStateInvoiceStore {
  invoice_listIsLoading: boolean;
  invoice_listData: IInvoiceListItem[];
  invoice_listPagination: IPaginationResponse;
  invoice_detailHeaderData: IInvoiceDetailHeader | null;
  invoice_detailHeaderLoading: boolean;
  invoice_detailProductListData: IInvoiceProduct[];
  invoice_detailProductIsLoading: boolean;
  invoice_detailProductListPagination: IPaginationResponse;
}
