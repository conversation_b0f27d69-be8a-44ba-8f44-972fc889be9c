// Components
import TaxInvoiceForm from '../components/TaxInvoiceForm.vue';
import TaxInvoiceHistory from '../components/TaxInvoiceHistory.vue'

// Interfaces
// import type { IProfileData } from '../interfaces/profile.interface';

export const TAX_INVOICE_LIST_TABS = [
  {
    component: TaxInvoiceForm,
    label: 'Input Faktur Pajak',
    value: 'input',
  },
  {
    component: TaxInvoiceHistory,
    label: 'History Faktur Pajak',
    value: 'history',
  },
  // {
  //   component: ProfileChangePassword,
  //   label: 'Ubah Password',
  //   value: 'password',
  // },
  // {
  //   component: ProfileInvoice,
  //   label: 'Faktur Pajak',
  //   value: 'invoice',
  // },
];

export const PROFILE_PERSONAL_DATA_INFORMATIONS = [
  {
    key: 'owner_name',
    label: '<PERSON>a',
    value: '<PERSON><PERSON>',
  },
  {
    key: 'phone_number',
    label: 'Nomor Telepon',
    value: '08123456789',
  },
  {
    key: 'email',
    label: 'E-mail',
    value: '<EMAIL>',
  },
  {
    key: 'idCard',
    label: 'Nomor KTP',
    value: '1234567890',
  },
];

export const PROFILE_NPWP_DATA_INFORMATIONS = [
  {
    key: 'npwp',
    label: 'No. NPWP',
    value: '1234567890',
  },
  {
    key: 'npwp_name',
    label: 'Nama',
    value: 'Dimas',
  },
  {
    key: 'npwp_address',
    label: 'Alamat',
    value: 'Jl. Kebon Jeruk No. 1',
  },
  {
    key: 'idCard',
    label: 'Nomor KTP',
    value: '1234567890',
  },
];

export const PROFILE_OUTLET_DATA_INFORMATIONS = [
  {
    key: 'outletName',
    label: 'Nama Outlet',
    value:
      'Jl Lorem Ipsum is simply dummy text Rt33/12, Kelurahan, Kecamatan, Kabupaten, Kota, Provinsi, Kode POS',
  },
];

// export const PROFILE_STATE_PROFILE_DATA: IProfileData = {
//   address: '',
//   credit_limit: '',
//   credit_limit_currency: 0,
//   credit_limit_remaining: '',
//   credit_limit_used: '',
//   credit_limit_used_percentage: '',
//   customer_id: '',
//   customer_type: '',
//   distribution_channel: '',
//   email: '',
//   is_active: false,
//   is_change_password: false,
//   is_pending_payment: false,
//   is_verified: false,
//   national_id: '',
//   npwp: '',
//   npwp_address: '',
//   npwp_city: '',
//   npwp_name: '',
//   owner_name: '',
//   phone_number: '',
//   rejection_reason: '',
//   store_list: [],
//   bank_list: [],
//   top: '',
//   top_days: '',
//   va_list: null,
// };
