<script setup lang="ts">
// Constants
import { TAX_INVOICE_LIST_TABS } from '../constants/tax-invoice.constant';

const selectedTab = ref(TAX_INVOICE_LIST_TABS[0].value);
const isActive = (value: string) => {
  return selectedTab.value === value
    ? 'border-b border-solid !border-header-orange !text-header-orange !font-medium'
    : '!font-normal !text-muted-secondary';
};
</script>

<template>
  <section id="profile" class="relative inset-0 z-0">
    <PrimeVueTabs v-model:value="selectedTab">
      <PrimeVueTabList
        class="w-fit"
        :pt="{
          activeBar: '!bg-header-orange',
        }"
      >
        <PrimeVueTab
          v-for="(tab, tabIndex) in TAX_INVOICE_LIST_TABS"
          :key="`tab-${tabIndex}`"
          :value="tab.value"
          :pt="{
            root: `text-sm ${isActive(tab.value)}`,
          }"
        >
          {{ tab.label }}
        </PrimeVueTab>
      </PrimeVueTabList>
      <PrimeVueTabPanels class="!px-0 !pt-6">
        <PrimeVueTabPanel
          v-for="(tab, tabIndex) in TAX_INVOICE_LIST_TABS"
          :key="`tab-panel-${tabIndex}`"
          :value="tab.value"
        >
          <component :is="tab.component" />
        </PrimeVueTabPanel>
      </PrimeVueTabPanels>
    </PrimeVueTabs>
  </section>
</template>
