<script setup lang="ts">
import type { ITaxInvoiceProvided } from '../interfaces/tax-invoice.interface.js';
/**
 * @description Injecting dependencies
 */
const {
  taxInvoice_formData,
  taxInvoice_formValidations,
  taxInvoice_isShowModal,
  taxInvoice_isShowConfirmationModal,
  taxInvoice_onConfirm,
  taxInvoice_onSubmit,
} = inject<ITaxInvoiceProvided>('invoiceTax')!;

// ITaxInvoiceProvided
const type = ref(['020', '010', '000']);
</script>

<template>
  <PrimeVueDialog
    v-model:visible="taxInvoice_isShowConfirmationModal"
    modal
    :dismissable-mask="true"
    :closable="true"
    :style="{ width: '50vw', padding: '0' }"
    :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
    :content-style="{ padding: '0' }"
    :content-class="'p-0'"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>
    <template #header>
      <div class="text-[18px] font-bold text-black">Konfirmasi Jenis Faktur Pajak</div>
    </template>
    <template #default>
      <div class="flex items-center justify-center flex-col p-4 border-y border-[#E5E6E8]">
        <div class="font-regular text-[16px] text-[#686F72] my-4">
          Sebelum melanjutkan, mohon periksa kembali apakah jenis faktur pajak sudah sesuai dengan permintaan di
          transaksi offline.
        </div>
      </div>
      <div class="flex px-4 py-2 gap-2 justify-end">
        <PrimeVueButton
          label="Batalkan"
          class="!bg-white !border-[#E5E6E8] !text-base !text-black !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted !mt-2"
          @click="taxInvoice_isShowConfirmationModal = false"
        />
        <PrimeVueButton
          label="Lanjut Konfrimasi"
          type="submit"
          class="!bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted !mt-2"
          @click="taxInvoice_onConfirm"
        />
      </div>
    </template>
  </PrimeVueDialog>

  <PrimeVueDialog
    v-model:visible="taxInvoice_isShowModal"
    modal
    :dismissable-mask="true"
    :closable="false"
    :style="{ width: '50vw' }"
    :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
  >
    <template #default>
      <div class="flex items-center justify-center flex-col p-6">
        <NuxtImg src="/icons/send-mail.svg" alt="close-circle" class="w-[5em] h-[5em]" />
        <div class="font-bold text-[18px] text-[#18191A]">Invoice Berhasil Dikirim!</div>
        <div class="font-regular text-[14px] text-[#686F72] my-4">
          Nomor invoice belanja Anda telah berhasil diinput. Faktur pajak akan segera diproses, dan invoice akan
          dikirim ke email yang terdaftar.
          <br />
          <br />
          Jika tidak menerima email dalam beberapa saat, silakan periksa folder spam atau hubungi tim support kami.
        </div>
        <PrimeVueButton
          label="Kembali"
          type="submit"
          class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
        />
      </div>
    </template>
  </PrimeVueDialog>
  <section id="authentication-login-form" class="flex flex-col items-center justify-center h-full w-full">
    <BaseAlert>
      <template #icon-prefix>
        <NuxtImg src="/icons/warning-danger.svg" alt="warning-icon" class="w-4 h-4" />
      </template>
    </BaseAlert>
    <form class="w-full" @submit.prevent="taxInvoice_onSubmit">
      <div class="bg-[#D4EAFF] px-3 py-3 my-4 flex gap-2 justify-start text-xs items-center">
        <img src="/icons/info-blue.svg" />
        Masukkan nomor invoice belanja untuk memproses faktur pajak anda
      </div>
      <BaseFormGroup
        v-slot="{ classes }"
        class-label="font-medium text-sm text-black block mb-2"
        is-name-as-label
        label-for="jenis_pajak"
        name="Jenis Pajak"
        spacing-bottom="mb-0"
        :validators="taxInvoice_formValidations.type"
        :is-mandatory="true"
      >
        <BaseSelectInput
          v-model:selected="taxInvoice_formData.type"
          :class="{ ...classes }"
          border="#ACB1B4"
          placeholder="Pilih Jenis Pajak"
          :disable="false"
          :options="type"
          padding=".25em"
        />
      </BaseFormGroup>

      <BaseFormGroup
        v-slot="{ classes }"
        class-label="font-medium text-sm text-black block mt-4"
        is-name-as-label
        label-for="invoice"
        name="Nomor Invoice Belanja"
        :validators="taxInvoice_formValidations.invoice_no"
        :is-mandatory="true"
      >
        <input
          id="invoice"
          v-model="taxInvoice_formData.invoice_no"
          v-bind="{ ...useBindStateForm('Silahkan Masukkan Invoice ') }"
          class="block w-full border border-solid border-[#ACB1B4] px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
          :class="{ ...classes }"
          type="text"
          v-on="useListenerForm(taxInvoice_formValidations, 'invoice_no')"
        />
      </BaseFormGroup>

      <PrimeVueButton
        :disabled="taxInvoice_formValidations.$invalid"
        label="Submit"
        type="submit"
        class="!w-full !bg-hero-black !border-none !text-base !text-white !py-3 !rounded-lg disabled:!bg-muted-tabs disabled:!text-muted"
      />
    </form>
  </section>
</template>
<style>
.p-dialog-content {
  padding: 0;
}
</style>
