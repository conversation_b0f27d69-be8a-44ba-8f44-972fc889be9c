<script setup lang="ts">
import {  defineProps } from 'vue';

interface IProps {
    dates: string;
    tax_invoice_number: string;
}
const props = defineProps<IProps>();

</script>
<template>
    <div class="group flex rounded-lg border border-[#E5E6E8] w-full items-center relative overflow-hidden transition-all duration-300 hover:border-[#FF5A00]">
        <img class="-rotate-[66deg] absolute -left-45" src="/images/topograph_1.png" />
        <img class="rotate-[16deg] absolute right-0" src="/images/topograph_2.png" />
        <div class="px-8 flex gap-2 items-center">
            <div>
                <NuxtImg src="/images/tax_item.svg" alt="close-circle" class="w-[80px] h-[80px] object-cover z-10" />
            </div>
            <div class="flex flex-col p-4 gap-y-4">
                <div class="bg-[#F5F6F6] text-[12px] w-fit px-2 py-1 transition-all duration-300 group-hover:bg-[#FF5A00] group-hover:text-white rounded-md">
                    {{ props.dates }}
                </div>
                <div class="font-bold text-[18px] text-black">
                    Nomor Faktur Pajak : {{ props.tax_invoice_number }}
                </div>
                <div class="flex gap-2">
                    <div class="px-3 py-2 border-[#ACB1B4] border-[1px] flex rounded-md text-medium text-[14px] gap-2 text-[#18191A] cursor-pointer">
                        <NuxtImg src="/icons/see-document.svg" alt="Show Document" />
                        Lihat
                    </div>
                    <div class="px-3 py-2 flex bg-black text-white rounded-md gap-2 cursor-pointer">
                        <NuxtImg src="/icons/download-white.svg" alt="Show Document" />
                        Unduh
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>