
<script setup lang="ts">
import type { ITaxInvoiceProvided } from '../interfaces/tax-invoice.interface.js' 
import TaxInvoiceItem from './TaxInvoiceItem.vue'
/**
 * @description Injecting dependencies
 */
const {
    taxInvoice_formData,
    taxInvoice_formValidations,
} = inject<ITaxInvoiceProvided>('invoiceTax')!;

// ITaxInvoiceProvided
const type = ref([
  'Terlama',
  'Terbaru'  
])

</script>

<template>
  <section id="authentication-login-form" class="flex flex-col items-center justify-center h-full w-full">
    <BaseAlert>
      <template #icon-prefix>
        <NuxtImg src="/icons/warning-danger.svg" alt="warning-icon" class="w-4 h-4" />
      </template>
    </BaseAlert>
    <div class="absolute top-0 right-0 flex gap-2">
        <BaseSelectInput 
            v-model:selected="taxInvoice_formData.type" 
            class="border-[#ACB1B4]"
            placeholder="Diurutkan berdasarkan Terlama"
            :disable="false"
            :options="type"
            padding="0"
            border="#ACB1B4"
            :icon-sorting="true"
        />
        <PrimeVueInputGroup>
          <PrimeVueInputText
          id="no_telp"
          v-model="taxInvoice_formData.invoice_no"
          v-bind="{ ...useBindStateForm('Type to find data') }"
          class="block w-full !border !border-solid !border-[#ACB1B4] px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
          type="text"
          inputmode="numeric"
          v-on="useListenerForm(taxInvoice_formValidations, 'invoice_no')"
          />
          <PrimeVueInputGroupAddon class="!bg-black border border-[#ACB1B4]">
            <p class="text-white bg-black text-[14px] font-normal">Search</p>
          </PrimeVueInputGroupAddon>
      </PrimeVueInputGroup>
    </div>
    <div class="flex flex-col gap-4 w-full">
      <TaxInvoiceItem dates="12 Okt 2025, 15:00" tax_invoice_number="41230129301491231"/>
    </div>
  </section>
</template>
