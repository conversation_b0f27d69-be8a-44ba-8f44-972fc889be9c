// Enums
import { EAlertType } from '~/app/src/core/enums/base-alert.enum';

// Interfaces
import type { ITaxInvoiceForm,ITaxInvoiceSearchData } from '../interfaces/tax-invoice.interface';

// Store
// import { storeToRefs } from 'pinia';
import { useInvoiceStore } from '../stores/tax-invoice.store';

// Vuelidate
import useVuelidate from '@vuelidate/core';
import { maxLength, required,numeric } from '@vuelidate/validators';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useInvoiceTaxService = () => {
  /**
   * @description Injected variables
   */
  const store = useInvoiceStore(); 
  const { $bus } = useNuxtApp();
  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const taxInvoice_formData = reactive<ITaxInvoiceForm>({
    type: '',
    invoice_no: '',
  });

  const taxInvoice_searchData = reactive<ITaxInvoiceSearchData>({
    search:''
  })

  const taxInvoice_isShowModal = ref(false);
  const taxInvoice_isShowConfirmationModal = ref(false)

  /**
   * @description Form validations
   */
  const taxInvoice_formRules: ComputedRef = computed(() => ({
    type: { required },
    invoice_no: { required,numeric,maxLength:maxLength(255) },
  }));
  const taxInvoice_formValidations = useVuelidate(
    taxInvoice_formRules,
    taxInvoice_formData,
    {
      $autoDirty: true,
    },
  );


  // function to call fetch check invoice
  // return: none
  const taxInvoice_fetchInvoice = async (): Promise<void> => {
      try {
        await store.fetchTaxInvoice(taxInvoice_formData.type,taxInvoice_formData.invoice_no);
      } catch (error) {
        $bus.emit('BaseAlert', {
          durationOfAutoClose: 3000,
          isHaveIconClose: true,
          isOpen: true,
          text: error as string,
          type: EAlertType.DANGER,
        } as IPropsBaseAlert);
      }
  }
  
  /**
   * @description Handle action on submit form.
  */
 const taxInvoice_onSubmit = async (): Promise<void> => {
   taxInvoice_formValidations.value.$touch();
    if (taxInvoice_formValidations.value.$invalid) return;
    taxInvoice_isShowConfirmationModal.value = !taxInvoice_isShowConfirmationModal.value
    console.log('execute')
  };
  
  /**
   * @description Handle action on submit form.
  */
 const taxInvoice_onConfirm = async (): Promise<void> => {
  taxInvoice_isShowModal.value = !taxInvoice_isShowModal.value
  taxInvoice_isShowConfirmationModal.value = !taxInvoice_isShowConfirmationModal.value
  await taxInvoice_fetchInvoice()
 };


  /**
   * @description Return everything what we need into an object
   */
  return {
    taxInvoice_formData,
    taxInvoice_searchData,
    taxInvoice_formRules,
    taxInvoice_formValidations,
    taxInvoice_onSubmit,
    taxInvoice_onConfirm,
    taxInvoice_isShowModal,
    taxInvoice_isShowConfirmationModal
  };
};
