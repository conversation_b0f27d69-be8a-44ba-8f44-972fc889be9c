<script setup lang="ts">
// Components
import MainLayout from '~/app/src/core/components/layouts/MainLayout.vue';
import TaxInvoiceTabs from '../components/TaxInvoiceTabs.vue'
// Services
import { useInvoiceTaxService } from '../services/invoice-tax.service';

/**
 * @description Destructure all the data and methods what we need
 */
const {
  taxInvoice_formData,
  taxInvoice_formValidations,
  taxInvoice_isShowModal,
  taxInvoice_onConfirm,
  taxInvoice_onSubmit,
  taxInvoice_isShowConfirmationModal
} = useInvoiceTaxService();

/**
 * @description Provide all the data and methods what we need
 */

 provide('invoiceTax', {
  taxInvoice_formData,
  taxInvoice_formValidations,
  taxInvoice_isShowModal,
  taxInvoice_isShowConfirmationModal,
  taxInvoice_onSubmit,
  taxInvoice_onConfirm
});

</script>

<template>
  <MainLayout page-title="Faktur Pajak">
    <section id="invoice-detail" class="flex flex-col w-full h-full inset-0 z-0 pt-6 relative">
      <TaxInvoiceTabs/>
    </section>
  </MainLayout>
</template>
