// Constants
// import { INVOICE_STATUS_TABS } from '../constants';
import { INVOICE_ENDPOINT_GET_LIST } from '../constants/tax-invoice.api.constant';

// Interfaces
import type {
  IInvoiceDetailHeader,
  IQueryParamsGetDetailInvoiceProducts,
  IQueryParamsGetListInvoice,
  IResponseInvoiceProductList,
  IStateInvoiceStore,
} from '../interfaces/tax-invoice.interface';

// Pinia
import { defineStore } from 'pinia';

export const useInvoiceStore = defineStore('invoice', {
  state: (): IStateInvoiceStore => ({
    invoice_listIsLoading: false,
    invoice_listData: [],
    invoice_listPagination: {
      total_data: 0,
      size: 0,
      active_page: 0,
      total_page: 0,
    },
    invoice_detailHeaderData: null,
    invoice_detailHeaderLoading: false,
    invoice_detailProductListData: [],
    invoice_detailProductIsLoading: false,
    invoice_detailProductListPagination: {
      total_data: 0,
      size: 12,
      active_page: 0,
      total_page: 0,
    },
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api get list invoice.
     * @url /invoice
     * @method GET
     * @access private
     */
    async fetchInvoice_getList(requestParams: IQueryParamsGetListInvoice): Promise<unknown> {
      try {
        this.invoice_listIsLoading = true;
        this.invoice_listData = [];
        this.invoice_listPagination = {
          total_data: 0,
          size: 0,
          active_page: 0,
          total_page: 0,
        };
        const params = {
          ...requestParams,
          // status: requestParams.status === INVOICE_STATUS_TABS[0].value ? '' : requestParams.status,
        };
        const { data, error } = await useApiFetch(INVOICE_ENDPOINT_GET_LIST, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            this.invoice_listData = response._data.data?.data ?? [];
            this.invoice_listPagination = {
              total_data: response._data.data?.total_data ?? 0,
              size: response._data.data?.size ?? 0,
              active_page: response._data.data?.active_page ?? 0,
              total_page: response._data.data?.total_page ?? 0,
            };
          },
          onRequestError: () => {
            this.invoice_listData = [];
            this.invoice_listPagination = {
              total_data: 0,
              size: 0,
              active_page: 0,
              total_page: 0,
            };
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.invoice_listIsLoading = false;
      }
    },

    /**
     * @description Handle fetch api get invoice header.
     * @url /invoice/:invoiceNumber/header
     * @method GET
     * @access private
     */
    async fetchInvoice_getInvoiceHeader(invoiceNumber: number): Promise<unknown> {
      try {
        this.invoice_detailHeaderLoading = true;
        this.invoice_detailHeaderData = null;
        const { data, error } = await useApiFetch(`${INVOICE_ENDPOINT_GET_LIST}/${invoiceNumber}/header`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const result: IInvoiceDetailHeader = response._data?.data || null;
            this.invoice_detailHeaderData = result;
          },
          onRequestError: () => {
            this.invoice_detailHeaderData = null;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.invoice_detailHeaderLoading = false;
      }
    },

    /**
     * @description Handle fetch api get detail product list.
     * @url /invoice/:invoiceNumber
     * @method GET
     * @access private
     */
    async fetchInvoice_getListProduct(
      invoiceNumber: number,
      params: IQueryParamsGetDetailInvoiceProducts,
    ): Promise<unknown> {
      try {
        this.invoice_detailProductIsLoading = true;
        this.invoice_detailProductListData = [];
        this.invoice_detailProductListPagination = {
          total_data: 0,
          size: 12,
          active_page: 0,
          total_page: 0,
        };

        const { data, error } = await useApiFetch(`${INVOICE_ENDPOINT_GET_LIST}/${invoiceNumber}/items`, {
          method: 'GET',
          params,
          onResponse: async ({ response }) => {
            const result: IResponseInvoiceProductList = response._data.data;
            console.log('RESULT of fetchInvoice_getListProduct->', result);
            this.invoice_detailProductListData = result.data;
            this.invoice_detailProductListPagination.total_data = result.total_data;
            this.invoice_detailProductListPagination.size = result.size;
            this.invoice_detailProductListPagination.active_page = result.active_page;
            this.invoice_detailProductListPagination.total_page = result.total_page;
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        this.invoice_detailProductListData = [];
        return Promise.reject(new Error(error as string));
      } finally {
        this.invoice_detailProductIsLoading = false;
      }
    },

    /**
     * @description Handle download invoice.
     * @url /invoice/:invoiceNumber/download
     * @method GET
     * @access private
     */
    async fetchInvoice_download(invoiceNumber: number): Promise<unknown> {
      try {
        await useApiFetch(`${INVOICE_ENDPOINT_GET_LIST}/${invoiceNumber}/download`, {
          method: 'GET',
          onResponse: async ({ response }) => {
            if (response?._data) {
              window.location.href = response._data;
              window.open(response._data, '_blank');
            }
          },
        });
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * @description Handle send invoice to email.
     * @url 
     * @method POST
     * @access private
     */
    async fetchTaxInvoice(type_tax: string,invoice_no:string) {
      console.log(`HIT API TAX INVOICE TYPE TAX = ${type_tax},INVOICE NO = ${invoice_no}`)
    }
  },
});
