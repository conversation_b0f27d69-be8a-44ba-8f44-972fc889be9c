import { CONFIGURATION_ENDPOINT } from '../constants/configurations.api.constant';
import type { IConfigurationStates } from '../interfaces/configurations.interface';

// Pinia
import { defineStore } from 'pinia';

export const useConfigurationStore = defineStore('configurations', {
  state: (): IConfigurationStates => ({
    config_isLoading: false,
    CUSTOM_ORDER_LOGO_PRICE: null,
    CUSTOM_ORDER_TEXT_PRICE: null,
    MIN_REGULAR_ORDER_QTY: null,
    MIN_REGULAR_ORDER_AMOUNT: null,
    MIN_CUSTOM_ORDER_AMOUNT: null,
    MIN_CUSTOM_ORDER_QTY: null,
    config_hideInquiryForm: true,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    async configurations_fetchConfigurations(): Promise<unknown> {
      try {
        this.config_isLoading = true;
        const { data, error } = await useApiFetch(CONFIGURATION_ENDPOINT, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const data = response._data.data;
            if (response?._data) {
              this.CUSTOM_ORDER_LOGO_PRICE = data?.CUSTOM_ORDER_LOGO_PRICE;
              this.CUSTOM_ORDER_TEXT_PRICE = data?.CUSTOM_ORDER_TEXT_PRICE;
              this.MIN_REGULAR_ORDER_QTY = data?.MIN_REGULAR_ORDER_QTY;
              this.MIN_REGULAR_ORDER_AMOUNT = data?.MIN_REGULAR_ORDER_AMOUNT;
              this.MIN_CUSTOM_ORDER_AMOUNT = data?.MIN_CUSTOM_ORDER_AMOUNT;
              this.MIN_CUSTOM_ORDER_QTY = data?.MIN_CUSTOM_ORDER_QTY;
            }
          },
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        this.config_isLoading = false;
      }
    },
  },

  persist: {
    storage: localStorage,
    pick: [
      'CUSTOM_ORDER_LOGO_PRICE',
      'CUSTOM_ORDER_TEXT_PRICE',
      'MIN_REGULAR_ORDER_QTY',
      'MIN_REGULAR_ORDER_AMOUNT',
      'MIN_CUSTOM_ORDER_AMOUNT',
      'MIN_CUSTOM_ORDER_QTY',
    ],
  },
});
