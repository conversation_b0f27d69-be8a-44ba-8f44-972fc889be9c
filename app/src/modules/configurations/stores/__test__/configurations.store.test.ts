import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useConfigurationStore } from '../configurations.store';

describe('Configurations store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useConfigurationStore();

      expect(store.config_isLoading).toBe(false);
      expect(store.CUSTOM_ORDER_LOGO_PRICE).toBeNull();
      expect(store.CUSTOM_ORDER_TEXT_PRICE).toBeNull();
      expect(store.MIN_REGULAR_ORDER_QTY).toBeNull();
      expect(store.MIN_REGULAR_ORDER_AMOUNT).toBeNull();
      expect(store.MIN_CUSTOM_ORDER_AMOUNT).toBeNull();
      expect(store.MIN_CUSTOM_ORDER_QTY).toBeNull();
    });

    it('has correct initial computed state', () => {
      const store = useConfigurationStore();
      expect(store.config_isLoading).toBe(false);
    });
  });

  it('Default logo price should be null', () => {
    const store = useConfigurationStore();
    expect(store.CUSTOM_ORDER_LOGO_PRICE).toBeNull();
  });

  // describe('Fetch configurations', () => {
  //   it('successfully fetch configurations', async () => {
  //     const store = useConfigurationStore();
  //     await store.configurations_fetchConfigurations();
  //   });
  // });
});
