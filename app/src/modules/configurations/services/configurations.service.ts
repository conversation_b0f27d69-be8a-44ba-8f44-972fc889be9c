// Store
import { storeToRefs } from 'pinia';
import { useConfigurationStore } from '../stores/configurations.store';
import type { IConfigurationProvided } from '../interfaces/configurations.interface';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useConfigurationService = (): IConfigurationProvided => {
  /**
   * @description Injected variables
   */
  const store = useConfigurationStore(); // Instance of the store
  const {
    config_isLoading,
    CUSTOM_ORDER_LOGO_PRICE,
    CUSTOM_ORDER_TEXT_PRICE,
    MIN_REGULAR_ORDER_QTY,
    MIN_REGULAR_ORDER_AMOUNT,
    MIN_CUSTOM_ORDER_AMOUNT,
    MIN_CUSTOM_ORDER_QTY,
    config_hideInquiryForm,
  } = storeToRefs(store);

  const toast = useToast();
  const { $bus } = useNuxtApp();

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description  Handle fetch api configurations.
   */
  const configuration_fetchConfigurations = async (): Promise<void> => {
    await store.configurations_fetchConfigurations();
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    config_isLoading,
    CUSTOM_ORDER_LOGO_PRICE,
    CUSTOM_ORDER_TEXT_PRICE,
    MIN_REGULAR_ORDER_QTY,
    MIN_REGULAR_ORDER_AMOUNT,
    MIN_CUSTOM_ORDER_AMOUNT,
    MIN_CUSTOM_ORDER_QTY,
    config_hideInquiryForm,
    configuration_fetchConfigurations,
  };
};
