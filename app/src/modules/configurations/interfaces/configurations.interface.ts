export interface IConfigurationStates {
  config_isLoading: boolean;
  CUSTOM_ORDER_LOGO_PRICE: number | null;
  CUSTOM_ORDER_TEXT_PRICE: number | null;
  MIN_REGULAR_ORDER_QTY: number | null;
  MIN_REGULAR_ORDER_AMOUNT: number | null;
  MIN_CUSTOM_ORDER_AMOUNT: number | null;
  MIN_CUSTOM_ORDER_QTY: number | null;
  config_hideInquiryForm: boolean;
}

export interface IConfigurationProvided {
  config_isLoading: Ref<boolean>;
  CUSTOM_ORDER_LOGO_PRICE: Ref<number | null>;
  CUSTOM_ORDER_TEXT_PRICE: Ref<number | null>;
  MIN_REGULAR_ORDER_QTY: Ref<number | null>;
  MIN_REGULAR_ORDER_AMOUNT: Ref<number | null>;
  MIN_CUSTOM_ORDER_AMOUNT: Ref<number | null>;
  MIN_CUSTOM_ORDER_QTY: Ref<number | null>;
  configuration_fetchConfigurations: () => Promise<void>;
  config_hideInquiryForm: Ref<boolean>;
}
