// Pinia
import { defineStore } from 'pinia';

/**
 * @description This store is used to handle the global states, mutations, and actions of the application.
 */
export const useAppStore = defineStore('app', {
  state: () => ({
    app_isLoading: false,
    app_searchQueryOfProduct: '',
    app_headerMenu: [],
    app_headerMenuIsLoading: false,
    app_logoutIsLoading: false,
  }),
  getters: {
    /**
     * @description Usually, we define getters if the getter name is different from the state name.
     */
  },
  actions: {
    /**
     * @description Handle fetch api logout.
     * @url /logout
     * @method GET
     * @access private
     */
    async app_fetchLogout(): Promise<unknown> {
      try {
        this.app_logoutIsLoading = true;
        this.app_isLoading = true;
        document.cookie = 'accessToken=; path=/; max-age=0';
        localStorage.removeItem('authentication');
        const { data, error } = await useApiFetch(APP_ENDPOINT_LOGOUT, {
          method: 'GET',
        });

        if (error.value) {
          throw new Error(error.value?.message);
        }

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(new Error(error as string));
      } finally {
        setTimeout(() => {
          this.app_logoutIsLoading = false;
          this.app_isLoading = false;
        }, 1000);
      }
    },

    /**
     * @description Handle fetch api get header menu.
     * @method GET
     * @access public
     */
    async app_fetchHeaderMenu(): Promise<unknown> {
      try {
        this.app_headerMenuIsLoading = true;
        const { data, error } = await useApiFetch(APP_ENDPOINT_HEADER_MENU, {
          method: 'GET',
          onResponse: async ({ response }) => {
            const data = response._data.data as IHeaderMenu[];
            this.app_headerMenu = data as unknown as never[];
          },
        });
        if (error.value) {
          throw new Error(error.value?.message);
        }
        return Promise.resolve(data);
      } catch {
        return Promise.reject(new Error('Failed to fetch header menu'));
      } finally {
        this.app_headerMenuIsLoading = false;
      }
    },
  },

  persist: {
    storage: localStorage,
    pick: ['app_headerMenu'],
  },
});
