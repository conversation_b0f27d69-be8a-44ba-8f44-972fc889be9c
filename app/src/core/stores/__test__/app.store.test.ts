import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActivePinia, createP<PERSON> } from 'pinia';
import { useAppStore } from '../app.store';

describe('App store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useAppStore();

      expect(store.app_isLoading).toBe(false);
      expect(store.app_searchQueryOfProduct).toBe('');
      expect(store.app_headerMenu).toMatchObject([]);
      expect(store.app_headerMenuIsLoading).toBe(false);
      expect(store.app_logoutIsLoading).toBe(false);
    });

    it('has correct initial computed state', () => {
      const store = useAppStore();
      expect(store.app_isLoading).toBe(false);
      expect(store.app_logoutIsLoading).toBe(false);
    });
  });

  it('Is correct default value cart data limit', () => {
    const store = useAppStore();
    expect(store.app_headerMenu).toMatchObject([]);
  });
});
