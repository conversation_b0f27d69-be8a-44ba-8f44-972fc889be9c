import { describe, it, expect } from 'vitest';
import { replaceParams } from '../replace-parameters.helper';

describe('replaceParams', () => {
  it('replaces a single parameter correctly', () => {
    const result = replaceParams('url/{id}', { id: '1' });
    expect(result).toBe('url/1');
  });

  it('replaces multiple parameters correctly', () => {
    const result = replaceParams('/user/{userId}/post/{postId}', {
      userId: '42',
      postId: '100',
    });
    expect(result).toBe('/user/42/post/100');
  });

  it('keeps placeholder if no replacement is provided', () => {
    const result = replaceParams('/user/{userId}/post/{postId}', {
      userId: '42',
    });
    expect(result).toBe('/user/42/post/{postId}');
  });

  it('returns original string if there are no placeholders', () => {
    const result = replaceParams('/static/url', { id: '123' });
    expect(result).toBe('/static/url');
  });

  it('returns original string if replacements object is empty', () => {
    const result = replaceParams('/user/{id}');
    expect(result).toBe('/user/{id}');
  });

  it('handles repeated placeholders correctly', () => {
    const result = replaceParams('/{id}/details/{id}', { id: '999' });
    expect(result).toBe('/999/details/999');
  });

  it('ignores placeholders with unsupported format (e.g., nested)', () => {
    const result = replaceParams('/user/{user.id}', { 'user.id': '123' });
    expect(result).toBe('/user/{user.id}');
  });

  it('works with numeric replacement values as strings', () => {
    const result = replaceParams('/order/{orderId}', { orderId: '456' });
    expect(result).toBe('/order/456');
  });

  it('leaves unknown placeholders unchanged', () => {
    const result = replaceParams('/path/{unknown}', {});
    expect(result).toBe('/path/{unknown}');
  });
});
