import { describe, it, expect } from 'vitest';
import {
  isContainLowerCase,
  isContainUpperCase,
  isContainNumber,
  isContainSpecialCharacter,
  getFilenameFromURL,
} from '../regex.helper';

describe('isContainLowerCase', () => {
  it('should return true if string contains at least one lowercase letter', () => {
    expect(isContainLowerCase('abc')).toBe(true);
    expect(isContainLowerCase('ABCd')).toBe(true);
  });

  it('should return false if string contains no lowercase letters', () => {
    expect(isContainLowerCase('1234')).toBe(false);
    expect(isContainLowerCase('ABC')).toBe(false);
    expect(isContainLowerCase('')).toBe(false);
  });
});

describe('isContainUpperCase', () => {
  it('should return true if string contains at least one uppercase letter', () => {
    expect(isContainUpperCase('abcD')).toBe(true);
    expect(isContainUpperCase('HELLO')).toBe(true);
  });

  it('should return false if string contains no uppercase letters', () => {
    expect(isContainUpperCase('hello')).toBe(false);
    expect(isContainUpperCase('1234')).toBe(false);
    expect(isContainUpperCase('')).toBe(false);
  });
});

describe('isContainNumber', () => {
  it('should return true if string contains at least one number', () => {
    expect(isContainNumber('abc123')).toBe(true);
    expect(isContainNumber('1')).toBe(true);
  });

  it('should return false if string contains no numbers', () => {
    expect(isContainNumber('abc')).toBe(false);
    expect(isContainNumber('ABC')).toBe(false);
    expect(isContainNumber('')).toBe(false);
  });
});

describe('isContainSpecialCharacter', () => {
  it('should return true if string contains at least one special character', () => {
    expect(isContainSpecialCharacter('hello!')).toBe(true);
    expect(isContainSpecialCharacter('abc@123')).toBe(true);
  });

  it('should return false if string contains no special characters', () => {
    expect(isContainSpecialCharacter('abc123')).toBe(false);
    expect(isContainSpecialCharacter('ABC')).toBe(false);
    expect(isContainSpecialCharacter('')).toBe(false);
  });
});

describe('getFilenameFromURL', () => {
  it('should return filename from a valid URL', () => {
    expect(getFilenameFromURL('https://example.com/files/report.pdf')).toBe('report.pdf');
    expect(getFilenameFromURL('http://localhost:3000/image.png')).toBe('image.png');
  });

  it('should return empty string if no filename in URL', () => {
    expect(getFilenameFromURL('https://example.com/')).toBe('');
    expect(getFilenameFromURL('https://example.com/folder/')).toBe('');
    expect(getFilenameFromURL('')).toBe('');
  });
});
