import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setTimestampOfFetchAPI } from '../set-timestamp-fetch.helper';

describe('setTimestampOfFetchAPI', () => {
  let mockDate: Date;

  beforeEach(() => {
    // Mock Date to have consistent test results
    mockDate = new Date('2024-01-15T10:30:00.000Z');
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('with valid object input', () => {
    it('should add fetchedAt timestamp to existing object', () => {
      const input = { name: '<PERSON>', age: 30 };
      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        name: 'John',
        age: 30,
        fetchedAt: mockDate,
      });
    });

    it('should preserve all existing properties', () => {
      const input = {
        id: 123,
        data: ['item1', 'item2'],
        nested: { key: 'value' },
        boolean: true,
        nullValue: null,
      };

      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        id: 123,
        data: ['item1', 'item2'],
        nested: { key: 'value' },
        boolean: true,
        nullValue: null,
        fetchedAt: mockDate,
      });
    });

    it('should handle empty object', () => {
      const input = {};
      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should overwrite existing fetchedAt if present', () => {
      const oldDate = new Date('2023-01-01T00:00:00.000Z');
      const input = { name: 'John', fetchedAt: oldDate };
      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        name: 'John',
        fetchedAt: mockDate,
      });
      expect(result.fetchedAt).not.toEqual(oldDate);
    });

    it('should handle arrays as objects', () => {
      const input = ['item1', 'item2', 'item3'];
      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        ...input,
        fetchedAt: mockDate,
      });
      expect(result).toHaveProperty('0', 'item1');
      expect(result).toHaveProperty('1', 'item2');
      expect(result).toHaveProperty('2', 'item3');
      expect(result).toHaveProperty('fetchedAt', mockDate);
    });

    it('should handle Date objects', () => {
      const inputDate = new Date('2023-06-15T14:30:00.000Z');
      const result = setTimestampOfFetchAPI(inputDate);

      expect(result).toEqual({
        ...inputDate,
        fetchedAt: mockDate,
      });
    });

    it('should not mutate the original input object', () => {
      const input = { name: 'John', age: 30 };
      const originalInput = { ...input };

      setTimestampOfFetchAPI(input);

      expect(input).toEqual(originalInput);
      expect(input).not.toHaveProperty('fetchedAt');
    });

    it('should handle complex nested objects', () => {
      const input = {
        user: {
          id: 1,
          profile: {
            name: 'John',
            settings: {
              theme: 'dark',
              notifications: true,
            },
          },
        },
        data: [1, 2, 3],
        metadata: {
          version: '1.0',
          lastModified: new Date('2023-01-01'),
        },
      };

      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        ...input,
        fetchedAt: mockDate,
      });
      expect(result.user.profile.name).toBe('John');
      expect(result.data).toEqual([1, 2, 3]);
    });
  });

  describe('with invalid or non-object input', () => {
    it('should return object with fetchedAt for null input', () => {
      const result = setTimestampOfFetchAPI(null);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for undefined input', () => {
      const result = setTimestampOfFetchAPI(undefined);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for string input', () => {
      const result = setTimestampOfFetchAPI('hello world');

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for number input', () => {
      const result = setTimestampOfFetchAPI(42);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for boolean input', () => {
      const result = setTimestampOfFetchAPI(true);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });

      const resultFalse = setTimestampOfFetchAPI(false);
      expect(resultFalse).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for function input', () => {
      const func = () => 'test';
      const result = setTimestampOfFetchAPI(func);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for symbol input', () => {
      const sym = Symbol('test');
      const result = setTimestampOfFetchAPI(sym);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });

    it('should return object with fetchedAt for BigInt input', () => {
      const bigInt = BigInt(123);
      const result = setTimestampOfFetchAPI(bigInt);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });
  });

  describe('timestamp behavior', () => {
    it('should create different timestamps for consecutive calls', () => {
      vi.useRealTimers(); // Use real timers for this test

      const input1 = { data: 'first' };
      const input2 = { data: 'second' };

      const result1 = setTimestampOfFetchAPI(input1);

      // Small delay to ensure different timestamps
      const delay = () => new Promise(resolve => setTimeout(resolve, 1));

      return delay().then(() => {
        const result2 = setTimestampOfFetchAPI(input2);

        expect(result1.fetchedAt).toBeInstanceOf(Date);
        expect(result2.fetchedAt).toBeInstanceOf(Date);
        expect(result1.fetchedAt.getTime()).toBeLessThanOrEqual(result2.fetchedAt.getTime());
      });
    });

    it('should always create a new Date instance', () => {
      const input = { test: 'data' };

      const result1 = setTimestampOfFetchAPI(input);
      const result2 = setTimestampOfFetchAPI(input);

      // Even with mocked time, they should be different instances
      expect(result1.fetchedAt).not.toBe(result2.fetchedAt);
      expect(result1.fetchedAt.getTime()).toBe(result2.fetchedAt.getTime());
    });
  });

  describe('type safety and edge cases', () => {
    it('should handle objects with prototype properties', () => {
      const input = Object.create({ inherited: 'value' });
      input.own = 'property';

      const result = setTimestampOfFetchAPI(input);

      expect(result).toHaveProperty('own', 'property');
      expect(result).toHaveProperty('fetchedAt', mockDate);
      // Should not include inherited properties in spread
      expect(result).not.toHaveProperty('inherited');
    });

    it('should handle objects with non-enumerable properties', () => {
      const input = {};
      Object.defineProperty(input, 'hidden', {
        value: 'secret',
        enumerable: false,
      });
      Object.defineProperty(input, 'visible', {
        value: 'public',
        enumerable: true,
      });

      const result = setTimestampOfFetchAPI(input);

      expect(result).toHaveProperty('visible', 'public');
      expect(result).toHaveProperty('fetchedAt', mockDate);
      expect(result).not.toHaveProperty('hidden');
    });

    it('should handle frozen objects', () => {
      const input = Object.freeze({ name: 'frozen' });
      const result = setTimestampOfFetchAPI(input);

      expect(result).toEqual({
        name: 'frozen',
        fetchedAt: mockDate,
      });
      expect(Object.isFrozen(result)).toBe(false);
    });

    it('should handle objects with getters and setters', () => {
      const input = {
        _value: 'test',
        get value() {
          return this._value;
        },
        set value(val) {
          this._value = val;
        },
      };

      const result = setTimestampOfFetchAPI(input);

      expect(result).toHaveProperty('_value', 'test');
      expect(result).toHaveProperty('value', 'test');
      expect(result).toHaveProperty('fetchedAt', mockDate);
    });
  });

  describe('real-world usage scenarios', () => {
    it('should work with typical API response format', () => {
      const apiResponse = {
        success: true,
        data: {
          users: [
            { id: 1, name: 'John' },
            { id: 2, name: 'Jane' },
          ],
        },
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
        },
      };

      const result = setTimestampOfFetchAPI(apiResponse);

      expect(result).toEqual({
        ...apiResponse,
        fetchedAt: mockDate,
      });
      expect(result.data.users).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
    });

    it('should work with error response format', () => {
      const errorResponse = {
        error: true,
        message: 'Something went wrong',
        code: 500,
        details: {
          field: 'validation error',
        },
      };

      const result = setTimestampOfFetchAPI(errorResponse);

      expect(result).toEqual({
        ...errorResponse,
        fetchedAt: mockDate,
      });
    });

    it('should handle empty API responses', () => {
      const emptyResponse = {};
      const result = setTimestampOfFetchAPI(emptyResponse);

      expect(result).toEqual({
        fetchedAt: mockDate,
      });
    });
  });
});
