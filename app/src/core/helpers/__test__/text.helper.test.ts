import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  isInputNumber,
  capitalizeFirstLetter,
  getInitialsName,
  formatSizeUnits,
  makeId,
  dataURLtoBlob,
} from '../text.helper';

describe('isInputNumber', () => {
  let mockEvent: KeyboardEvent;

  beforeEach(() => {
    mockEvent = {
      key: '',
      preventDefault: vi.fn(),
    } as unknown as KeyboardEvent;
  });

  it('should allow numeric keys', () => {
    const numericKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    numericKeys.forEach(key => {
      mockEvent.key = key;
      isInputNumber(mockEvent);
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    });
  });

  it('should prevent non-numeric keys', () => {
    const nonNumericKeys = ['a', 'b', 'z', 'A', 'Z', '!', '@', '#', ' ', 'Enter', 'Backspace'];

    nonNumericKeys.forEach(key => {
      const event = {
        key,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      isInputNumber(event);
      expect(event.preventDefault).toHaveBeenCalled();
    });
  });

  it('should prevent special characters', () => {
    const specialKeys = ['.', ',', '-', '+', '=', '/', '*'];

    specialKeys.forEach(key => {
      const event = {
        key,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      isInputNumber(event);
      expect(event.preventDefault).toHaveBeenCalled();
    });
  });
});

describe('capitalizeFirstLetter', () => {
  it('should capitalize first letter of lowercase string', () => {
    expect(capitalizeFirstLetter('hello')).toBe('Hello');
    expect(capitalizeFirstLetter('world')).toBe('World');
  });

  it('should handle uppercase strings', () => {
    expect(capitalizeFirstLetter('HELLO')).toBe('Hello');
    expect(capitalizeFirstLetter('WORLD')).toBe('World');
  });

  it('should handle mixed case strings', () => {
    expect(capitalizeFirstLetter('hELLo')).toBe('Hello');
    expect(capitalizeFirstLetter('wORLD')).toBe('World');
  });

  it('should handle single character strings', () => {
    expect(capitalizeFirstLetter('a')).toBe('A');
    expect(capitalizeFirstLetter('Z')).toBe('Z');
  });

  it('should handle empty string', () => {
    expect(capitalizeFirstLetter('')).toBe('');
  });

  it('should handle null and undefined', () => {
    expect(capitalizeFirstLetter(null as any)).toBe('');
    expect(capitalizeFirstLetter(undefined as any)).toBe('');
  });

  it('should handle strings with spaces', () => {
    expect(capitalizeFirstLetter('hello world')).toBe('Hello world');
    expect(capitalizeFirstLetter(' hello')).toBe(' hello');
  });

  it('should handle strings with numbers', () => {
    expect(capitalizeFirstLetter('123abc')).toBe('123abc');
    expect(capitalizeFirstLetter('abc123')).toBe('Abc123');
  });
});

describe('getInitialsName', () => {
  it('should get initials from full name', () => {
    expect(getInitialsName('John Doe')).toBe('JD');
    expect(getInitialsName('Jane Smith')).toBe('JS');
  });

  it('should handle three or more names', () => {
    expect(getInitialsName('John Michael Doe')).toBe('JM');
    expect(getInitialsName('Mary Jane Watson Smith')).toBe('MJ');
  });

  it('should handle single name', () => {
    expect(getInitialsName('John')).toBe('J');
    expect(getInitialsName('Madonna')).toBe('M');
  });

  it('should pick first character when pickFirstChar is true', () => {
    expect(getInitialsName('John Doe', true)).toBe('J');
    expect(getInitialsName('Jane Smith Watson', true)).toBe('J');
  });

  it('should handle names with extra spaces', () => {
    expect(getInitialsName('John  Doe')).toBe('JD');
    expect(getInitialsName(' John Doe ')).toBe('JD');
  });

  it('should convert to uppercase', () => {
    expect(getInitialsName('john doe')).toBe('JD');
    expect(getInitialsName('jane smith')).toBe('JS');
  });

  it('should handle empty string', () => {
    expect(getInitialsName('')).toBe('');
  });

  it('should handle single character names', () => {
    expect(getInitialsName('A B')).toBe('AB');
    expect(getInitialsName('A')).toBe('A');
  });
});

describe('formatSizeUnits', () => {
  it('should format bytes correctly', () => {
    expect(formatSizeUnits(0)).toBe('0 Byte');
    expect(formatSizeUnits(1)).toBe('1 Byte');
    expect(formatSizeUnits(2)).toBe('1 Bytes');
  });

  it('should format KB correctly', () => {
    expect(formatSizeUnits(1024)).toBe('1.0 KB');
    expect(formatSizeUnits(2048)).toBe('2.0 KB');
    expect(formatSizeUnits(1536)).toBe('1.512 KB'); // 1.5 KB
  });

  it('should format MB correctly', () => {
    expect(formatSizeUnits(1048576)).toBe('1.0 MB'); // 1 MB
    expect(formatSizeUnits(2097152)).toBe('2.0 MB'); // 2 MB
  });

  it('should format GB correctly', () => {
    expect(formatSizeUnits(1073741824)).toBe('1.0 GB'); // 1 GB
    expect(formatSizeUnits(2147483648)).toBe('2.0 GB'); // 2 GB
  });

  it('should handle large numbers', () => {
    const largeNumber = 5 * 1024 * 1024 * 1024; // 5 GB
    const result = formatSizeUnits(largeNumber);
    expect(result).toContain('GB');
  });
});

describe('makeId', () => {
  it('should generate ID with default length of 12', () => {
    const id = makeId();
    expect(id).toHaveLength(12);
  });

  it('should generate ID with custom length', () => {
    expect(makeId(5)).toHaveLength(5);
    expect(makeId(20)).toHaveLength(20);
    expect(makeId(1)).toHaveLength(1);
  });

  it('should only contain allowed characters', () => {
    const id = makeId(100);
    const allowedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    for (const char of id) {
      expect(allowedChars).toContain(char);
    }
  });

  it('should generate different IDs on multiple calls', () => {
    const id1 = makeId();
    const id2 = makeId();
    const id3 = makeId();

    expect(id1).not.toBe(id2);
    expect(id2).not.toBe(id3);
    expect(id1).not.toBe(id3);
  });

  it('should generate uppercase characters and numbers only', () => {
    const id = makeId(50);
    expect(id).toMatch(/^[A-Z0-9]+$/);
  });
});

describe('dataURLtoBlob', () => {
  it('should convert data URL to Blob', () => {
    // Simple base64 encoded "hello" text
    const dataUrl = 'data:text/plain;base64,aGVsbG8=';
    const blob = dataURLtoBlob(dataUrl);

    expect(blob).toBeInstanceOf(Blob);
    expect(blob.type).toBe('text/plain');
    expect(blob.size).toBe(5); // "hello" is 5 bytes
  });

  it('should handle image data URLs', () => {
    // 1x1 transparent PNG
    const dataUrl =
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    const blob = dataURLtoBlob(dataUrl);

    expect(blob).toBeInstanceOf(Blob);
    expect(blob.type).toBe('image/png');
    expect(blob.size).toBeGreaterThan(0);
  });

  it('should handle different MIME types', () => {
    const jsonDataUrl = 'data:application/json;base64,eyJ0ZXN0IjoidmFsdWUifQ=='; // {"test":"value"}
    const blob = dataURLtoBlob(jsonDataUrl);

    expect(blob).toBeInstanceOf(Blob);
    expect(blob.type).toBe('application/json');
  });

  it('should create blob with correct content', async () => {
    const originalText = 'Hello, World!';
    const base64 = btoa(originalText);
    const dataUrl = `data:text/plain;base64,${base64}`;

    const blob = dataURLtoBlob(dataUrl);
    const text = await blob.text();

    expect(text).toBe(originalText);
  });

  it('should handle empty data', () => {
    const dataUrl = 'data:text/plain;base64,';
    const blob = dataURLtoBlob(dataUrl);

    expect(blob).toBeInstanceOf(Blob);
    expect(blob.type).toBe('text/plain');
    expect(blob.size).toBe(0);
  });
});

// Additional integration tests
describe('Integration Tests', () => {
  it('should work with makeId and various lengths', () => {
    for (let i = 1; i <= 20; i++) {
      const id = makeId(i);
      expect(id).toHaveLength(i);
      expect(id).toMatch(/^[A-Z0-9]*$/);
    }
  });

  it('should handle edge cases for getInitialsName', () => {
    expect(getInitialsName('A')).toBe('A');
    expect(getInitialsName('A B C D E F', true)).toBe('A');
    expect(getInitialsName('A B C D E F', false)).toBe('AB');
  });

  it('should handle capitalizeFirstLetter with various inputs', () => {
    const testCases = [
      { input: 'test', expected: 'Test' },
      { input: 'TEST', expected: 'Test' },
      { input: 'tEST', expected: 'Test' },
      { input: 't', expected: 'T' },
      { input: '123abc', expected: '123abc' },
      { input: '', expected: '' },
    ];

    testCases.forEach(({ input, expected }) => {
      expect(capitalizeFirstLetter(input)).toBe(expected);
    });
  });
});
