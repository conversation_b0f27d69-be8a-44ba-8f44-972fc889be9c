export const isInputNumber = (evt: KeyboardEvent) => {
  const keysAllowed = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  const keyPressed = evt.key;

  if (!keysAllowed.includes(keyPressed)) {
    evt.preventDefault();
  }
};

export const capitalizeFirstLetter = (val: string) => {
  if (!val) return '';
  return val.charAt(0).toUpperCase() + val.slice(1).toLowerCase();
};

/**
 * @description Get initials name - used for the purposes of create avatar text
 *
 * @param {string} fullName
 * @param {boolean} pickFirstChar
 * @returns {string}
 */
export const getInitialsName = (fullName: string, pickFirstChar?: boolean): string => {
  const initialsName = fullName
    .split(' ')
    .map(str => str[0])
    .join('')
    .toUpperCase();

  return initialsName.length > 1 ? initialsName.substring(0, pickFirstChar ? 1 : 2) : initialsName;
};

export const formatSizeUnits = (bytes: number): string => {
  let _bytes = '';
  if ((bytes >> 30) & 0x3ff) _bytes = (bytes >>> 30) + '.' + (bytes & (3 * 0x3ff)) + ' GB';
  else if ((bytes >> 20) & 0x3ff) _bytes = (bytes >>> 20) + '.' + (bytes & (2 * 0x3ff)) + ' MB';
  else if ((bytes >> 10) & 0x3ff) _bytes = (bytes >>> 10) + '.' + (bytes & 0x3ff) + ' KB';
  else if ((bytes >> 1) & 0x3ff) _bytes = (bytes >>> 1) + ' Bytes';
  else _bytes = bytes + ' Byte';
  return _bytes;
};

export const makeId = (paramLength?: number) => {
  const length = paramLength ? paramLength : 12;
  let str = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  for (let i = 0; i < length; i++) {
    str += characters.charAt(Math.floor(Math.random() * 36));
  }
  return str;
};

export const dataURLtoBlob = (dataUrl: string) => {
  const arr = dataUrl.split(',');
  const mime = arr[0].split(':')[1].split(';')[0];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  // eslint-disable-next-line no-plusplus
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
};
