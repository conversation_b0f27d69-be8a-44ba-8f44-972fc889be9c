export const VALIDATION_MESSAGE: Record<string, string> = {
  alpha: '{attribute} Harus berupa alfabet',
  alphaNum: '{attribute} <PERSON><PERSON> boleh alfabet dan angka',
  alphaSpace: '{attribute} Harus berupa alfabet',
  available: '{attribute} sudah ada',
  date: '{attribute} tanggal rentang tidak valid',
  decimal: 'Kolom {attribute} harus berupa angka',
  email: 'Email tidak valid',
  invalidCloseHour: 'Jam tutup harus lebih besar dari jam buka',
  invalidDateEnd: 'Tanggal selesai harus lebih kecil dari tanggal mulai',
  invalidDateStart: 'Tanggal mulai harus lebih besar dari tanggal selesai',
  invalidOpenHour: 'Jam buka harus lebih kecil dari jam tutup',
  isContainLowerCase: '{attribute} harus berisi setidaknya satu huruf kecil',
  isContainNumber: '{attribute} setidaknya harus mengandung satu angka',
  isContainSpecialCharacter: '{attribute} setidaknya harus mengandung satu simbol',
  isContainUpperCase: '{attribute} setidaknya harus mengandung satu huruf besar',
  isNotSameAsNewPassword: 'Kata Sandi Baru tidak boleh sama dengan Kata Sandi Saat Ini',
  lessThanMax: '{attribute} harus kurang dari {max}',
  maxLength: 'Panjang maksimum {attribute} adalah {max} karakter',
  maxValue: 'Nilai maksimum dari {attribute} adalah {max}',
  minLength: 'Panjang minimum {attribute} adalah {min} karakter',
  minValue: 'Nilai minimum dari {attribute} adalah {min}',
  moreThanMin: '{attribute} harus lebih dari {min}',
  numeric: 'Kolom {attribute} harus berupa angka',
  password: 'Kata sandi harus terdiri dari alfabet, angka, dan simbol (ex: Mypassword!@44)',
  phone: 'Format nomor telepon pada kolom {attribute} tidak valid',
  phoneNumber: 'Format nomor telepon tidak valid',
  required: 'Kolom {attribute} wajib diisi',
  sameAs: 'Massukan {attribute} Anda sesuai dengan Sandi baru yang telah Anda buat',
  sameAsCreatedPassword: 'Massukan {attribute} Anda sesuai dengan Kata Sandi yang telah Anda buat',
  sameAsPassword: 'Re-enter New Password does not match New Password',
  url: '{attribute} field contains an invalid url',
  username: '{attribute} hanya mengizinkan alfabet, angka',
  invalid_full_name: 'Kolom {attribute} hanya boleh huruf',
  invalid_phone_number: '{attribute} tidak valid',
  invalid_npwp_digits: 'Kolom {attribute} harus 16 digit',
  invalid_ktp_digits: 'Kolom {attribute} harus 16 digit angka',
  noAngleBrackets: 'Kolom {attribute} tidak boleh mengandung karakter < atau >',
};
