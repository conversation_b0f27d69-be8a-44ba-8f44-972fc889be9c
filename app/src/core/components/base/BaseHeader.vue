<script setup lang="ts">
// Components
import BaseContainer from './BaseContainer.vue';

/**
 * Props
 */
defineProps<{ pageTitle?: string; pageSubTitle?: string }>();
</script>

<template>
  <header class="flex flex-col w-full mt-4">
    <BaseContainer class="mb-5">
      <BaseBreadcrumb />
      <h1 class="font-bold font-druk text-4xl leading-12 mt-3">
        {{ pageTitle }}
      </h1>
      <p class="text-[#686F72]">{{ pageSubTitle }}</p>
    </BaseContainer>
  </header>
</template>
