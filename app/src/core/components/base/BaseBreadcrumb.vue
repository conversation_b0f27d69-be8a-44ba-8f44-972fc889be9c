<script setup lang="ts">
const route = useRoute();

// refs
const breadcrumbs = computed<IBreadcrumb[]>(() => {
  return (
    route.meta.breadcrumbs?.map(item => ({
      label: item.label,
      url: item.path ?? null,
    })) ?? []
  );
});
</script>

<template>
  <section id="base-breadcrumb" class="relative inset-0 z-0">
    <PrimeVueBreadcrumb
      :model="breadcrumbs"
      class="!p-0"
      :pt="{
        itemLink: 'text-[12px] !text-[#686F72]',
      }"
    >
      <template #separator> / </template>
    </PrimeVueBreadcrumb>
  </section>
</template>
