<script setup lang="ts">
import Button from 'primevue/button';

const year = new Date().getFullYear();

interface MenuItem {
  label: string;
  path: string;
}

const footerLinks = ref<MenuItem[]>([
  {
    label: 'Info Perusahaan',
    path: '/',
  },
  {
    label: 'EIGER Adventure',
    path: '/',
  },
  {
    label: 'Sponsorship',
    path: '/',
  },
  {
    label: '<PERSON>rir',
    path: '/',
  },
  {
    label: 'Lokasi EIGERINDO MPI',
    path: '/',
  },
  {
    label: 'Sustainability',
    path: '/',
  },
]);

const socialLinks = [
  {
    icon: '/icons/mdi_instagram.svg',
    url: 'https://www.instagram.com/eigeradventure',
  },
  {
    icon: '/icons/ic_outline-tiktok.svg',
    url: 'https://www.tiktok.com/@eigeradventure',
  },
  {
    icon: '/icons/ic_outline-facebook.svg',
    url: 'https://www.facebook.com/eigeradventure/',
  },
  {
    icon: '/icons/mdi_twitter.svg',
    url: 'https://x.com/eigeradventure',
  },
];

const onClickSocialLink = (url: string) => {
  window.open(url, '_blank');
};
</script>

<template>
  <footer class="relative inset-0 z-0 border-t-4 border-solid border-header-orange pt-15">
    <BaseContainer>
      <section id="contents" class="grid sm:grid-cols-[60fr_20fr_20fr] grid-cols-1 sm:p-14 p-4 sm:gap-y-0 gap-y-5">
        <section class="flex flex-col gap-4">
          <NuxtLink to="/">
            <NuxtImg src="/images/app-logo-black.png" alt="app-logo" class="w-[150px] mb-6" />
          </NuxtLink>
          <span class="font-medium">Media Social </span>
          <section class="-ml-[10px]">
            <Button
              v-for="item in socialLinks"
              :key="item.url"
              variant="link"
              class="!rounded-full"
              @click="() => onClickSocialLink(item.url)"
            >
              <template #icon>
                <NuxtImg :src="item.icon" :alt="item.url" class="w-6 h-6" />
              </template>
            </Button>
          </section>
        </section>
        <section class="flex">
          <section id="help-menus" class="flex flex-col gap-2">
            <h6 class="font-bold text-base text-black">Eigerindo MPI</h6>
            <NuxtLink
              v-for="(item, index) in footerLinks"
              :key="String(index)"
              :to="item.path"
              class="text-muted !text-sm"
            >
              {{ item.label }}
            </NuxtLink>
          </section>
        </section>
        <section class="flex sm:flex-row flex-col min-w-[320px] gap-2">
          <NuxtImg src="/icons/customer-support-icon.svg" alt="phone" class="w-[40px] h-[40px]" />
          <section class="flex flex-col gap-1.5">
            <h6 class="font-bold text-base text-black">Hubungi Kami</h6>
            <p class="text-sm flex gap-1 underline">
              <img src="/icons/whatsapp-black.svg" alt="whatsapp" />
              08112311632
            </p>
            <p class="text-sm flex gap-1">
              <img src="/icons/whatsapp-black.svg" alt="whatsapp" />
              <span class="underline">0811200002588</span> (Whatsapp B2B)
            </p>
            <p class="text-sm flex gap-1">
              <img src="/icons/mail-black.svg" alt="email" />
              <span class="underline"><EMAIL></span>
            </p>
            <p class="text-sm">Setiap Hari 7/24</p>
            <p class="text-sm">Setiap Hari | 08.00 - 22.00 (Social Media)</p>
          </section>
        </section>
      </section>

      <section
        id="copyright"
        class="flex justify-center border-t border-solid border-[#E5E6E8] py-3 w-full lg:w-[1192px] mx-auto sm:px-0 px-4"
      >
        <p class="text-xs text-gray-copyright">
          Copyright © {{ year }} Eigerindo Multi Produk Industri, Inc. All rights reserved.
        </p>
      </section>
    </BaseContainer>
  </footer>
</template>
