<script setup lang="ts">
/**
 * @description Define the props interface
 */
interface IProps {
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  modelValue?: string;
}

/**
 * @description Define props with default values and interfaces
 */
const props = withDefaults(defineProps<IProps>(), {
  placeholder: '',
  disabled: false,
  readonly: false,
  modelValue: '',
});

/**
 * @description Define event emitters
 */
const emit = defineEmits(['update:modelValue']);

/**
 * @description Reactive data binding
 */
const type = ref('password');
const inputRef = ref<HTMLInputElement | null>(null);

/**
 * @description Handle business logic for event input
 */
const onInput = (event: Event): void => {
  const target = event.target as HTMLInputElement;
  emit('update:modelValue', target.value);
};

/**
 * @description Handle business logic for toggling password visibility
 */
const togglePasswordVisibility = (): void => {
  type.value = type.value === 'password' ? 'text' : 'password';
};
</script>

<template>
  <div class="input-password">
    <label class="input-password__label">
      <input
        ref="inputRef"
        class="input-password__input px-4 py-3 rounded-lg placeholder:text-muted"
        :type="type"
        :placeholder="props.placeholder"
        :disabled="props.disabled"
        :readonly="props.readonly"
        :value="modelValue"
        autocomplete="new-password"
        @input="onInput"
      />
      <button
        type="button"
        class="input-password__toggle"
        aria-label="Toggle password visibility"
        @click="togglePasswordVisibility"
      >
        <NuxtImg v-if="type === 'password'" src="/icons/eye-slash.svg" alt="Hide Password" />

        <NuxtImg v-else src="/icons/eye.svg" alt="Show Password" />
      </button>
    </label>
  </div>
</template>

<style scoped>
.input-password {
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 8px;
}

.input-password__label {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #e5e6e8;
  border-radius: 8px;
  padding: 0 !important;
}

.input-password__input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 12px 12px 12px;
  font-size: 1rem;
  border-radius: 8px;
}

.input-password__input:disabled {
  background-color: #f5f5f5;
}

.input-password__toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  padding-right: 8px;
  margin-left: 0.5rem;
}

.input-password__toggle img {
  width: 20px;
  height: 20px;
}

.input-password__toggle:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
</style>
