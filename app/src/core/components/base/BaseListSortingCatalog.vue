
<script setup lang="ts">
import Select from 'primevue/select';
import { ref,defineProps,defineEmits } from "vue";


type ISortingCatalog = { name : string, code: string};

interface IProps  {
    options?: ISortingCatalog[]
    selected: string;
}

const props = defineProps<IProps>();
const emit = defineEmits(['update:selected']);

const selectedValue = ref(props.selected);

watch(() => props.selected, (newValue) => {
    console.log(newValue)
    selectedValue.value = newValue;
});

watch(selectedValue, (newValue) => {
    emit('update:selected', newValue);
});
</script>

<template>
    <div class="card flex justify-center">
        <Select v-model="selectedValue" :options="props.options" option-label="name" placeholder="Terbaru" class="" />
    </div>
</template>

<style scoped>
:deep(.p-select) {
    border-color: #ACB1B4;
    border-radius:10px;
}

:deep(.p-select:not(.p-disabled).p-focus),
:deep(.p-select:not(.p-disabled).p-dropdown-open) {
    border-color: #ACB1B4 !important;
}

:deep(.p-select .p-dropdown-label) {
    color: #ACB1B4 !important;
}

:deep(.p-select-label.p-placeholder) {
    color:black;
}
</style>
