<script setup lang="ts">
import { ref, defineProps,defineEmits } from 'vue';

type SortingList = { name: string; code: string;};

interface IProps {
    options?:  SortingList[];
    selected: string | null;
    placeholder?: string;
    disable: boolean;
}
const props = defineProps<IProps>();
const emit = defineEmits(['update:selected']);

const selectedValue = ref(props.selected);

const handleSelectListBox = (event: { code: string | null; })  => {
    selectedValue.value = event.code
}

watch(selectedValue, (newValue) => {
    emit('update:selected', newValue);
});

</script>

<template>
    <div class="card flex justify-center">
        <PrimeVueListBox
         v-model="selectedValue" 
         :options="props.options" 
         option-label="name" 
         class="w-full !border-white" 
         :pt="{
             root:{
                 class:'border-red-500'
                }
            }"
        @click="handleSelectListBox(selectedValue)"
         />
    </div>
</template>

