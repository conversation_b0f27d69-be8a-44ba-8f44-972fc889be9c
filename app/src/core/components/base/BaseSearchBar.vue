<script lang="ts" setup>

/**
 * @description Define props interface
 */
interface IPSearchBarProps {
  placeholder?: string;
  handleSearch: ( q: string) => void;
}

const props = defineProps<IPSearchBarProps>();

const searchQuery = ref('')


const handleKeyUp = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    props.handleSearch(searchQuery.value)
  }
}
</script>

<template>
  <section id="search-bar" class="flex flex-row w-fit justify-between items-center">
    <section
      class="flex items-center border border-gray-300 has-[input:focus-within]:outline-1 has-[input:focus-within]:-outline-offset-1 has-[input:focus-within]:outline-black"
    >
      <section id="icon-search" class="w-10 justify-items-center">
        <NuxtImg src="/icons/magnifying-glass.svg" alt="search-icon" class="w-5 h-5" />
      </section>
      <input
        v-model="searchQuery"
        :placeholder="placeholder"
        type="text"
        class="outline-none py-2 px-1"
        @keyup="handleKeyUp"
      />
    </section>
    <PrimeVueButton class="!text-white !border !border-black !bg-[#18191A] !py-2 !px-3 !rounded-none" @click="handleSearch(searchQuery)">
      Cari
    </PrimeVueButton>
  </section>
</template>

<style scoped>
</style>
