<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed, useAttrs } from 'vue';
import Select from 'primevue/select';

const attrs = useAttrs();

type Provinces = { name: string; code: string; island?: string };
type Cities = { name: string; id: string; code: string };
type Subdistrict = { name: string; code: string; postal_code: string };
type SortingList = { name: string; code: string };

interface IProps {
  options?: string[] | Provinces[] | Cities[] | Subdistrict[] | SortingList[];
  selected: string | Provinces | Cities | Subdistrict | SortingList | null;
  placeholder?: string;
  disable: boolean;
  sorting?: boolean;
  padding?: string;
  border?: string;
  radius?: string;
  isPostalCode?: boolean;
  iconSorting?: boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(['update:selected']);

const defaultPadding = '.5em 0em';
const defaultBorder = '#E5E6E8';
const defaultRadius = '10px';

const selectedValue = ref(props.selected);
const optionsList = ref(JSON.parse(JSON.stringify(props.options)));

watch(
  () => props.selected,
  newValue => {
    selectedValue.value = newValue;
  },
);

watch(
  () => props.options,
  newValue => {
    optionsList.value = newValue;
  },
);

if (props.sorting) {
  watch(selectedValue, newValue => {
    const emitValue = newValue && typeof newValue === 'object' && 'code' in newValue ? newValue.code : newValue;
    emit('update:selected', emitValue);
  });
} else {
  watch(selectedValue, newValue => {
    emit('update:selected', newValue);
  });
}

const isStringOptions = computed(() => {
  return props.options && props.options.length > 0 && typeof props.options[0] === 'string';
});

const optionLabel = computed(() => {
  if (isStringOptions.value) return undefined;
  if (props.isPostalCode) return 'postal_code';
  return 'name';
});

const autoPlaceholder = computed(() => {
  if (props.placeholder) return props.placeholder;
  if (!props.options || props.options.length === 0) return 'Silahkan Pilih';

  const first = props.options[0];

  if (typeof first === 'string') return 'Silahkan Pilih';
  if ('island' in first) return 'Pilih Provinsi';
  if ('id' in first && 'code' in first) return 'Pilih Kota/Kabupaten';
  if ('postal_code' in first) return 'Pilih Kecamatan';
  if ('code' in first && !('id' in first)) return 'Pilih Sorting';

  return 'Silahkan Pilih';
});

// Fix for the first error - create a function to safely check class
// const hasInvalidClass = computed(() => {
//   if (typeof attrs.class === 'string') {
//     return attrs.class.includes('border-red-600');
//   }
//   return false;
// });
</script>

<template>
  <span class="input-select__label rounded-xl border-0 !border-transparent">
    <Select
      v-model="selectedValue"
      :options="optionsList"
      :option-label="optionLabel"
      :placeholder="props.placeholder"
      :disabled="props.disable || !props.options || props.options.length === 0"
      :style="{
        padding: props.padding || defaultPadding,
        borderColor: props.border || defaultBorder,
        borderRadius: props.radius || defaultRadius,
      }"
      class="w-full"
      :class="[attrs.class, { 'p-invalid': hasInvalidClass }]"
    >
      <template #dropdownicon>
        <template v-if="iconSorting">
          <NuxtImg src="/icons/sorting.svg" />
        </template>
        <template v-else>
          <NuxtImg src="/icons/chevron-bottom.svg" />
        </template>
      </template>

      <template #option="slotProps">
        <div>
          <span v-if="typeof slotProps.option === 'string'">
            {{ slotProps.option }}
          </span>
          <span v-else-if="props.isPostalCode">
            {{ slotProps.option.postal_code }} - {{ slotProps.option.name }}
          </span>
          <span v-else>
            {{ slotProps.option.name }}
          </span>
        </div>
      </template>

      <template #value="slotProps">
        <div
          v-if="
            slotProps.value &&
            !(typeof slotProps.value === 'object' && Object.values(slotProps.value).every(v => v === ''))
          "
        >
          <span v-if="typeof slotProps.value === 'string'">
            {{ slotProps.value }}
          </span>
          <span v-else-if="props.isPostalCode">
            {{ slotProps.value.postal_code }} - {{ slotProps.value.name }}
          </span>
          <span v-else>
            {{ slotProps.value.name || slotProps.value }}
          </span>
        </div>
        <span v-else class="text-gray-400">
          {{ autoPlaceholder }}
        </span>
      </template>
    </Select>
  </span>
</template>

<style scoped>
:deep(.p-select) {
  padding: 0.5em 0em;
  border-radius: 10px;
}

:deep(.p-select:not(.p-disabled).p-focus),
:deep(.p-select:not(.p-disabled).p-dropdown-open) {
  border-color: #e5e6e8 !important;
}

:deep(.p-select .p-dropdown-label) {
  color: #e5e6e8 !important;
}

:deep(.p-select-label.p-placeholder) {
  color: #676f72;
}

/* Hide the default dropdown arrow */
:deep(.p-select .p-dropdown-trigger-icon) {
  display: none;
}

/* Style for the default V icon */
:deep(.default-icon) {
  color: #676f72;
  font-size: 12px;
  font-weight: bold;
  transform: scaleX(0.7);
}

/* Rotate icon when sorting is active */
:deep(.icon-rotated) {
  transform: rotate(180deg);
}

/* Position the custom icon properly in the trigger area */
:deep(.p-dropdown-trigger) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Add rotation transition for NuxtImg */
:deep(img) {
  transition: transform 0.3s ease;
}

.p-select.p-inputwrapper {
  padding: 0.3em 0em !important;
}
</style>
