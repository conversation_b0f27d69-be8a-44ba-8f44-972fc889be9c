<script setup lang="ts">
// Enums
import { EAlertType } from '../../enums/base-alert.enum';

/**
 * @description Injecting dependencies
 */
const { $bus } = useNuxtApp();

/**
 * @description Reactive data binding
 */
const alertOptions = ref<IPropsBaseAlert>({
  isOpen: false,
  text: '',
  type: EAlertType.INFO,
  isHaveIconClose: false,
  durationOfAutoClose: 0,
});

/**
 * @description Handle business logic for ending the transition
 */
const endTransition = (el: Element) => {
  const element = el as HTMLElement;
  element.style.height = '';
};

/**
 * @description Handle business logic for closing the alert
 */
const onClose = (): void => {
  alertOptions.value.isOpen = false;
};

/**
 * @description Handle business logic for auto closing the alert
 */
const onStartAutoClose = (): void => {
  if (alertOptions.value.durationOfAutoClose > 0) {
    setTimeout(() => {
      onClose();
    }, alertOptions.value.durationOfAutoClose);
  }
};

/**
 * @description Handle business logic for starting the transition
 */
const startTransition = (el: Element) => {
  const element = el as HTMLElement;
  element.style.height = element.scrollHeight + 'px';
};

onMounted(() => {
  onStartAutoClose();
});

$bus.on('BaseAlert', (params: unknown) => {
  alertOptions.value = params as IPropsBaseAlert;
  onStartAutoClose();
});
</script>

<template>
  <section v-if="alertOptions.isOpen" id="base-alert" class="mx-auto my-3">
    <transition
      name="slide-down"
      @enter="startTransition"
      @after-enter="endTransition"
      @before-leave="startTransition"
      @after-leave="endTransition"
    >
      <div v-if="alertOptions.isOpen" class="alert " :class="alertOptions.type">
        <section id="content" class="flex items-center gap-2 text-base font-normal leading-[24px]">
          <template v-if="alertOptions.type === EAlertType.DANGER">
            <NuxtImg src="/icons/warning-danger.svg" alt="warning-icon" class="w-4 h-4" />
          </template>

          <template v-else-if="alertOptions.type === EAlertType.INFO">
            <NuxtImg src="/icons/mail-info.svg" alt="info-icon" class="w-4 h-4" />
          </template>

          {{ alertOptions.text }}
        </section>
      </div>
    </transition>
  </section>
</template>

<style scoped>
.alert {
  inset: 0;
  bottom: 76px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 100px;
  overflow: hidden;
  text-align: center;
}

.success {
  background-color: #43a047;
  color: white;
}

.info {
  color: #18191A;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}

.danger {
  background-color: #ffe0e1;
  color: #18191a;
}

.slide-down-enter-active,
.slide-down-leave-active {
  will-change: height, opacity;
  transition: height 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
}

.slide-down-enter,
.slide-down-leave-to {
  height: 0 !important;
  opacity: 0;
}
</style>
