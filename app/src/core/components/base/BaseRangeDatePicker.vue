<script setup lang="ts">

interface IBaseRangeDatePickerProps {
  onChange: (dateFrom: string | null, dateTo: string | null) => void;
  onReset: () => void;
  placeholder?: string;
  isMobile?: boolean;
}

/**
 * @description init props filter date picker
 */
const props = withDefaults(defineProps<IBaseRangeDatePickerProps>(), {
  placeholder: 'dd / mm / yyyy',
  isMobile: false,
});

const date = ref();
const selectedDate = ref();
const isDropdownOpen = ref(false);
const width = ref(window.innerWidth);


const listSelect = ref([
  { name: '<PERSON>', code: '1' },
  { name: '7 <PERSON>', code: '7' },
  { name: '30 Hari Te<PERSON>hir', code: '30' },
  { name: '60 Hari Terakhir', code: '60' },
  { name: '90 Hari Terakhir', code: '90' },
]);

/**
 * @description Function to handle dropdown
 */
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};

/**
 * @description handle effect dropdown
 */
const handleClickOutside = (event: MouseEvent) => {
  const dropdown = document.getElementById('dropdown-menu');
  const button = document.getElementById('menu-button');
  if (dropdown && button && !dropdown.contains(event.target as Node) && !button.contains(event.target as Node)) {
    isDropdownOpen.value = false;
  }
};

/**
 * @description Function to calculate date range
 */
const setDateRange = (days: number) => {
  const today = new Date();
  const pastDate = new Date();
  pastDate.setDate(today.getDate() - days);
  date.value = [pastDate, today];
};

/**
 * @description Watch for changes in the selectedDate
 */
const handleDateSelection = () => {
  if (selectedDate.value?.code === '7') {
    setDateRange(7);
  } else if (selectedDate.value?.code === '1') {
    setDateRange(0);
  } else if (selectedDate.value?.code === '30') {
    setDateRange(30);
  } else if (selectedDate.value?.code === '60') {
    setDateRange(60);
  } else if (selectedDate.value?.code === '90') {
    setDateRange(90);
  }
};

/**
 * @description Handle reset for filter
 */
const handleReset = () => {
  date.value = undefined;
  selectedDate.value = undefined;
  props?.onReset();
};

const handleResize = () => {
  width.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="relative inline-block text-right w-full z-auto">
    <div class="h-full sm:w-[320px] w-fit">
      <button
        id="menu-button"
        type="button"
        class="inline-flex sm:w-full w-fit items-center justify-between rounded-md bg-white sm:px-3 sm:py-2 px-0 py-0 text-md font-light ring-1 sm:shadow-xs sm:ring-[#E5E6E8] ring-transparent ring-inset hover:bg-gray-50 h-[45px]"
        aria-expanded="true"
        aria-haspopup="true"
        @click="toggleDropdown"
      >
        <div  class="flex items-center gap-x-3">
          <NuxtImg src="/icons/calendar.svg" alt="upload-icon" class="sm:w-[16px] sm:h-[16px] w-[20px] h-[20px]" />
          <div v-if="width > 640">
            <section v-if="date" class="text-black flex flex-row">
              <p v-if="date?.[0]" class="text-sm">
                {{ useDateFormat(date[0], 'DD/MM/YYYY') }}
              </p>
              <p v-if="date?.[1]" class="text-sm">-{{ useDateFormat(date[1], 'DD/MM/YYYY') }}</p>
            </section>
            <section v-else class="text-[#686F72]">{{ placeholder }}</section>
          </div>
        </div>
        <svg
          class="-mr-1 size-5 text-[#686F72] sm:block hidden"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
          data-slot="icon"
        >
          <path
            fill-rule="evenodd"
            d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>

    <div
      v-if="isDropdownOpen"
      id="dropdown-menu"
      class="absolute right-0 z-50 mt-1 w-[320px] sm:w-[320px] md:w-[850px] origin-top-right shadow-lg focus:outline-hidden bg-white rounded-md border overflow-hidden border-gray-300 pb-4 pr-4"
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="menu-button"
      tabindex="-1"
    >
      <div class="" role="none">
        <section id="date-picker" class="flex flex-col-reverse md:flex-row" role="none">
          <section class="flex flex-col justify-between relative w-full md:w-auto">
            <section class="hidden md:block">
              <PrimeVueListbox
                v-model="selectedDate" 
                :options="listSelect"
                option-label="name"
                class="w-full md:w-56 !border-white !rounded-tr-none !rounded-br-none !shadow-none"
                @update:model-value="handleDateSelection"
              />
            </section>
          </section>
          <PrimeVueDatePicker
            v-model="date"
            inline
            class="!w-full !border-white"
            selection-mode="range"
            :number-of-months="2"
            :pt="{
              panel: '!border-white !rounded-tl-none !rounded-bl-none',
            }"
          />
        </section>
      </div>
      <section class="gap-y-2 flex flex-row gap-4 w-full justify-end sm:pr-3 sm:pb-3 pr-0 pb-0 py-2">
        <PrimeVueButton
          outlined
          class="!bg-white!border-white !text-black !border !border-black !ring-0 !px-4 !text-[14.04px] min-w-[142px]"
          @click="
            handleReset();
            isDropdownOpen = false;
          "
        >
          Reset
        </PrimeVueButton>
        <PrimeVueButton
          class="!bg-black !text-white !border-black !px-4 !text-[14.04px] min-w-[142px]"
          @click="
            onChange(
              date?.[0] ? useDateFormat(date[0], 'DD-MM-YYYY').value : null,
              date?.[1] ? useDateFormat(date[1], 'DD-MM-YYYY').value : null,
            );
            isDropdownOpen = false;
          "
        >
          Apply
        </PrimeVueButton>
      </section>
    </div>
  </div>
</template>

<style lang="css" scoped>
:deep(.p-listbox-option.p-listbox-option-selected) {
  color: #ff5a00 !important;
  background-color: #ffedd3 !important;
}
:deep(.p-datepicker-day-selected) {
  color: #fff !important;
  background-color: #ff5a00 !important;
}
:deep(.p-datepicker-day-selected-range) {
  color: #000000 !important;
  background-color: #ffedd3 !important;
}
:deep(.p-listbox-option),
:deep(.p-datepicker-day) {
  font-size: 0.9rem !important;
  font-weight: 500;
}
</style>
