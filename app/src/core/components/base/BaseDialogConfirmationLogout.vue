<script setup lang="ts">
/**
 * @description Injecting dependencies
 */
const {
  navbar_isOpenDialogConfirmationLogout,
  navbar_onCloseDialogConfirmationLogout,
  navbar_onLogout,
  navbar_logoutIsLoading,
} = inject<INavbarProvided>('main')!;
</script>

<template>
  <PrimeVueDialog
    v-model:visible="navbar_isOpenDialogConfirmationLogout"
    modal
    class="w-full h-fit max-w-sm"
    :draggable="false"
    :dismissable-mask="true"
  >
    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold font-druk text-3xl text-black">Log Out?</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col gap-4">
        <span class="text-base text-black">Apakah kamu yakin untuk keluar da<PERSON> B2B CARE OM?</span>

        <section id="btn-actions" class="flex items-center justify-end gap-4">
          <PrimeVueButton
            label="Cancel"
            type="button"
            class="!bg-transparent !border-none !text-black !text-base !font-druk !text-center font-medium !uppercase !w-fit !px-0"
            @click="navbar_onCloseDialogConfirmationLogout"
          />

          <PrimeVueButton
            type="button"
            label="Log Out"
            size="large"
            class="!border-none !text-base !font-druk !text-center font-medium !uppercase !rounded-lg !w-fit"
            :class="
              navbar_logoutIsLoading
                ? '!bg-[#9f9f9f] !border-[#9f9f9f] !text-black'
                : '!border-black !bg-black !text-white'
            "
            :disabled="navbar_logoutIsLoading"
            @click="navbar_onLogout"
          />
        </section>
      </section>
    </template>
  </PrimeVueDialog>
</template>
