<script setup lang="ts">
import { capitalizeFirstLetter } from '../../helpers/text.helper';

interface Props {
  product: IProductList;
}
const props = defineProps<Props>();

const { navbar_isOpenDrawer } = inject<INavbarProvided>('main')!;

const onClickDetail = () => {
  window.location.href = `/product/detail/${props.product.sku}`;
  navbar_isOpenDrawer.value = false;
};

// const outOffStock = computed(() => props.product.stock === 0);
const outOffStock = computed(() => false);
</script>

<template>
  <section id="drawer-product" class="grid grid-cols-[80px_1fr] gap-3">
    <div class="relative overflow-hidden rounded-lg border border-[#F0F2F4] h-[80px] w-[80px]">
      <NuxtImg
        :src="product.image"
        alt="product-thumbnail"
        class="w-full h-full max-w-20 max-h-20 rounded-sm cursor-pointer"
        @click="onClickDetail"
      />
      <div
        v-if="outOffStock"
        class="absolute bg-[#18191A] text-[#fbfbfb] rounded-b-lg opacity-70 bottom-0 left-0 py-1 px-1 w-full flex items-center justify-center"
      >
        <span class="text-[9px] font-medium">STOK TIDAK ADA</span>
      </div>
    </div>
    <section id="content" class="flex flex-col justify-between py-1">
      <header class="flex flex-col gap-1 cursor-pointer" @click="onClickDetail">
        <div class="w-[calc(100%-10px)] h-[22px] truncate">
          <h6 class="font-medium text-base text-black truncate">{{ capitalizeFirstLetter(product?.name) }}</h6>
        </div>
        <span class="text-xs text-muted"> SKU {{ product?.sku ?? '-' }} </span>
      </header>

      <section id="price" @click="onClickDetail">
        <p v-if="product.price" class="font-bold text-sm text-black">
          {{ useCurrencyFormat(Number(product.price)) }}
        </p>
      </section>
    </section>
  </section>
</template>
