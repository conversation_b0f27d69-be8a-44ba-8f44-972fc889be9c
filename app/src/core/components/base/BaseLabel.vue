<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script lang="ts" setup>
interface IBaseLabel {
  text: string;
  color?: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger';
  onClick?: () => void;
}

const props = defineProps<IBaseLabel>();

const getSeverity = (label: string) => {
  switch (props?.text?.trim()?.toLocaleLowerCase()) {
    case 'baru':
      return 'warning';
    case 'pembayaran':
    case 'lunas':
      return 'success';
    case 'belum dibayar':
      return 'danger';
    default:
      return props?.color || 'primary';
  }
};
const providedColor = computed<string>(() => {
  return getSeverity(props.text);
});

const emit = defineEmits<{
  (e: 'click'): void;
}>();

const handleClick = () => {
  emit('click');
};
</script>

<template>
  <div
    class="border inline-flex rounded-full px-3 py-1.5 leading-none text-sm font-medium"
    :class="[
      !providedColor && 'text-[#FF5A00] !border-[#F59E0B] bg-[#FFEDD3]',
      providedColor === 'warning' && 'text-[#FF5A00] !border-[#F59E0B] bg-[#FFEDD3]',
      providedColor === 'success' && 'text-[#05964C] !border-[#05964C] bg-[#DFF0D8]',
      providedColor === 'danger' && 'text-[#E9151D] !border-[#E9151D] bg-[#F8D7DA]',
      (providedColor === 'info' || providedColor === 'primary') && 'text-[#1966F7] !border-[#147FFF] bg-[#D1ECF1]',
    ]"
    @click="handleClick"
  >
    {{ text }}
  </div>
</template>

<style scoped></style>
