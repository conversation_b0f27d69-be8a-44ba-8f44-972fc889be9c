<script setup lang="ts"></script>

<template>
  <PrimeVueDialog
    v-model:visible="isOpenDialogConfirmConfirmOrder"
    modal
    class="h-fit w-full md:w-[532px]"
    :pt="{
      header: '!py-2 border-b border-b-[#E5E6E8]',
      content: '!py-4',
      footer: '!py-3 border-t border-t-[#E5E6E8]',
    }"
    :draggable="false"
  >
    <template #closeicon>
      <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
    </template>

    <template #header>
      <header class="flex items-center justify-between">
        <h4 class="font-bold text-[19px] text-black">Konfirmasi Pesanan</h4>
      </header>
    </template>

    <template #default>
      <section id="content" class="flex flex-col gap-4">
        <span class="text-base text-black"
          >Pastikan barang yang ingin kamu pesan telah sesuai sebelum memproses pesanan
        </span>
      </section>
    </template>
    <template #footer>
      <section id="btn-actions" class="flex items-center justify-end gap-4">
        <PrimeVueButton
          label="Batal"
          type="button"
          class="!bg-transparent !border-[#ACB1B4] !text-sm !text-black !text-center font-medium !w-fit !px-4"
          @click="isOpenDialogConfirmConfirmOrder = false"
        />
        <PrimeVueButton
          type="button"
          label="Konfirmai Pesanan"
          size="large"
          class="!bg-black !border-none text-white !text-sm !text-center font-medium !rounded-lg !w-fit !px-5"
          @click="handleConfirmOrder"
        />
      </section>
    </template>
  </PrimeVueDialog>
</template>
