<script lang="ts" setup>
/**
 * @description Define props interface
 */
interface BaseEmptyStateProps {
  title?: string;
  subtitle?: string;
  image?: string;
}
withDefaults(defineProps<BaseEmptyStateProps>(), {
  image: '/images/undraw_export_files_re_99ar.png',
  title: '',
  subtitle: '',
});
</script>

<template>
  <section class="empty-state py-6" aria-busy="true" aria-label="Base empty state">
    <div class="h-full w-full flex flex-col items-center justify-center">
      <div v-if="image" class="text-center mb-6 md:mb-10 w-[270px] h-full mx-auto">
        <img class="base-error-img w-full" :src="image" alt="empty" />
      </div>

      <h3 class="text-[#686F72] text-base">
        {{ title }}
      </h3>

      <p class="empty-text text-sm">
        {{ subtitle }}
      </p>

      <div class="mx-auto text-center">
        <slot name="footer"></slot>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped></style>
