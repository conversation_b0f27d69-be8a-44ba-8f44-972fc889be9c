<script setup lang="ts">
// Constants
import { VALIDATION_MESSAGE } from '../../constants/validations.constant';

// Helpers
import { replaceParams } from '../../helpers/replace-parameters.helper';

// Interfaces
import type { BaseValidation, ErrorObject } from '@vuelidate/core';

/**
 * @description Define the props interface
 */
interface IProps {
  classLabel?: string;
  isHideErrorMessage?: boolean;
  isNameAsLabel?: boolean;
  isNameAsPlaceholder?: boolean;
  isNotHaveSpacing?: boolean;
  labelFor?: string;
  name: string;
  spacingBottom?: string;
  validators: BaseValidation;
  isMandatory?: boolean;
  isSuccessMessage?: string;
  isErrorMessage?: string;
  hideLabel?: boolean;
}

/**
 * @description Define props with default values and interfaces
 */
const props = withDefaults(defineProps<IProps>(), {
  classLabel: '',
  isHideErrorMessage: false,
  isNameAsLabel: false,
  isNameAsPlaceholder: false,
  isNotHaveSpacing: false,
  labelFor: '',
  name: '',
  spacingBottom: 'mb-4',
  validators: undefined,
  isMandatory: false,
  isSuccessMessage: '',
  isErrorMessage: '',
});

/**
 * @description Check if the form is invalid
 */
const isInvalid = computed(() => props.validators.$dirty && props.validators.$invalid);

/**
 * @description Check the error message and retrieve the first error
 */
const error: ComputedRef<ErrorObject | null> = computed(() => {
  if (!props.validators && (props.validators as BaseValidation)?.$errors.length === 0) return null;

  return props.validators.$errors[0];
});

/**
 *
 * @description Handle format error message should same as on the consants, Then don't forget to replace special character {attribute} with the value from the form
 */
const message: ComputedRef<string> = computed(() => {
  const errorValidator = error.value ? error?.value?.$validator : null;

  if (!errorValidator) return '';

  return replaceParams(VALIDATION_MESSAGE[errorValidator] ?? `Error : ${errorValidator}`, {
    attribute: props.name,
    ...error?.value?.$params,
  });
});
</script>

<template>
  <div id="form-group" class="w-full" :class="{ [spacingBottom]: !isNotHaveSpacing }">
    <template v-if="!hideLabel">
      <label v-if="isNameAsLabel" :class="classLabel" :for="labelFor">
        {{ name }}
        <span v-if="isMandatory" class="text-[14px] text-[#E9151D]">*</span>
      </label>
    </template>
    <slot
      :is-error="!!error"
      :is-valid="isInvalid"
      :classes="{ '!border !border-solid !border-red-600': !!error, '!p-success': !isInvalid }"
    />
    <div v-if="!isErrorMessage && message" class="flex items-center gap-2 mt-1">
      <NuxtImg src="/icons/info-red.svg" alt="warning-icon" class="w-[14px] h-[14px]" />
      <small class="text-[#E9151D]">{{ message }}</small>
    </div>
    <div v-else-if="isErrorMessage" class="flex items-center gap-2 mt-1">
      <NuxtImg src="/icons/info-red.svg" alt="warning-icon" class="w-[14px] h-[14px]" />
      <small class="text-[#E9151D]">{{ isErrorMessage }}</small>
    </div>
    <div v-else-if="isSuccessMessage && !message" class="flex items-center gap-2 mt-1">
      <NuxtImg src="/icons/check-circle.svg" alt="warning-icon" class="w-[14px] h-[14px]" />
      <small class="text-[#05964C]">{{ isSuccessMessage }}</small>
    </div>
  </div>
</template>
