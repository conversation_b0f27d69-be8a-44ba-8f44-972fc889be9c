<template>
<PrimeVueDialog
        v-model:visible="isOpenModalInquiryForm"
        modal
        header="Header"
        :style="{ width: '50vw' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      >
        <template #closeicon>
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </template>
        <template #header>
          <header class="flex items-center justify-between">
            <h4 class="font-druk font-bold text-[42px] text-black">B2B Inquiry Form</h4>
          </header>
        </template>
        <template #default>
          <section id="content" class="flex flex-col">
            <div>
              <BaseFormGroup
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Tipe Kustomer"
                :validators="order_formValidations.type"
              >
                <div class="flex flex-wrap gap-10">
                  <div class="flex items-center gap-2">
                    <PrimeVueRadioButton
                      v-model="order_formInquiry.type"
                      input-id="Perusahaan"
                      name="type"
                      value="Perusahaan"
                      class="!border-black"
                      :pt="{
                        root: {
                          style: `
                            --p-radiobutton-checked-background: #FFF;
                            --p-radiobutton-checked-border-color: #FF5A00;
                            --p-radiobutton-icon-checked-color: #FF5A00;`
                        }
                      }"
                    />
                    <label for="Perusahaan">Perusahaan</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <PrimeVueRadioButton
                      v-model="order_formInquiry.type"
                      input-id="Individu"
                      name="type"
                      value="Individu"
                      :pt="{
                        root: {
                          style: `
                            --p-radiobutton-checked-background: #FFF;
                            --p-radiobutton-checked-border-color: #FF5A00;
                            --p-radiobutton-icon-checked-color: #FF5A00;`
                        }
                      }"
                    />
                    <label for="Individu">Individu</label>
                  </div>
                </div>
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Full Name"
                :validators="order_formValidations.full_name"
              >
                <PrimeVueInputText
                  id="full_name"
                  v-model="order_formInquiry.full_name"
                  v-bind="{ ...useBindStateForm('masukan nama lengkap') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(order_formValidations, 'full_name')"
                />
              </BaseFormGroup>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Nomor HP"
                :validators="order_formValidations.no_telp"
              >
                <PrimeVueInputGroup>
                  <PrimeVueInputGroupAddon class="!bg-[#F9FAFB]">
                    <p class="text-[#18191A] text-base font-normal">+62</p>
                  </PrimeVueInputGroupAddon>
                  <PrimeVueInputText
                    id="no_telp"
                    v-model="order_formInquiry.no_telp"
                    v-bind="{ ...useBindStateForm('Masukan nomor handphone') }"
                    class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                    :class="{ ...classes }"
                    type="text"
                    inputmode="numeric"
                    v-on="useListenerForm(order_formValidations, 'no_telp')"
                  />
              </PrimeVueInputGroup>
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Nama Perusahaan"
                :validators="order_formValidations.instituion_name"
              >
                <PrimeVueInputText
                  id="instituion_name"
                  v-model="order_formInquiry.instituion_name"
                  v-bind="{ ...useBindStateForm('masukan nama perusahaan') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(order_formValidations, 'instituion_name')"
                />
              </BaseFormGroup>
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Alamat Email"
                :validators="order_formValidations.email"
              >
                <PrimeVueInputText
                  id="email"
                  v-model="order_formInquiry.email"
                  v-bind="{ ...useBindStateForm('masukan alamat email') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  v-on="useListenerForm(order_formValidations, 'email')"
                />
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Alamat NPWP"
                :validators="order_formValidations.npwp_address"
              >
                <PrimeVueTextarea
                  id="npwp_address"
                  v-model="order_formInquiry.npwp_address"
                  v-bind="{ ...useBindStateForm('masukan alamat perusahaan sesuai NPWP') }"
                  class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                  :class="{ ...classes }"
                  type="text"
                  rows="3"
                  v-on="useListenerForm(order_formValidations, 'npwp_address')"
                />
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <BaseFormGroup
                v-slot="{ classes }"
                class-label="font-medium text-sm text-black block mb-2"
                is-name-as-label
                label-for="password"
                name="Topik Pembahasan"
                :validators="order_formValidations.topic"
              >
              <PrimeVueSelect
                v-model="order_formInquiry.topic"
                :options="options"
                option-label="name"
                placeholder="Pilih Informasi Umum"
                class="!w-full !rounded-lg"
                :class="{ ...classes }"
                v-on="useListenerForm(order_formValidations, 'topic')"
              />
              </BaseFormGroup>
            </div>
            <div class="flex justify-between gap-5">
              <PrimeVueTextarea
                id="email"
                v-model="order_formInquiry.question"
                v-bind="{ ...useBindStateForm('Berikan rincian tentang pertanyaan Anda di sini') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                type="text"
                rows="3"
              />
            </div>
          </section>
        </template>
        <template #footer>
          <section id="btn-actions" class="flex items-center justify-end w-full">
            <PrimeVueButton
              type="button"
              label="Submit"
              size="large"
              class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !px-5 !w-full"
              @click="onInquryFormSubmit"
            />
          </section>
        </template>
      </PrimeVueDialog>
    </template>