<script lang="ts" setup>
import { useCustomerSupportService } from '../../services/customerSupport.service';

/**
 * @description Destructure all the data and methods what we need
 */
const { customerSupport_formInquiry, customerSupport_formValidations } = useCustomerSupportService();

const emit = defineEmits(['onSubmit']);

const onSubmitForm = () => {
  customerSupport_formValidations.value.$touch();
  if (customerSupport_formValidations.value.$invalid) return;
  emit('onSubmit');
};

const options = ref([
  { name: 'Informasi Umum', code: 'Informasi Umum' },
  { name: 'Produk & Kustomisasi', code: 'Produk & Kustomisasi' },
  { name: '<PERSON><PERSON>emesanan', code: '<PERSON><PERSON>' },
  { name: 'Pengiriman & Pengantaran', code: 'Pengiriman & Pengantaran' },
  { name: '<PERSON><PERSON><PERSON> & Pengembalian', code: '<PERSON><PERSON><PERSON> & Pengembalian' },
]);
</script>

<template>
  <section id="inquiry-form">
    <section id="content" class="flex flex-col">
      <div>
        <BaseFormGroup
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Tipe Kustomer"
          :validators="customerSupport_formValidations.type"
        >
          <div class="flex flex-wrap gap-10">
            <div class="flex items-center gap-2">
              <PrimeVueRadioButton
                v-model="customerSupport_formInquiry.type"
                input-id="Perusahaan"
                name="type"
                value="Perusahaan"
                class="!border-black"
                :pt="{
                  root: {
                    style: `
                          --p-radiobutton-checked-background: #FFF;
                          --p-radiobutton-checked-border-color: #FF5A00;
                          --p-radiobutton-icon-checked-color: #FF5A00;`,
                  },
                }"
              />
              <label for="Perusahaan">Perusahaan</label>
            </div>
            <div class="flex items-center gap-2">
              <PrimeVueRadioButton
                v-model="customerSupport_formInquiry.type"
                input-id="Individu"
                name="type"
                value="Individu"
                :pt="{
                  root: {
                    style: `
                          --p-radiobutton-checked-background: #FFF;
                          --p-radiobutton-checked-border-color: #FF5A00;
                          --p-radiobutton-icon-checked-color: #FF5A00;`,
                  },
                }"
              />
              <label for="Individu">Individu</label>
            </div>
          </div>
        </BaseFormGroup>
      </div>
      <div class="flex w-full justify-between">
        <div class="w-full grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-5">
          <BaseFormGroup
            v-slot="{ classes }"
            is-mandatory
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="full_name"
            name="Nama Lengkap"
            :validators="customerSupport_formValidations.full_name"
          >
            <PrimeVueInputText
              id="full_name"
              v-model="customerSupport_formInquiry.full_name"
              v-bind="{ ...useBindStateForm('Masukkan nama lengkap') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              v-on="useListenerForm(customerSupport_formValidations, 'full_name')"
            />
          </BaseFormGroup>
          <BaseFormGroup
            v-slot="{ classes }"
            is-mandatory
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="no_telp"
            name="Nomor HP"
            :validators="customerSupport_formValidations.no_telp"
          >
            <PrimeVueInputGroup>
              <PrimeVueInputGroupAddon class="!bg-[#F9FAFB]">
                <p class="text-[#18191A] text-base font-normal">+62</p>
              </PrimeVueInputGroupAddon>
              <PrimeVueInputText
                id="no_telp"
                v-model="customerSupport_formInquiry.no_telp"
                v-bind="{ ...useBindStateForm('Masukkan nomor handphone') }"
                class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
                :class="{ ...classes }"
                type="text"
                inputmode="numeric"
                v-on="useListenerForm(customerSupport_formValidations, 'no_telp')"
              />
            </PrimeVueInputGroup>
          </BaseFormGroup>
        </div>
      </div>

      <div class="flex w-full justify-between">
        <div class="grid w-full grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-5">
          <BaseFormGroup
            v-slot="{ classes }"
            is-mandatory
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="instituion_name"
            name="Nama Perusahaan"
            :validators="customerSupport_formValidations.instituion_name"
          >
            <PrimeVueInputText
              id="instituion_name"
              v-model="customerSupport_formInquiry.instituion_name"
              v-bind="{ ...useBindStateForm('Masukkan nama perusahaan') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              v-on="useListenerForm(customerSupport_formValidations, 'instituion_name')"
            />
          </BaseFormGroup>
          <BaseFormGroup
            v-slot="{ classes }"
            is-mandatory
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="email"
            name="Alamat Email"
            :validators="customerSupport_formValidations.email"
          >
            <PrimeVueInputText
              id="email"
              v-model="customerSupport_formInquiry.email"
              v-bind="{ ...useBindStateForm('Masukkan alamat email') }"
              class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
              :class="{ ...classes }"
              type="text"
              v-on="useListenerForm(customerSupport_formValidations, 'email')"
            />
          </BaseFormGroup>
        </div>
      </div>

      <div class="flex justify-between gap-5">
        <BaseFormGroup
          v-slot="{ classes }"
          is-mandatory
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="npwp_address"
          name="Alamat Perusahaan Sesuai NPWP"
          :validators="customerSupport_formValidations.npwp_address"
        >
          <PrimeVueTextarea
            id="npwp_address"
            v-model="customerSupport_formInquiry.npwp_address"
            v-bind="{ ...useBindStateForm('Masukkan alamat perusahaan sesuai NPWP') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            rows="3"
            v-on="useListenerForm(customerSupport_formValidations, 'npwp_address')"
          />
        </BaseFormGroup>
      </div>
      <div class="flex justify-between gap-5">
        <BaseFormGroup
          v-slot="{ classes }"
          is-mandatory
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="password"
          name="Topik Pembahasan"
          :validators="customerSupport_formValidations.topic"
        >
          <PrimeVueSelect
            v-model="customerSupport_formInquiry.topic"
            :options="options"
            option-label="name"
            placeholder="Pilih Informasi Umum"
            class="!w-full !rounded-lg"
            :class="{ ...classes }"
            v-on="useListenerForm(customerSupport_formValidations, 'topic')"
          />
        </BaseFormGroup>
      </div>
      <div class="flex justify-between gap-5 mb-4">
        <PrimeVueTextarea
          id="email"
          v-model="customerSupport_formInquiry.question"
          v-bind="{ ...useBindStateForm('Berikan rincian tentang pertanyaan Anda di sini') }"
          class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
          type="text"
          rows="3"
        />
      </div>
    </section>
    <section class="flex items-start gap-2 mb-6">
      <PrimeVueCheckbox
        id="customerSupport_formInquiry"
        v-model="customerSupport_formInquiry.terms"
        binary
        input-id="terms"
        class="!border-black"
      />
      <label for="terms" class="text-[16px] -mt-[3px]">
        Saya memahami dan menyetujui bahwa data pribadi saya akan diproses sesuai dengan
        <span class="text-[#147FFF] font-bold underline">kebijakan privasi</span> yang berlaku.
      </label>
    </section>
    <section id="btn-actions" class="flex items-center justify-end w-full">
      <PrimeVueButton
        :disabled="customerSupport_formValidations.$invalid || !customerSupport_formInquiry.terms"
        type="button"
        label="Submit"
        size="large"
        class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !px-5 !w-full"
        @click="onSubmitForm"
      />
    </section>
  </section>
</template>
