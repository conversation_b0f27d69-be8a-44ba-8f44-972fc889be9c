<script lang="ts" setup>
import type { IConfigurationProvided } from '~/app/src/modules/configurations/interfaces/configurations.interface';
import { useCustomerSupportService } from '../../services/customerSupport.service';

/**
 * @description Destructure all the data and methods what we need
 */
const { customerSupport_openWhatsApp } = useCustomerSupportService();
const { config_hideInquiryForm } = inject<IConfigurationProvided>('configurations')!;

const isOpenModalInquiryForm = ref(false);

const onOpenModal = () => {
  isOpenModalInquiryForm.value = true;
};

const onSubmitInquiryForm = () => {
  isOpenModalInquiryForm.value = false;
};
</script>

<template>
  <div class="relative border border-[#E5E6E8] rounded-lg p-4 bg-white w-full">
    <NuxtImg src="/icons/customer-support/ellipse-3.svg" alt="" class="absolute right-0 top-0" />
    <NuxtImg src="/icons/customer-support/ellipse-4.svg" alt="" class="absolute right-0 top-0" />
    <NuxtImg src="/icons/customer-support/ellipse-5.svg" alt="" class="absolute left-0 bottom-0" />

    <p class="text-sm font-normal">Adakah yang ingin ditanyakan seputar produk kami?</p>

    <div v-if="!config_hideInquiryForm" class="mt-4">
      <h3 class="font-bold text-base mb-2">Sales Inquiry</h3>
      <PrimeVueButton
        class="w-full !bg-white !border-[#ACB1B4] !text-black !text-base !font-medium"
        @click="onOpenModal"
      >
        <NuxtImg src="/icons/question-mark.svg" alt="question-mark" class="w-6 h-6" />
        <span>Formulir Pertanyaan</span>
      </PrimeVueButton>
    </div>

    <div class="mt-4">
      <h3 class="font-bold text-base mb-2">Hubungi Tim Kami</h3>
      <PrimeVueButton
        class="w-full !bg-white !border-[#ACB1B4] !text-black !text-base !font-medium"
        @click="customerSupport_openWhatsApp"
      >
        <NuxtImg src="/icons/whatsapp.svg" alt="whatsapp" class="w-6 h-6" />
        <span>Kontak WhatsApp</span>
      </PrimeVueButton>
    </div>

    <!-- Modal inquiry form -->
    <PrimeVueDialog
      v-if="!config_hideInquiryForm"
      v-model:visible="isOpenModalInquiryForm"
      modal
      header="Header"
      class="!w-[90%] md:!w-[872px]"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :pt="{
        content: '!px-5',
        mask: 'sm:!flex !hidden',
      }"
    >
      <template #closeicon>
        <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
      </template>
      <template #header>
        <header class="flex items-center justify-between">
          <h4 class="font-druk text-[28px] md:text-[42px] text-black font-bold">B2B Inquiry Form</h4>
        </header>
      </template>
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDialog>
    <!-- Drawer -->
    <PrimeVueDrawer
      id="drawer-login-mobile"
      v-model:visible="isOpenModalInquiryForm"
      position="bottom"
      class="!h-[620px] !rounded-t-2xl sm:!hidden !flex"
      :pt="{
        header: '!hidden',
        content: '!px-6 !pt-4',
        mask: '!flex sm:!hidden',
      }"
    >
      <template #default>
        <BaseInquiryForm @on-submit="onSubmitInquiryForm" />
      </template>
    </PrimeVueDrawer>
  </div>
</template>
