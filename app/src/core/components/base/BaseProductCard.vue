<script setup lang="ts">
// import type { IDashboardCatalogProvided } from '~/app/src/modules/dashboard/interfaces/dashboard-catalog.interface';

/**
 * @description Define the props interface
 */
interface IProps {
  product: IProductList;
}

/**
 * @description Define props with default values and interfaces
 */
const props = defineProps<IProps>();

/**
 * @description router
 */
const router = useRouter();

/**
 * @description Injecting dependencies
 */
// const { catalog_isOpenDialogAddToCart, catalog_fetchProductDetail } =
//   inject<IDashboardCatalogProvided>('dashboardCatalog')!;

const displayImage = ref<string | null>(props.product.image);
// const selectedImageIndex = ref<number>(0);

// const onClickChangeImage = (value: string, index: number) => {
//   displayImage.value = value;
//   selectedImageIndex.value = index;
// };

// const outOffStock = computed(() => props.product.stock === 0);

const onClickDetail = () => {
  if (props.product?.sku) {
    router.push({
      name: 'product.detail',
      params: {
        sku: props.product.sku,
      },
    });
  }
};
</script>

<template>
  <section :id="'product-card-' + product.id" class="flex flex-col w-full h-fit gap-3">
    <div class="w-full h-full max-h-80 rounded relative flex items-center justify-center border border-[#F0F2F4]">
      <NuxtImg
        :src="(displayImage as string)"
        alt="product"
        class="w-full h-full max-h-80 rounded"
        loading="lazy"
        @click="onClickDetail"
      />
    </div>

    <section id="information" class="flex flex-col gap-1">
      <div class="h-[24px] overflow-hidden">
        <h6 class="font-medium text-[16px] text-black">
          {{ product.name }}
        </h6>
      </div>
      <p class="text-[13px] text-muted">SKU {{ product?.sku ? product?.sku : '-' }}</p>
    </section>

    <p id="price" class="font-semibold text-[16px] text-black">
      {{ product?.price ? useCurrencyOfIDR(Number(product?.price)) : '-' }}
    </p>

    <section id="btn-actions" class="flex items-center gap-3 w-full">
      <PrimeVueButton
        type="button"
        label="Lihat Produk"
        size="large"
        class="!bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !w-full !py-0 px-4 !h-[44px]"
        @click="onClickDetail"
      />
    </section>
  </section>
</template>
