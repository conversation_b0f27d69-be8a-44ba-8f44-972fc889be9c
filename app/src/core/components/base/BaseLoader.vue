<template>
  <section class="loader-section" aria-busy="true" aria-label="Loading">
    <div class="box-spinner">
      <slot name="text"></slot>
      <div class="spinner" aria-hidden="true"></div>
    </div>
  </section>
</template>

<style scoped>
.loader-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
}

.box-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  width: 56px;
  height: 56px;
  margin: auto;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #007fc8 94%, transparent) top/9px 9px no-repeat,
    conic-gradient(transparent 30%, #007fc8);
  -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 9px), #000 0);
  mask: radial-gradient(farthest-side, transparent calc(100% - 9px), #000 0);
  animation: spin 1s infinite linear;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}
</style>
