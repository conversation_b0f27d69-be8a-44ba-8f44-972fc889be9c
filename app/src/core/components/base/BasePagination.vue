<script setup lang="ts">
/**
 * @description Define props interface
 */

interface IPaginationProps {
  data: IPaginationResponse;
  clickActionNext: () => void;
  clickActionPrev: () => void;
  clickActionPerPage: (perPage: number) => void;
  sizes?: number[];
  loading?: boolean;
}

const props = withDefaults(defineProps<IPaginationProps>(), {
  sizes: () => Array.from(BASE_PAGINATION_SIZE),
  data: () => ({
    total_data: 0,
    size: 12,
    active_page: 0,
    total_page: 0,
  }),
});

const currentStartShowPage = computed(() => {
  if (props.data.active_page === 1) {
    return 1;
  } else {
    return props.data.active_page === 2 ? props.data.size + 1 : props.data.size * (props.data.active_page - 1) + 1;
  }
});

const currentEndShowPage = computed(() => {
  if (props.data.active_page === 1) {
    return props.data.total_data < props.data.size ? props.data.total_data : props.data.size;
  } else {
    return props.data.active_page * props.data.size > props.data.total_data
      ? props.data.total_data
      : props.data.active_page * props.data.size;
  }
});
</script>

<template>
  <div class="flex justify-between items-center">
    <div v-if="loading" class="w-full flex items-center justify-between gap-3">
      <div class="flex items-center justify-between gap-3">
        <PrimeVueSkeleton width="3rem" class="!h-7"></PrimeVueSkeleton>
        <PrimeVueSkeleton width="4rem" class="!h-7"></PrimeVueSkeleton>
      </div>
      <PrimeVueSkeleton width="6rem" class="!h-7"></PrimeVueSkeleton>
    </div>
    <template v-else>
      <div class="flex items-center">
        <div class="flex items-center">
          <span class="text-[12px] hidden md:block">Menampilkan</span>
          <span class="mx-2">
            <select
              v-model="data.size"
              class="block text-[12px] w-full px-1 py-1 border border-[#E5E6E8] focus:outline-none focus:ring-1 focus:ring-orange-500"
              @update:model-value="clickActionPerPage"
            >
              <option v-for="item in sizes" :key="item" :value="item" selected>
                {{ item }}
              </option>
            </select>
          </span>
          <span class="text-[12px]">per halaman</span>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <p class="text-[12px] text-black">
          <span>{{ currentStartShowPage ?? 0}} </span>
          <span> - </span>
          <span>{{ currentEndShowPage ?? 0}}</span>
          <span> dari </span>
          <span>{{ data.total_data ?? 0}}</span>
        </p>
        <div class="flex gap-2">
          <div>
            <PrimeVuebutton
              class="!bg-white !text-black !h-7 !w-7 !border !border-black !rounded-none !font-semibold text-sm md:text-base"
              :disabled="props?.data?.active_page ? props?.data?.active_page === 1 : true"
              @click="clickActionPrev && clickActionPrev()"
            >
              &lt;
            </PrimeVuebutton>
          </div>
          <div>
            <PrimeVuebutton
              class="!bg-white !text-black !h-7 !w-7 !border !border-black !rounded-none !font-semibold text-sm md:text-base"
              :disabled="props.data.active_page ? props.data.active_page === props.data.total_page : true"
              @click="clickActionNext && clickActionNext()"
            >
              &gt;
            </PrimeVuebutton>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
