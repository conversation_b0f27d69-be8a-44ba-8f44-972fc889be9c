<script setup lang="ts">
const { navbar_isOpenBottomSheetConfirmationLogout,navbar_onCloseBottomSheetConfirmationLogout,navbar_onLogout} =
inject<INavbarProvided>('main')!;
</script>
<template>
    <div class="card">
        <PrimeVueDrawer 
        v-model:visible="navbar_isOpenBottomSheetConfirmationLogout" 
        class="rounded-t-xl" 
        header="Bottom Drawer" 
        position="bottom" 
        style="height: auto"
        >
            <template #closeicon>
                <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
            </template>
            <template #header>
                <div class="text-[18px] text-[#18191A] font-bold p-4">
                    Apakah Anda ingin keluar?
                </div>
            </template>
            <template #default>
                <div class="p-4 border-y border-[#E5E6E8]">
                    <div class="font-regular text-[16px] text-[#18191A] my-4">
                        <PERSON><PERSON> juga akan keluar dari semua akses <PERSON> B2B
                    </div>
                    <div class="flex gap-2">
                        <PrimeVueButton
                            class="!w-full !bg-white !border-black !text-black !py-3"
                            @click="navbar_onCloseBottomSheetConfirmationLogout"
                        >
                            Batal
                        </PrimeVueButton>
                        <PrimeVueButton
                            class="!w-full !bg-[#E9151D] !border-[#E9151D] !text-white !py-3"
                            @click="navbar_onLogout"
                        >
                            Keluar
                        </PrimeVueButton>
                    </div>
                </div>
            </template>
        </PrimeVueDrawer>
    </div>
</template>
<style>
    .p-drawer-header { 
        padding: 0 !important;
    }
    .p-drawer-content {
        padding: 0 !important;
    }
</style>