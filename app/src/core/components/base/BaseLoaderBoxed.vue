<script lang="ts" setup>
/**
 * @description Define props interface
 */
interface BaseLoaderBoxProps {
  height?: number;
  size?: 'xs' | 'sm' | 'md';
}
defineProps<BaseLoaderBoxProps>();
</script>

<template>
  <section class="loader-boxed" aria-busy="true" aria-label="Loading" :style="{ height: height + 'px' }">
    <div class="box-spinner">
      <slot name="text"></slot>
      <div
        class="spinner"
        :class="{
          'h-[24px] w-[24px]': size === 'xs',
          'h-[40px] w-[40px]': size === 'sm',
          'h-[56px] w-[56px]': !size || size === 'md',
        }"
        aria-hidden="true"
      ></div>
    </div>
  </section>
</template>

<style scoped>
.loader-boxed {
  z-index: 10;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
}

.box-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  margin: auto;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #007fc8 94%, transparent) top/9px 9px no-repeat,
    conic-gradient(transparent 30%, #007fc8);
  -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 9px), #000 0);
  mask: radial-gradient(farthest-side, transparent calc(100% - 9px), #000 0);
  animation: spin 1s infinite linear;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}
</style>
