<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import Select from 'primevue/select';
type SortingList = {name : string; code : string}
interface IProps {
    options?: SortingList[];
    selected: string; 
    placeholder?: string;
    disable: boolean;
}
const props = defineProps<IProps>();
const emit = defineEmits(['update:selected']);

const selectedValue = ref(props.selected);

watch(() => props.selected, (newValue) => {
    selectedValue.value = newValue;
});

const selectedOption = computed(() => {
    return props.options?.find(option => option.code === selectedValue.value);
});

function onSelectionChange(event: { value: SortingList | null }) {
    if (event.value) {
        emit('update:selected', event.value.code);
    }
}
</script>

<template>
    <label class="input-select__label">
        <Select 
            v-model="selectedOption" 
            :options="props.options" 
            :disabled="props.disable" 
            option-label="name"
            option-value="code"
            :placeholder="props.placeholder"
            class="w-full"
            @change="onSelectionChange"
        />
    </label>
</template>