<script setup lang="ts">
// Components
import BaseContainer from './BaseContainer.vue';
import NotificationContent from '~/app/src/modules/notification/components/NotificationContent.vue';

// Interfaces
import type { ICartProvided } from '~/app/src/modules/cart/interfaces/cart.interface';
import { useAuthenticationStore } from '~/app/src/modules/authentication/stores/authentication.store';
import type { INotificationProvided } from '~/app/src/modules/notification/interfaces/notification.interface';

import type { VNodeRef } from 'vue';
import { capitalizeFirstLetter, getInitialsName } from '../../helpers/text.helper';
import type { IAuthenticationProvided } from '~/app/src/modules/authentication/interfaces/authentication.interface';
import type { IDashboardCatalogProvided } from '~/app/src/modules/dashboard/interfaces/dashboard-catalog.interface';

interface Props {
  isLandingPage?: boolean;
}

defineProps<Props>();

// store
const authStore = useAuthenticationStore();

/**
 * @description Injecting dependenciess
 */
const {
  navbar_searchQuery,
  navbar_onOpenDialogConfirmationLogout,
  navbar_isOpenDrawer,
  navbar_onHandleResize,
  navbar_width,
} = inject<INavbarProvided>('main')!;
const { authentication_isOpenModalLogin, authentication_isLoggedIn } = inject<IAuthenticationProvided>('auth')!;
const { app_headerMenu } = inject<IAppProvided>('app')!;
const { cart_dataLimit } = inject<ICartProvided>('navbarCart')!;
const { notification_count } = inject<INotificationProvided>('notification')!;
// const { catalog_tempQueryParamsOfProducts, catalog_queryParamsOfProducts, catalog_fetchProducts } =
//   inject<IDashboardCatalogProvided>('rootCatalog')!;

const router = useRouter();

// refs
const menuRef = ref();
const authMenuRef = ref();
const selectedCurrentMenu = ref('');
const notificationPopoverRef = ref();
const drawerRef = ref<VNodeRef | null>(null);

const toggleMenu = (event: MouseEvent) => {
  menuRef.value.toggle(event);
};

const toggleAuthMenu = (event: MouseEvent) => {
  authMenuRef.value.toggle(event);
};

const onClickNotificationBtn = (event: MouseEvent) => {
  if (authentication_isLoggedIn.value) {
    if (notificationPopoverRef.value) {
      notificationPopoverRef.value.toggle(event);
    }
  } else {
    authentication_isOpenModalLogin.value = true;
  }
};

const onClickSearch = () => {
  navbar_isOpenDrawer.value = !navbar_isOpenDrawer.value;
};

const menuItemsDropdown = [
  {
    label: 'Dashboard',
    icon: '/icons/dashboad-icon.svg',
    path: '/dashboard',
  },
  {
    label: 'Profile',
    icon: '/icons/menu-person.svg',
    path: '/profile',
  },
  {
    label: 'Pesanan Saya',
    icon: '/icons/menu-order.svg',
    path: '/order',
  },
  {
    label: 'Tagihan',
    icon: '/icons/menu-bill.svg',
    path: '/bill',
  },
  // {
  //   label: 'Faktur Pajak',
  //   icon: '/icons/tax-icon.svg',
  //   path: '/bill',
  // },
];

const onClickMenu = (path: string) => {
  if (path) {
    router.push(path);
  }
};

const onCloseMegaMenu = () => {
  setTimeout(() => {
    drawerRef.value?.hide();
    // isShowMegaMenu.value = false;
  }, 250);
};

const handleMenuClick = async (valParams: string, typeParams: string) => {
  onCloseMegaMenu();
  router.push({
    name: 'catalog',
    query: {
      [typeParams]: valParams,
    },
  });
};

const onClickCart = () => {
  if (authentication_isLoggedIn.value) {
    router.push('/cart');
  } else {
    authentication_isOpenModalLogin.value = true;
  }
};

const onClickRegister = () => {
  if (navbar_width.value < 640) {
    router.push({
      name: 'register-mobile',
    });
  } else {
    router.push({
      name: 'register-form',
    });
  }
};

const onClickLogin = () => {
  router.push({
    name: 'login',
  });
};

const currentMenu = computed<IHeaderMenu>(() => {
  return Object.values(app_headerMenu.value).find(i => i?.menu === selectedCurrentMenu.value) as IHeaderMenu;
});

const toggle = (event: MouseEvent, menu: string) => {
  selectedCurrentMenu.value = menu;
  drawerRef.value?.show?.(event);
};

const onClickLogo = () => {
  navbar_searchQuery.value = '';
  navigateTo('/');
};

const userName = computed<string>(() => {
  return authStore.authentication_userData?.owner_name ?? '';
});

const hasAuthenticated = computed<boolean>(() => {
  return Boolean(authStore.authentication_accessToken) && Boolean(authStore.authentication_userData?.owner_name);
});

const cartCount = computed(() => cart_dataLimit?.value?.cart_count ?? 0);

const unreadCount = computed(() => {
  return notification_count.value?.Semua;
});

const searchIcon = computed(() => {
  if (hasAuthenticated.value && router.currentRoute.value.name == 'homepage') {
    return '/icons/search-white.svg';
  }
  if (hasAuthenticated.value) {
    return '/icons/search-black.svg';
  }
  if (!hasAuthenticated.value && router.currentRoute.value.name == 'homepage') {
    return '/icons/search-white.svg';
  }
  return '/icons/search-black.svg';
});

const hamburgerMenu = computed(() => {
  if (router.currentRoute.value.name === 'homepage') {
    return '/icons/hamburger-white.svg';
  } else {
    return '/icons/hamburger-black.svg';
  }
});

onMounted(() => {
  window.addEventListener('resize', navbar_onHandleResize);
});
</script>

<template>
  <section
    id="base-navbar"
    class="relative inset-0 z-20 w-full border-b border-b-[#E5E6E8] sm:border-b-[transparent]"
    :class="[isLandingPage ? 'bg-[#18191A]' : 'bg-white']"
  >
    <BaseContainer>
      <nav class="w-full px-0 mx-auto sticky flex items-center py-2 sm:py-4 gap-4">
        <div class="grid grid-cols-2 sm:grid-cols-[150px_1fr] gap-4 w-full">
          <section id="app-logo">
            <div class="flex sm:items-center md:justify-center cursor-pointer -ml-4 md:-ml-6" @click="onClickLogo">
              <NuxtImg
                :src="isLandingPage ? '/images/logo-white.svg' : '/images/logo-black.svg'"
                alt="app-logo"
                class="w-[120px] sm:w-[150px]"
              />
            </div>
          </section>
          <section id="content" class="flex sm:flex-col flex-row gap-2 w-full">
            <section id="call-to-actions" class="flex items-center justify-end gap-3 w-full">
              <PrimeVueInputText
                id="product-search-input-"
                v-model="navbar_searchQuery"
                type="text"
                placeholder="Masukkan nama produk"
                class="!border-none !rounded-sm !text-sm !h-[48px] font-medium w-full !bg-[#E5E6E8] !px-6 sm:block hidden"
                autocomplete="new-password"
              />
              <section
                v-if="hasAuthenticated"
                id="right-content"
                class="flex items-center justify-items-end gap-1"
              >
                <PrimeVueButton
                  variant="link"
                  size="small"
                  class="!rounded-full !px-[4px] !py-[4px]"
                  @click="onClickSearch"
                >
                  <NuxtImg
                    :src="searchIcon"
                    alt="notification"
                    class="sm:w-[32px] sm:h-[32px] w-[24px] h-[24px] cursor-pointer"
                  />
                </PrimeVueButton>
                <PrimeVueButton
                  variant="link"
                  size="small"
                  class="!rounded-full relative !overflow-visible !px-[4px] !py-[4px]"
                  @click="onClickNotificationBtn"
                >
                  <div
                    v-if="unreadCount"
                    class="bg-[#E63B14] rounded-full w-[8px] h-[8px] px-[3px] absolute top-[4px] sm:top-[10px] right-[7px] sm:right-[7px] text-white text-[10px]"
                  >
                    <!-- {{ unreadCount }} -->
                  </div>
                  <NuxtImg
                    :src="
                      isLandingPage
                        ? '/icons/notification-white-icon.svg'
                        : '/icons/notification-outline-black.svg'
                    "
                    alt="notification"
                    class="sm:w-[32px] sm:h-[32px] w-[24px] h-[24px] cursor-pointer"
                  />
                </PrimeVueButton>

                <PrimeVueButton
                  variant="link"
                  size="small"
                  class="!rounded-full relative !overflow-visible !px-[4px] !py-[4px]"
                  @click="onClickCart"
                >
                  <div
                    v-if="cartCount > 0"
                    class="bg-[#E63B14] rounded-full min-w-[15px] min-h-[15px] px-[3px] absolute top-[0px] sm:top-[3px] right-[0px] sm:right-[0px] text-white text-[10px]"
                  >
                    {{ cartCount }}
                  </div>
                  <NuxtImg
                    alt="cart"
                    :src="isLandingPage ? '/icons/cart-white-icon.svg' : '/icons/cart-outline-black.svg'"
                    class="sm:w-[32px] sm:h-[32px] w-[24px] h-[24px] cursor-pointer"
                  />
                </PrimeVueButton>
                <div class="w-[8px]" />
                <PrimeVueButton
                  variant="link"
                  class="!rounded-full relative !w-[32px] !h-[32px] overflow-hidden !bg-[#ACB1B4] !text-white !cursor-pointer aspect-square"
                  @click="toggleMenu"
                >
                  {{ getInitialsName(userName) }}
                </PrimeVueButton>
              </section>

              <section v-else class="flex items-center gap-3">
                <div class="sm:flex gap-2 hidden">
                  <PrimeVueButton
                    variant="outlined"
                    class="!w-[150px] !text-sm !hover:bg-transparent !focus:bg-transparent"
                    :class="[
                      isLandingPage ? '!text-white !border-white hover:!text-black' : '!text-black !border-black',
                    ]"
                    @click="onClickRegister"
                  >
                    Register
                  </PrimeVueButton>
                  <PrimeVueButton
                    class="!w-[150px] !bg-[#FF5A00] !border-[#FF5A00] !text-white !text-sm"
                    @click="onClickLogin"
                  >
                    Login
                  </PrimeVueButton>
                </div>
                <div class="sm:hidden block">
                  <PrimeVueButton variant="link" size="small" class="!rounded-full" @click="onClickSearch">
                    <NuxtImg
                      :src="searchIcon"
                      alt="notification"
                      class="sm:w-[34px] sm:h-[34px] w-[24px] h-[24px] cursor-pointer"
                    />
                  </PrimeVueButton>
                  <PrimeVueButton variant="link" size="small" class="!rounded-full" @click="toggleAuthMenu">
                    <NuxtImg
                      :src="hamburgerMenu"
                      alt="notification"
                      class="sm:w-[34px] sm:h-[34px] w-[24px] h-[24px] cursor-pointer"
                    />
                  </PrimeVueButton>
                </div>
              </section>
            </section>

            <ul
              id="list-menus"
              class="items-center sm:flex hidden flex-row flex-wrap justify-center md:justify-start px-1 font-bebas text-base font-normal"
            >
              <li
                v-for="(item, index) in app_headerMenu"
                :ref="'button' + index"
                :key="String(index)"
                class="mr-3"
              >
                <a
                  class="!text-[16px] cursor-pointer"
                  :class="[isLandingPage ? 'text-white' : '!text-black']"
                  href="#"
                  @click="e => toggle(e, item.menu)"
                >
                  {{ capitalizeFirstLetter(item.menu) }}
                </a>
              </li>
            </ul>
          </section>

          <!-- <div class="flex items-center justify-center md:hidden ml-auto">
            <PrimeVueButton
              v-if="hasAuthenticated"
              variant="link"
              class="!rounded-full relative !w-[32px] !h-[32px] overflow-hidden !bg-[#ACB1B4] !text-white !cursor-pointer"
              @click="toggleMenu"
            >
              {{ getInitialsName(userName) }}
            </PrimeVueButton>
            <PrimeVueButton
              v-else
              variant="link"
              class="!rounded-full relative !p-0 !w-[32px] !h-[32px] overflow-hidden !cursor-pointer"
              @click="toggleAuthMenu"
            >
              <NuxtImg
                :src="isLandingPage ? '/icons/menu-icon-white.svg' : '/icons/menu-icon.svg'"
                class="h-[30px] w-[30px]"
              />
            </PrimeVueButton>
          </div> -->
        </div>
      </nav>
    </BaseContainer>
  </section>

  <PrimeVuePopover
    id="popover-mega-menu"
    ref="drawerRef"
    :pt="{
      root: 'w-full bg-[#18191A] shadow-md  m-auto pb-10 px-10',
    }"
  >
    <div
      id="app-mega-menu"
      class="pb-2"
      :class="[isLandingPage ? 'text-white  bg-[#18191A]' : 'text-[#18191A] bg-white']"
    >
      <div class="flex flex-col">
        <BaseContainer class="flex">
          <div class="flex-none">
            <div class="h-auto rounded-md pt-4 pb-2">
              <button v-if="Object.keys(app_headerMenu).length" class="font-bold cursor-pointer">
                {{ currentMenu?.menu }}
              </button>
            </div>
          </div>
        </BaseContainer>

        <BaseContainer class="flex">
          <div v-for="(submenu, index) in currentMenu?.submenu" :key="index" class="flex flex-col w-44 pr-8">
            <div class="text-base font-semibold">
              <div class="flex-none">
                <div class="h-auto rounded-md py-4">
                  <button>
                    {{ submenu.name }}
                  </button>
                </div>
              </div>
            </div>
            <div class="text-xs font-normal">
              <div v-for="(menu_item, indx) in submenu.menu_item" :key="indx" class="flex-none">
                <div class="h-auto rounded-md py-2">
                  <button class="text-left cursor-pointer" @click="handleMenuClick(menu_item, 'subcategory')">
                    {{ menu_item }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </BaseContainer>
      </div>
    </div>
  </PrimeVuePopover>

  <PrimeVuePopover
    ref="menuRef"
    class="w-[196px]"
    :pt="{
      content: '!p-0',
    }"
  >
    <section id="popover-menu-content" class="relative inset-0 z-0">
      <PrimeVueButton
        v-for="(item, index) in menuItemsDropdown"
        :key="String(index)"
        variant="text"
        size="large"
        class="w-full text-[15px] flex !items-center !justify-start"
        @click="onClickMenu(item.path)"
      >
        <NuxtImg :src="item.icon" alt="close" class="w-[18px] h-[18px]" />
        <span class="text-[15px] ml-1 text-[#18191A]">{{ item.label }}</span>
      </PrimeVueButton>
      <PrimeVueButton
        variant="text"
        size="large"
        class="w-full flex !items-center !justify-start"
        @click="navbar_onOpenDialogConfirmationLogout"
      >
        <NuxtImg src="/icons/logout-icon.svg" alt="log-out" class="w-[20px] h-[20px]" />
        <span class="text-[15px] ml-1 text-[#18191A]">Keluar</span>
      </PrimeVueButton>
    </section>
  </PrimeVuePopover>

  <PrimeVuePopover
    ref="authMenuRef"
    class="w-[190px]"
    :pt="{
      content: '!p-0',
    }"
  >
    <section
      id="popover-menu-content"
      class="relative inset-0 z-0 flex flex-col items-center justify-center w-full gap-4 py-3"
    >
      <PrimeVueButton
        variant="outlined"
        class="!w-[150px] !text-sm !hover:bg-transparent !focus:bg-transparent !text-black !border-black"
        @click="onClickRegister"
      >
        Register
      </PrimeVueButton>
      <PrimeVueButton
        class="!w-[150px] !bg-[#FF5A00] !border-[#FF5A00] !text-white !text-sm"
        @click="onClickLogin"
      >
        Login
      </PrimeVueButton>
    </section>
  </PrimeVuePopover>

  <PrimeVuePopover
    ref="notificationPopoverRef"
    class="sm:w-[420px] sm:-mr-[80px] w-full sm:h-[50vh] !h-[80vh] mr-0"
    :pt="{
      content: '!p-0',
    }"
  >
    <NotificationContent :closeable="true" @on-close="notificationPopoverRef.hide()" />
  </PrimeVuePopover>
</template>

<style lang="css" scoped>
:deep(#list-menus li) {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
</style>
