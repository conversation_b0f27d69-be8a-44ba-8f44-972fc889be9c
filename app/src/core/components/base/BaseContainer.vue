<script setup lang="ts">
interface AppContainerProps {
  id?: string;
  noGutter?: boolean;
}

defineProps<AppContainerProps>();
</script>

<template>
  <div class="app-base-container mx-auto" :class="[{ '!px-0': noGutter }]">
    <slot />
  </div>
</template>

<style lang="css" scoped>
.app-base-container {
  width: 100%;
  padding: 0 10px;
  @media (min-width: 1232px) {
    width: 1228px;
    padding: unset !important;
  }
  @media (min-width: 1480px) {
    width: 1392px;
    padding: unset !important;
  }
}
</style>
