<script lang="ts" setup>
import type { IOrderCustomerShipment, IOrderDetail } from '../../../modules/order/interfaces/order.interface';
import type { ICheckoutConfirmPayload, ICheckoutProvided,ICheckoutShipmentAddress } from '../../../modules/checkout/interfaces/checkout.interface';



/**
 * @description Destructure all the data and methods what we need
 */
const emit = defineEmits(['onSubmit']);
/**
 * @description Injecting dependencies
 */
 const {
    checkout_formValidations,
    checkout_addresPayload,
    checkout_isOpenModalAddress,
    checkout_listProvince,
    checkout_listCities,
    checkout_listDistrict,
    checkout_addAddress,
    checkout_changeAddress,
    checkout_listShipment,
    checkout_editAddress,
    checkout_deleteAddress,
    checkout_createAddress,
    checkout_isAddingAddress,
    checkout_isLoading,
    checkout_modifyAddress,
    checkout_isEditAddress,
    checkout_resetAddressPayload,
} = inject<ICheckoutProvided>('checkout')!;

const selectedShipment = ref<ICheckoutShipmentAddress | null>(checkout_listShipment.value.find(shipment_address => shipment_address.is_selected == 1) || null);

onMounted(() => {
  console.log(selectedShipment.value?.customer_shipment_id,'== Selected Shipment from onMounted')
})

const selectShipment = (item: ICheckoutShipmentAddress) => {
  selectedShipment.value = item;
};

watch(checkout_listShipment, (newVal) => {
  const selected = newVal.find(shipment_address => shipment_address.is_selected === 1);
  selectedShipment.value = selected || null;
  console.log(selected?.customer_shipment_id, '== Updated Selected Shipment from watch');
});

const onChangeProvince = (test: any) => {
  console.log(test, '== onChangeProvince');
  checkout_addresPayload.city = {
    id: '',
    name: '',
    code: '',
    city_name: '',
  };
  checkout_addresPayload.district = {
    name: '',
    code: '',
  };
};

const onChangeCity = () => {
  checkout_addresPayload.district = {
    name: '',
    code: '',
  };
};

</script>

<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-[#0707071c] rounded-xl" v-if="checkout_isLoading">
    <PrimeVueProgressSpinner 
      style="width: 50px; height: 50px" 
      strokeWidth="8" 
      fill="transparent"
      animationDuration=".5s" 
      aria-label="Custom ProgressSpinner" 
    />
  </div>
    <!-- List Address -->
    <section id="content" class="flex flex-col gap-4" v-if="!checkout_isAddingAddress && !checkout_isEditAddress">
      <section class="flex items-center justify-between">
        <header class="flex">
          <h4 class="font-bold text-[28px] text-black font-druk">Ubah Alamat Pengiriman</h4>
        </header>
        <PrimeVueButton
          class="!border-none !bg-transparent !text-black !rounded-full"
          @click="checkout_isOpenModalAddress = false"
        >
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </PrimeVueButton>
      </section>
      <div class="bg-[#ECF6FF] p-2 rounded-md flex gap-2 py-4 text-[#18191A] text-[14px] items-center">
        <NuxtImg src="/icons/info-blue.svg" class="h-[24px] w-[24px]"/>
        <div class="">
          Anda hanya dapat menambahkan <span class="font-bold"> 1 alamat tambahan </span>
        </div>
      </div>
      <div class="border border-[#E5E6E8] rounded-2xl p-6 cursor-pointer" v-for="(items,index) in checkout_listShipment" :key="index" @click="selectShipment(items)">
        
        <div class="flex justify-between">
          <div class="flex items-center gap-2 font-medium text-[16px] text-[#18191A] border-b border-black pb-1 w-fit">
            <NuxtImg src="/icons/maps.svg"/>
            {{ items.is_primary == 1 ? 'Alamat Utama' : 'Alamat Tambahan' }}
          </div>
          <div class="flex items-center gap-6">
            <div class="flex items-center gap-3">
              <div v-if="checkout_listShipment.length > 1" class="" @click.stop="checkout_deleteAddress(items.customer_shipment_id)">
                <NuxtImg src="/icons/red-trash.svg" class="w-[20px] h-[20px]"/>
              </div>
              <div class="border border-[#ACB1B4] hover:bg-gray-100 rounded-lg px-3 py-2 font-medium text-[#18191A]" @click.stop="checkout_modifyAddress(items.customer_shipment_id)">
                Edit
              </div>
            </div>
            <PrimeVueRadioButton
              :inputId="'shipment-' + index"
              name="shipment"
              :modelValue="selectedShipment?.customer_shipment_id"
              :value="items.customer_shipment_id"
              @change="selectShipment(items)"
            />
          </div>
        </div>
        <div class="grid sm:grid-cols-2 grid-cols-1 my-2 gap-y-2">
          <div class="grid sm:grid-cols-[30fr_70fr] grid-cols-[40fr_60fr] gap-x-[1px]">
            <div class="text-[16px] text-[#686F72]">Provinsi</div>
            <div class="text-[16px] text-[#18191A] font-medium">: {{ items.province }}</div>
          </div>
          <div class="grid sm:grid-cols-[30fr_70fr] grid-cols-[40fr_60fr] gap-x-[1px]">
            <div class="text-[16px] text-[#686F72]">Kota / Kab</div>
            <div class="text-[16px] text-[#18191A] font-medium">: {{ items.city }}</div>
          </div>
          <div class="grid sm:grid-cols-[30fr_70fr] grid-cols-[40fr_60fr] gap-x-[1px]">
            <div class="text-[16px] text-[#686F72]">Kecamatan</div>
            <div class="text-[16px] text-[#18191A] font-medium">: {{ items.district}}</div>
          </div>
          <div class="grid sm:grid-cols-[30fr_70fr] grid-cols-[40fr_60fr] gap-x-[1px]">
            <div class="text-[16px] text-[#686F72]">Kode POS</div>
            <div class="text-[16px] text-[#18191A] font-medium">: {{ items.zip_code }}</div>
          </div>
          <div class="sm:col-span-2 grid sm:grid-cols-[15fr_85fr] grid-cols-[40fr_60fr] gap-x-[1px]">
            <div class="text-[16px] text-[#686F72]">Alamat Lengkap</div>
            <div class="text-[16px] text-[#18191A] font-medium">: {{ items.address }}</div>
          </div>
        </div>
      </div>
      <section id="btn-actions" class="flex sm:flex-row flex-col gap-2 sm:my-4 my-6">
        <PrimeVueButton
          type="button"
          label="Tambah Alamat"
          :disabled="(checkout_listShipment.length ?? 0) > 1"
          size="large"
          class="!w-full !text-center font-medium !rounded-lg !px-5 sm:order-2 order-1"
          :class="checkout_listShipment.length > 1 ? '!bg-[#F5F6F6] !border !border-[#E5E6E8] !text-[#CED1D3]' : '!bg-white !text-black !border !border-[#ACB1B4]'"
          @click="checkout_createAddress"
        />
        <PrimeVueButton
          type="button"
          label="Pilih Alamat"
          size="large"
          @click="checkout_changeAddress(selectedShipment?.customer_shipment_id || ''); checkout_isOpenModalAddress = false"
          class="!w-full !bg-black !border-none text-white !text-base !text-center font-medium !rounded-lg !px-5"
        />
      </section>
    </section>

    <!-- Add Address Form -->
    <section class="flex flex-col p-3" v-if="checkout_isAddingAddress">
      <div class="flex justify-between w-full">
        <div class="">
          <NuxtImg src="/icons/chevron-right.svg" class="rotate-180 w-8 h-8 cursor-pointer" @click="checkout_isAddingAddress = false; checkout_resetAddressPayload()"/>
        </div>
        <header class="flex">
          <h4 class="font-bold text-[28px] text-black font-druk">Tambah Alamat Baru</h4>
        </header>
        <PrimeVueButton
          class="!border-none !bg-transparent !text-black !rounded-full"
          @click="checkout_isOpenModalAddress = false; checkout_isAddingAddress = false; checkout_resetAddressPayload()"
        >
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </PrimeVueButton>
      </div>
      <div class="grid sm:grid-cols-2 grid-cols-1 sm:gap-y-1 gap-4 ">
        <BaseFormGroup
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="Province"
          name="Provinsi"
          spacing-bottom="mb-0"
          class="sm:col-span-1 col-span-2"
          :is-mandatory="true"
          :validators="checkout_formValidations.province"
        >
          <BaseSelectInput
            v-model:selected="checkout_addresPayload.province"
            placeholder="Pilih Provinsi"
            class="rounded-lg"
            :disable="false"
            :options="checkout_listProvince"
            @update:selected="onChangeProvince"
          />
        </BaseFormGroup>
        <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="City"
            name="Kota"
            spacing-bottom="mb-0"
            class="sm:col-span-1 col-span-2"
            :is-mandatory="true"
            :validators="checkout_formValidations.city"
          >
          <BaseSelectInput
            v-model:selected="checkout_addresPayload.city"
            placeholder="Pilih Kota"
            class="rounded-lg"
            :disable="false"
            :options="checkout_listCities"
            @update:selected="onChangeCity"
          />
        </BaseFormGroup>
        <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="District"
            name="Kecamatan"
            spacing-bottom="mb-0"
            class="sm:col-span-1 col-span-2"
            :is-mandatory="true"
            :validators="checkout_formValidations.district"
          >
          <BaseSelectInput
            id="District"
            v-model:selected="checkout_addresPayload.district"
            placeholder="Pilih Kecamatan"
            class="rounded-lg"
            :disable="false"
            :options="checkout_listDistrict"
          />
        </BaseFormGroup>
        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="zip_code"
          class="sm:col-span-1 col-span-2"
          name="Kode Pos"
          :validators="checkout_formValidations.zip_code"
          :is-mandatory="true"
        >
          <input
            id="zip_code"
            v-model="checkout_addresPayload.zip_code"
            v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
            class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            v-on="useListenerForm(checkout_formValidations, 'zip_code')"
          />
        </BaseFormGroup>
        <BaseFormGroup
            v-slot="{ classes }"
            :validators="checkout_formValidations.address"
            is-mandatory
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            class="sm:col-span-2 col-span-1"
            label-for="address_detail"
            name="Alamat lengkap"
            >
          <input
            id="address_detail"
            v-model="checkout_addresPayload.address"
            v-bind="{ ...useBindStateForm('Masukkan alamat pengiriman terbaru') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            />
        </BaseFormGroup>
      </div> 
      <PrimeVueButton
          type="button"
          label="Simpan Alamat"
          size="large"
          :disabled="checkout_formValidations.$invalid"
          class="!w-full !bg-black !border-black !text-white !text-base !text-center font-medium !rounded-lg !px-5 mt-4"
          @click="checkout_addAddress"
        />  
    </section>

    <!-- Edit Address Form -->
    <section class="flex flex-col p-3" v-if="checkout_isEditAddress">
      <div class="flex justify-between w-full">
        <div class="">
          <NuxtImg src="/icons/chevron-right.svg" class="rotate-180 w-8 h-8 cursor-pointer" @click="checkout_isEditAddress = false; checkout_resetAddressPayload()"/>
        </div>
        <header class="flex">
          <h4 class="font-bold text-[28px] text-black font-druk">Ubah Alamat</h4>
        </header>
        <PrimeVueButton
          class="!border-none !bg-transparent !text-black !rounded-full"
          @click="checkout_isOpenModalAddress = false; checkout_isEditAddress = false; checkout_resetAddressPayload()"
        >
          <NuxtImg src="/icons/icon-close-circle-black.svg" alt="close-circle" class="w-[22px] h-[22px]" />
        </PrimeVueButton>
      </div>
      <div class="grid sm:grid-cols-2 grid-cols-1 sm:gap-y-1 gap-4">
        <BaseFormGroup
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="Province"
          name="Provinsi"
          spacing-bottom="mb-0"
          class="sm:col-span-1 col-span-2"
          :is-mandatory="true"
          :validators="checkout_formValidations.province"
        >
          <BaseSelectInput
            v-model:selected="checkout_addresPayload.province"
            placeholder="Pilih Provinsi"
            class="rounded-lg"
            :disable="false"
            :options="checkout_listProvince"
            @update:selected="onChangeProvince"
          />
        </BaseFormGroup>
        <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="City"
            name="Kota"
            spacing-bottom="mb-0"
            class="sm:col-span-1 col-span-2"
            :is-mandatory="true"
            :validators="checkout_formValidations.city"
          >
          <BaseSelectInput
            v-model:selected="checkout_addresPayload.city"
            placeholder="Pilih Kota"
            class="rounded-lg"
            :disable="false"
            :options="checkout_listCities"
            @update:selected="onChangeCity"
          />
        </BaseFormGroup>
        <BaseFormGroup
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            label-for="District"
            name="Kecamatan"
            spacing-bottom="mb-0"
            class="sm:col-span-1 col-span-2"
            :is-mandatory="true"
            :validators="checkout_formValidations.district"
          >
          <BaseSelectInput
            id="District"
            v-model:selected="checkout_addresPayload.district"
            placeholder="Pilih Kecamatan"
            class="rounded-lg"
            :disable="false"
            :options="checkout_listDistrict"
          />
        </BaseFormGroup>
        <BaseFormGroup
          v-slot="{ classes }"
          class-label="font-medium text-sm text-black block mb-2"
          is-name-as-label
          label-for="zip_code"
          name="Kode Pos"
          class="sm:col-span-1 col-span-2"
          :validators="checkout_formValidations.zip_code"
          :is-mandatory="true"
        >
          <input
            id="zip_code"
            v-model="checkout_addresPayload.zip_code"
            v-bind="{ ...useBindStateForm('Masukkan Kode Pos') }"
            class="block w-full border border-solid border-input-gray px-4 py-[14px] rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            v-on="useListenerForm(checkout_formValidations, 'zip_code')"
          />
        </BaseFormGroup>
        <BaseFormGroup
            v-slot="{ classes }"
            :validators="checkout_formValidations.address"
            is-mandatory
            class-label="font-medium text-sm text-black block mb-2"
            is-name-as-label
            class="sm:col-span-2 col-span-1"
            label-for="address"
            name="Alamat lengkap"
            >
          <input
            id="address"
            v-model="checkout_addresPayload.address"
            v-bind="{ ...useBindStateForm('Masukkan alamat pengiriman terbaru') }"
            class="block w-full border border-solid border-input-gray px-4 py-3 rounded-lg focus-visible:outline-none placeholder:text-muted"
            :class="{ ...classes }"
            type="text"
            v-on="useListenerForm(checkout_formValidations, 'address')"
          />
        </BaseFormGroup>
      </div>
      <PrimeVueButton
          type="button"
          label="Simpan Perubahan"
          size="large"
          :disabled="checkout_formValidations.$invalid"
          class="!w-full !bg-black !border-black !text-white !text-base !text-center font-medium !rounded-lg !px-5 mt-4"
          @click="checkout_editAddress()"
        />  
    </section>
</template>