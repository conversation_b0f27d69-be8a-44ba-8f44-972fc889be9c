<script setup lang="ts">
// import type { IDashboardCatalogProvided } from '~/app/src/modules/dashboard/interfaces/dashboard-catalog.interface';
import { useCatalog } from '~/app/src/modules/dashboard/services/catalog.service';
import type { IProductProvided } from '~/app/src/modules/product/interfaces/product.interface';
// import { useProductService } from '~/app/src/modules/product/services/product.service';

/**
 * @description Injecting dependencies
 */
const { navbar_isOpenDrawer, navbar_onApplySearch, navbar_onClearSearch, navbar_searchQuery } =
  inject<INavbarProvided>('main')!;
const {
  product_searchData,
  product_searchIsLoading,
  product_searchHistory,
  product_searchProducts,
  product_clearSearchHistory,
  product_searchRecommendationData,
  product_getSearchProductRecommendation,
} = inject<IProductProvided>('productSearch')!;

const inputRef = ref<HTMLInputElement | null>(null);

const { catalog_queryParamsOfProducts, catalog_tempQueryParamsOfProducts, catalog_fetchProducts } = useCatalog();

const onClickSeeMore = () => {
  const searchValue = navbar_searchQuery.value ? String(navbar_searchQuery.value) : '';
  navbar_isOpenDrawer.value = false;
  catalog_queryParamsOfProducts.search = searchValue;
  catalog_tempQueryParamsOfProducts.search = searchValue;
  catalog_fetchProducts({}, true);
  navigateTo('/catalog');
};

const onClickClearSearchHistory = () => {
  product_clearSearchHistory();
};

const onClickHistoryItem = (val: string) => {
  product_searchProducts(val);
  product_getSearchProductRecommendation(val);
  navbar_searchQuery.value = val;
};

const onClickRecommendItem = (sku: string) => {
  window.location.href = `/product/detail/${sku}`;
  navbar_isOpenDrawer.value = false;
};

const width = ref(window.innerWidth);

const handleResize = () => (width.value = window.innerWidth);

const onSubmitSearch = () => {
  catalog_queryParamsOfProducts.search = navbar_searchQuery.value ?? '';
  catalog_tempQueryParamsOfProducts.search = navbar_searchQuery.value ?? '';
  setTimeout(() => {
    catalog_fetchProducts({ search: navbar_searchQuery.value, page: 1 }, true);
  }, 250);
  navbar_onApplySearch();
  navbar_isOpenDrawer.value = false;
};

const onChangeSearch = useDebounce((value: string | undefined) => {
  if (value) {
    catalog_queryParamsOfProducts.search = value ?? '';
    catalog_tempQueryParamsOfProducts.search = value ?? '';
    setTimeout(() => {
      catalog_fetchProducts({ search: navbar_searchQuery.value, page: 1 }, true);
    }, 250);
  } else {
    catalog_queryParamsOfProducts.search = '';
    catalog_tempQueryParamsOfProducts.search = '';
    setTimeout(() => {
      catalog_fetchProducts({ search: '', page: 1 }, true);
    }, 250);
  }
}, 500);

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

watch(
  navbar_searchQuery,
  useDebounce(value => {
    if (value) {
      product_searchProducts(value as string);
      product_getSearchProductRecommendation(value as string);
    }
  }, 250),
);

watch(navbar_isOpenDrawer, value => {
  if (value) {
    setTimeout(() => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      inputRef.value?.$el?.focus();
    }, 250);
  }
});
</script>

<template>
  <section id="drawer-navbar" class="relative inset-0 z-0">
    <PrimeVueDrawer
      v-model:visible="navbar_isOpenDrawer"
      position="top"
      class="h-auto"
      :show-close-icon="false"
      :pt="{
        content: 'relative inset-0 left-1/2 translate-x-[-50%] sm:!w-[90%] !w-full !p-0',
        header: '!p-0',
        root: '!bg-transparent !border-none !shadow-none !h-fit',
      }"
    >
      <template #header>
        <header class="flex sm:hidden items-center bg-[#ffffff] w-full px-6 py-3 gap-3">
          <PrimeVueIconField class="w-full">
            <PrimeVueInputIcon class="pl-1">
              <NuxtImg src="/icons/search.svg" alt="search" class="w-5 h-5" />
            </PrimeVueInputIcon>
            <PrimeVueInputText
              ref="inputRef"
              v-model="navbar_searchQuery"
              type="text"
              placeholder="Search Produk"
              class="!bg-[#E5E6E8] !text-base !text-black w-full !py-4 !border-none !pl-12 !rounded"
              @update:model-value="onChangeSearch"
              @keydown.enter="onSubmitSearch"
            />
          </PrimeVueIconField>

          <NuxtImg
            src="/icons/close.svg"
            alt="close"
            class="w-6 h-6 cursor-pointer"
            @click="navbar_onClearSearch"
          />
        </header>

        <header class="hidden sm:flex items-center bg-muted-tabs w-full px-11 py-3 gap-6">
          <NuxtLink to="/">
            <NuxtImg src="/images/logo-black.svg" alt="app-logo" class="w-[150px] sm:block hidden" />
          </NuxtLink>

          <PrimeVueIconField class="w-full">
            <PrimeVueInputIcon class="pl-1">
              <NuxtImg src="/icons/search.svg" alt="search" class="w-5 h-5" />
            </PrimeVueInputIcon>
            <PrimeVueInputText
              v-model="navbar_searchQuery"
              type="text"
              placeholder="Search Produk"
              class="!bg-muted-input-drawer !text-base !text-black w-full !py-4 !border-none !pl-12 !rounded-3xl"
              @update:model-value="onChangeSearch"
              @keydown.enter="onSubmitSearch"
            />
          </PrimeVueIconField>

          <NuxtImg
            src="/icons/close.svg"
            alt="close"
            class="w-6 h-6 cursor-pointer"
            @click="navbar_onClearSearch"
          />
        </header>
      </template>

      <template #default>
        <section id="container" class="grid grid-rows-1 sm:grid-cols-12 grid-cols-2">
          <section id="left-content" class="col-span-5">
            <PrimeVueScrollPanel
              class="bg-muted-tabs w-full h-full"
              :pt="{
                barY: '!bg-muted-scrollbar',
              }"
            >
              <section
                id="content"
                class="flex flex-col gap-8 p-5"
                v-if="width > 640 || navbar_searchQuery === ''"
                v-show="navbar_searchQuery === '' || width > 640"
              >
                <section id="last-search" class="flex flex-col gap-4">
                  <h6 class="font-medium text-[14px] text-black">Pencarian Terakhir</h6>
                  <section
                    v-if="product_searchHistory?.length > 0"
                    id="list-keywords"
                    class="flex flex-wrap items-center gap-2"
                  >
                    <button
                      v-for="(item, index) in product_searchHistory"
                      :key="String(index)"
                      class="border border-[#DCE0E4] rounded-full px-4 py-2 text-[14px] cursor-pointer !text-[#535B66] hover:bg-[#f1f1f1] hover:text-black"
                      @click="onClickHistoryItem(item)"
                    >
                      {{ item }}
                    </button>
                  </section>
                  <section v-else class="flex items-center justify-center">
                    <p class="text-sm text-gray-500">Tidak ada riwayat pencarian</p>
                  </section>
                  <PrimeVueButton
                    v-if="product_searchHistory?.length > 0"
                    variant="text"
                    class="w-fit"
                    @click="onClickClearSearchHistory"
                  >
                    <section id="content" class="flex items-center gap-1">
                      <NuxtImg src="/icons/trash-gray.svg" alt="trash" class="w-4 h-4" />
                      <span class="font-medium text-sm text-gray-copyright"> Hapus riwayat pencarian </span>
                    </section>
                  </PrimeVueButton>
                </section>

                <section id="product-recommendation" class="flex flex-col gap-4">
                  <h6 class="font-medium text-[14px] text-black">Rekomendasi Produk</h6>
                  <section
                    v-if="product_searchRecommendationData?.length > 0"
                    id="list-keywords"
                    class="flex flex-wrap items-center gap-2"
                  >
                    <button
                      v-for="(item, index) in product_searchRecommendationData"
                      :key="String(index)"
                      class="border border-[#DCE0E4] rounded-full px-4 py-2 text-[14px] cursor-pointer !text-[#535B66] hover:bg-[#f1f1f1] hover:text-black"
                      @click="onClickRecommendItem(item.sku_code_c)"
                    >
                      {{ item.product_name_c }}
                    </button>
                  </section>
                  <!-- <section id="list-recommendations" class="flex flex-wrap items-center gap-2">
                    <PrimeVueChip
                      label="Insulated Jacket"
                      :pt="{
                        root: '!border-muted-chip !bg-white px-4 py-2',
                        label: 'font-normal !text-sm !text-muted-secondary',
                      }"
                    /> 
                  </section> -->
                </section>
              </section>
            </PrimeVueScrollPanel>
          </section>

          <section
            id="right-content"
            class="col-span-7 flex flex-col bg-white gap-2 p-5 overflow-auto sm:overflow-scroll h-[50vh] sm:h-auto"
          >
            <section id="keyword" class="flex items-center gap-2">
              <NuxtImg src="/icons/search.svg" alt="search" class="w-5 h-5" />
              <span class="font-medium text-sm text-muted">Pencarian: {{ navbar_searchQuery }}</span>
            </section>

            <section id="product-search-section">
              <div class="!px-0 !pb-0 !pt-6">
                <template v-if="product_searchIsLoading">
                  <section class="grid grid-rows-1 grid-cols-12 gap-5">
                    <div v-for="(_, index) in 6" :key="index" class="col-span-full md:col-span-6">
                      <PrimeVueSkeleton width="100" class="!h-[80px]"></PrimeVueSkeleton>
                    </div>
                  </section>
                </template>
                <template v-else>
                  <section v-if="product_searchData?.length > 0" class="max-h-[250px] overflow-scroll">
                    <div class="grid grid-rows-1 grid-cols-12 gap-5">
                      <div
                        v-for="(item, index) in product_searchData"
                        :key="index"
                        class="col-span-full md:col-span-6"
                      >
                        <BaseDrawerProduct :product="item" />
                      </div>
                    </div>
                  </section>
                  <section v-else class="flex items-center justify-center">
                    <p class="text-sm text-gray-500">Hasil pencarian tidak ditemukan</p>
                  </section>
                </template>
                <section id="btn-actions" class="col-span-full mt-3">
                  <PrimeVueButton
                    type="button"
                    label="Lihat Semua Produk yang Anda Cari"
                    size="large"
                    variant="outlined"
                    severity="secondary"
                    class="!text-black !text-base !text-center font-medium !border-input-gray w-full"
                    @click="onClickSeeMore"
                  />
                </section>
              </div>
            </section>
          </section>
        </section>
      </template>
    </PrimeVueDrawer>
  </section>
</template>
