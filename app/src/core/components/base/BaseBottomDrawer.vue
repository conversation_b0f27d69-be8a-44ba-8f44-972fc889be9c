<script setup>
import { ref,defineProps } from "vue";
import Drawer from 'primevue/drawer';
const visibleBottom = ref(false);
const props = defineProps({
  buttonLabel: {
    type: String,
    default: "Filter",
  },
  buttonIcon: {
    type: String,
    default: "/icons/filter-black.svg",
  },
  drawerHeader: {
    type: String,
    default: "Bottom Drawer",
  },
  drawerHeight: {
    type: String,
    default: "10rem",
  },
});

</script>

<template>
    <div class="card">
      <div class="flex gap-2 justify-center">
        <button
          class="flex gap-2 border border-gray-400 px-3 py-2 rounded-md rounded-tl-lg cursor-pointer"
          @click="visibleBottom = true"
        >
          <img :src="props.buttonIcon" alt="" />
          {{ props.buttonLabel }}
        </button>
      </div>
      <Drawer v-model:visible="visibleBottom" :header="props.drawerHeader" position="bottom" :style="{ height: props.drawerHeight }">
        <slot />
      </Drawer>
    </div>
  </template>
  
<style>
.p-drawer-bottom .p-drawer {
    width: 100%;
    border-block-start-width: 1px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}</style>