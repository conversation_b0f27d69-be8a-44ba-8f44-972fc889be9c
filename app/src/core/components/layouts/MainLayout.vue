<script setup lang="ts">
// Services
import { useProductService } from '~/app/src/modules/product/services/product.service';
import { useAppService } from '../../services/app.service';
import { useNavbarService } from '../../services/navbar.service';
import { useCartService } from '~/app/src/modules/cart/services/cart.service';
import { useNotificationService } from '~/app/src/modules/notification/services/notification.service';

import { useCounter, useIdle } from '@vueuse/core';
import { useAuthenticationService } from '~/app/src/modules/authentication/services/authentication.service';
import AuthenticationModalLoginForm from '~/app/src/modules/authentication/components/AuthenticationModalLoginForm.vue';
import { useAuthenticationStore } from '~/app/src/modules/authentication/stores/authentication.store';
import { useConfigurationService } from '~/app/src/modules/configurations/services/configurations.service';
import { useCatalog } from '~/app/src/modules/dashboard/services/catalog.service';

const { inc } = useCounter();

const { idle, reset } = useIdle(15 * 60 * 1000); // 15 min

const message = ref('');
const socketConnected = ref(false);

/**
 * @description Injecting dependencies
 */
const { $websocket } = useNuxtApp();

/**
 * Props
 */
const props = defineProps<{
  pageTitle?: string;
  pageSubTitle?: string;
  hideFooter?: boolean;
  requiredAuth?: boolean;
}>();

const route = useRoute();
const authStore = useAuthenticationStore();

/**
 * @description Destructure all the data and methods what we need
 */
const {
  config_isLoading,
  CUSTOM_ORDER_LOGO_PRICE,
  CUSTOM_ORDER_TEXT_PRICE,
  MIN_REGULAR_ORDER_QTY,
  MIN_REGULAR_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_QTY,
  config_hideInquiryForm,
  configuration_fetchConfigurations,
} = useConfigurationService();

const {
  authentication_isOpenModalLogin,
  authentication_isLoggedIn,
  authentication_loginFormData,
  authentication_loginFormValidations,
  authentication_loginIsLoading,
  authentication_onSubmitLogin,
} = useAuthenticationService();

const { app_headerMenu, app_headerMenuIsLoading, app_fetchHeaderMenu, app_isLoading } = useAppService();

const {
  navbar_isOpenDialogConfirmationLogout,
  navbar_isOpenBottomSheetConfirmationLogout,
  navbar_isOpenDrawer,
  navbar_onApplySearch,
  navbar_onClearSearch,
  navbar_onCloseDialogConfirmationLogout,
  navbar_onCloseBottomSheetConfirmationLogout,
  navbar_onOpenDialogConfirmationLogout,
  navbar_onLogout,
  navbar_logoutIsLoading,
  navbar_hasToken,
  navbar_searchQuery,
  navbar_width,
  navbar_onHandleResize,
} = useNavbarService();

const {
  notification_list,
  notification_count,
  notification_detailData,
  notification_pagination,
  notification_detailIsLoading,
  notification_queryParamsGetListNotification,
  notification_readAll,
  notification_getListNotifications,
  notification_getNotificationsCount,
  notification_getNotificationDetail,
  notification_onDeleteNotification,
} = useNotificationService();

const {
  product_searchData,
  product_searchHistory,
  product_searchIsLoading,
  product_saveSearchHistory,
  product_clearSearchHistory,
  product_searchProducts,
  product_searchRecommendationData,
  product_searchRecommendationIsLoading,
  product_getSearchProductRecommendation,
} = useProductService();
const { cart_dataLimit, cart_getList } = useCartService();

const { catalog_tempQueryParamsOfProducts, catalog_queryParamsOfProducts, catalog_fetchProducts } = useCatalog();

provide('auth', {
  authentication_isOpenModalLogin,
  authentication_isLoggedIn,
  authentication_loginFormData,
  authentication_loginFormValidations,
  authentication_loginIsLoading,
  authentication_onSubmitLogin,
});
provide('app', {
  app_headerMenu,
  app_headerMenuIsLoading,
  app_isLoading,
});
provide('main', {
  navbar_isOpenDialogConfirmationLogout,
  navbar_isOpenBottomSheetConfirmationLogout,
  navbar_isOpenDrawer,
  navbar_onApplySearch,
  navbar_onClearSearch,
  navbar_onCloseDialogConfirmationLogout,
  navbar_onCloseBottomSheetConfirmationLogout,
  navbar_onLogout,
  navbar_logoutIsLoading,
  navbar_onOpenDialogConfirmationLogout,
  navbar_searchQuery,
  navbar_width,
  navbar_onHandleResize,
});
provide('notification', {
  notification_list,
  notification_count,
  notification_detailData,
  notification_pagination,
  notification_detailIsLoading,
  notification_queryParamsGetListNotification,
  notification_readAll,
  notification_getListNotifications,
  notification_getNotificationDetail,
  notification_onDeleteNotification,
});
provide('productSearch', {
  product_searchData,
  product_searchHistory,
  product_searchIsLoading,
  product_saveSearchHistory,
  product_clearSearchHistory,
  product_searchProducts,
  product_searchRecommendationData,
  product_searchRecommendationIsLoading,
  product_getSearchProductRecommendation,
});
provide('navbarCart', {
  cart_dataLimit,
  cart_getList,
});
provide('configurations', {
  config_isLoading,
  CUSTOM_ORDER_LOGO_PRICE,
  CUSTOM_ORDER_TEXT_PRICE,
  MIN_REGULAR_ORDER_QTY,
  MIN_REGULAR_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_QTY,
  config_hideInquiryForm,
  configuration_fetchConfigurations,
});
provide('rootCatalog', {
  catalog_tempQueryParamsOfProducts,
  catalog_queryParamsOfProducts,
  catalog_fetchProducts,
});

/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onBeforeMount(async () => {
  $websocket.connector.pusher.connection.bind('disconnected', function () {
    socketConnected.value = false;
    message.value = 'terputus dengan socket, memuat ulang aplikasi dalam 30 detik';
    setTimeout(() => {
      window.location.reload();
    }, 30000);
  });
  $websocket.connector.pusher.connection.bind('unavailable', function () {
    socketConnected.value = false;
    message.value = 'Hubungan socket terputus, menghubungkan kembali...';
  });
  $websocket.connector.pusher.connection.bind('connecting', () => {
    socketConnected.value = false;
    message.value = 'Mohon Menunggu';
  });
  setTimeout(() => {
    if (authStore?.authentication_accessToken && authStore?.authentication_profileData?.customer_id) {
      $websocket
        .channel('user.' + authStore?.authentication_profileData?.customer_id)
        .on('pusher:subscription_succeeded', () => {
          socketConnected.value = true;
        })
        .listen('.notification.new', () => {
          setTimeout(() => {
            notification_getListNotifications();
            notification_getNotificationsCount();
          }, 250);
        });
    }
  }, 2000);
});
onMounted(async () => {
  /**
   * @description In this case, we use custom composable called useAuthenticate to check if user is authenticated or not. Because, when we use ddd pattern or we try to use middleware in layout component, it won't work. So, we need to check the authentication status using custom composable.
   */
  if (navbar_hasToken()) {
    await Promise.allSettled([
      notification_getListNotifications(),
      notification_getNotificationsCount(),
      app_fetchHeaderMenu(),
    ]);

    if (route?.name !== 'cart') {
      await Promise.allSettled([cart_getList()]);
    }
  }

  if (props.requiredAuth) {
    useAuthenticate();
  }
  await Promise.allSettled([app_fetchHeaderMenu(), configuration_fetchConfigurations()]);
});

watch(idle, idleValue => {
  if (idleValue) {
    inc();
    if (route?.meta?.requireAuth) {
      navbar_onLogout();
      reset(); // restarts the idle timer. Does not change lastActive value
    }
  }
});
</script>

<template>
  <section id="default-layout" class="w-dvw h-dvh relative inset-0 z-0 flex flex-col overflow-x-hidden">
    <BaseNavbar />
    <BaseDrawerNavbar />

    <template v-if="!navbar_searchQuery && pageTitle">
      <BaseHeader :page-title="pageTitle" :page-sub-title="pageSubTitle" />
    </template>

    <section id="default-body" class="grow z-50 relative">
      <BaseContainer>
        <slot />
      </BaseContainer>
    </section>

    <BaseFooter v-if="!hideFooter" />
    <BaseDialogConfirmationLogout />
    <BaseBottomSheetConfirmationLogout />
    <PrimeVueToast />

    <!-- Desktop modal login -->
    <PrimeVueDialog
      v-if="!authentication_isLoggedIn"
      v-model:visible="authentication_isOpenModalLogin"
      modal
      class="!w-[441px] !hidden sm:!flex"
      :draggable="false"
      :closable="true"
      :close-on-escape="true"
      :dismissable-mask="true"
      :pt="{
        header: '!hidden',
        content: '!px-5',
        mask: 'sm:!flex !hidden',
      }"
    >
      <template #default>
        <AuthenticationModalLoginForm />
      </template>
    </PrimeVueDialog>

    <!-- Mobile bottom sheet login -->
    <PrimeVueDrawer
      v-if="!authentication_isLoggedIn"
      id="drawer-login-mobile"
      v-model:visible="authentication_isOpenModalLogin"
      position="bottom"
      class="!h-[620px] !rounded-t-2xl sm:!hidden !flex"
      :pt="{
        header: '!hidden',
        content: '!px-6 !pt-4',
        mask: '!flex sm:!hidden',
      }"
    >
      <template #header> </template>

      <template #default>
        <AuthenticationModalLoginForm />
      </template>
    </PrimeVueDrawer>
  </section>
</template>
