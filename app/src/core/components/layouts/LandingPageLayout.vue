<script setup lang="ts">
// Services
import { useProductService } from '~/app/src/modules/product/services/product.service';
import { useAppService } from '../../services/app.service';
import { useNavbarService } from '../../services/navbar.service';
import { useCartService } from '~/app/src/modules/cart/services/cart.service';
import { useNotificationService } from '~/app/src/modules/notification/services/notification.service';
import { useAuthenticationService } from '~/app/src/modules/authentication/services/authentication.service';
import { useAuthenticationStore } from '~/app/src/modules/authentication/stores/authentication.store';
import { useConfigurationService } from '~/app/src/modules/configurations/services/configurations.service';
import { useCatalog } from '~/app/src/modules/dashboard/services/catalog.service';

/**
 * Props
 */
defineProps<{ pageTitle?: string; pageSubTitle?: string; hideFooter?: boolean }>();

/**
 * @description Destructure all the data and methods what we need
 */

const message = ref('');
const socketConnected = ref(false);

/**
 * @description Injecting dependencies
 */
const { $websocket } = useNuxtApp();
const authStore = useAuthenticationStore();

const { app_headerMenu, app_headerMenuIsLoading, app_fetchHeaderMenu, app_isLoading } = useAppService();
const {
  config_isLoading,
  CUSTOM_ORDER_LOGO_PRICE,
  CUSTOM_ORDER_TEXT_PRICE,
  MIN_REGULAR_ORDER_QTY,
  MIN_REGULAR_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_QTY,
  config_hideInquiryForm,
  configuration_fetchConfigurations,
} = useConfigurationService();
const { authentication_isOpenModalLogin, authentication_isLoggedIn } = useAuthenticationService();
const {
  product_searchData,
  product_searchHistory,
  product_searchIsLoading,
  product_saveSearchHistory,
  product_clearSearchHistory,
  product_searchProducts,
  product_searchRecommendationData,
  product_searchRecommendationIsLoading,
  product_getSearchProductRecommendation,
} = useProductService();

const {
  navbar_isOpenDialogConfirmationLogout,
  navbar_isOpenBottomSheetConfirmationLogout,
  navbar_isOpenDrawer,
  navbar_onApplySearch,
  navbar_onClearSearch,
  navbar_onCloseDialogConfirmationLogout,
  navbar_onCloseBottomSheetConfirmationLogout,
  navbar_onLogout,
  navbar_logoutIsLoading,
  navbar_onOpenDialogConfirmationLogout,
  navbar_searchQuery,
  navbar_hasToken,
  navbar_width,
  navbar_onHandleResize,
} = useNavbarService();

const {
  notification_list,
  notification_count,
  notification_detailData,
  notification_pagination,
  notification_detailIsLoading,
  notification_queryParamsGetListNotification,
  notification_readAll,
  notification_getListNotifications,
  notification_getNotificationsCount,
  notification_getNotificationDetail,
  notification_onDeleteNotification,
} = useNotificationService();

const { catalog_tempQueryParamsOfProducts, catalog_queryParamsOfProducts, catalog_fetchProducts } = useCatalog();

const { cart_dataLimit, cart_getList } = useCartService();

/**
 * @description Provide all the data and methods what we need
 */
provide('app', {
  app_headerMenu,
  app_headerMenuIsLoading,
  app_isLoading,
});
provide('configurations', {
  config_isLoading,
  CUSTOM_ORDER_LOGO_PRICE,
  CUSTOM_ORDER_TEXT_PRICE,
  MIN_REGULAR_ORDER_QTY,
  MIN_REGULAR_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_AMOUNT,
  MIN_CUSTOM_ORDER_QTY,
  config_hideInquiryForm,
  configuration_fetchConfigurations,
});

provide('auth', {
  authentication_isOpenModalLogin,
  authentication_isLoggedIn,
});
provide('main', {
  navbar_isOpenDialogConfirmationLogout,
  navbar_isOpenBottomSheetConfirmationLogout,
  navbar_isOpenDrawer,
  navbar_onApplySearch,
  navbar_onClearSearch,
  navbar_onCloseDialogConfirmationLogout,
  navbar_onCloseBottomSheetConfirmationLogout,
  navbar_onLogout,
  navbar_logoutIsLoading,
  navbar_onOpenDialogConfirmationLogout,
  navbar_searchQuery,
  navbar_width,
  navbar_onHandleResize,
});
provide('notification', {
  notification_list,
  notification_count,
  notification_detailData,
  notification_pagination,
  notification_detailIsLoading,
  notification_queryParamsGetListNotification,
  notification_readAll,
  notification_getListNotifications,
  notification_getNotificationDetail,
  notification_onDeleteNotification,
});
provide('productSearch', {
  product_searchData,
  product_searchHistory,
  product_searchIsLoading,
  product_saveSearchHistory,
  product_clearSearchHistory,
  product_searchProducts,
  product_searchRecommendationData,
  product_searchRecommendationIsLoading,
  product_getSearchProductRecommendation,
});
provide('navbarCart', {
  cart_dataLimit,
  cart_getList,
});
provide('rootCatalog', {
  catalog_tempQueryParamsOfProducts,
  catalog_queryParamsOfProducts,
  catalog_fetchProducts,
});
/**
 * @description Lifecycle hook that is called after data-bound properties of a directive are initialized.
 */
onBeforeMount(async () => {
  $websocket.connector.pusher.connection.bind('disconnected', function () {
    socketConnected.value = false;
    message.value = 'terputus dengan socket, memuat ulang aplikasi dalam 30 detik';
    setTimeout(() => {
      window.location.reload();
    }, 30000);
  });
  $websocket.connector.pusher.connection.bind('unavailable', function () {
    socketConnected.value = false;
    message.value = 'Hubungan socket terputus, menghubungkan kembali...';
  });
  $websocket.connector.pusher.connection.bind('connecting', () => {
    socketConnected.value = false;
    message.value = 'Mohon Menunggu';
  });
  setTimeout(() => {
    if (authStore?.authentication_accessToken && authStore?.authentication_profileData?.customer_id) {
      $websocket
        .channel('user.' + authStore?.authentication_profileData?.customer_id)
        .on('pusher:subscription_succeeded', () => {
          socketConnected.value = true;
        })
        .listen('.notification.new', () => {
          setTimeout(() => {
            notification_getListNotifications();
            notification_getNotificationsCount();
          }, 250);
        });
    }
  }, 2000);
});
onMounted(async () => {
  if (navbar_hasToken()) {
    await Promise.allSettled([
      notification_getListNotifications(),
      notification_getNotificationsCount(),
      app_fetchHeaderMenu(),
      cart_getList(),
    ]);
  }
  await Promise.allSettled([app_fetchHeaderMenu(), configuration_fetchConfigurations()]);
});
</script>

<template>
  <section id="landing-page-layout" class="w-dvw h-dvh relative inset-0 z-0 overflow-x-hidden">
    <BaseNavbar :is-landing-page="true" />
    <BaseDrawerNavbar />
    <slot></slot>
    <BaseFooter />
    <BaseDialogConfirmationLogout />
    <BaseBottomSheetConfirmationLogout />
    <PrimeVueToast />
  </section>
</template>
