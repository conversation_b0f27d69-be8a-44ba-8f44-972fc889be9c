<script setup lang="ts">
const onClickLogo = () => {
  window.location.href = '/';
};
</script>

<template>
  <section id="layout-authentication" class="w-dvw h-dvh relative inset-0 z-0 overflow-y-hidden">
    <header class="inset-0 z-100 px-20 border-t-4 border-solid border-header-orange h-[70px] flex items-center">
      <div class="flex md:items-center md:justify-center cursor-pointer -ml-4 md:-ml-6" @click="onClickLogo">
        <NuxtImg src="/images/logo-black.svg" alt="app-logo" class="w-[120px] md:w-[150px]" />
      </div>
    </header>
    <slot />
  </section>
</template>
