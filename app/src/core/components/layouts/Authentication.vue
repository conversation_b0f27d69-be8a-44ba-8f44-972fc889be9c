
<script setup lang="ts">
const onClickLogo = () => {
  window.location.href = '/';
};
</script>
<template>
  <section id="layout-authentication" class="w-dvw h-dvh relative inset-0 z-0 flex flex-col overflow-x-hidden">
    <header
      class="inset-0 px-20 sm:border-t-2 border-t-0 sm:border-b-0 border-b-2 border-solid border-header-orange max-h-fit flex sm:justify-start justify-center py-1 md:py-4 bg-white z-10"
    >
      <NuxtImg src="/images/logo-black.svg" alt="app-logo" class="w-[150px] cursor-pointer" @click="onClickLogo"/>
    </header>
    <section id="default-body" class="grow">
      <slot />
    </section>
    <PrimeVueToast />
  </section>
</template>
