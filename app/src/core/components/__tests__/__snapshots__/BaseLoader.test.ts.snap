// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`BaseLoader > matches component snapshot with text slot 1`] = `
"<section data-v-71df45c0="" class="loader-section" aria-busy="true" aria-label="Loading">
  <div data-v-71df45c0="" class="box-spinner">
    <p>Loading your content...</p>
    <div data-v-71df45c0="" class="spinner" aria-hidden="true"></div>
  </div>
</section>"
`;

exports[`BaseLoader > matches component snapshot without slot 1`] = `
"<section data-v-71df45c0="" class="loader-section" aria-busy="true" aria-label="Loading">
  <div data-v-71df45c0="" class="box-spinner">
    <div data-v-71df45c0="" class="spinner" aria-hidden="true"></div>
  </div>
</section>"
`;
