import { ref } from 'vue';
import { mount } from '@vue/test-utils';
import type { VueWrapper } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import SearchBarComponent from '../base/BaseSearchBar.vue';

const MockNuxtImg = {
  name: 'NuxtImg',
  props: ['src', 'alt', 'class'],
  template: '<img :src="src" :alt="alt" :class="class" />',
};

// Mock PrimeVueButton component
const MockPrimeVueButton = {
  name: 'PrimeVueButton',
  props: ['class'],
  template: '<button :class="class" @click="$emit(\'click\')"><slot /></button>',
  emits: ['click'],
};

// Mock Vue's ref function
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue');
  return {
    ...actual,
    ref: vi.fn(value => ({ value })),
  };
});

describe('SearchBarComponent', () => {
  let wrapper: VueWrapper;
  const mockHandleSearch = vi.fn();

  const defaultProps = {
    handleSearch: mockHandleSearch,
    placeholder: 'Search...',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    vi.mocked(ref).mockImplementation(value => ({ value }));
  });

  const createWrapper = (props = {}) => {
    return mount(SearchBarComponent, {
      props: { ...defaultProps, ...props },
      global: {
        components: {
          NuxtImg: MockNuxtImg,
          PrimeVueButton: MockPrimeVueButton,
        },
      },
    });
  };

  describe('Component Rendering', () => {
    it('should render input with correct placeholder', () => {
      const customPlaceholder = 'Enter search term...';
      wrapper = createWrapper({ placeholder: customPlaceholder });

      const input = wrapper.find('input[type="text"]');
      expect(input.attributes('placeholder')).toBe(customPlaceholder);
    });

    it('should apply correct CSS classes to main container', () => {
      wrapper = createWrapper();

      const mainSection = wrapper.find('#search-bar');
      expect(mainSection.classes()).toContain('flex');
      expect(mainSection.classes()).toContain('flex-row');
      expect(mainSection.classes()).toContain('w-fit');
      expect(mainSection.classes()).toContain('justify-between');
      expect(mainSection.classes()).toContain('items-center');
    });

    it('should apply correct CSS classes to input container', () => {
      wrapper = createWrapper();

      const inputContainer = wrapper.find('#search-bar > section');
      expect(inputContainer.classes()).toContain('flex');
      expect(inputContainer.classes()).toContain('items-center');
      expect(inputContainer.classes()).toContain('border');
      expect(inputContainer.classes()).toContain('border-gray-300');
    });
  });

  describe('User Interactions', () => {
    it('should update searchQuery when typing in input', async () => {
      wrapper = createWrapper();

      const input = wrapper.find('input[type="text"]');
      await input.setValue('test search');

      // Since we're mocking ref, we need to verify the v-model binding differently
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      expect(input.element.value).toBe('test search');
    });

    it('should not call handleSearch when other keys are pressed', async () => {
      wrapper = createWrapper();

      const input = wrapper.find('input[type="text"]');
      await input.setValue('search term');

      await input.trigger('keyup', { key: 'Escape' });
      await input.trigger('keyup', { key: 'Tab' });
      await input.trigger('keyup', { key: 'Space' });

      expect(mockHandleSearch).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle case-sensitive key comparison', async () => {
      wrapper = createWrapper();

      const input = wrapper.find('input[type="text"]');
      await input.setValue('test');

      // Test different cases of 'enter'
      await input.trigger('keyup', { key: 'enter' }); // lowercase
      await input.trigger('keyup', { key: 'ENTER' }); // uppercase
      await input.trigger('keyup', { key: 'Enter' }); // proper case

      // Only the proper case 'Enter' should trigger the search
      expect(mockHandleSearch).toHaveBeenCalledTimes(1);
      expect(mockHandleSearch).toHaveBeenCalledWith('');
    });

    it('should handle modifier keys with Enter', async () => {
      wrapper = createWrapper();

      const input = wrapper.find('input[type="text"]');
      await input.setValue('modified enter');

      // Test Enter with various modifier keys
      await input.trigger('keyup', { key: 'Enter', ctrlKey: true });
      await input.trigger('keyup', { key: 'Enter', shiftKey: true });
      await input.trigger('keyup', { key: 'Enter', altKey: true });

      // Should still call handleSearch for all cases
      expect(mockHandleSearch).toHaveBeenCalledTimes(3);
    });
  });

  describe('Component Structure and Accessibility', () => {
    it('should have proper semantic structure', () => {
      wrapper = createWrapper();

      const mainSection = wrapper.find('#search-bar');
      const iconSection = wrapper.find('#icon-search');
      const input = wrapper.find('input[type="text"]');

      expect(mainSection.element.tagName).toBe('SECTION');
      expect(iconSection.element.tagName).toBe('SECTION');
      expect(input.element.tagName).toBe('INPUT');
    });

    it('should have accessible image alt text', () => {
      wrapper = createWrapper();

      const searchIcon = wrapper.findComponent(MockNuxtImg);
      expect(searchIcon.props('alt')).toBe('search-icon');
    });

    it('should maintain input focus capabilities', () => {
      wrapper = createWrapper();

      const input = wrapper.find('input[type="text"]');
      expect(input.attributes('type')).toBe('text');
      expect(input.classes()).toContain('outline-none');
    });
  });

  describe('Component Cleanup', () => {
    it('should properly unmount without errors', () => {
      wrapper = createWrapper();

      expect(() => {
        wrapper.unmount();
      }).not.toThrow();
    });

    it('should handle multiple mount/unmount cycles', () => {
      for (let i = 0; i < 3; i++) {
        wrapper = createWrapper();
        expect(wrapper.find('#search-bar').exists()).toBe(true);
        wrapper.unmount();
      }
    });
  });
});
