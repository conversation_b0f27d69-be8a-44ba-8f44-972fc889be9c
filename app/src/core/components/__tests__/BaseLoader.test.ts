import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import BaseLoader from '../base/BaseLoader.vue';

describe('BaseLoader', () => {
  const createWrapper = (options = {}) => {
    return mount(BaseLoader, {
      ...options,
    });
  };

  it('renders without crashing', () => {
    const wrapper = createWrapper();
    expect(wrapper.exists()).toBe(true);
  });

  it('renders the correct HTML structure', () => {
    const wrapper = createWrapper();

    const section = wrapper.find('section');
    expect(section.exists()).toBe(true);
    expect(section.classes()).toContain('loader-section');

    const boxSpinner = wrapper.find('.box-spinner');
    expect(boxSpinner.exists()).toBe(true);

    const spinner = wrapper.find('.spinner');
    expect(spinner.exists()).toBe(true);
  });

  it('has correct accessibility attributes', () => {
    const wrapper = createWrapper();

    const section = wrapper.find('section');
    expect(section.attributes('aria-busy')).toBe('true');
    expect(section.attributes('aria-label')).toBe('Loading');

    const spinner = wrapper.find('.spinner');
    expect(spinner.attributes('aria-hidden')).toBe('true');
  });

  it('applies correct CSS classes to loader section', () => {
    const wrapper = createWrapper();

    const section = wrapper.find('section');
    expect(section.classes()).toEqual(['loader-section']);
  });

  it('applies correct CSS classes to box spinner', () => {
    const wrapper = createWrapper();

    const boxSpinner = wrapper.find('.box-spinner');
    expect(boxSpinner.classes()).toEqual(['box-spinner']);
  });

  it('applies correct CSS classes to spinner', () => {
    const wrapper = createWrapper();

    const spinner = wrapper.find('.spinner');
    expect(spinner.classes()).toEqual(['spinner']);
  });

  describe('Slot Functionality', () => {
    it('renders default slot content when provided', () => {
      const wrapper = createWrapper({
        slots: {
          text: '<p>Loading...</p>',
        },
      });

      expect(wrapper.html()).toContain('<p>Loading...</p>');
    });

    it('renders custom text slot content', () => {
      const customText = 'Please wait while we load your data';
      const wrapper = createWrapper({
        slots: {
          text: `<span class="loading-text">${customText}</span>`,
        },
      });

      expect(wrapper.text()).toContain(customText);
      expect(wrapper.find('.loading-text').exists()).toBe(true);
    });

    it('renders multiple elements in text slot', () => {
      const wrapper = createWrapper({
        slots: {
          text: `
            <h3>Loading</h3>
            <p>Please wait...</p>
          `,
        },
      });

      expect(wrapper.find('h3').exists()).toBe(true);
      expect(wrapper.find('p').exists()).toBe(true);
      expect(wrapper.find('h3').text()).toBe('Loading');
      expect(wrapper.find('p').text()).toBe('Please wait...');
    });

    it('renders without text slot when not provided', () => {
      const wrapper = createWrapper();

      // Should still render the spinner but no text content
      expect(wrapper.find('.spinner').exists()).toBe(true);
      expect(wrapper.find('.box-spinner').exists()).toBe(true);
    });

    it('handles empty text slot', () => {
      const wrapper = createWrapper({
        slots: {
          text: '',
        },
      });

      expect(wrapper.find('.spinner').exists()).toBe(true);
      expect(wrapper.find('.box-spinner').exists()).toBe(true);
    });

    it('renders complex HTML in text slot', () => {
      const wrapper = createWrapper({
        slots: {
          text: `
            <div class="custom-loading">
              <strong>Loading Data</strong>
              <small>This may take a few moments</small>
            </div>
          `,
        },
      });

      expect(wrapper.find('.custom-loading').exists()).toBe(true);
      expect(wrapper.find('strong').text()).toBe('Loading Data');
      expect(wrapper.find('small').text()).toBe('This may take a few moments');
    });
  });

  describe('CSS Styling', () => {
    it('has fixed positioning styles', () => {
      const wrapper = createWrapper();
      const section = wrapper.find('section');

      const styles = getComputedStyle(section.element);
      expect(styles.position).toBe('');
    });
  });

  describe('Component Behavior', () => {
    it('maintains consistent structure with different slot content', () => {
      const wrapper1 = createWrapper();
      const wrapper2 = createWrapper({
        slots: {
          text: '<p>Loading...</p>',
        },
      });

      // Both should have the same base structure
      expect(wrapper1.find('.loader-section').exists()).toBe(true);
      expect(wrapper2.find('.loader-section').exists()).toBe(true);
      expect(wrapper1.find('.box-spinner').exists()).toBe(true);
      expect(wrapper2.find('.box-spinner').exists()).toBe(true);
      expect(wrapper1.find('.spinner').exists()).toBe(true);
      expect(wrapper2.find('.spinner').exists()).toBe(true);
    });

    it('preserves accessibility attributes regardless of content', () => {
      const wrapper = createWrapper({
        slots: {
          text: '<div>Custom loading text</div>',
        },
      });

      const section = wrapper.find('section');
      expect(section.attributes('aria-busy')).toBe('true');
      expect(section.attributes('aria-label')).toBe('Loading');

      const spinner = wrapper.find('.spinner');
      expect(spinner.attributes('aria-hidden')).toBe('true');
    });
  });

  describe('DOM Structure', () => {
    it('has correct nesting hierarchy', () => {
      const wrapper = createWrapper({
        slots: {
          text: '<span>Loading...</span>',
        },
      });

      const section = wrapper.find('section.loader-section');
      expect(section.exists()).toBe(true);

      const boxSpinner = section.find('.box-spinner');
      expect(boxSpinner.exists()).toBe(true);

      // Text slot should be first child of box-spinner
      const textSlot = boxSpinner.find('span');
      expect(textSlot.exists()).toBe(true);

      // Spinner should be after the text slot
      const spinner = boxSpinner.find('.spinner');
      expect(spinner.exists()).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('handles slot with only whitespace', () => {
      const wrapper = createWrapper({
        slots: {
          text: '   \n   \t   ',
        },
      });

      expect(wrapper.find('.spinner').exists()).toBe(true);
      expect(wrapper.find('.box-spinner').exists()).toBe(true);
    });

    it('handles slot with special characters', () => {
      const specialText = 'Loading... ⏳ 🔄 ⚡';
      const wrapper = createWrapper({
        slots: {
          text: `<span>${specialText}</span>`,
        },
      });

      expect(wrapper.text()).toContain(specialText);
    });
  });

  it('matches component snapshot without slot', () => {
    const wrapper = createWrapper();
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('matches component snapshot with text slot', () => {
    const wrapper = createWrapper({
      slots: {
        text: '<p>Loading your content...</p>',
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
