import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';

// primevue button
import PrimeVueButton from 'primevue/button';

describe('PrimeVueButton', () => {
  // Render button
  it('renders properly with default props', () => {
    const wrapper = mount(PrimeVueButton);
    expect(wrapper.find('button').exists()).toBe(true);
    expect(wrapper.classes()).toContain('p-button');
  });

  it('renders button with label', () => {
    const label = 'Submit';
    const wrapper = mount(PrimeVueButton, {
      props: { label },
    });
    expect(wrapper.find('.p-button-label').text()).toBe(label);
  });

  it('renders button with slot content', () => {
    const wrapper = mount(PrimeVueButton, {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      slots: {
        default: 'Click me',
      },
    });
    expect(wrapper.text()).toContain('Click me');
  });

  // Icon tests
  it('renders button with left icon', () => {
    const wrapper = mount(PrimeVueButton, {
      props: {
        icon: 'pi pi-check',
        label: 'Submit',
      },
    });
    const icon = wrapper.find('.p-button-icon');
    expect(icon.exists()).toBe(true);
    expect(icon.classes()).toContain('pi-check');
    expect(icon.classes()).toContain('p-button-icon-left');
  });

  it('renders button with right icon', () => {
    const wrapper = mount(PrimeVueButton, {
      props: {
        icon: 'pi pi-arrow-right',
        iconPos: 'right',
        label: 'Next',
      },
    });
    const icon = wrapper.find('.p-button-icon');
    expect(icon.exists()).toBe(true);
    expect(icon.classes()).toContain('pi-arrow-right');
    expect(icon.classes()).toContain('p-button-icon-right');
  });

  it('renders button loading icon when loading is true', () => {
    const wrapper = mount(PrimeVueButton, {
      props: {
        loading: true,
      },
    });
    expect(wrapper.find('.p-button-loading-icon').exists()).toBe(true);
    expect(wrapper.classes()).toContain('p-button-loading');
  });

  // Style variants tests
  it('applies button severity classes correctly', () => {
    const severities = ['secondary', 'success', 'info', 'warning', 'danger'];

    severities.forEach(severity => {
      const wrapper = mount(PrimeVueButton, {
        props: { severity },
      });
      expect(wrapper.classes()).toContain(`p-button-${severity}`);
    });
  });

  it('applies button size classes correctly', () => {
    const smallWrapper = mount(PrimeVueButton, {
      props: { size: 'small' },
    });
    expect(smallWrapper.classes()).toContain('p-button-sm');

    const largeWrapper = mount(PrimeVueButton, {
      props: { size: 'large' },
    });
    expect(largeWrapper.classes()).toContain('p-button-lg');
  });

  it('applies button style variant classes correctly', () => {
    const variants = {
      outlined: 'p-button-outlined',
      text: 'p-button-text',
      raised: 'p-button-raised',
      rounded: 'p-button-rounded',
    };

    for (const [prop, className] of Object.entries(variants)) {
      const props = { [prop]: true };
      const wrapper = mount(PrimeVueButton, { props });
      expect(wrapper.classes()).toContain(className);
    }
  });

  // Behavior tests
  it('emits click event when clicked', async () => {
    const wrapper = mount(PrimeVueButton);
    await wrapper.trigger('click');
    expect(wrapper.emitted()).toHaveProperty('click');
    expect(wrapper.emitted().click).toHaveLength(1);
  });

  it('button does not emit click event when disabled', async () => {
    const wrapper = mount(PrimeVueButton, {
      props: { disabled: true },
    });
    await wrapper.trigger('click');
    expect(wrapper.emitted().click).toBeUndefined();
  });

  it('button does not emit click event when loading', async () => {
    const wrapper = mount(PrimeVueButton, {
      props: { loading: true },
    });
    await wrapper.trigger('click');
    expect(wrapper.emitted().click).toBeUndefined();
  });

  it('renders button with correct type attribute', () => {
    const wrapper = mount(PrimeVueButton, {
      props: { type: 'submit' },
    });
    expect(wrapper.attributes('type')).toBe('submit');
  });

  it('defaults button to type="button" when type not specified', () => {
    const wrapper = mount(PrimeVueButton);
    expect(wrapper.attributes('type')).toBe('button');
  });

  it('sets aria-label attribute correctly', () => {
    const label = 'Submit Form';
    const wrapper = mount(PrimeVueButton, {
      props: { label },
    });
    expect(wrapper.attributes('aria-label')).toBe(label);
  });

  it('applies button disabled attribute when disabled', () => {
    const wrapper = mount(PrimeVueButton, {
      props: { disabled: true },
    });
    expect(wrapper.attributes('disabled')).toBe('');
  });

  it('applies button disabled attribute when loading', () => {
    const wrapper = mount(PrimeVueButton, {
      props: { loading: true },
    });
    expect(wrapper.attributes('disabled')).toBe('');
  });

  it('applies button icon-only class when only icon is provided', () => {
    const wrapper = mount(PrimeVueButton, {
      props: {
        icon: 'pi pi-trash',
        label: '',
      },
    });
    expect(wrapper.classes()).toContain('p-button-icon-only');
  });
});
