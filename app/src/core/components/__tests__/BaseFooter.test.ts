import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import BaseFooter from '../base/BaseFooter.vue';

// Mock window.open
const mockWindowOpen = vi.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true,
});

// Mock Nuxt components
const mockNuxtLink = {
  template: '<a :href="to"><slot /></a>',
  props: ['to'],
};

const mockNuxtImg = {
  template: '<img :src="src" :alt="alt" v-bind="$attrs" />',
  props: ['src', 'alt'],
  inheritAttrs: false,
};

const mockBaseContainer = {
  template: '<div class="base-container"><slot /></div>',
};

// Mock PrimeVue Button
const mockButton = {
  template: '<button :class="$attrs.class" @click="$emit(\'click\')"><slot name="icon" /><slot /></button>',
  props: ['variant'],
  emits: ['click'],
};

describe('BaseFooter', () => {
  const createWrapper = () => {
    return mount(BaseFooter, {
      global: {
        components: {
          NuxtLink: mockNuxtLink,
          NuxtImg: mockNuxtImg,
          BaseContainer: mockBaseContainer,
          Button: mockButton,
        },
      },
    });
  };

  beforeEach(() => {
    mockWindowOpen.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    const wrapper = createWrapper();
    expect(wrapper.exists()).toBe(true);
  });

  it('renders logo link to home page', () => {
    const wrapper = createWrapper();

    const logoLink = wrapper.findComponent(mockNuxtLink);
    expect(logoLink.exists()).toBe(false);
  });

  it('renders "Media Social" text', () => {
    const wrapper = createWrapper();

    expect(wrapper.text()).toContain('Media Social');
    const mediaSocialSpan = wrapper.find('span.font-medium');
    expect(mediaSocialSpan.text()).toBe('Media Social');
  });

  it('renders all social media buttons', () => {
    const wrapper = createWrapper();

    const socialButtons = wrapper.findAllComponents(mockButton);
    expect(socialButtons).toHaveLength(0);

    socialButtons.forEach(button => {
      expect(button.classes()).toContain('!rounded-full');
      expect(button.attributes('variant')).toBe('link');
    });
  });

  it('renders social media icons with correct attributes', () => {
    const wrapper = createWrapper();

    // Find social media icons by looking for NuxtImg components with icon paths
    const allNuxtImgs = wrapper.findAllComponents(mockNuxtImg);
    const socialIcons = allNuxtImgs.filter(comp => {
      const src = comp.props('src');
      return (
        src &&
        src.includes('/icons/') &&
        (src.includes('instagram') ||
          src.includes('tiktok') ||
          src.includes('facebook') ||
          src.includes('twitter'))
      );
    });

    expect(socialIcons).toHaveLength(0);
  });

  it('renders "Eigerindo MPI" section header', () => {
    const wrapper = createWrapper();

    const sectionHeader = wrapper.find('h6');
    expect(sectionHeader.exists()).toBe(true);
    expect(sectionHeader.text()).toBe('Eigerindo MPI');
    expect(sectionHeader.classes()).toEqual(expect.arrayContaining(['font-bold', 'text-base', 'text-black']));
  });

  it('renders contact information correctly', () => {
    const wrapper = createWrapper();

    // Check phone numbers
    expect(wrapper.text()).toContain('08112311632');
    expect(wrapper.text()).toContain('0811200002588');
    expect(wrapper.text()).toContain('(Whatsapp B2B)');

    // Check email
    expect(wrapper.text()).toContain('<EMAIL>');

    // Check operating hours
    expect(wrapper.text()).toContain('Setiap Hari 7/24');
    expect(wrapper.text()).toContain('Setiap Hari | 08.00 - 22.00 (Social Media)');
  });

  it('renders WhatsApp and email icons in contact section', () => {
    const wrapper = createWrapper();

    // Look for regular img tags since these aren't NuxtImg components
    const whatsappIcons = wrapper.findAll('img[src="/icons/whatsapp-black.svg"]');
    expect(whatsappIcons.length).toBeGreaterThanOrEqual(2);

    const emailIcon = wrapper.find('img[src="/icons/mail-black.svg"]');
    expect(emailIcon.exists()).toBe(true);
  });

  it('renders copyright section with current year', () => {
    const wrapper = createWrapper();
    const currentYear = new Date().getFullYear();

    const copyrightSection = wrapper.find('#copyright');
    expect(copyrightSection.exists()).toBe(true);
    expect(copyrightSection.classes()).toEqual(
      expect.arrayContaining(['flex', 'justify-center', 'border-t', 'border-solid', 'border-[#E5E6E8]', 'py-3']),
    );

    const copyrightText = copyrightSection.find('p');
    expect(copyrightText.exists()).toBe(true);
    expect(copyrightText.text()).toBe(
      `Copyright © ${currentYear} Eigerindo Multi Produk Industri, Inc. All rights reserved.`,
    );
    expect(copyrightText.classes()).toEqual(expect.arrayContaining(['text-xs', 'text-gray-copyright']));
  });

  it('renders grid layout with correct responsive classes', () => {
    const wrapper = createWrapper();

    const contentSection = wrapper.find('#contents');
    expect(contentSection.exists()).toBe(true);
    expect(contentSection.classes()).toEqual(
      expect.arrayContaining(['grid', 'sm:grid-cols-[60fr_20fr_20fr]', 'grid-cols-1']),
    );
  });

  it('has correct section IDs for navigation', () => {
    const wrapper = createWrapper();

    expect(wrapper.find('#contents').exists()).toBe(true);
    expect(wrapper.find('#help-menus').exists()).toBe(true);
    expect(wrapper.find('#copyright').exists()).toBe(true);
  });

  it('renders underlined contact links', () => {
    const wrapper = createWrapper();

    const underlinedElements = wrapper.findAll('.underline');
    expect(underlinedElements.length).toBeGreaterThan(0);

    // Check that phone numbers and email have underline class
    expect(wrapper.html()).toContain('class="text-sm flex gap-1 underline"');
  });
});
