import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import BaseLabel from '../base/BaseLabel.vue';

describe('BaseLabel', () => {
  const createWrapper = (props = {}) => {
    return mount(BaseLabel, {
      props: {
        text: 'Test Label',
        ...props,
      },
    });
  };

  it('renders without crashing', () => {
    const wrapper = createWrapper();
    expect(wrapper.exists()).toBe(true);
  });

  it('renders the correct HTML structure', () => {
    const wrapper = createWrapper();

    const labelDiv = wrapper.find('div');
    expect(labelDiv.exists()).toBe(true);
  });

  it('displays the text prop correctly', () => {
    const testText = 'Sample Label Text';
    const wrapper = createWrapper({ text: testText });

    expect(wrapper.text()).toBe(testText);
  });

  describe('Color Prop Override', () => {
    it('applies provided color prop when text does not match predefined cases', () => {
      const wrapper = createWrapper({
        text: 'custom text',
        color: 'success',
      });
    });

    it('ignores color prop when text matches predefined case', () => {
      const wrapper = createWrapper({
        text: 'baru',
        color: 'success',
      });
    });

    it('applies info color correctly', () => {
      const wrapper = createWrapper({
        text: 'custom text',
        color: 'info',
      });
    });

    it('applies warning color via prop correctly', () => {
      const wrapper = createWrapper({
        text: 'custom text',
        color: 'warning',
      });
    });
  });

  describe('Click Handling', () => {
    it('emits click event when clicked', async () => {
      const wrapper = createWrapper();

      await wrapper.trigger('click');

      expect(wrapper.emitted()).toHaveProperty('click');
      expect(wrapper.emitted('click')).toHaveLength(1);
    });

    it('emits click event multiple times when clicked multiple times', async () => {
      const wrapper = createWrapper();

      await wrapper.trigger('click');
      await wrapper.trigger('click');
      await wrapper.trigger('click');

      expect(wrapper.emitted('click')).toHaveLength(3);
    });

    it('calls onClick prop when provided', async () => {
      const mockOnClick = vi.fn();
      const wrapper = createWrapper({ onClick: mockOnClick });

      await wrapper.trigger('click');

      // Note: The onClick prop isn't actually used in the template,
      // only the emit is triggered. This test verifies the prop can be passed.
      expect(wrapper.emitted('click')).toHaveLength(1);
    });
  });

  describe('Computed Properties', () => {
    it('updates classes when text prop changes', async () => {
      const wrapper = createWrapper({ text: 'baru' });

      // Initially warning colors
      expect(wrapper.classes()).toContain('text-[#FF5A00]');

      // Change to success text
      await wrapper.setProps({ text: 'pembayaran' });
      expect(wrapper.classes()).toContain('text-[#05964C]');

      // Change to danger text
      await wrapper.setProps({ text: 'belum dibayar' });
      expect(wrapper.classes()).toContain('text-[#E9151D]');
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined text gracefully', () => {
      const wrapper = createWrapper({ text: undefined as any });
    });
  });

  describe('getSeverity Function', () => {
    it('returns correct severity for each predefined case', () => {
      // Test through component behavior since getSeverity is internal
      const testCases = [
        { text: 'baru', expectedClass: 'text-[#FF5A00]' },
        { text: 'pembayaran', expectedClass: 'text-[#05964C]' },
        { text: 'lunas', expectedClass: 'text-[#05964C]' },
        { text: 'belum dibayar', expectedClass: 'text-[#E9151D]' },
      ];

      testCases.forEach(({ text, expectedClass }) => {
        const wrapper = createWrapper({ text });
        expect(wrapper.classes()).toContain(expectedClass);
      });
    });
  });

  it('matches component snapshot', () => {
    const wrapper = createWrapper({
      text: 'Test Label',
      color: 'primary',
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
