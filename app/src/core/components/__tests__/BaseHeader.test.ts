import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import BaseHeader from '../base/BaseHeader.vue';

// Mock the child components
const mockBaseContainer = {
  template: '<div class="base-container"><slot /></div>',
};

const mockBaseBreadcrumb = {
  template: '<nav class="breadcrumb">Home > Current Page</nav>',
};

describe('BaseHeader', () => {
  const createWrapper = (props = {}) => {
    return mount(BaseHeader, {
      props,
      global: {
        components: {
          BaseContainer: mockBaseContainer,
          BaseBreadcrumb: mockBaseBreadcrumb,
        },
      },
    });
  };

  it('renders without crashing', () => {
    const wrapper = createWrapper();
    expect(wrapper.exists()).toBe(true);
  });

  it('renders the correct HTML structure', () => {
    const wrapper = createWrapper();

    // Check main header element
    const header = wrapper.find('header');
    expect(header.exists()).toBe(true);
    expect(header.classes()).toContain('flex');
    expect(header.classes()).toContain('flex-col');
    expect(header.classes()).toContain('w-full');
    expect(header.classes()).toContain('mt-4');
  });

  it('renders page title when provided', () => {
    const pageTitle = 'Test Page Title';
    const wrapper = createWrapper({ pageTitle });

    const h1 = wrapper.find('h1');
    expect(h1.exists()).toBe(true);
    expect(h1.text()).toBe(pageTitle);
    expect(h1.classes()).toEqual(
      expect.arrayContaining(['font-bold', 'font-druk', 'text-4xl', 'leading-12', 'mt-3']),
    );
  });

  it('renders page subtitle when provided', () => {
    const pageSubTitle = 'Test page subtitle';
    const wrapper = createWrapper({ pageSubTitle });

    const subtitle = wrapper.find('p');
    expect(subtitle.exists()).toBe(true);
    expect(subtitle.text()).toBe(pageSubTitle);
    expect(subtitle.classes()).toContain('text-[#686F72]');
  });

  it('renders both title and subtitle when both are provided', () => {
    const pageTitle = 'Main Title';
    const pageSubTitle = 'Subtitle Description';
    const wrapper = createWrapper({ pageTitle, pageSubTitle });

    const h1 = wrapper.find('h1');
    const subtitle = wrapper.find('p');

    expect(h1.text()).toBe(pageTitle);
    expect(subtitle.text()).toBe(pageSubTitle);
  });

  it('renders empty title when pageTitle is not provided', () => {
    const wrapper = createWrapper();

    const h1 = wrapper.find('h1');
    expect(h1.exists()).toBe(true);
    expect(h1.text()).toBe('');
  });

  it('renders empty subtitle when pageSubTitle is not provided', () => {
    const wrapper = createWrapper();

    const subtitle = wrapper.find('p');
    expect(subtitle.exists()).toBe(true);
    expect(subtitle.text()).toBe('');
  });

  it('handles undefined props gracefully', () => {
    const wrapper = createWrapper({
      pageTitle: undefined,
      pageSubTitle: undefined,
    });

    const h1 = wrapper.find('h1');
    const subtitle = wrapper.find('p');

    expect(h1.text()).toBe('');
    expect(subtitle.text()).toBe('');
  });

  it('has correct component structure', () => {
    const wrapper = createWrapper({
      pageTitle: 'Test Title',
      pageSubTitle: 'Test Subtitle',
    });

    // Verify the complete structure
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('applies correct CSS classes to title element', () => {
    const wrapper = createWrapper({ pageTitle: 'Test' });
    const h1 = wrapper.find('h1');

    const expectedClasses = ['font-bold', 'font-druk', 'text-4xl', 'leading-12', 'mt-3'];
    expectedClasses.forEach(className => {
      expect(h1.classes()).toContain(className);
    });
  });

  it('applies correct CSS class to subtitle element', () => {
    const wrapper = createWrapper({ pageSubTitle: 'Test' });
    const subtitle = wrapper.find('p');

    expect(subtitle.classes()).toContain('text-[#686F72]');
  });
});
