export function useScreen() {
  const width = ref(import.meta.env.SSR ? 0 : window.innerWidth)
  const height = ref(import.meta.env.SSR ? 0 : window.innerHeight)

  const isMobile = computed(() => width.value < 768)

  const updateSize = () => {
    width.value = window.innerWidth
    height.value = window.innerHeight
  }

  onMounted(() => {
    window.addEventListener("resize", updateSize)
  })

  onBeforeUnmount(() => {
    window.removeEventListener("resize", updateSize)
  })

  return { width, height, isMobile }
}
