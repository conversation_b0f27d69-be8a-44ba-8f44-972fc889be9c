// Store
import { useAuthenticationStore } from '../../modules/authentication/stores/authentication.store';
import { useProfileStore } from '../../modules/profile/stores/profile.store';

export const useAuthenticate = async (): Promise<void> => {
  const store = useAuthenticationStore();
  const isAuthenticated = store.authentication_accessToken;

  const profileStore = useProfileStore();

  try {
    const result = await profileStore.fetchProfile_getProfile();
    if (!result) {
      await navigateTo('/authentication/login');
    }
  } catch {
    await navigateTo('/authentication/login');
  }

  if (!isAuthenticated) {
    console.log(!isAuthenticated, 'isAuthenticated');
    await navigateTo('/authentication/login');
  }
};
