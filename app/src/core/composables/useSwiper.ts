import { ref } from 'vue'
import type { Ref } from 'vue'
import type Swiper from 'swiper'
import type { SwiperOptions } from 'swiper/types'

export function useSwiper() {
  const swiperInstance: Ref<Swiper | null> = ref(null)
  
  const initSwiper = async (options: SwiperOptions): Promise<void> => {
    // Dynamically import Swiper to ensure it only runs on client-side
    const SwiperModule = await import('swiper')
    const SwiperClass = SwiperModule.default
    
    swiperInstance.value = new SwiperClass('.swiper-container', options)
  }
  
  const destroySwiper = (): void => {
    if (swiperInstance.value) {
      swiperInstance.value.destroy(true, true)
      swiperInstance.value = null
    }
  }
  
  return {
    swiperInstance,
    initSwiper,
    destroySwiper
  }
}