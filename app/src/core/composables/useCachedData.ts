/**
 * @description Handle business logic for using cached data. This function is usually used in asyncData or useFetch composable. For more information, you can watch the video below.
 *
 * @see https://www.youtube.com/watch?v=aQPR0xn-MMk
 */
export function useCachedData<T>(key: string): T | undefined {
  const nuxtApp = useNuxtApp();
  const data = nuxtApp.payload.data[key] || nuxtApp.static.data[key];

  if (!data) {
    return;
  }

  // Set expiration time to 24 hours
  const expirationDate = new Date(data.fetchedAt);
  expirationDate.setTime(expirationDate.getTime() + 1000 * 60 * 60 * 24);

  // Check if data is expired
  const isExpired = expirationDate.getTime() < new Date().getTime();

  if (isExpired) {
    return; // Return undefined to indicate re-fetching is needed
  }

  return data as T;
}
