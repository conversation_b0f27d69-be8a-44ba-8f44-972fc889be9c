/**
 * @description use object to form data.
 */

export function useObjectToFormData(
  payload: Record<string, unknown>,
  formData: FormData = new FormData(),
  parentKey: string | null = null,
): FormData {
  for (const key in payload) {
    if (Object.prototype.hasOwnProperty.call(payload, key)) {
      const value = payload[key];
      const formKey = parentKey ? `${parentKey}[${key}]` : key;

      if (value instanceof File || value instanceof Blob) {
        formData.append(formKey, value);
      } else if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        useObjectToFormData(value, formData, formKey);
      } else {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        formData.append(formKey, value);
      }
    }
  }

  return formData;
}
