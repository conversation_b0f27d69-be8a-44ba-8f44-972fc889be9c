import dayjs from 'dayjs';
import 'dayjs/locale/id';

/**
 * Converts a date string from DD-MM-YYYY format to YYYY-MM-DD format
 * @param {string} dateString - Date in DD-MM-YYYY format (e.g., "02-03-2025")
 * @returns {string} Date in YYYY-MM-DD format (e.g., "2025-03-02")
 */
export const convertToPayloadDate = (dateString: string): string => {
  // Split the input date string by the hyphen
  const parts = dateString.split('-');

  // Make sure we have exactly 3 parts
  if (parts.length !== 3) {
    throw new Error('Invalid date format. Expected DD-MM-YYYY');
  }

  const day = parts[0];
  const month = parts[1];
  const year = parts[2];

  // Rearrange to YYYY-MM-DD format
  return `${year}-${month}-${day}`;
};

export const useDayJsFormat = (date: string, format?: string) => {
  dayjs.locale('id');
  const _format = format ? format : 'DD/MM/YYYY';
  return dayjs(date).format(_format);
};
