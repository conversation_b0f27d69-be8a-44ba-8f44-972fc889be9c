import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  useCapitalize,
  useCamelCase,
  useCurrencyFormat,
  useNumberFormat,
  useParseStringHtmlToText,
  useRemoveSpecialCharacter,
  useRemoveSpace,
  useFormatDateTime,
} from '../useText';
import { capitalizeFirstLetter } from '../../helpers/text.helper';

// Mock the constants
vi.mock('../constants/useText.constant', () => ({
  DEFAULT_CURRENCY_OPTIONS: {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
}));

describe('useCapitalize', () => {
  it('should be return the capitalize first name', () => {
    const productName = 'VAPOUR LT BAGGY SHORT AF';
    const productNameCapitalize = capitalizeFirstLetter(productName);
    expect(productNameCapitalize).toEqual('Vapour lt baggy short af');
  });
  it('should capitalize the first letter of each word', () => {
    expect(useCapitalize('hello world')).toBe('Hello World');
    expect(useCapitalize('javascript is awesome')).toBe('Javascript Is Awesome');
    expect(useCapitalize('UPPERCASE TEXT')).toBe('Uppercase Text');
    expect(useCapitalize('lowercase text')).toBe('Lowercase Text');
  });

  it('should handle single word', () => {
    expect(useCapitalize('hello')).toBe('Hello');
    expect(useCapitalize('WORLD')).toBe('World');
    expect(useCapitalize('tEsT')).toBe('Test');
  });

  it('should handle empty string', () => {
    expect(useCapitalize('')).toBe('');
  });

  it('should handle multiple spaces', () => {
    expect(useCapitalize('hello  world')).toBe('Hello  World');
    expect(useCapitalize('  test  case  ')).toBe('  Test  Case  ');
  });

  it('should handle single characters', () => {
    expect(useCapitalize('a b c')).toBe('A B C');
    expect(useCapitalize('x')).toBe('X');
  });

  it('should handle mixed case words', () => {
    expect(useCapitalize('hELLo WoRLd')).toBe('Hello World');
    expect(useCapitalize('jAVaScRiPt')).toBe('Javascript');
  });
});

describe('useCamelCase', () => {
  it('should convert space-separated words to camelCase', () => {
    expect(useCamelCase('hello world')).toBe('helloWorld');
    expect(useCamelCase('javascript is awesome')).toBe('javascriptIsAwesome');
    expect(useCamelCase('test case example')).toBe('testCaseExample');
  });

  it('should handle single word', () => {
    expect(useCamelCase('hello')).toBe('hello');
    expect(useCamelCase('WORLD')).toBe('wORLD');
  });

  it('should handle undefined input', () => {
    expect(useCamelCase(undefined)).toBe('');
  });

  it('should handle empty string', () => {
    expect(useCamelCase('')).toBe('');
  });

  it('should handle already camelCase text', () => {
    expect(useCamelCase('alreadyCamelCase')).toBe('alreadyCamelCase');
  });

  it('should handle mixed case input', () => {
    expect(useCamelCase('Hello World Test')).toBe('helloWorldTest');
    expect(useCamelCase('UPPER CASE TEXT')).toBe('uPPERCASETEXT');
  });
});

describe('useNumberFormat', () => {
  it('should format numbers with English locale', () => {
    expect(useNumberFormat(1000)).toBe('1,000');
    expect(useNumberFormat(1000000)).toBe('1,000,000');
    expect(useNumberFormat(500)).toBe('500');
  });

  it('should handle large numbers', () => {
    expect(useNumberFormat(1234567890)).toBe('1,234,567,890');
  });

  it('should handle decimal numbers (rounds due to maximumFractionDigits: 0)', () => {
    expect(useNumberFormat(1000.99)).toBe('1,001');
    expect(useNumberFormat(1000.49)).toBe('1,000');
  });
});

describe('useParseStringHtmlToText', () => {
  // Mock DOMParser for testing environment
  beforeEach(() => {
    global.DOMParser = class DOMParser {
      parseFromString(htmlString: string, type: string) {
        // Simple mock implementation
        const cleanText = htmlString
          .replace(/<[^>]*>/g, '') // Remove HTML tags
          .replace(/&nbsp;/g, ' ')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"');

        return {
          body: {
            textContent: cleanText,
          },
        };
      }
    };
  });

  it('should parse HTML string and return text content', () => {
    expect(useParseStringHtmlToText('<p>Hello World</p>')).toBe('Hello World');
    expect(useParseStringHtmlToText('<div><span>Test</span></div>')).toBe('Test');
    expect(useParseStringHtmlToText('<h1>Title</h1><p>Paragraph</p>')).toBe('TitleParagraph');
  });

  it('should handle plain text without HTML tags', () => {
    expect(useParseStringHtmlToText('Just plain text')).toBe('Just plain text');
  });

  it('should handle empty string', () => {
    expect(useParseStringHtmlToText('')).toBe('');
  });

  it('should handle HTML entities', () => {
    expect(useParseStringHtmlToText('&nbsp;Hello&nbsp;World&nbsp;')).toBe(' Hello World ');
    expect(useParseStringHtmlToText('&amp;&lt;&gt;&quot;')).toBe('&<>"');
  });

  it('should handle complex HTML structures', () => {
    expect(useParseStringHtmlToText('<div class="container"><p>Hello</p><span>World</span></div>')).toBe(
      'HelloWorld',
    );
  });
});

describe('useRemoveSpecialCharacter', () => {
  it('should remove special characters and keep alphanumeric and spaces', () => {
    expect(useRemoveSpecialCharacter('Hello@World!')).toBe('HelloWorld');
    expect(useRemoveSpecialCharacter('Test#123$')).toBe('Test123');
    expect(useRemoveSpecialCharacter('Normal Text 123')).toBe('Normal Text 123');
  });

  it('should handle empty string', () => {
    expect(useRemoveSpecialCharacter('')).toBe('');
  });

  it('should keep spaces between words', () => {
    expect(useRemoveSpecialCharacter('Hello World!')).toBe('Hello World');
    expect(useRemoveSpecialCharacter('Test 123 @#$')).toBe('Test 123 ');
  });

  it('should handle string with only special characters', () => {
    expect(useRemoveSpecialCharacter('@#$%^&*()')).toBe('');
  });

  it('should handle mixed content', () => {
    expect(useRemoveSpecialCharacter('<EMAIL>')).toBe('userexamplecom');
    expect(useRemoveSpecialCharacter('Price: $100.99')).toBe('Price 10099');
  });

  it('should preserve numbers and letters', () => {
    expect(useRemoveSpecialCharacter('ABC123xyz')).toBe('ABC123xyz');
  });
});

describe('useRemoveSpace', () => {
  it('should remove all spaces from string', () => {
    expect(useRemoveSpace('Hello World')).toBe('HelloWorld');
    expect(useRemoveSpace('Test 123 Example')).toBe('Test123Example');
    expect(useRemoveSpace('   Multiple   Spaces   ')).toBe('MultipleSpaces');
  });

  it('should handle empty string', () => {
    expect(useRemoveSpace('')).toBe('');
  });

  it('should handle string with only spaces', () => {
    expect(useRemoveSpace('   ')).toBe('');
  });

  it('should handle string without spaces', () => {
    expect(useRemoveSpace('NoSpaces')).toBe('NoSpaces');
    expect(useRemoveSpace('123456')).toBe('123456');
  });
});

describe('useFormatDateTime', () => {
  it('should format valid datetime string to Indonesian format with WIB', () => {
    expect(useFormatDateTime('2023-12-25 14:30:00')).toBe('25 Desember 2023 14:30 WIB');
    expect(useFormatDateTime('2023-01-01 09:15:30')).toBe('01 Januari 2023 09:15 WIB');
    expect(useFormatDateTime('2023-06-15 23:45:12')).toBe('15 Juni 2023 23:45 WIB');
  });

  it('should handle undefined input', () => {
    expect(useFormatDateTime(undefined)).toBe('');
  });

  it('should handle empty string', () => {
    expect(useFormatDateTime('')).toBe('');
  });

  it('should handle invalid date string', () => {
    expect(useFormatDateTime('invalid-date')).toBe('');
    expect(useFormatDateTime('not-a-date')).toBe('');
    expect(useFormatDateTime('2023-13-45 25:70:90')).toBe('');
  });

  it('should handle edge cases with time', () => {
    expect(useFormatDateTime('2023-12-25 00:00:00')).toBe('25 Desember 2023 00:00 WIB');
    expect(useFormatDateTime('2023-12-25 23:59:59')).toBe('25 Desember 2023 23:59 WIB');
  });

  it('should handle different months in Indonesian', () => {
    expect(useFormatDateTime('2023-01-15 12:00:00')).toBe('15 Januari 2023 12:00 WIB');
    expect(useFormatDateTime('2023-02-15 12:00:00')).toBe('15 Februari 2023 12:00 WIB');
    expect(useFormatDateTime('2023-03-15 12:00:00')).toBe('15 Maret 2023 12:00 WIB');
    expect(useFormatDateTime('2023-04-15 12:00:00')).toBe('15 April 2023 12:00 WIB');
    expect(useFormatDateTime('2023-05-15 12:00:00')).toBe('15 Mei 2023 12:00 WIB');
  });
});

describe('Integration tests', () => {
  it('should work with multiple functions together', () => {
    const htmlString = '<p>hello world test</p>';
    const textContent = useParseStringHtmlToText(htmlString);
    const capitalized = useCapitalize(textContent);
    const camelCased = useCamelCase(textContent);
    const noSpaces = useRemoveSpace(capitalized);

    expect(textContent).toBe('hello world test');
    expect(capitalized).toBe('Hello World Test');
    expect(camelCased).toBe('helloWorldTest');
    expect(noSpaces).toBe('HelloWorldTest');
  });

  it('should handle currency and number formatting together', () => {
    const number = 1234567;
    const numberFormat = useNumberFormat(number);
    expect(numberFormat).toBe('1,234,567');
  });
});
