import { describe, expect, it } from 'vitest';
import { useObjectToFormData } from '../useObjectToFormData';

describe('object to form data', () => {
  it('should be convert the object to form data', () => {
    const user = {
      avatar: new File(['Avatar'], 'avatar.txt', { type: 'text/plain' }),
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 20,
    };
    const formData = useObjectToFormData(user);

    const formDataEntryCount = Array.from(formData.entries()).length;

    expect(formData).toBeInstanceOf(FormData);
    expect(formDataEntryCount).toBe(Object.keys(user).length);
  });
});
