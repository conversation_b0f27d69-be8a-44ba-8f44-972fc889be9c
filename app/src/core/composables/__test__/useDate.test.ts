import dayjs from 'dayjs';
import { describe, it, expect, beforeEach } from 'vitest';
import { convertToPayloadDate, useDayJsFormat } from '../useDate';

const ERROR_MESSAGE = 'Invalid date format. Expected DD-MM-YYYY';

describe('convertToPayloadDate', () => {
  it('should convert valid DD-MM-YYYY format to YYYY-MM-DD', () => {
    expect(convertToPayloadDate('02-03-2025')).toBe('2025-03-02');
    expect(convertToPayloadDate('15-12-2024')).toBe('2024-12-15');
    expect(convertToPayloadDate('01-01-2023')).toBe('2023-01-01');
    expect(convertToPayloadDate('31-08-2022')).toBe('2022-08-31');
  });

  it('should handle single digit days and months', () => {
    expect(convertToPayloadDate('1-5-2025')).toBe('2025-5-1');
    expect(convertToPayloadDate('9-11-2024')).toBe('2024-11-9');
  });

  it('should handle leading zeros', () => {
    expect(convertToPayloadDate('01-05-2025')).toBe('2025-05-01');
    expect(convertToPayloadDate('09-11-2024')).toBe('2024-11-09');
  });

  it('should throw error for invalid format - too few parts', () => {
    expect(() => convertToPayloadDate('02-03')).toThrow(ERROR_MESSAGE);
    expect(() => convertToPayloadDate('2025')).toThrow(ERROR_MESSAGE);
    expect(() => convertToPayloadDate('')).toThrow(ERROR_MESSAGE);
  });

  it('should throw error for invalid format - too many parts', () => {
    expect(() => convertToPayloadDate('02-03-2025-extra')).toThrow(ERROR_MESSAGE);
    expect(() => convertToPayloadDate('02-03-2025-12-34')).toThrow(ERROR_MESSAGE);
  });

  it('should throw error for invalid format - no hyphens', () => {
    expect(() => convertToPayloadDate('02/03/2025')).toThrow(ERROR_MESSAGE);
    expect(() => convertToPayloadDate('02032025')).toThrow(ERROR_MESSAGE);
  });

  it('should handle edge cases with different separators', () => {
    expect(() => convertToPayloadDate('02.03.2025')).toThrow(ERROR_MESSAGE);
    expect(() => convertToPayloadDate('02 03 2025')).toThrow(ERROR_MESSAGE);
  });
});

describe('useDayJsFormat', () => {
  beforeEach(() => {
    // Reset dayjs locale before each test to ensure clean state
    dayjs.locale('en');
  });

  it('should format date with default DD/MM/YYYY format', () => {
    const result = useDayJsFormat('2025-03-02');
    expect(result).toBe('02/03/2025');
  });

  it('should format date with custom format', () => {
    expect(useDayJsFormat('2025-03-02', 'YYYY-MM-DD')).toBe('2025-03-02');
    expect(useDayJsFormat('2025-03-02', 'DD-MM-YYYY')).toBe('02-03-2025');
    expect(useDayJsFormat('2025-03-02', 'MMM DD, YYYY')).toBe('Mar 02, 2025');
  });

  it('should handle different date input formats', () => {
    expect(useDayJsFormat('2025/03/02')).toBe('02/03/2025');
    expect(useDayJsFormat('03-02-2025')).toBe('02/03/2025');
    expect(useDayJsFormat('March 2, 2025')).toBe('02/03/2025');
  });

  it('should work with Indonesian locale formatting', () => {
    // Test that Indonesian month names work with custom format
    const result = useDayJsFormat('2025-03-02', 'DD MMMM YYYY');
    // Since we set locale to 'id', this should show Indonesian month name
    expect(result).toBe('02 Maret 2025');
  });

  it('should handle various custom format patterns', () => {
    const date = '2025-12-25';

    expect(useDayJsFormat(date, 'DD/MM/YY')).toBe('25/12/25');
    expect(useDayJsFormat(date, 'MM-DD-YYYY')).toBe('12-25-2025');
    expect(useDayJsFormat(date, 'YYYY')).toBe('2025');
    expect(useDayJsFormat(date, 'MM')).toBe('12');
    expect(useDayJsFormat(date, 'DD')).toBe('25');
  });

  it('should handle edge cases with different date formats', () => {
    expect(useDayJsFormat('2025-01-01')).toBe('01/01/2025');
    expect(useDayJsFormat('2025-12-31')).toBe('31/12/2025');
  });

  it('should work with dayjs supported date strings', () => {
    expect(useDayJsFormat('2025-03-02T10:30:00')).toBe('02/03/2025');
    expect(useDayJsFormat('2025-03-02T10:30:00Z')).toBe('02/03/2025');
  });

  it('should handle invalid dates gracefully', () => {
    // dayjs will return 'Invalid Date' for invalid input
    const result = useDayJsFormat('invalid-date');
    expect(result).toBe('Invalid Date');
  });

  it('should handle empty string input', () => {
    const result = useDayJsFormat('');
    expect(result).toBe('Invalid Date');
  });
});

describe('Integration tests', () => {
  it('should work together - convert then format', () => {
    const ddmmyyyy = '15-06-2025';
    const converted = convertToPayloadDate(ddmmyyyy);
    const formatted = useDayJsFormat(converted);

    expect(converted).toBe('2025-06-15');
    expect(formatted).toBe('15/06/2025');
  });

  it('should handle round-trip conversion', () => {
    const originalDate = '25-12-2024';
    const converted = convertToPayloadDate(originalDate);
    const backToOriginal = useDayJsFormat(converted, 'DD-MM-YYYY');

    expect(backToOriginal).toBe(originalDate);
  });
});
