import { describe, expect, it, vi } from 'vitest';

describe('active tab', () => {
  it('should be call function after specified delay', () => {
    vi.useFakeTimers();

    const mockFunc = vi.fn();
    const debounceFunc = useDebounce(mockFunc, 500);

    debounceFunc();

    expect(mockFunc).not.toHaveBeenCalled();
    vi.advanceTimersByTime(500);
    expect(mockFunc).toHaveBeenCalled();

    vi.useRealTimers();
  });

  it('should be called once', () => {
    vi.useFakeTimers();

    const mockFunc = vi.fn();
    const debounceFunc = useDebounce(mockFunc, 500);

    for (let i = 0; i < 10; i++) {
      debounceFunc();
    }

    vi.advanceTimersByTime(500);
    expect(mockFunc).toHaveBeenCalledTimes(1);

    vi.useRealTimers();
  });
});
