import useVuelidate from "@vuelidate/core";
import { email, numeric, required,helpers,maxLength } from "@vuelidate/validators";

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useCustomerSupportService = ():ICustomerSupportProvided => {
  /**
   * @description Reactive data binding
   */
  const customerSupport_formInquiry = reactive({
    type: 'Perusahaan',
    full_name: '',
    no_telp: '',
    instituion_name: '',
    email: '',
    npwp_address: '',
    topic: '',
    question: '',
    terms: false,
  });

  /**
   * @description Form validations
   */
  const customerSupport_formRules: ComputedRef = computed(() => ({
    type: { required },
    full_name: { 
      required,
      invalid_full_name: helpers.withParams({customMessage: 'invalid characters' },
                  function (value: string) {
                    return  /^[a-zA-Z\s]+$/.test(value)
                }) },
    no_telp: { 
      required, 
      numeric,
      maxLength: maxLength(15),
                invalid_phone_number: helpers.withParams({customMessage: 'invalid characters' },
                  function (value: string) {
                    return  /^[1-9][0-9]*$/.test(value)
                })   
      },
    instituion_name: { required },
    email: { email, required },
    npwp_address: { required },
    topic: { required },
  }));

  const customerSupport_formValidations = useVuelidate(
    customerSupport_formRules,
    customerSupport_formInquiry,
    {
      $autoDirty: true, 
    },
  );

  /**
   * @description Handle business logic for open contact
   */
  const customerSupport_openWhatsApp = (): void => {
    window.open(
      "https://api.whatsapp.com/send/?phone=6281120002588&text&type=phone_number&app_absent=0",
      "_blank"
    );
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    customerSupport_formInquiry,
    customerSupport_formValidations,
    customerSupport_openWhatsApp
  };
};
