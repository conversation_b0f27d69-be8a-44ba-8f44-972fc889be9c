// Store
import { storeToRefs } from 'pinia';
import { useAppStore } from '../stores/app.store';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useAppService = (): IAppProvided => {
  /**
   * @description Injected variables
   */
  const store = useAppStore(); // Instance of the store
  const { app_headerMenu, app_headerMenuIsLoading, app_isLoading } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  /**
   * @description Handle fetch api logout. We call the app_fetchLogout function from the store to handle the request.
   */
  const app_fetchHeaderMenu = async (): Promise<void> => {
    await store.app_fetchHeaderMenu();
  };

  /**
   * @description Return everything what we need into an object
   */
  return {
    app_headerMenu,
    app_headerMenuIsLoading,
    app_isLoading,
    app_fetchHeaderMenu,
  };
};
