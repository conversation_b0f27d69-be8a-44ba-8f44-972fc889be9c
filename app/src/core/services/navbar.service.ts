// Primevue
import { useToast } from 'primevue/usetoast';

// Store
import { storeToRefs } from 'pinia';
import { useAppStore } from '../stores/app.store';
import { useAuthenticationStore } from '../../modules/authentication/stores/authentication.store';

/**
 * @description Closure function that returns everything what we need into an object
 */
export const useNavbarService = (): INavbarProvided => {
  /**
   * @description Injected variables
   */
  const route = useRouter(); // Instance of the router
  const store = useAppStore(); // Instance of the store
  const toast = useToast(); // Instance of the toast
  const authStore = useAuthenticationStore();
  const { app_searchQueryOfProduct, app_logoutIsLoading } = storeToRefs(store);

  /**
   * @description Constant variables
   */
  // Please put your constant variables here

  /**
   * @description Reactive data binding
   */
  const navbar_isOpenDialogConfirmationLogout = ref<boolean>(false);
  const navbar_isOpenBottomSheetConfirmationLogout = ref<boolean>(false);
  const navbar_isOpenDrawer = ref<boolean>(false);
  const navbar_width = ref<number>(window.innerWidth);
  /**
   * @description Handle fetch api logout. We call the app_fetchLogout function from the store to handle the request.
   */
  const navbar_fetchLogout = async (): Promise<void> => {
    try {
      await store.app_fetchLogout();

      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Logout successfully',
        life: 1000,
      });

      authStore.$patch(state => {
        state.authentication_accessToken = '';
        state.authentication_isLoading = false;
        state.authentication_userData = null;
        state.authentication_profileData = null;
        state.authentication_emailRegister = '';
        state.authentication_passwordRegister = '';
      });

      // Redirect to login page
      setTimeout(async () => {
        await navigateTo('/authentication/login');
      }, 1000);
    } catch (error) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: error as string,
        life: 3000,
      });
    }
  };

  /**
   * @description Handle business logic when user already applied the search query and press enter
   */
  const navbar_onApplySearch = async (): Promise<void> => {
    if (route.currentRoute.value.path === '/catalog') {
      return;
    }

    await navigateTo('/catalog');
  };

  /**
   * @description Handle business logic for clearing search query
   */
  const navbar_onClearSearch = (): void => {
    app_searchQueryOfProduct.value = '';
    navbar_isOpenDrawer.value = false;
  };

  /**
   * @description Handle change size width
   */
  const navbar_onHandleResize = () => (navbar_width.value = window.innerWidth);

  /**
   * @description Handle business logic for closing dialog confirmation logout
   */
  const navbar_onCloseDialogConfirmationLogout = (): void => {
    console.log('execute');
    navbar_isOpenDialogConfirmationLogout.value = false;
  };

  /**
   * @description Handle business logic for closing dialog confirmation logout
   */
  const navbar_onCloseBottomSheetConfirmationLogout = (): void => {
    navbar_isOpenBottomSheetConfirmationLogout.value = false;
  };

  /**
   * @description Handle business logic for logout
   */
  const navbar_onLogout = (): void => {
    navbar_fetchLogout();
  };

  /**
   * @description Handle business logic for opening dialog confirmation logout
   */
  const navbar_onOpenDialogConfirmationLogout = (): void => {
    console.log(navbar_isOpenBottomSheetConfirmationLogout);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    navbar_width.value < 640
      ? (navbar_isOpenBottomSheetConfirmationLogout.value = true)
      : (navbar_isOpenDialogConfirmationLogout.value = true);
  };

  /**
   * @description Handle if user is authenticated or not
   */
  const navbar_hasToken = (): boolean => {
    return !!authStore.authentication_accessToken;
  };

  /**
   * @description Handle side effect when reactive data binding is changed
   */
  watch(
    app_searchQueryOfProduct,
    useDebounce(searchQuery => {
      navbar_isOpenDrawer.value = searchQuery !== '';
    }, 500),
  );

  /**
   * @description Return everything what we need into an object
   */
  return {
    navbar_isOpenDialogConfirmationLogout,
    navbar_isOpenBottomSheetConfirmationLogout,
    navbar_isOpenDrawer,
    navbar_onApplySearch,
    navbar_onClearSearch,
    navbar_onCloseDialogConfirmationLogout,
    navbar_onCloseBottomSheetConfirmationLogout,
    navbar_onLogout,
    navbar_logoutIsLoading: app_logoutIsLoading,
    navbar_onOpenDialogConfirmationLogout,
    navbar_hasToken,
    navbar_width,
    navbar_onHandleResize,
    navbar_searchQuery: app_searchQueryOfProduct,
  };
};
