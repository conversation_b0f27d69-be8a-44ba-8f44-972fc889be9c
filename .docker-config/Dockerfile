# Stage 1 : Build Stage
FROM node:20-alpine as build-stage

# Install dependencies and required tools
RUN apk add --no-cache curl bash

RUN mkdir -p /home/<USER>/app/node_modules && chown -R node:node /home/<USER>/app
WORKDIR /home/<USER>/app
USER node

# Install Bun directly using the recommended method
RUN curl -fsSL https://bun.sh/install | bash

# Make sure bun is in the PATH for the node user
ENV PATH="/home/<USER>/.bun/bin:$PATH"

# Copy package.json and bun.lockb (if available) to install dependencies
COPY --chown=node package*.json ./
COPY --chown=node bun.lockb ./

# Install dependencies using Bun
RUN bun install

# Nuxt Build
COPY --chown=node .env .env
COPY --chown=node . .
RUN bun run build

ENV PORT=3100

EXPOSE 3100

CMD [ "node", ".output/server/index.mjs" ]