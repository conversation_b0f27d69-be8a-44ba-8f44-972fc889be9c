{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "start:dev": "nuxt dev", "generate": "nuxt generate", "generate:module": "node generate-module.mjs", "lint:check": "node lint-check.mjs", "preview": "nuxt preview", "postinstall": "nuxt prepare", "prepare": "husky", "test": "vitest", "test:watch": "vitest:watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"@nuxt/eslint": "^0.7.5", "@nuxt/image": "1.9.0", "@nuxt/kit": "^3.15.3", "@pinia/nuxt": "^0.9.0", "@primevue/themes": "^4.2.5", "@tailwindcss/vite": "^4.0.0", "@types/fabric": "^5.3.10", "@vitest/coverage-v8": "^3.1.4", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/core": "^13.1.0", "@vueuse/nuxt": "^13.1.0", "axios": "^1.8.4", "consola": "^3.4.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "eslint": "^9.18.0", "fabric": "5.2.1", "intl": "^1.2.5", "laravel-echo": "^2.0.2", "nuxt": "^3.15.3", "nuxt-security": "2.1.5", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "4.2.0", "pusher-js": "^8.4.0", "swiper": "^11.2.2", "tailwindcss": "^4.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-remove-console": "^2.2.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-carousel": "^0.13.0", "vue3-circle-progress": "^1.0.7"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@nuxt/test-utils": "^3.15.4", "@primevue/nuxt-module": "^4.2.5", "@types/intl": "^1.2.2", "@vue/test-utils": "^2.4.6", "happy-dom": "^16.7.3", "husky": "^9.1.7", "playwright-core": "^1.50.0", "typescript": "^5.7.3", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^3.0.4"}}