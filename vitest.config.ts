import { defineVitestConfig } from '@nuxt/test-utils/config';

export default defineVitestConfig({
  test: {
    environment: 'nuxt',
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        '.nuxt/**',
        'coverage/**',
        'dist/**',
        'packages/*/test{,s}/**',
        '**/*.d.ts',
        'cypress/**',
        'test{,s}/**',
        'test{,-*}.{js,cjs,mjs,ts,tsx,jsx}',
        '**/*{.,-}test.{js,cjs,mjs,ts,tsx,jsx}',
        '**/*{.,-}spec.{js,cjs,mjs,ts,tsx,jsx}',
        '**/__tests__/**',
        '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
        '**/.{eslint,mocha,prettier}rc.{js,cjs,yml}',
        'app.vue',
        'commitlint.config.js',
        'eslint.config.mjs',
        'generate-module.mjs',
        'lint-check.mjs',
        'nuxt.config.ts',
        'app/src/modules/**/components/**',
        'app/src/modules/**/views/**',
        'app/src/modules/**/constants/**',
        'app/src/modules/**/interfaces/**',
        'app/src/modules/**/services/**',
        'app/src/modules/**/routes/**',
        'app/src/modules/**/stores/**',
        'app/src/core/components/base/**',
        'app/src/core/components/layouts/**',
        'app/src/modules/authentication/constants**',
      ],
    },
  },
});
