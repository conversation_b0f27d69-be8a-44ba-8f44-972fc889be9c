// Primevue theme
import <PERSON><PERSON> from '@primevue/themes/aura';
import { definePreset } from '@primevue/themes';

// Path
import path from 'path';

// Tailwind CSS
import tailwindcss from '@tailwindcss/vite';

/**
 * @description Vite plugin to minify svg files
 * @see https://github.com/vbenjs/vite-plugin-svg-icons
 */
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

/**
 * @description Vite plugin to remove console.log and other console.* calls from your code.
 * @see https://github.com/xiaoxian521/vite-plugin-remove-console
 */
import removeConsole from 'vite-plugin-remove-console';

/**
 * @description Vite plugin to compress the build output using vite-plugin-compression.
 * @see https://github.com/vbenjs/vite-plugin-compression
 */
import viteCompression from 'vite-plugin-compression';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  /**
   * @description Here's you can configure the app options
   */
  app: {
    head: {
      title: 'EIGER B2B',
    },
  },

  /**
   * @description Here's you can configure the build options
   */
  build: {},

  /**
   * @description Specify a compatibility date for your app. This is used to control the behavior of presets in Nitro, Nuxt Image and other modules that may change behavior without a major version bump.
   */
  compatibilityDate: '2024-11-01',

  /**
   * @description Here's you can configure the components options
   */
  components: {
    dirs: [
      {
        path: 'app/src/core/components',
        pathPrefix: false,
      },
    ],
  },

  /**
   * @description Here's you can configure the list of css
   */
  css: ['./app/main.css'],

  /**
   * @description Here's you can configure to turn on the dev tools
   */
  devtools: {
    enabled: true,
  },

  /**
   * @description Here's you can configure the directory options
   */
  dir: {
    // assets: 'app/src/core/assets',
    layouts: 'app/src/core/components/layouts',
    middleware: 'app/middlewares',
    plugins: 'app/src/plugins',
  },

  /**
   * @description Here's you can configure the features options
   * @note This is one of approach to enable us to migrate to Nuxt 4. For more information, please visit https://www.youtube.com/watch?v=r4wFKlcJK6c
   */
  future: {
    compatibilityVersion: 3, // Please set this to 4 when you are ready to migrate to Nuxt 4
  },

  /**
   * @description Here's you can configure the nuxt image options
   */
  image: {
    // dir: 'app/src/core/assets',
    domains: ['careom-middleware-dev.eigerindo.co.id', 'careom-middleware.eigerindo.co.id'],
    format: ['webp'],
  },

  /**
   * @description Here's you can configure the auto import options
   */
  imports: {
    dirs: ['./app/src/core/composables', './app/src/core/constants'],
  },

  /**
   * @description Here's you can configure the modules
   */
  modules: [
    '@nuxt/eslint',
    '@nuxt/image',
    '@nuxt/test-utils/module',
    '@pinia/nuxt',
    '@primevue/nuxt-module',
    '@vueuse/nuxt',
    'nuxt-security',
    'pinia-plugin-persistedstate/nuxt',
    '@vueuse/nuxt',
  ],

  /**
   * @description Here's you can configure to turn on automatic route generation
   */
  pages: true,

  primevue: {
    autoImport: true,
    components: {
      prefix: 'PrimeVue',
      include: '*',
    },
    options: {
      locale: {
        fileSizeTypes: [],
        dayNames: ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'],
        dayNamesShort: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
        dayNamesMin: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
        monthNames: [
          'Januari',
          'Februari',
          'Maret',
          'April',
          'Mei',
          'Juni',
          'Juli',
          'Agustus',
          'September',
          'Oktober',
          'November',
          'Desember',
        ],
        monthNamesShort: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'],
      },
      theme: {
        preset: definePreset(Aura, {
          semantic: {
            primary: {
              50: '{orange.50}',
              100: '{orange.100}',
              200: '{orange.200}',
              300: '{orange.300}',
              400: '{orange.400}',
              500: '{orange.500}',
              600: '{orange.600}',
              700: '{orange.700}',
              800: '{orange.800}',
              900: '{orange.900}',
              950: '{orange.950}',
            },
            /*
            * This code snippet comes from the AppConfigurator.vue file,
            * from the getPresetExt function that returns this configuration,
            I thought it would be prudent to bring it too
            */
            colorScheme: {
              light: {
                primary: {
                  color: '{primary.500}',
                  contrastColor: '#ffffff',
                  hoverColor: '{primary.600}',
                  activeColor: '{primary.700}',
                },
                highlight: {
                  background: '{primary.50}',
                  focusBackground: '{primary.100}',
                  color: '{primary.700}',
                  focusColor: '{primary.800}',
                },
                surface: {
                  0: '#ffffff',
                  50: '{slate.50}',
                  100: '{slate.100}',
                  200: '{slate.200}',
                  300: '{slate.300}',
                  400: '{slate.400}',
                  500: '{slate.500}',
                  600: '{slate.600}',
                  700: '{slate.700}',
                  800: '{slate.800}',
                  900: '{slate.900}',
                  950: '{slate.950}',
                },
              },
              dark: {
                primary: {
                  color: '{primary.400}',
                  contrastColor: '{surface.900}',
                  hoverColor: '{primary.300}',
                  activeColor: '{primary.200}',
                },
                highlight: {
                  background: 'color-mix(in srgb, {primary.400}, transparent 84%)',
                  focusBackground: 'color-mix(in srgb, {primary.400}, transparent 76%)',
                  color: 'rgba(255,255,255,.87)',
                  focusColor: 'rgba(255,255,255,.87)',
                },
                surface: {
                  0: '#ffffff',
                  50: '{slate.50}',
                  100: '{slate.100}',
                  200: '{slate.200}',
                  300: '{slate.300}',
                  400: '{slate.400}',
                  500: '{slate.500}',
                  600: '{slate.600}',
                  700: '{slate.700}',
                  800: '{slate.800}',
                  900: '{slate.900}',
                  950: '{slate.950}',
                },
              },
            },
          },
        }),
        options: {
          darkModeSelector: '.dark',
        },
      },
      ripple: false,
    },
  },

  /**
   * @description Here's you can configure the router options
   */
  router: {
    options: {
      scrollBehaviorType: 'smooth',
    },
  },

  /**
   * @description Here's you can configure runtime options
   */
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE_URL ?? 'https://careom-api-dev.eigerindo.co.id/api',
      middlewareBaseUrl:
        process.env.NUXT_PUBLIC_MIDDLEWARE_BASE_URL ?? 'https://careom-middleware-dev.eigerindo.co.id',
      wsHost: process.env.NUXT_PUBLIC_WS_URL,
    },
  },

  /**
   * @description Here's you can configure to use ssr or static site generation
   */
  ssr: false,

  /**
   * @description Here's you can configure the vite options
   */
  vite: {
    plugins: [
      createSvgIconsPlugin({
        // Specify the icon folder to be cached
        iconDirs: [path.resolve(process.cwd(), 'app/assets/icons')],

        // Specify symbolId format
        symbolId: 'icon-[dir]-[name]',
      }),
      removeConsole(),
      tailwindcss(),
      viteCompression(),
    ],
  },
  security: {
    rateLimiter: false,
    headers: {
      contentSecurityPolicy: {
        'img-src': ["'self'", 'data:', '*'],
      },
    },
  },
});
